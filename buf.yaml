version: v2
modules:
  - path: ./api
    name: buf.build/zltx/card-mall
  - path: ./third_party
    name: buf.build/zltx/third-party
deps:
  - buf.build/envoyproxy/protoc-gen-validate
  - buf.build/googleapis/googleapis
  - buf.build/kratos-go/kratos
lint:
  use:
    - DEFAULT # https://docs.buf.build/lint/rules#default
    - PACKAGE_NO_IMPORT_CYCLE
#    - COMMENT_ENUM
#    - COMMENT_ENUM_VALUE
    - COMMENT_FIELD
#    - COMMENT_MESSAGE
#    - COMMENT_ONEOF
#    - COMMENT_RPC
#    - COMMENT_SERVICE
  except:
   - ENUM_VALUE_PREFIX
   - ENUM_ZERO_VALUE_SUFFIX
   - PACKAGE_VERSION_SUFFIX
   - RPC_REQUEST_STANDARD_NAME
   - RPC_RESPONSE_STANDARD_NAME
  enum_zero_value_suffix: _UNKNOWN
  rpc_allow_same_request_response: false
  rpc_allow_google_protobuf_empty_requests: false
  rpc_allow_google_protobuf_empty_responses: false
  ignore:
    - ./third_party
breaking:
  use:
    - FILE
