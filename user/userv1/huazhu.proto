syntax = "proto3";

package user.userv1;
option go_package = "./user/userv1;userv1";

// 产品服务
service HuazhuRpc {
  rpc Enable (EnableReq) returns (EnableResp) {}
  rpc Disable (DisableReq) returns (DisableResp) {}
  rpc GetLoginUrl (GetLoginUrlReq) returns (GetLoginUrlResp) {}
  rpc GetHuazhuConf (GetHuazhuConfReq) returns (GetHuazhuConfResp) {}
}

message EnableReq {
  int32 userId = 1;
}
message EnableResp{}message DisableReq {
  int32 userId = 1;
}
message DisableResp{}

message GetLoginUrlReq {
  // 钉钉免密授权码
  string dingAuthCode = 1;
}
message GetLoginUrlResp{
  // 华住登录跳转地址
  string url = 1;
  // 是否有权限
  bool hasPerm = 2;
}
message GetHuazhuConfReq{}
message GetHuazhuConfResp{
  string dingCorpId = 1;
}
