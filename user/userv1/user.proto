syntax = "proto3";

package user.userv1;
import "validate/validate.proto";
import "user/userv1/common.proto";
option go_package = "./user/userv1;userv1";

/**
 * 用户相关
 */
service User {
  // 修改密码
  rpc UpdatePassword (ReqUserUpdatePassword) returns (RespEmpty);

  // 获取用户钉钉信息
  rpc GetDingtalk (ReqUserGetDingtalk) returns (RespUserDingtalk);

  // 获取用户信息
  rpc GetInfo (ReqUserGetInfo) returns (RespUser);

  // 获取用户信息
  rpc Search (ReqUserGetList) returns (RespUsers);

  // 获取用户信息
  rpc SimpleListAllUser (SimpleListAllUserReq) returns (SimpleListAllUserResp);

  //修改状态
  rpc ChangeStatus (ReqUserChangeStatus) returns (RespEmpty) {}

  // 管理员保存用户信息
  rpc SaveByManager(ReqUserSaveByManager) returns (RespUser) {}

  // 取用户简介
  rpc GetProfile (ReqUserGetProfile) returns (RespUser) {}

  //取用户配置的组信息
  rpc GetGroups (ReqUserGetGroups) returns (RespUserGroups){}

  //取用户配置的组信息
  rpc GetSystems (ReqUserGetSystems) returns (RespUserSystems){}

  //获取所有的新组织部门信息
  rpc GetNewDepartments(ReqEmpty) returns(RespUserNewDepartments) {}

  //获取所有的新组织岗位信息
  rpc GetNewPosts(ReqEmpty) returns(RespUserNewPosts) {}

  //获取所有的新组织岗位信息
  rpc GetAllSystems(ReqEmpty) returns(RespUserAllSystems) {}

  //取消子账号
  rpc CancelSubordinate(ReqUserCancelSubordinate) returns(RespEmpty) {}

  //获取有权限的菜单
  rpc GetMyMenus(ReqUserGetMyMenus) returns(RespUserGetMyMenus) {}

  // 获取用户列表
  rpc GetUsersByIds(ReqUserGetUsersByIds) returns(RespUsers) {}

  // 管理员重置密码
  rpc UpdatePasswordByManager (ReqUserUpdatePasswordByManager) returns (RespEmpty);

  // 根据钉钉用户id获取钉钉用户信息
  rpc GetDingUsersMapByDIds (ReqGetDingUsersMapByDIds) returns (RespReqGetDingUsersMapByDIds);
}

message ReqGetDingUsersMapByDIds {
  repeated string dingUserIds = 1;
}

message RespReqGetDingUsersMapByDIds {
  map<string, DingUser> userMap = 1;
  message DingUser {
    // 钉钉用户id
    string userId = 1;

    // 昵称
    string nick = 2;

    // openid
    string openId = 3;

    // 职位
    string title = 4;

    // 手机号
    string mobile = 5;

    // 邮箱
    string email = 6;

    // 头像
    string avatarUrl = 7;

    // 是否雇用中
    bool isEmploye = 8;

    // 姓名
    string name = 9;
  }
}

message RespUsers {
  repeated RespUser list = 1;
  RespPage page = 2;
}

/**
 * 登录成功信息
 */
message RespUser {
  int32  id = 1;
  string Username = 2;
  string password = 3;
  string unionID = 4;
  string remark = 5;
  string realname = 6;
  string phone = 7;
  int32  structureID = 8;
  int32  postID = 9;
  int32  status = 10;
  int32  createTime = 11;
  RespUserDingtalk dingtalk = 12;
  string statusName = 13;
  int32 mainId = 14;
  //开通的系统信息
  repeated System systems = 15;
  //是否是用户中心管理员
  bool IsAdminUserCenter = 16;
  string dingtalk_userid = 17 ;
  string dingtalk_nickname = 18 ;
  string avatar_url = 19;

  // 开通的系统信息
  message System {
    int32 id = 1;
    string name = 2;
    bool isAdmin = 3;
    string code = 4;
    // 数据类型：1-所有，2-账号
    int32 dataType = 5;
  }

	// 上级的uid
	int32 leadership = 20;
	// 上级名称
	string leadershipName = 21;
}


/**
 * 修改用户密码
 */
message ReqUserUpdatePassword {
  int32 userId = 1 [(validate.rules).int32.gt = 0]; // 密码
  string password = 2 [(validate.rules).string.min_len = 6]; // 密码
  string oldPassword = 3 [(validate.rules).string.min_len = 6]; // 旧密码
}

message ReqUserUpdatePasswordByManager {
  int32 userId = 1 [(validate.rules).int32.gt = 0]; // 密码
  string password = 2 [(validate.rules).string.min_len = 6]; // 密码
}

// 获取用户信息
message ReqUserGetInfo {
  int32 userId = 1 [(validate.rules).int32.gt = 0]; // 用户id
}

// 获取用户信息
message ReqUserGetDingtalk {
  int32 userId = 1 [(validate.rules).int32.gt = 0]; // 用户id
}

// 用户钉钉信息
message RespUserDingtalk {
  int32 id = 1;
  string nick = 2;
  string avatarURL = 3;
  string mobile = 4;
  string openID = 5;
  string unionID = 6;
  string email = 7;
  string stateCode = 8;
  string userID = 9;
  string name = 10;
  string jobNumber = 11;
  string title = 12;
  string extension = 13;
  string deptIDList = 14;
  int32 hiredDate = 16;
  int32 createTime = 17;
  int32 updateTime = 18;
  int32 hiredDays = 19;
  string shortDepartment = 20; //简短的展示部门信息
  RespUserDingtalk managerUserDingtalk = 21; //上级领域信息
  bool enableHuazhu = 22;
}


// 请求用户列表
message ReqUserGetList {
  ReqStatus status = 1 [(validate.rules).enum.defined_only = true]; // 搜索状态
  ReqType type = 2 [(validate.rules).enum.defined_only = true]; // 搜索账号类型
  string phone = 3; // 搜索手机
  string realName = 4; //搜索真实姓名
  int32 mainId = 5; //搜索某主账号下的子账号

  ReqPage page = 6; // 分页

  repeated string group_codes = 7; // 组编码
  string systemCode = 8; // 系统编码
  //账号状态
  enum ReqStatus {
    STATUS_ALL = 0; //所有
    STATUS_DISABLE = 1; //禁用
    STATUS_ENABLE = 2; //启用
  }
  //账号类型
  enum ReqType {
    TYPE_ALL = 0; //所有
    TYPE_MAIN = 1; //主账号
    TYPE_SUB = 2; //子账号
  }
}

// 改变用户状态
message ReqUserChangeStatus {
  int32 id = 1 [(validate.rules).int32.gt = 0];
  Status status = 2 [(validate.rules).enum.defined_only = true];
  enum Status {
    DISABLE = 0; // 禁用
    ENABLE = 1; // 开启
    DELETE = -1; // 删除
  }
}

// 保存用户
message ReqUserSaveByManager {
  int32  id = 1;
  string Username = 2;
  string password = 3;
  string realname = 6;
  string phone = 7;
  repeated int32 departmentDingtalkIds = 8; //部门钉钉ids,多个用,号隔开
  string postName = 9; //职位名称
  string avatarUrl = 11;

  // 已经开通的系统
  repeated OpenSystem openSystems = 12;
  // 主账号id
  int32 mainId = 13;

  // 用户中心角色名称
  message OpenSystem {
    // 系统 id
    int32 systemId = 1;
    // 组 id
    repeated int32 groupIds = 2;
    // 是否是管理员
    bool isAdmin = 3;
    // 数据类型
    int32 dataType = 4;
  }
	// 上级的uid
	int32 leadership = 17;
}

// 取用户简介
message ReqUserGetProfile {
  int32  id = 1;
}

// 获取用户配置信息
message ReqUserGetGroups {
  int32  id = 1 [(validate.rules).int32.gt = 0];
}

// 获取用户配置信息
message RespUserGroups {
  repeated UserGroup list = 1;
  message UserGroup {
    int32 groupId = 1;
    int32 systemId = 2;
    string code = 3;
  }
}


// 获取用户配置信息
message ReqUserGetSystems {
  int32  id = 1 [(validate.rules).int32.gt = 0];
}

// 返回系统
message RespUserSystems {
  repeated RespUserSystem list = 1;
}

// 用户系统
message RespUserSystem {
  int32 id = 1;
  string name = 2;
  string code = 3;
  bool isOpen = 4;
  bool isAdmin = 5;
  // 数据类型：1-所有，2-账号
  int32 dataType = 6;
  bool enable= 7;
}

// 新部门
message RespUserNewDepartments {
  repeated Item list = 1;
  message Item {
    int32 id = 1;
    int32 dingtalkId = 2;
    string name = 3;
  }
}

// 新职位
message RespUserNewPosts {
  repeated Item list = 1;
  message Item {
    int32 id = 1;
    string name = 2;
  }
}

// 所有的系统
message RespUserAllSystems {
  repeated RespUserSystem list = 1;
}


// 取消子账号请求参数
message ReqUserCancelSubordinate {
  int32 id = 1; // 子账号 id
}

// 获取用户菜单
message ReqUserGetMyMenus {
  int32 systemId = 1 [(validate.rules).int32.gt = 0];
  int32 userId = 2 [(validate.rules).int32.gt = 0];
}

// 用户菜单
message RespUserGetMyMenus {
  repeated Menu list = 1;
  // 菜单
  message Menu {
    int32 id = 1;
    int32 parentId = 2;
    string name = 3;
    string url = 4;
    string icon = 5;
    int32 sort = 6;
    int32 status = 7;
    int32 type = 8;
    int32 systemId = 9;
    string code = 10;
    string remark = 11;
    int32 createTime = 12;
    int32 updateTime = 13;
  }
}

message ReqUserGetUsersByIds {
  repeated int32 ids = 1;
}

message SimpleListAllUserReq{
  string keyword = 1;
}
message SimpleListAllUserResp{
  repeated AdminSimpleInfo list = 1;
}
message AdminSimpleInfo{
  int32 id = 1;
  string username = 2;
  string realname = 3;
}