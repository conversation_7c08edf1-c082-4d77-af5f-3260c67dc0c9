syntax = "proto3";

package user.userv1;
import "validate/validate.proto";
import "user/userv1/common.proto";
option go_package = "./user/userv1;userv1";
// 用户组（角色）
service Group {
  // 列表
  rpc list (ReqGroupList) returns (RespGroupList);

  // 详情
  rpc info (ReqGroupInfo) returns (RespGroupInfo);

  // 保存
  rpc save (ReqGroupSave) returns (RespGroupInfo);

  // 改变状态
  rpc changeStatus (ReqGroupChangeStatus) returns (RespEmpty);

  // 组中有用户的数量
  rpc HaveUserCnt (ReqGroupHaveUserCnt) returns (RespGroupHaveUserCnt);
}

// 组列表
message ReqGroupList {
  int32 systemId = 1; //系统 id
  Status status = 2; // 状态
  int32 userId = 3;// 限制取用户拥有的组
  enum Status {
    ALL = 0; // 全部
    ENABLE = 1; // 启用
    DISABLE = 2; // 禁用
  }
}

// 返回角色列表
message RespGroupList {
  repeated RespGroupInfo list = 1;
}

// 角色详情
message RespGroupInfo {
  int32 id = 1; //角色 id
  string title = 2; // 角色名称
  string menus = 3; // 角色权限 id
  int32 pid = 4; // 上级 id
  string remark = 5; // 备注
  int32 status = 6; // 状态
  int32 systemID = 7; // 所属系统 id
  string statusName = 8; // 状态名称

  RespGroupInfo prevGroup = 9; // 上级信息
  string code = 10; // code
  string menuDataPrivilege = 11;
}

// 获取详情
message ReqGroupInfo {
  int32 id = 1; //角色 id
}

// 保存角色
message ReqGroupSave {
  int32 id = 1; //角色 id，大于0表示更新
  string title = 2 [(validate.rules).string.min_len = 1]; // 角色名称
  string menus = 3; // 角色权限 id
  int32 pid = 4; // 上级 id
  string remark = 5; // 备注
  int32 systemID = 7 [(validate.rules).int32.gt = 0]; // 所属系统 id
  string code = 8; // code
  string menuDataPrivilege = 9;
}

// 更新角色状态
message ReqGroupChangeStatus {
  int32 id = 1; //角色 id
  int32 status = 6 [(validate.rules).int32.gte = -1]; // 状态
}

// 请求角色是否有用户
message ReqGroupHaveUserCnt {
  int32 id = 1; //角色 id
}

// 响应角色是否有用户
message RespGroupHaveUserCnt {
  int32 cnt = 1; // 用户数量
}