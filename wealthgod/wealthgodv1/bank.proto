syntax = "proto3";

package wealthGod.api.bank;

option go_package = "./wealthgod/wealthgodv1;wealthgodv1";

service Bank {
	rpc ListBank (ListBankRequest) returns (ListBankReply);
	// 支付
	rpc Pay(PayRequest) returns (PayReply);
	// 支付查询
	rpc PayQuery(PayQueryRequest) returns (PayReply);
	// 回单查询
	rpc Receipt(ReceiptQueryRequest) returns (ReceiptReply);
}

message ListBankRequest {
	string keyword = 1;
}
message ListBankReply {
	repeated BankItem list = 1;
}

message PayRequest {
	// 订单号
	string orderNo = 1;
	// 支付金额(分)
	double amount = 2;
	// 收方帐号
	string payeeAccount = 3;
	// 收方户名
	string payeeName = 4;
	// 收方开户行
	string payeeBank = 5;
	// 转出帐号
	string payerAccount = 6;
	// 转出户名
	string payerName = 7;
	// 转出开户行
	string payerBank = 8;
	// 用途
	string purpose = 9;
	// 备注
	string remark = 10;
	// 是否跨行
	bool isCrossBank = 11;
	// 联行号
	string brdNbr = 12;
	// 银行编码
	//  cmb 招商银行
	string bankCode = 13;
	// 转出开户行地址
	string payerBankAddress = 14;
}

message PayReply {
	// 支付流水号
	string tradeNo = 1;
	// 支付状态 1-成功 2-处理中 3-失败
	int32 status = 2;
	// 支付明细信息
	string payReq = 3;
	// 支付响应信息
	string payReply = 4;
	// 错误信息
	string errMsg = 5;
}

message PayQueryRequest {
	// 订单号
	string orderNo = 1;
	//  cmb 招商银行
	string bankCode = 2;
}

message BankItem {
	// 银行编号
	string bnknbr = 1;
	// 联行号
	string brdnbr = 2;
	// 联行类型
	string brdtyp = 3;
	// 城市代码
	string ctycod = 4;
	// 内部编号
	string innnbr = 5;
	// 更新日期
	string upddat = 6;
	// 银行名称
	string whlnam = 7;
}

message ReceiptQueryRequest {
	// 订单号
	string orderNo = 1;
	//  cmb 招商银行
	string bankCode = 2;
	// 转出帐号
	string payerAccount = 3;
}

message ReceiptReply {
	// 订单号
	string orderNo = 1;
	// 回单流水号
	string tradeID = 2;
	// 回单信息
	string pdfB64 = 3;
	// 错误信息
	string errMsg = 4;
}