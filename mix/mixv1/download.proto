syntax = "proto3";

package mixservice.mixv1;
import "validate/validate.proto";
import "mix/mixv1/common.proto";
option go_package = "./mix/mixv1;mixv1";

// 下载
service Download {
  // 列表
  rpc SearchList (ReqDownloadSearchList) returns (RespDownloadSearchList) {}

  // 获取
  rpc GetLastValidMapByTypeIds (ReqGetLastValidMap) returns (RespGetLastValidMap) {}

  // 创建.
  rpc Create(ReqDownloadCreate) returns (RespResourceDownloadDetail) {}

  // Delete 删除
  rpc Delete(ReqDownloadDelete) returns (Empty) {}

  // ChangeStatus 改变状态
  rpc ChangeStatus(ReqDownloadChangeStatus) returns (RespResourceDownloadDetail) {}

  // SaveDownloadInfo 审批通过，保存下载信息
  rpc SaveDownloadInfo(ReqDownloadSaveDownloadInfo) returns (RespResourceDownloadDetail) {}

  // SaveDownloadInfo 保存下载信息
  rpc GetDownloadUrl(ReqDownloadGetDownloadUrl) returns (RespDownloadGetDownloadUrl) {}

  // 驳回
  rpc Reject(ReqDownloadReject) returns (Empty) {}
}

message ReqDownloadReject {
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
}

message ReqDownloadSaveDownloadInfo {
  // id
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
  // 过期时间，如果为0，则由 server 默认过期时间
  int32 expire_time = 3 [(validate.rules).int32 = {gte:0}];
}

message ReqDownloadDelete {
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
  int32 user_id = 2 [(validate.rules).int32 = {gt:0}];
}

message ReqDownloadChangeStatus {
  //-1-已删除，0-终止，1-审批中，2-审批通过，3-生成中，4-已生成
  enum Status {
    Stop = 0; // 终止
    Approving = 1; // 审批中
    Approved = 2; // 审批通过
    Generating = 3; // 生成中
    Generated = 4; // 已生成
    Reject = 5; // 驳回
  }
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
  Status status = 2;
}

message ReqDownloadCreate {
  int32 user_id = 1 [(validate.rules).int32 = {gt:0}];
  int32 type = 2 [(validate.rules).int32 = {gt:0}];
  string name = 3 [(validate.rules).string = {min_len:1}];
  int32 type_out_id = 4 [(validate.rules).int32 = {gt:0}];
  string url = 5 [(validate.rules).string = {min_len:1,max_len:1000}];
}

message ReqGetLastValidMap {
  enum Type {
    UNKNOWN = 0;
    CONTRACT = 1;
  }
  // 类型
  Type type = 1;
  // 类型关联id
  repeated int32 type_out_ids = 2 [(validate.rules).repeated = {min_items:1,unique:true, items:{int32:{gt:0}}}];
  // 用户id
  int32 user_id = 3 [(validate.rules).int32 = {gt:0}];
}

message RespGetLastValidMap {
  map<int32, RespResourceDownloadDetail> maps = 1;
}

message ReqDownloadGetDownloadUrl {
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
  int32 user_id = 2 [(validate.rules).int32 = {gt:0}];
}

message RespDownloadGetDownloadUrl {
  string download_url = 1;
  int32 end_time = 2;
  string name = 3;
  int32 type = 4 ;
  int32 type_out_id = 5;
}

message ReqDownloadSearchList {
  // 类型
  repeated int32 types = 1 [(validate.rules).repeated = {unique:true, items:{int32:{gt:0}}}];
  // 类型关联id
  repeated int32 type_out_ids = 2 [(validate.rules).repeated = {unique:true, items:{int32:{gt:0}}}];
  // 用户ids
  repeated int32 user_ids = 3 [(validate.rules).repeated = {unique:true, items:{int32:{gt:0}}}];
  // 分页请求
  ReqPage page = 4;
  // 关键词
  string keyword = 5;
}

// 响应操作日志搜索列表
message RespDownloadSearchList {
  // 列表
  repeated RespResourceDownloadDetail list = 1;
  // 分页
  RespPage page = 2;
}

// 日志详情
message RespResourceDownloadDetail {
  int32 id = 1;
  int32 user_id = 2; // 用户id
  int32 type = 3; // 类型,1-合同
  string name = 4; // 资源名称
  int32 type_out_id = 5; // 关联的外部 id,合同类型-contract.id
  string url = 6; // 私有链接
  string download_url = 7; // 下载链接
//  int32 download_cnt = 8; // 下载次数
  int32 end_time = 9; // 下载链接失效时间
  int32 create_time = 10; // 创建时间
  int32 update_time = 11; // 更新时间
  int32 status = 12; // 状态，-1-已删除，0-终止，1-审批中，2-审批通过，3-生成中，4-已生成,5-驳回
  string status_name = 13; // 状态名称
  string type_name = 14; // 类型名称
}