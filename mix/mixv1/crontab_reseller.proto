syntax = "proto3";

import "mix/mixv1/common.proto";
import "mix/mixv1/crontab.proto";

package mixservice.mixv1;
option go_package = "./mix/mixv1;mixv1";

service CrontabReseller {
  // 上游定时任务列表
  rpc SearchCrontabReseller(SearchCrontabResellerReq) returns (SearchCrontabResellerResp) {}
  // 添加上游定时任务
  rpc AddCrontabReseller(AddCrontabResellerReq) returns (AddCrontabResellerResp) {}

  // 修改上游定时任务
  rpc UpdateCrontabReseller(UpdateCrontabResellerReq) returns (UpdateCrontabResellerResp) {}

}

message SearchCrontabResellerReq {
  ReqPage page = 1;
  string  title = 2;
  optional sint32 status = 3;
  int32 planned_start_time = 4;
  int32 planned_end_time = 5;
  int32 reseller_id = 6;
  int32 ours_product_id = 7;

  bool withCrontab = 8;
}

message SearchCrontabResellerResp {
  repeated CrontabResellerBatchWithCrontab list = 1;
  int32 count = 2;
}

message CrontabResellerBatchWithCrontab {
  int32 crontab_id = 1;
  int32 reseller_id = 2;
  string reason = 3;
  string exec_event = 4;
  string ours_product_ids = 5;

  CrontabDetail crontab = 10;
}


message AddCrontabResellerReq {
  int32 process_mode = 1;
  string title = 2;
  int32 reseller_id = 3;
  int32 mode = 4;
  double price = 5;
  double discount = 6;
  string exec_event = 7;
  string reason = 9;
  string rule = 11;

  string ours_product_ids = 13;  // 逗号分隔 解析exec_event获得
  int32 user_id = 14;
}
message AddCrontabResellerResp {}

message UpdateCrontabResellerReq{
  int32 process_mode = 1;
  string title = 2;
  int32 reseller_id = 3;
  int32 mode = 4;
  double price = 5;
  double discount = 6;
  string exec_event = 7;
  string ids = 8;
  string reason = 9;
  string rule = 11;

  string ours_product_ids = 13;  // 逗号分隔 解析exec_event获得
  int32 user_id = 14;
  int32 crontab_id = 15;
}
message UpdateCrontabResellerResp{}

