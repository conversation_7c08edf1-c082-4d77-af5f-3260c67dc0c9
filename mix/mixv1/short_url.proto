syntax = "proto3";
package mixservice.mixv1;
import "validate/validate.proto";
import "mix/mixv1/common.proto";
import "google/api/annotations.proto";
option go_package = "./mix/mixv1;mixv1";

// 短链接服务
service ShortUrl {

  // 使用文件上传方式创建
  rpc CreateByOss (ShortUrlCreateByOssReq) returns (ShortUrlCreateByOssResp) {
    option (google.api.http) = {
      post: "/v1/proxy/mixservice.mixv1.ShortUrl/CreateByOss"
      body: "*"
    };
  }

  // 批量创建
  rpc BatchCreate (ShortUrlBatchCreateReq) returns (ShortUrlBatchCreateResp) {
    option (google.api.http) = {
      post: "/v1/proxy/mixservice.mixv1.ShortUrl/BatchCreate"
      body: "*"
    };
  }

  // 批量搜索列表
  rpc BatchSearchList (ShortUrlBatchSearchListReq) returns (ShortUrlBatchSearchListResp) {
    option (google.api.http) = {
      post: "/v1/proxy/mixservice.mixv1.ShortUrl/BatchSearchList"
      body: "*"
    };
  }

  // 搜索明细列表
  rpc SearchList (ShortUrlSearchListReq) returns (ShortUrlSearchListResp) {
    option (google.api.http) = {
      post: "/v1/proxy/mixservice.mixv1.ShortUrl/SearchList"
      body: "*"
    };
  }

  // 下载批次
  rpc Download (ShortUrlDownloadReq) returns (ShortUrlDownloadResp) {
    option (google.api.http) = {
      post: "/v1/proxy/mixservice.mixv1.ShortUrl/Download"
      body: "*"
    };
  }

  // 检查批次
  rpc CheckBatch (ShortUrlCheckBatchReq) returns (ShortUrlCheckBatchResp) {
    option (google.api.http) = {
      post: "/v1/proxy/mixservice.mixv1.ShortUrl/CheckBatch"
      body: "*"
    };
  }

  // 获取详情
  rpc GetInfoByShort (ShortUrlGetInfoByShortReq) returns (ShortUrlGetInfoByShortResp) {}
}

message ShortUrlCheckBatchReq {
  // 批次号
  int64 batch_no = 1;
}

message ShortUrlCheckBatchResp {
  // 批次id
  int32 id = 1;
  // 处理状态
  ProcessStatus process_status = 2;
  enum ProcessStatus {
    // 未知
    UNKNOWN = 0;
    // 处理中
    PROCESSING = 1;
    // 批次已创建
    CREATED = 2;
    // 已完成
    FINISHED = 3;
  }
}

message ShortUrlDownloadResp {
  // 下载链接
  string download_url = 1;
}

message ShortUrlDownloadReq {
  // id
  int64 id = 1;

  // 用户id
  int32 user_id = 2;
}

// 创建批次请求
message ShortUrlBatchCreateReq {
  // 名称
  string batch_name = 1;
  // 用户id
  int32 user_id = 2;
  // 链接
  repeated string urls = 3 [(validate.rules).repeated.items.string.pattern = "^https?://.*$"];

  // 开始时间，yyyy-mm-dd
  string start_time_str = 4 [(validate.rules).string.pattern = "^\\d{4}-\\d{2}-\\d{2}$"];
  // 结束时间，yyyy-mm-dd，不填则永久有效
  string end_time_str = 5  [(validate.rules).string = {ignore_empty: true, pattern:"^\\d{4}-\\d{2}-\\d{2}$"}];
}

// 创建批次响应
message ShortUrlBatchCreateResp {
  // id
  int32 id = 1;
  // 名称
  string batch_name = 2;
  // 批次号
  int64 batch_no = 3;
}

// 批量搜索列表请求
message ShortUrlBatchSearchListReq {
  // 批次号
  int64 batch_no = 1;
  // 名称
  string batch_name = 2;
  // 分页
  ReqPage page = 4;
  // 用户id
  int32 user_id = 5;
}

// 批量搜索列表响应
message ShortUrlBatchSearchListResp {
  // 列表
  repeated Item list = 1;
  // 分页
  RespPage page = 2;

  message Item {
    // id
    int32 id = 1;
    // 名称
    string batch_name = 2;
    // 批次号
    int64 batch_no = 3;
    // 创建时间
    int32 create_time = 4;
    // 状态:1启用，2-停用
    Status status = 6;
    // 创建用户
    int32 user_id = 7;
    // 创建用户
    User user = 8;
    // 短链接数量
    int32 short_cnt = 9;
  }
  message User {
    // 用户id
    int32 id = 1;
    // 用户名
    string userName = 2;
  }
  // 状态:1启用，2-停用
  enum Status {
    // 未知
    UNKNOWN = 0;
    // 启用
    ENABLE = 1;
    // 停用
    DISABLE = 2;
  }
}

// 搜索明细列表请求
message ShortUrlSearchListReq {
  // 批次号
  int64 batchNo = 1;
  // 原始链接
  string origin_url = 2;
  // 短链接
  string short_url = 3;
  // 分页
  MyReqPage page = 4;
  // 用户id
  int32 user_id = 5;

  message MyReqPage {
    int32 num = 1 [(validate.rules).int32 = {gte:1}]; //页码
    int32 size = 2 [(validate.rules).int32 = {gte:1}]; //每页数量
  }
}

// 搜索明细列表响应
message ShortUrlSearchListResp {
  // 列表
  repeated Item list = 1;
  // 分页
  RespPage page = 2;

  message Item {
    // id
    int32 id = 1;
    // 批次号
    int64 batch_no = 3;
    // 短链接
    string short_url = 5;
    // 创建时间
    int32 create_time = 6;
    // 状态:1启用，2-停用
    int32 status = 8;
    // 用户id
    int32 user_id = 10;
    // 访问次数
    int32 access_cnt = 11;
    // 原始链接
    string origin_url = 12;
    // 开始时间
    int32 start_time = 13;
    // 结束时间
    int32 end_time = 14;
  }
}

// 获取详情请求
message ShortUrlGetInfoByShortReq {
  // 短链接
  string short_url = 1 [(validate.rules).string.min_len = 1];
  // 是否记录访问次数
  bool is_add_access_cnt = 2;
}

// 获取详情响应
message ShortUrlGetInfoByShortResp {
  // id
  int32 id = 1;
  // 批次号
  int64 batch_no = 3;
  // 短链接
  string short_url = 5;
  // 创建时间
  int32 create_time = 6;
  // 状态:1启用，2-停用
  Status status = 8;
  // 用户id
  int32 user_id = 10;
  // 访问次数
  int32 access_cnt = 11;
  // 原始链接
  string origin_url = 12;
  // 开始时间
  int32 start_time = 13;
  // 结束时间
  int32 end_time = 14;

  enum Status {
    // 未知
    UNKNOWN = 0;
    // 启用
    ENABLE = 1;
    // 停用
    DISABLE = 2;
  }
}

message ShortUrlCreateByOssReq {
  // 名称
  string batch_name = 1;
  // 用户id
  int32 user_id = 2;
  // oss 文件地址
  string file_url = 3 [(validate.rules).string.pattern = "^https?://.*$"];
  // 开始时间，yyyy-mm-dd
  string start_time_str = 4 [(validate.rules).string.pattern = "^\\d{4}-\\d{2}-\\d{2}$"];
  // 结束时间，yyyy-mm-dd，不填则永久有效
  string end_time_str = 5  [(validate.rules).string = {ignore_empty: true, pattern:"^\\d{4}-\\d{2}-\\d{2}$"}];
}

message ShortUrlCreateByOssResp {
  // 批次id
  int32 id = 1;
  // 名称
  string batch_name = 2;
  // 批次号
  int64 batch_no = 3;
  // 成功条数
  int32 success_cnt = 4;
}