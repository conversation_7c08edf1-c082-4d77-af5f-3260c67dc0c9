syntax = "proto3";
package mixservice.mixv1;
import "validate/validate.proto";
option go_package = "./mix/mixv1;mixv1";

// 附件服务
service Attachment {
  // 批量获取公开地址
  rpc BatchPreviewPublicUrl (ReqBatchPreviewPublicUrl) returns (RespBatchPreviewPublicUrl) {}

  // 批量获取私有地址
  rpc BatchPreviewPrivateUrl (ReqBatchPreviewPrivateUrl) returns (RespBatchPreviewPrivateUrl) {}

  // 批量获取私有地址
  rpc BatchPreviewPrivateUrlMap (ReqBatchPreviewPrivateUrl) returns (RespBatchPreviewPrivateUrlMap) {}
}

message RespBatchPreviewPrivateUrlMap {
  // 访问地址，key 为oss 地址，value 为访问地址
  map<string, string> url_map = 1;
}

// 批量获取公开地址请求
message ReqBatchPreviewPublicUrl {
  repeated  PreviewPublicUrl PreviewUrl = 1 [(validate.rules).repeated.min_items = 1];
}

// 批量获取公开地址响应
message RespBatchPreviewPublicUrl {
  repeated string urls = 1;
}

// 批量获取私有地址请求
message ReqBatchPreviewPrivateUrl {
  repeated  PreviewPrivateUrl PreviewUrl = 1 [(validate.rules).repeated.min_items = 1];
}

// 批量获取私有地址响应
message RespBatchPreviewPrivateUrl {
  repeated string urls = 1;
}

message PreviewPrivateUrl {
  // 附件地址
  string attachmentUrl = 1;
  // 处理参数
  string param = 2;
  // 水印
  string water = 3;
  // 过期时间
  int64 expireAt = 4;
  // 文件下载名
  string fileName = 5;
}

message PreviewPublicUrl {
  // 附件地址
  string attachmentUrl = 1;
  // 处理参数
  string param = 2;
}