syntax = "proto3";
package mixservice.mixv1;
import "validate/validate.proto";
import "mix/mixv1/common.proto";
option go_package = "./mix/mixv1;mixv1";


// 短信服务
service Sms {
  // 通用发送验证码
  rpc SendCaptcha (SmsSendCaptchaReq) returns (SmsSendCaptchaResp) {}
  // 通用验证验证码
  rpc ValidCaptcha (SmsValidCaptchaReq) returns (SmsValidCaptchaResp) {}
}

message SmsValidCaptchaResp {
  // 是否验证通过
  bool is_ok = 1;
}

message SmsSendCaptchaReq {
  // 业务类型编码
  string type_code = 1 [(validate.rules).string.min_len = 1];
  // 手机号
  string mobile = 2 [(validate.rules).string.len = 11];
  //ip
  string ip = 3;
  //滑动验证码验证参数，如果不为空，则会验证
  string captcha_verify_param = 4;
}

message SmsSendCaptchaResp {

}