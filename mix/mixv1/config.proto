syntax = "proto3";

package mixservice.mixv1;
option go_package = "./mix/mixv1;mixv1";

// 配置服务
service Config {
  // 获取配置
  rpc GetConfig (GetConfigReq) returns (GetConfigResp) {}
  // 更新配置
  rpc UpdateConfig (UpdateConfigReq) returns (UpdateConfigResp) {}
  // 获取内置通知手机号
  rpc GetDefaultRemindPhone (GetDefaultRemindPhoneReq) returns (GetDefaultRemindPhoneResp) {}
}

// 获取内置通知手机号请求
message GetDefaultRemindPhoneReq {

}

// 获取内置通知手机号响应
message GetDefaultRemindPhoneResp {
  repeated string default_remind_phone = 1;
}

// 更新配置请求
message UpdateConfigReq {
  string tag = 1;
  string value = 2;
}

// 更新配置响应
message UpdateConfigResp {

}

// 获取配置请求
message GetConfigReq {
  string tag = 1;
}

// 获取配置响应
message GetConfigResp {
  string config = 1;
}