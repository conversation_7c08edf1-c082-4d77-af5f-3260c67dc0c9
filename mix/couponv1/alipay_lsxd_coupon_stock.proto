syntax = "proto3";

package mixservice.couponv1;

option go_package = "./mix/couponv1;couponv1";


service AlipayLSXDCouponStock {
  //列表
  rpc GetSearchList(AlipayLSXDCouponStockGetSearchListReq) returns (AlipayLSXDCouponStockGetSearchListResp) {}
  //详情
  rpc Detail(AlipayLSXDCouponStockDetailReq) returns (AlipayLSXDCouponStockInfo) {}
  //修改
  rpc Update(AlipayLSXDCouponStockInfo) returns (AlipayLSXDCouponStockUpdateResp) {}
  //创建
  rpc Create(AlipayLSXDCouponStockInfo) returns (AlipayLSXDCouponStockInfo) {}
}

message AlipayLSXDCouponStockInfo {
  //数据id
  int32 id = 1;
  //优惠活动名称
  string title = 2;
  //我们的商品分类
  int32 ours_product_category_id = 3;
  //我们的商品id，进行分割
  string ours_product_ids = 4;
  //折扣
  double discount = 5;
  //有效期开始时间
  int32 start_time = 7;
  //有效期截止时间
  int32 end_time = 8;
  //领取后有效时长
  int32 duration = 9;
  //优惠码数量
  int32 card_quantity = 10;
  //已使用数量
  int32 card_used = 11;
  //批次是否激活。默认1激活， 2关闭
  int32 status = 12;
  //活动描述信息
  string describe = 13;
  //绑定商家
  int32 reseller_id = 14;
  //优惠创建时间
  int32 create_time = 15;
  //活动页面地址
  string url = 16;
  //劵码混合类型，1表示16数字字母
  int32 code_mixed_type = 17;
  //支付宝立减金批次号
  string alipay_stock = 18;
  //支付宝立减金兑换域名
  string domain_prefix = 19;
  //产品面额
  double coupon_price = 20;
  //产品使用门槛
  double transaction_minimum = 21;
  //支付宝关联主体
  string subject_info = 22;
  //模板id
  string template_id = 23;
  //使用说明
  string body = 24;
}

message AlipayLSXDCouponStockGetSearchListReq {
  //卡券名称
  string keyword = 1;
  //页码
  int32 page = 2;
  //每页数据条数
  int32 limit = 3;
}

message AlipayLSXDCouponStockGetSearchListResp {
  int32 dataCount = 1;
  repeated AlipayLSXDCouponStockInfo list = 2;
}

message AlipayLSXDCouponStockDetailReq {
  int32 id = 1;
  optional int32 status = 2;
}

message AlipayLSXDCouponStockUpdateResp {
  int32 effect_rows = 1;
}