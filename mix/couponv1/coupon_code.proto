syntax = "proto3";

package mixservice.couponv1;

option go_package = "./mix/couponv1;couponv1";

service CouponCode {
  //列表
  rpc GetSearchList(GetCouponCodeSearchListReq) returns (GetCouponCodeSearchListResp){}
  //批量修改优惠券状态
  rpc BatchUpdateStatus(BachUpdateCouponCodeStatusReq) returns (BachUpdateCouponCodeStatusResp){}
}

message GetCouponCodeSearchListReq {
  //活动id
  int32 activity_id = 1;
  //状态:状态，0待使用，1已使用，2已作废，使用中
  optional int32 status = 2;
  //页码
  int32 page = 3;
  //每页数据条数
  int32 limit = 4;
}

message GetCouponCodeSearchListResp {
  //数据总量
  int32  dataCount = 1;
  repeated CouponCodeInfo list = 2;
}

message CouponCodeInfo {
  //数据id
  int32 id = 1;
  //活动编号
  int32 activity_id = 2;
  //优惠码卡密
  string code = 3;
  //状态，0待使用，1已使用，2已作废，使用中
  int32 status = 4;
  //使用时间
  int32 use_time = 5;
  //使用订单号
  string order_number = 6;
  //分销商id
  int32 reseller_id = 7;
  //商品id
  int32 goods_id = 8;
  //作废时间
  int32 abolish_time = 9;
  //创建时间
  int32 create_time = 10;
}

message BachUpdateCouponCodeStatusReq {
  repeated int32 ids = 1;
  optional int32 status = 2;
}

message BachUpdateCouponCodeStatusResp {
  int32 effect_rows = 1;
}