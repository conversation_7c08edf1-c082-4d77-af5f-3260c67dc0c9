syntax = "proto3";

package mixservice.mixerr;

import "errors/errors.proto";

option go_package = "./mixservice/mixservice;mixerr";

// 服务内部定义的错误码，10001开始，如果要定义和serviceerr中一样的码，请保持code一致
enum Err  {
	// GRPC项目缺省错误码或显示指定的码必须符合如下链接中定义的http码
	// https://github.com/googleapis/googleapis/blob/master/google/rpc/code.proto
	// 原因：此码为http状态码，在GRPC服务中，输出时会将其转换为grpc的状态码，在客户端调用调用返回err时，
	// 	可以使用统一定义生成的err，来判断是否是某种错误，如IsDbNotFound方法，
	// 	可以看到方法中调用了errors.FromError，它会将grpc的状态码转换为http状态码
	// 	因此需要保证定义的code必须为如上链接中可以相互转换的状态码
	option (errors.default_code) = 2;

	// 未知的,
	UNKNOWN = 0;

	// 数据未找到
	NOT_FOUND_DB = 2;

	// 参数错误
//	PARAM = 3;
  //@todo 待统一 参数校验失败
  PARAM_INVALID = 3;

	// 不允许
	NOT_ALLOW = 4;

	// 异常
	EXCEPTION = 5;

	// 无效的行为验证码
	ACTION_CAPTCHA = 6;
}