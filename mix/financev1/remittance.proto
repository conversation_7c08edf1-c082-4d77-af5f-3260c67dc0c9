syntax = "proto3";
package mixservice.financev1;
option go_package = "./mix/financev1;financev1";

// 汇款账户服务
service Remittance {
  // 汇款账户列表
  rpc GetRemittanceList(GetRemittanceListReq) returns (GetRemittanceListResp) {}
  // 添加汇款账户
  rpc CreateRemittance(CreateRemittanceReq)  returns (CreateRemittanceResp) {}
  // 更新汇款账户
  rpc UpdateRemittance(UpdateRemittanceReq)  returns (UpdateRemittanceResp) {}
  // 删除汇款账户
  rpc DeleteRemittance(DeleteRemittanceReq)  returns (DeleteRemittanceResp) {}
  // 汇款账户详情
  rpc DetailRemittance(DetailRemittanceReq)  returns (DetailRemittanceResp) {}
}

// 汇款账户列表请求
message GetRemittanceListReq {
  // 页码
  int32 page = 1;
  // 每页条数
  int32 limit = 2;
  // 分销商id
  int32 reseller_id = 3;

  // 多个分销商id
  repeated int32 reseller_ids = 4;
}

// 汇款账户列表返回
message GetRemittanceListResp {
  int32 data_count = 1;
  repeated RemittanceItem list = 2;
}

message RemittanceItem {
  // 主键
  int32 id = 1;
  // 分销商
  int32 reseller_id = 2;
  // 汇款账号
  string remittance = 3;
  // 汇款人姓名
  string remittance_name = 4;
  // 创建时间
  int32 create_time = 5;
  // 更新时间
  int32 update_time = 6;
  // 1正常 0删除
  int32 status = 7;
}

// 添加汇款账户请求
message CreateRemittanceReq {
  // 分销商
  int32 reseller_id = 1;
  // 汇款账号
  string remittance = 2;
  // 汇款人姓名
  string remittance_name = 3;
}

// 添加汇款账户返回
message CreateRemittanceResp {}

// 更新汇款账户请求
message UpdateRemittanceReq {
  // 分销商
  int32 reseller_id = 1;
  // 汇款账号
  string remittance = 2;
  // 汇款人姓名
  string remittance_name = 3;
  // 主键
  int32 id = 4;
}

// 更新汇款账户返回
message UpdateRemittanceResp {}

// 删除汇款账户请求
message DeleteRemittanceReq {
  int32 id = 1;
}

// 删除汇款账户返回
message DeleteRemittanceResp {}

message DetailRemittanceReq {
  int32 reseller_id = 1;
}

message DetailRemittanceResp {
  RemittanceItem detail = 1;
}