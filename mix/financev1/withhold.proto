syntax = "proto3";
package mixservice.financev1;
import "validate/validate.proto";
option go_package = "./mix/financev1;financev1";

// 扣款记录服务
service Withhold {
  // 扣款记录列表
  rpc GetWithholdList (GetWithholdListReq) returns (GetWithholdListResp) {}
  // 添加扣款记录
  rpc CreateWithhold(CreateWithholdReq)  returns (CreateWithholdResp) {}
}

// 扣款记录列表请求
message GetWithholdListReq {
  // 分销商id
  int32 reseller_id = 1 ;
  // 记录时间
  repeated int32 create_time = 2;
  // 页码
  int32 page = 3;
  // 每页条数
  int32 limit = 4;
}

// 扣款记录列表返回
message GetWithholdListResp {
  // 数据量
  int32 data_count = 1;
  // 数据列表
  repeated WithholdInfo list = 2;
}

// 扣款记录
message WithholdInfo {
  // 主键id
  int32 id = 1;
  // 扣款金额
  double money = 2;
  // 扣款时间
  int32 create_time = 3;
  // 备注
  string remark = 4;
  // 分销商id
  int32 reseller_id = 5;
  // 操作人
  int32 executor = 6;
}

// 添加扣款记录请求
message CreateWithholdReq {
  // 扣款时间
  int32 create_time = 1 [(validate.rules).int32.gt = 0];
  // 扣款金额
  double money = 2 [(validate.rules).double.gt = 0];
  // 备注
  string remark = 3;
  // 分销商id
  int32 reseller_id = 4;
  // 操作人id
  int32 executor_id = 5;
}

// 添加扣款记录返回
message CreateWithholdResp {
  // 主键id
  int32 id = 1;
  // 扣款金额
  double money = 2;
  // 扣款时间
  int32 create_time = 3;
  // 备注
  string remark = 4;
  // 分销商id
  int32 reseller_id = 5;
}