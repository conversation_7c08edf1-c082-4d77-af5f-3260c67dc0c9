syntax = "proto3";
package mixservice.financev1;
option go_package = "./mix/financev1;financev1";

// 收款账户服务
service Recharge {
  // 获取充值列表
  rpc GetRechargeList (GetRechargeListReq) returns (GetRechargeListResp) {}
  // 添加充值
  rpc CreateRecharge(CreateRechargeReq)  returns (CreateRechargeResp) {}
  // 充值审核
  rpc ExamineRecharge(ExamineRechargeReq)  returns (ExamineRechargeResp) {}
}

// 充值审核请求
message ExamineRechargeReq {
  int32 recharge_id = 1;
  int32 status = 2;
  string remark = 3;
  int32 auditor_id = 4;
}

// 充值审核返回
message ExamineRechargeResp {

}

// 获取充值列表请求
message GetRechargeListReq {
  repeated string remittance_time = 1;
  // 状态
  optional int64 status = 2;
  // 汇款用户
  string remittance_name = 3;
  // 分销商id
  int64 reseller_id = 4;
  // 收款id
  int64 receivables_id = 5;
  // 金额【英文逗号隔开的，范围查询】
  string money = 6;
  // 页码
  int32 page = 7;
  // 每页条数
  int32 limit = 8;
  // 收款情况
  optional int64 receive_status = 9;

  // 多个分销商id
  repeated int32 reseller_ids = 10;
}

// 获取充值列表请求返回
message GetRechargeListResp {
  // 数据量
  int32 data_count = 1;
  // 数据列表
  repeated RechargeInfo list = 2;
}

// 添加充值请求
message CreateRechargeReq {
  // 收款账户id
  int32 receivables_id = 1 ;
  // 线上、线下
  int32 type = 2 ;
  // 汇款账号
  string remittance = 3;
  // 汇款时间
  int32 remittance_time = 4;
  // 汇款用户
  string remittance_name = 5;
  // 汇款金额
  double reseller_money = 6;
  // 网络平台涉及到手续费，所以实际到账金额有区别
  double receiver_money = 7;
  // 充值金额
  double recharge_money = 8;
  // 用户ID
  int32 reseller_id = 9;
  // 凭证流水号
  string voucher = 10;
  int32 platform = 11;
  string account = 12;
  // 备注
  string remark = 13;
  // 收款情况  1已收款 -1未收款 0部分收款
  int32 receive_status = 14;
  // 凭证文件
  string voucher_file = 15;
  int32 user_id = 16;
}

// 添加充值返回
message CreateRechargeResp {

}

message RechargeInfo {
  uint32 id = 1;
  // 收款账户id
  int32 receivables_id = 2;
  // 线上、线下
  uint32 type = 3;
  // 汇款账号
  string remittance = 4;
  // 汇款时间
  string remittance_time = 5;
  // 汇款用户
  string remittance_name = 6;
  // 汇款金额
  double reseller_money = 7;
  // 网络平台涉及到手续费，所以实际到账金额有区别
  double receiver_money = 8;
  // 充值金额
  double recharge_money = 9;
  // 用户ID
  int32 reseller_id = 10;
  // 凭证文件
  string voucher_file = 11;
  // 凭证流水号
  string voucher = 12;
  // 录入人id
  int32 enter_man = 13;
  // 备注
  string remark = 14;
  // 状态0待审核 -1驳回 1通过
  int32 status = 15;
  // 创建时间
  int32 create_time = 16;
  // 更新时间
  int32 update_time = 17;
  // 收款情况  1已收款 -1未收款 0部分收款
  int32 receive_status = 18;
  // 状态描述
  string  status_text = 19;
  // 充值审核处理记录
  repeated RechargeExamineInfo examines = 20;
  // 充值的收款记录
  repeated RechargeReceiveInfo receives = 21;
  // 收款账户
  Receivables receivables = 22;
}

// 收款账户
message Receivables {
  // ID
  int32 id = 1;
  // 账户类型
  int32 type = 2;
  // 开户行/平台
  string platform = 3;
  // 开户行信息
  string bank_info = 4;
  // 账号
  string account = 5;
  // 开户名
  string open_name = 6;
  // 开户时间
  int64 open_time = 7;
  // 备注
  string remark = 8;
  // 创建时间
  int64 create_time = 9;
  // 更新时间
  int64 update_time = 10;
  // 累计收款
  double received = 11;
  // 累计到账
  double collected = 12;
  // 账户状态
  int32 status = 13;
}

// 充值审核处理记录
message RechargeExamineInfo {
  uint32 id = 1;
  // 充值id
  int64 recharge_id = 2;
  // 审核状态
  int32 status = 3;
  // 审核人
  int32 auditor = 4;
  // 备注
  string remark = 5;
  // 审核时间
  int32 create_time = 6;
}

// 充值的收款记录
message RechargeReceiveInfo {
  // 收款金额
  double money = 1;
  // 收到款的时间
  int32 receive_time = 2;
  // 备注
  string remark = 3;
}