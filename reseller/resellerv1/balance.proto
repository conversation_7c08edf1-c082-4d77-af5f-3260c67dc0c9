syntax = "proto3";

package reseller.resellerv1;
import "validate/validate.proto";
option go_package = "./reseller/resellerv1;resellerv1";

// 分销商余额服务
service Balance {
  // 余额变更
  rpc UpdateBalance(UpdateBalanceReq) returns (UpdateBalanceResp) {}

  // 修改分销商的余额预警值
  rpc UpdateWarn(UpdateWarnReq) returns (UpdateWarnResp) {}

  // 设置手机预警
  rpc UpdateWarnDevice(UpdateWarnDeviceReq) returns (UpdateWarnDeviceResp) {}

  // 获取余额变化记录
  rpc GetBalanceLogList(GetBalanceLogListReq) returns (GetBalanceLogListResp) {}

  // 获取余额
  rpc GetBalance(GetBalanceReq) returns (GetBalanceResp) {}

  // 分销商充值处理
  rpc Recharge(RechargeReq) returns (RechargeResp) {}

  // 创建流水
  rpc CreateBankTrade(CreateBankTradeReq) returns (CreateBankTradeResp) {}
}

// 分销商充值处理请求
message RechargeReq {
  // 分销商_编号
  int32 reseller_id = 1;
  // 充值金额
  double recharge_money = 2;
  // 到账金额
  double receiver_money = 3;
  // 时间
  optional int32 time = 4;
}

// 分销商充值处理返回
message RechargeResp {

}

// 获取余额变化记录请求
message GetBalanceLogListReq {
  repeated string ids = 1 [(validate.rules).repeated = {min_items:1}];
}

// 获取余额变化记录返回
message GetBalanceLogListResp {
  repeated ResellerBalanceLogInfo list = 1;
}

// 获取余额请求
message GetBalanceReq {
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
}
message GetBalanceResp {
  ResellerBalance data = 1;
}

message ResellerBalance {
  // 编号
  int32 reseller_id = 1;
  // 默认折扣1-100
  double default_discount = 2;
  // 累计消费
  double total_consumption = 3;
  // 余额
  double balance = 4;
  // 更新时间
  int32 update_time = 5;
  string sign = 6;
  // 低于设定金额时，进行通知提醒
  double balance_warning = 7;
  // 通知时间
  int32 balance_warning_notify = 8;
  // 授信
  double extension = 9;
  // 余额预警设备，可以设置多个手机号
  string warn_device = 10;
}


// 余额变化记录
message ResellerBalanceLogInfo {
  // 主键id
  int32 id = 1;
  // 分销商id
  int32 reseller_id = 2;
  // 金额
  double money = 3;
  // 操作
  int32 operate = 4;
  // 类型 \r\n1充值 2订单消费 3冲账 4充值 5扣款
  int32 type = 5;
  // 发生时间
  int32 create_time = 6;
  // 签名
  string sign = 7;
  // 备注
  string remark = 8;
  // 变更前的余额
  double before_balance = 9;
  // 当前余额
  double current_balance = 10;
  // 执行操作的人 \r\n0表示系统自动执行\r\n非0则根据executor_type来判断是后台人员还是分销商
  int32 executor = 11;
  // 执行操作的人的类型\r\n0系统 1后台 2分销商
  int32 executor_type = 12;
}

// 修改分销商的余额预警值请求
message UpdateWarnReq {
  int32 reseller_id = 1[(validate.rules).int32 = {gt:0}];
  string balance_warning = 2;
}

// 修改分销商的余额预警值响应
message UpdateWarnResp {

}

// 设置手机预警请求
message UpdateWarnDeviceReq {
  int32 reseller_id = 1[(validate.rules).int32 = {gt:0}];
  string warn_device = 2;
}

// 设置手机预警响应
message UpdateWarnDeviceResp {

}

// 余额变更类型
enum BalanceType {
  BALANCE_TYPE_UNKNOWN = 0;
  // 充值
  BALANCE_TYPE_RECHARGE = 1;
  // 订单消费
  BALANCE_TYPE_ORDER = 2;
  // 冲账
  BALANCE_TYPE_DEDUCTION = 3;
  // 退款
  BALANCE_TYPE_REFUND = 4;
  // 扣款
  BALANCE_TYPE_WITHHOLD = 5;
  // 授信
  BALANCE_TYPE_CREDIT_EXTENSION = 6;
  // 卡密订单消费
  BALANCE_TYPE_CARD_ORDER = 7;
}

// 余额变更操作
enum BalanceOperate {
  // 减少
  BALANCE_OPERATE_DECREASE = 0;
  // 增加
  BALANCE_OPERATE_INCREASE = 1;
}

// 操作人类型
enum BalanceExecutorType {
  // 系统自动
  BALANCE_EXECUTOR_TYPE_SYSTEM = 0;
  // 后台操作
  BALANCE_EXECUTOR_TYPE_ADMIN = 1;
  // 分销商操作
  BALANCE_EXECUTOR_TYPE_RESELLER = 2;
}

// 余额变更请求
message UpdateBalanceReq {
  // 分销商id
  int32 reseller_id = 2;
  // 金额
  double money = 3;
  // 操作
  BalanceOperate operate = 4;
  // 类型 \r\n1充值 2订单消费 3冲账 4充值 5扣款
  BalanceType type = 5;
  // 备注
  string remark = 8;
  // 执行操作的人 \r\n0表示系统自动执行\r\n非0则根据executor_type来判断是后台人员还是分销商
  int32 executor = 11;
  // 执行操作的人的类型\r\n0系统 1后台 2分销商
  BalanceExecutorType executor_type = 12;
}

// 余额变更返回
message UpdateBalanceResp {

}

message CreateBankTradeReq {
  // 银行标识
  string bank = 2 [json_name="bank"];
  // 交易流水号
  string serialNumber = 3 [json_name="serial_number"];
  // 金额
  double amount = 4 [json_name="amount"];
  // 摘要（转账标识）
  string tag = 5 [json_name="tag"];
  // 附言（业务ID）
  string businessNumber = 6 [json_name="business_number"];
  // 交易类型（收入或支出）
  string tradeType = 7 [json_name="trade_type"];
  // 交易时间
  int64 tradeTime = 8 [json_name="trade_time"];
  // 内部账号信息（自身账户）
 string inside = 9 [json_name="inside"];
  // 外部账号信息（支付方或者收款方）
  string outside = 10 [json_name="outside"];
  // 付款原始数据
  string data = 11 [json_name="data"];
  // 状态
  int32 status = 12 [json_name="status"];
  // 交易凭证
  string voucher = 15 [json_name="voucher"];
  // 处理失败原因
  string errorMsg = 16 [json_name="error_msg"];
  // 分销商ID
  int32 resellerId = 17 [json_name="reseller_id"];
  // 分销商所属商务ID
  int32 aftermarket = 18 [json_name="aftermarket"];
}
message CreateBankTradeResp {}