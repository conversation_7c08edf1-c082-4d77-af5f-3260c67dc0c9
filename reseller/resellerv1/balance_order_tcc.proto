syntax = "proto3";

package reseller.resellerv1;
import "validate/validate.proto";
option go_package = "./reseller/resellerv1;resellerv1";

// 分销商余额订单类型的变更TCC服务
service BalanceOrderTcc {
  // 冻结支付金额（由于底层只有一个字段，所以调用后余额就真实发生了改变）
  rpc TryPay(BalanceOrderTccTryPayReq) returns (BalanceOrderTccTryPayResp) {}

  // 退款
  rpc TryRefund(BalanceOrderTccTryRefundReq) returns (BalanceOrderTccTryRefundResp) {}

  // 确认操作（确认的目的是减少定时系统核对一致性的数据量）
  rpc Confirm(BalanceOrderTccConfirmReq) returns (BalanceOrderTccConfirmResp) {}

  // 取消操作（回滚金额）
  rpc Cancel(BalanceOrderTccCancelReq) returns (BalanceOrderTccResp) {}
}

message BalanceOrderTccTryPayReq {
  // 原子请求id，在相同type下唯一，如：有新业务接入时，请联系相关负责人分配type
  string request_id = 1 [(validate.rules).string = {min_len:1,max_len:100}];

  // 分销商id
  int32 reseller_id = 2;

  // 金额：元*10000倍的整数，已废弃！！！，因为溢出问题，最大仅支持20多万，请使用amount_big
  int32 amount = 3;

  // 金额，优先级高于上面的amount
  int64 amount_big = 4;

  // 更多类型请看值对象中的限制。百胜支付订单为9，更多类型请看值对象：ResellerLogTypeObj
  int32 type = 5 [(validate.rules).int32.gt = 0];

  // 备注，通常为你业务系统的ID
  string remark = 8;

  // 执行操作的人：0表示系统自动执行、非0则根据executor_type来判断是后台人员还是分销商
  int32 executor = 11;

  // 执行操作的人的类型0系统 1后台 2分销商
  ExecutorType executor_type = 12 [(validate.rules).enum.defined_only = true];

  // 异常时回查的http地址
  // HTTP协议：如果为开头为http时，会使用HTTP协议，请求地址为：query_url + "?requestId=" + request_id。响应结构为{"status":1}，1未知，2成功，3失败，当返回失败时，会回滚余额的操作
  // GRPC协议，非http开头时，会使用GRPC协议，格式为{addr@methodName}，其中addr为服务地址，methodName为方法名（在生成的*_grpc.pb.goGRPC文件中找到FullMethodName的常量值），如：192.168.6.194:9002@/user.userv1.Query/Query
  // GRPC接口的请求和响应pb如下：
  /*
  // 请求参数
  message QueryReq {
    // 原子请求id，结构为："业务标识-业务数据id-雪花ID"，如：yum-123-123456789012345678，有新业务接入时，请联系相关负责人分配业务标识
    string request_id = 1;
  }

  message QueryResp {
    // 查询结果，1未知，2成功，3失败，当返回失败时，会回滚余额的操作
    int32 status = 1;
  }
  */
  string query_url = 13;

  // 操作人类型
  enum ExecutorType {
    BALANCE_EXECUTOR_TYPE_SYSTEM = 0; // 系统自动
    BALANCE_EXECUTOR_TYPE_ADMIN = 1; // 后台操作
    BALANCE_EXECUTOR_TYPE_RESELLER = 2;  // 分销商操作
  }
}

message BalanceOrderTccTryRefundReq {
  // 原子请求id，在相同type下唯一，如：有新业务接入时，请联系相关负责人分配type
  string request_id = 1 [(validate.rules).string = {min_len:1,max_len:100}];

  // 原支付时的请求id，退款是针对它进行退款
  string pay_request_id = 2 [(validate.rules).string = {min_len:1,max_len:100}];

  // 原支付时的类型，更多类型请看值对象中的限制。百胜支付订单为9，更多类型请看值对象：ResellerLogTypeObj
  int32 pay_type = 3 [(validate.rules).int32.gt = 0];

  // 金额：元*10000倍的整数，已废弃！！！，因为溢出问题，最大仅支持20多万，请使用amount_big
  int32 amount = 4;

  // 异常时回查的http地址
  // HTTP协议：如果为开头为http时，会使用HTTP协议，请求地址为：query_url + "?requestId=" + request_id。响应结构为{"status":1}，1未知，2成功，3失败，当返回失败时，会回滚余额的操作
  // GRPC协议，非http开头时，会使用GRPC协议，格式为{addr@methodName}，其中addr为服务地址，methodName为方法名（在生成的*_grpc.pb.goGRPC文件中找到FullMethodName的常量值），如：192.168.6.194:9002@/user.userv1.Query/Query
  // GRPC接口的请求和响应pb如下：
  /*
  // 请求参数
  message QueryReq {
    // 原子请求id，结构为："业务标识-业务数据id-雪花ID"，如：yum-123-123456789012345678，有新业务接入时，请联系相关负责人分配业务标识
    string request_id = 1;
  }

  message QueryResp {
    // 查询结果，1未知，2成功，3失败，当返回失败时，会回滚余额的操作
    int32 status = 1;
  }
  */
  string query_url = 5;

  // 更多类型请看值对象中的限制。百胜退款订单为10，更多类型请看值对象：ResellerLogTypeObj
  int32 type = 6;

  // 金额，优先级高于上面的amount
  int64 amount_big = 7;
}

message BalanceOrderTccConfirmReq {
  // 原子请求id
  string request_id = 1 [(validate.rules).string = {min_len:1,max_len:100}];

  // 卡密订单消费，更多类型请看值对象中的限制。百胜支付订单为9，更多类型请看值对象：ResellerLogTypeObj
  int32 type = 5 [(validate.rules).int32.gt = 0];
}

message BalanceOrderTccCancelReq {
  // 原子请求id
  string request_id = 1 [(validate.rules).string = {min_len:1,max_len:100}];

  // 更多类型请看值对象中的限制。百胜支付订单为9，更多类型请看值对象：ResellerLogTypeObj
  int32 type = 5 [(validate.rules).int32.gt = 0];
}

message BalanceOrderTccTryPayResp {}
message BalanceOrderTccTryRefundResp {}
message BalanceOrderTccConfirmResp {}
message BalanceOrderTccResp {}