syntax = "proto3";

package reseller.resellerv1;
import "reseller/resellerv1/common.proto";
import "reseller/resellerv1/balance.proto";
import "validate/validate.proto";
option go_package = "./reseller/resellerv1;resellerv1";


// 分销商
service Reseller {
  // 搜索分销商列表
  rpc SearchList (ReqResellerSearchList) returns (RespResellerSearchList) {}

  // 根据id 获取用户 map 结构信息
  rpc GetMapByIds(ReqResellerGetMap) returns (RespResellerGetMap) {}

  // 获取分销商列表
  rpc GetResellerList(GetResellerListReq) returns (GetResellerListResp) {}

  // 获取分销商列表(话费系统)
  rpc GetResellerListForLife(GetResellerListForLifeReq) returns (GetResellerListResp) {}

  // 创建分销商
  rpc CreateReseller(CreateResellerReq) returns (CreateResellerResp) {}

  // 获取分销商详情
  rpc GetResellerDetail(GetResellerDetailReq) returns (GetResellerDetailResp) {}

  // 获取分销商详情
  rpc GetResellerSimple(GetResellerSimpleReq) returns (GetResellerSimpleResp) {}

  // 更新分销商
  rpc UpdateReseller(UpdateResellerReq) returns (UpdateResellerResp) {}

  rpc UpdateResellerTest(UpdateResellerTestReq) returns (UpdateResellerTestResp) {}

  // 变更所属的商务
  rpc ChangeAfterMarket(ChangeAfterMarketReq) returns (ChangeAfterMarketResp) {}

  // 获取分销商归属商务
  rpc GetAfterMarketById(GetAfterMarketByIdReq) returns (GetAfterMarketByIdResp) {}

  // 修改分销商密码
  rpc UpdatePassword(UpdatePasswordReq) returns (UpdatePasswordResp) {}

  // 搜索用分销商列表
  rpc GetSearchList(GetSearchListReq) returns (GetSearchListResp) {}

  // 搜索分销商职员列表
  rpc SearchMemberList(SearchMemberListReq) returns (SearchMemberListResp) {}

  // 获取分销商api设置
  rpc GetApiSetting(GetApiSettingReq) returns (GetApiSettingResp) {}

  // 分销商余额列表
  rpc SearchBalance(SearchBalanceReq) returns (SearchBalanceResp) {}

  // 分销商余额日列表
  rpc SearchBalanceDay(SearchBalanceDayReq) returns (SearchBalanceDayResp) {}

  // UpdateByCrontab
  rpc UpdateByCrontab (UpdateByCrontabReq) returns (UpdateByCrontabResp){}

  rpc ResellerBalanceWarn(ResellerBalanceWarnReq) returns (ResellerBalanceWarnResp){}

  // 获取分销商自定义密钥
  rpc GetCustomKey(GetCustomKeyReq) returns (GetCustomKeyResp){}

  // 根据商务负责人列表获取分销商ID列表（用于数据权限控制）
  rpc GetResellerIdsByAftermarkets (GetResellerIdsByAftermarketsReq) returns (GetResellerIdsByAftermarketsResp){}

  // 修改分销商收款账户
  rpc UpdateResellerReceivables (UpdateResellerReceivablesReq) returns (UpdateResellerReceivablesResp) {}

  // 验证商户密码
  rpc ValidateMemberPwd (ValidateMemberPwdReq) returns (ValidateMemberPwdResp){}

  // 通过分销商主体和加tag获取分销商详情
  rpc GetResellerDetailBySubjectName(GetResellerDetailBySubjectNameReq) returns (GetResellerDetailBySubjectNameResp){}
}

message GetResellerDetailBySubjectNameReq {
  string subject_name = 1;
  string tag = 2;
}
message GetResellerDetailBySubjectNameResp {
  int32 id = 1;
  string name = 2;
}

// 修改分销商收款账户请求
message UpdateResellerReceivablesReq {
  int32 reseller_id = 1;
  int32 receivables_id = 2;
}

// 修改分销商收款账户响应
message UpdateResellerReceivablesResp {

}

// 获取分销商自定义密钥请求
message GetCustomKeyReq {
  int32 reseller_id = 1;
}

// 获取分销商自定义密钥响应
message GetCustomKeyResp {
  int32 reseller_id = 1;
  string our_key = 2;
  string reseller_key = 3;
}

// 获取分销商归属商务请求
message GetAfterMarketByIdReq {
  int32 reseller_id = 1;
}

// 获取分销商归属商务响应
message GetAfterMarketByIdResp {
  int32 aftermarket = 1;
}

// 搜索分销商职员列表请求
message SearchMemberListReq {
  // 职员id集合
  repeated int32 member_ids = 1;
  // 分销商id
  int32 reseller_id = 2;
}

// 搜索分销商职员列表响应
message SearchMemberListResp {
  repeated ResellerMemberInfo list = 1;
}

// 分销商职员
message ResellerMemberInfo {
  int32 id = 1;
  // 分销商编号
  int32 reseller_id = 2;
  // 职员登录账号
  string name = 3;
  // 分销商职员的登录密码
  string password = 4;
  string telephone = 5;
  string union_id = 6;
  // 状态
  int32 status = 7;
  // 创建时间
  int32 create_time = 8;
  // 更新时间
  int32 update_time = 9;
  // 最后登录时间
  int32 last_login_time = 10;
  // 最后登录IP
  int64 last_login_ip = 11;
  string rules = 12;
  // 主管理员
  int32 is_master = 13;
}

// 搜索分销商列表请求
message GetSearchListReq {
  // 分销商名称
  string name = 1;
  // 分销商状态
  optional int32 status = 2;

  // 分销商归属商务
  int32 aftermarket = 3;
}

// 搜索分销商列表响应
message GetSearchListResp {
  repeated SearchResellerInfo List = 1;
}

message SearchResellerInfo {
  int32 id = 1;
  string name = 2;
  int32 status = 3;
}
// 修改分销商密码请求
message UpdatePasswordReq {
  int32 reseller_id = 1 [(validate.rules).int32 = {gt:0}];
  string password = 2 [(validate.rules).string = {min_len:1}];
}

// 修改分销商密码响应
message UpdatePasswordResp {

}

// 变更所属的商务请求
message ChangeAfterMarketReq {
  int32 aftermarket = 1 [(validate.rules).int32 = {gt:0}];
  repeated int32 reseller_ids = 2[(validate.rules).repeated = {min_items:1}];
}

// 变更所属的商务响应
message ChangeAfterMarketResp {

}

message UpdateResellerTestReq{
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
  int32 update_time = 3 ;
}
message UpdateResellerTestResp{}

// 更新分销商请求
message UpdateResellerReq {
  int32 id = 1 [(validate.rules).int32 = {gt:0}];
  int32 user_id = 2 [(validate.rules).int32 = {gt:0}];
  int32 reseller_grade_id = 3 ;
  string name = 4 ;
  string full_name = 5 ;
  string subject_name = 6;
  string contacts = 7 ;
  string telephone = 8 ;
  string union_id = 9;
  int32  aftermarket = 10 ;
  int32 sign_company = 11;
  string password = 12;
  string industry_mcc = 13;
  string report_name = 14;
  string report_code = 15;
  int32 show_card_order = 19;
  AuthenticationReq authentication = 16;
  ApiSettingReq api_setting = 17;
  optional SocialCreditReq social_credit = 18;
  string email = 20;
}

// 更新分销商响应
message UpdateResellerResp {

}
// 获取分销商详情请求
message GetResellerDetailReq {
  int32 id = 1;
}

// 获取分销商详情响应
message GetResellerDetailResp {
  int32 id = 1;
  string name = 2;
  int32 create_time = 3;
  // 全称（公司主体名）
  string full_name = 4;
  // 联系人电话
  string telephone = 5;
  // 联系人姓名
  string contacts = 6;
  // 商务负责人 id
  int32 aftermarket = 7;
  // 行业
  string industry_mcc = 8;
  int32 reseller_grade_id = 9;
  string subject_name = 10;
  int32 status = 11;
  int32 update_time = 12;
  string union_id = 13;
  int32 search_time = 14;
  int32 sign_company = 15;
  bool show_card_order = 16;
  string account_tag = 17;
  string report_name = 18;
  string report_code = 19;
  //  SocialCreditInfo social_credit = 20 ;
  ResellerAuthenticationInfo authentication = 21 ;
  ResellerBalance balance = 22 ;
  ResellerApiSettingInfo api_setting = 23 ;
  repeated AuthorizeProducts authorize_products = 24 ;
  string email = 25;
  int32 receivables_id = 26;
}

// 获取分销商
message GetResellerSimpleReq {
  int32 id = 1;
}
// 获取分销商详情响应
message GetResellerSimpleResp {
  int32 id = 1;
  string name = 2;
  int32 create_time = 3;
  // 全称（公司主体名）
  string full_name = 4;
  // 联系人电话
  string telephone = 5;
  // 联系人姓名
  string contacts = 6;
  // 商务负责人 id
  int32 aftermarket = 7;
  // 行业
  string industry_mcc = 8;
  int32 reseller_grade_id = 9;
  string subject_name = 10;
  int32 status = 11;
  int32 update_time = 12;
  string union_id = 13;
  int32 search_time = 14;
  int32 sign_company = 15;
  bool show_card_order = 16;
  string account_tag = 17;
  string report_name = 18;
  string report_code = 19;
  string email = 20;
  int32 receivables_id = 21;
}

message AuthorizeProducts {

}

message ResellerApiSettingInfo {
  // 编号
  int32 reseller_id = 1;
  string notify_url = 2;
  string ip = 3;
  string secret_key = 4;
  // 数据签名
  string sign = 5;
  // 下单接口授权
  int32 trade_api = 6;
  // 余额接口授权
  int32 balance_api = 7;
  // 订单状态接口授权
  int32 trade_status_api = 8;
  // 订单列表接口授权
  int32 trade_list_api = 9;
  // 商品接口授权
  int32 product_api = 10;
  // web充值授权
  int32 web_api = 11;
  // 更新时间
  int32 update_time = 12;
  // 权限
  optional int32 auth = 13;
  // 允许访问1 不允许访问0
  int32 status = 14;
  // 订单失败后处理逻辑：0直接返回失败；1进入卡单处理
  int32 is_stuck = 15;
  // 用户类型：1，直客；2、同行
  int32 role_type = 16;
  // 接收订单后的处理模式
  int32 process_mode = 17;
  // 1限制请求ip，0不限制请求ip
  int32 ip_request_limit = 18;
  // 是否支持自定义秘钥 ，0使用系统默认， 1自定义秘钥
  int32 is_custom_key = 19;
}

message SocialCreditInfo {
  int32 id = 1;
  // 主体名称
  string enterprise_name = 2;
  // 统一社会信用编码
  string social_credit_code = 3;
  // 爱奇艺侧编码
  string fv = 4;
  string create_time = 5;
  string update_time = 6;
}

message ResellerAuthenticationInfo {
  // 编号
  int32 reseller_id = 1;
  string company_name = 2;
  string company_license = 3;
  string company_license_file = 4;
  string person_charge = 5;
  // 负责人身份证号码
  string person_id = 6;
  string person_id_face = 7;
  string person_id_back = 8;
  // 审核状态 【1通过 0待审核 -1失败】
  int32 status = 9;
  // 创建时间
  int32 create_time = 10;
  // 更新时间
  int32 update_time = 11;
}

// 创建分销商请求
message CreateResellerReq {
  int32 reseller_grade_id = 1 ;
  string name = 2 ;
  string full_name = 3 ;
  string subject_name = 4;
  string contacts = 5 ;
  string telephone = 6 ;
  string union_id = 7;
  int32  aftermarket = 8 ;
  int32 sign_company = 9;
  string password = 10;
  string industry_mcc = 11;
  int32 status = 12 ;
  string report_name = 13;
  string report_code = 14;
  AuthenticationReq authentication = 15;
  ApiSettingReq api_setting = 16;
  optional SocialCreditReq social_credit = 17;
  string email = 18;
}
message AuthenticationReq {
  string company_name = 1 ;
  string company_license = 2 ;
  string company_license_file = 3;
  string person_id = 4 ;
  string person_charge = 5 ;
  string person_id_face = 6;
  string person_id_back = 7;
  int32 status = 8;
}

message ApiSettingReq {
  string ip = 1;
  int32 trade_api = 2 ;
  int32 product_api = 3 ;
  int32 balance_api = 4 ;
  int32 trade_status_api = 5 ;
  int32 trade_list_api = 6 ;
  int32 web_api = 7 ;
  int32 status = 8;
  int32 role_type = 9;
  int32 is_stuck = 10;
  int32 ip_request_limit = 11;
  int32 process_mode = 12;
  string notify_url = 13;
}

message SocialCreditReq {
  int32 social_credit_id = 1;
}

// 创建分销商响应
message CreateResellerResp {
  // 编号
  int32 id = 1;
  // 分销商分类的id
  int32 reseller_grade_id = 2;
  // 银联类目
  string industry_mcc = 3;
  // 主体名称
  string subject_name = 4;
  string name = 5;
  string full_name = 6;
  string contacts = 7;
  string telephone = 8;
  // 状态
  int32 status = 9;
  // 创建时间
  int32 create_time = 10;
  // 更新时间
  int32 update_time = 11;
  string union_id = 12;
  // 售后服务（销售人员id）
  int32 aftermarket = 13;
  // 作为查询条件被查询的次数
  int32 search_time = 14;
  // 签约公司
  int32 sign_company = 15;
  // 是否显示分销商卡密订单 0不显示，1显示
  uint32 show_card_order = 16;
  // 账户CODE，用于自动加款
  string account_tag = 17;
  // 上报信息，报告给上游渠道
  string report_name = 18;
  // 上报信息，报告给上游渠道（统一社会信用代码）
  string report_code = 19;
  string email = 20;
  int32 receivables_id = 21;
}

// 获取分销商列表请求
message GetResellerListReq {
  int32 reseller_grade = 1;
  string name = 2 ;
  optional int32 status = 3 ;
  string contacts = 4;
  string telephone = 5;
  int32 page = 6  ;
  int32 limit = 7  ;
  int32 aftermarket = 8;
}

// 获取分销商列表请求(话费系统)
message GetResellerListForLifeReq {
  int32 reseller_grade_id = 1;
  string name = 2 ;
  optional int32 status = 3 ;
  string contacts = 4;
  string telephone = 5;
  int32 page = 6  ;
  int32 limit = 7  ;
  int32 aftermarket = 8;
  optional int32 min_balance = 9;
  optional int32 max_balance = 10;
  repeated int32 ids = 11;
  repeated int32 aftermarketList = 12;
  // 主体名称模糊查询
  string subjectName = 13;
  // 登录账号模糊查询
  string memberName = 14;
}

// 获取分销商列表响应
message GetResellerListResp {
  // 数据量
  int32 data_count = 1;
  // 列表数据
  repeated ResellerInfo list = 3;
}

message ResellerInfo {
  // 编号
  int32 id = 1;
  // 分销商分类的ID
  int32 reseller_grade_id = 2;
  // 银联类目
  string industry_mcc = 3;
  // 主体名称
  string subject_name = 4;
  string name = 5;
  string full_name = 6;
  string contacts = 7;
  string telephone = 8;
  // 状态
  int32 status = 9;
  // 创建时间
  int32 create_time = 10;
  // 更新时间
  int32 update_time = 11;
  string union_id = 12;
  // 售后服务（销售人员id）
  int32 aftermarket = 13;
  // 作为查询条件被查询的次数
  int32 search_time = 14;
  // 签约公司
  int32 sign_company = 15;
  // 是否显示分销商卡密订单 0不显示，1显示
  uint32 show_card_order = 16;
  // 账户CODE，用于自动加款
  string account_tag = 17;
  // 上报信息，报告给上游渠道
  string report_name = 18;
  // 上报信息，报告给上游渠道（统一社会信用代码）
  string report_code = 19;
  ApiSettings api_setting = 20;
  ResellerBalance balance = 21;
  ResellerGrade grade = 22;
  repeated  Members members = 23;
  string email = 24;
  int32  receivables_id = 25;
}


message ResellerGrade {
  int32 id = 1;
  string name = 2;
  // 商品接口调用频率
  int32 product_rate = 3;
  // 下单接口调用频率
  int32 trade_rate = 4;
  // 余额查询接口调用频率
  int32 balance_query_rate = 5;
  // 订单状态查询接口调用频率
  int32 trade_status_rate = 6;
  // 订单列表接口调用频率
  int32 trade_list_rate = 7;
  // 默认商品折扣
  double default_discount = 8;
  // 状态
  int32 status = 9;
}



message ApiSettings {
  int32 process_mode = 1;
  int32 reseller_id = 2;
}

message Members {
  string login_name = 1;
  int32 reseller_id = 2;
}

message ReqResellerGetMap {
  repeated int32 ids = 1 ;
}

message RespResellerGetMap {
  map<int32, RespResellerInfo> maps = 1;
}

// 搜索分销商列表请求
message ReqResellerSearchList {
  // 关键字
  string keyword = 1;
  // 分页
  ReqPage page = 2;

  repeated int32 reseller_ids = 3;
}

// 平台详情响应
message RespResellerInfo {
  int32 id = 1;
  string name = 2;
  int32 createTime = 3;
  // 全称（公司主体名）
  string fullName = 4;
  // 联系人电话
  string telephone = 5;
  // 联系人姓名
  string contacts = 6;
  // 商务负责人 id
  int32 aftermarket = 7;
  // 行业
  string industryMcc = 8;
  int32 reseller_grade_id = 9;
  string subject_name = 10 ;
  int32 status = 11;
  int32 update_time = 12 ;
  string union_id = 13 ;
  int32 search_time = 14 ;
  int32 sign_company = 15 ;
  bool show_card_order = 16 ;
  string account_tag = 17 ;
  string report_name = 18 ;
  string report_code = 19 ;
  string email = 20;
  int32 receivables_id = 21;
}

//搜索平台列表响应
message RespResellerSearchList {
  repeated RespResellerInfo list = 1;
}

message GetApiSettingReq {
  int32 reseller_id = 1;
}

message GetApiSettingResp {
  // 编号
  int32 reseller_id = 1;
  string notify_url = 2;
  string ip = 3;
  string secret_key = 4;
  // 数据签名
  string sign = 5;
  // 下单接口授权
  int32 trade_api = 6;
  // 余额接口授权
  int32 balance_api = 7;
  // 订单状态接口授权
  int32 trade_status_api = 8;
  // 订单列表接口授权
  int32 trade_list_api = 9;
  // 商品接口授权
  int32 product_api = 10;
  // web充值授权
  int32 web_api = 11;
  // 更新时间
  int32 update_time = 12;
  // 权限
  optional int32 auth = 13;
  // 允许访问1 不允许访问0
  int32 status = 14;
  // 订单失败后处理逻辑：0直接返回失败；1进入卡单处理
  int32 is_stuck = 15;
  // 用户类型：1，直客；2、同行
  int32 role_type = 16;
  // 接收订单后的处理模式
  int32 process_mode = 17;
  // 1限制请求ip，0不限制请求ip
  int32 ip_request_limit = 18;
  // 是否支持自定义秘钥 ，0使用系统默认， 1自定义秘钥
  int32 is_custom_key = 19;
}

message SearchBalanceReq{
  int32 reseller_id = 1;
  ReqPage page = 2;
}
message SearchBalanceResp{
  int32 data_count = 1;
  repeated ResellerBalanceSimpleInfo list = 2;
}
message ResellerBalanceSimpleInfo{
  double balance = 1;
  int32 id = 2;
  string name = 3;
}


message SearchBalanceDayReq{
  string day = 1;
  ReqPage page = 2;
}
message SearchBalanceDayResp{
  int32 data_count = 1;
  repeated ResellerBalanceDaySimpleInfo list = 2;
}
message ResellerBalanceDaySimpleInfo{
  double balance = 1;
  int32 id = 2;
  string name = 3;
  string day = 4;
}

message UpdateByCrontabReq{
  int32 user_id = 1;
  string realname = 2;
  string exec_event = 3;
  int32 reseller_id = 4;
}
message UpdateByCrontabResp{}


message ResellerBalanceWarnReq{
  string day = 1;
}
message ResellerBalanceWarnResp{}

message GetResellerIdsByAftermarketsReq {
  // 商务ID列表比传， 为空时候无数据
  repeated int32 aftermarket_ids = 1;
  // 如果需要指定部分分销商，则指定， 功能类似merge
  repeated int32 reseller_ids = 2;
}
message GetResellerIdsByAftermarketsResp {
  repeated int32 reseller_ids = 1;
}


message ValidateMemberPwdReq {
  // 账号名称
  string name = 1;
  // 密码
  string pwd = 2;
  // 是否登录系统，为true时会记录登录的最后时间、ip，false表示仅验证密码
  bool is_login = 3;
}

message ValidateMemberPwdResp {
  int32 id = 1;
  string name = 2;
  string telephone = 3;
  Reseller reseller = 4;
  message Reseller {
    int32 id = 1;
    string name = 2;
  }
  bool showCardOrder = 5;
}