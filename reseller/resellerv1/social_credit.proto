syntax = "proto3";

package reseller.resellerv1;
import "reseller/resellerv1/common.proto";
option go_package = "./reseller/resellerv1;resellerv1";

service SocialCredit{
  rpc Create(ReqSocialCreditCreate) returns(RespSocialCreditCreate){}
  rpc Update(ReqSocialCreditUpdate) returns(RespSocialCreditUpdate){}
  rpc List(ReqSocialCreditList) returns(RespSocialCreditList){}
  rpc Detail(ReqSocialCreditDetail) returns(SocialCreditDetail){}
  rpc Delete(ReqSocialCreditDelete) returns(SocialCreditDelete){}
}

message ReqSocialCreditCreate{
  string enterprise_name = 1;
  string social_credit_code = 2;

}
message RespSocialCreditCreate{
  int32 id = 1;
}

message ReqSocialCreditUpdate{
  int32 id = 1;
  string enterprise_name = 2;
  string social_credit_code = 3;
}
message RespSocialCreditUpdate{
  int32 id = 1;
}

message ReqSocialCreditList{
  // 关键字
  string keyword = 1;
  // 分页
  ReqPage page = 2;
}
message RespSocialCreditList{
  RespPage page = 1;
  repeated SocialCreditDetail data = 2;
}

message ReqSocialCreditDetail{
  int32 id = 1;
  string fv = 2;
}

message SocialCreditDetail{
  int32 id = 1;
  string enterprise_name = 2;
  string social_credit_code = 3;
  string fv = 4;
}

message ReqSocialCreditDelete{
  int32 id = 1;
}

message SocialCreditDelete{
  string mes = 1;
}