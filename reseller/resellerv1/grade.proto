syntax = "proto3";

package reseller.resellerv1;
import "validate/validate.proto";
option go_package = "./reseller/resellerv1;resellerv1";

// 分销商等级服务
service Grad {
  // 获取分销商等级列表
  rpc GetGradList(GetGradListReq) returns (GetGradListResp) {}
  // 添加分销商等级
  rpc AddGrad(AddGradReq) returns (AddGradResp) {}
  // 修改分销商等级
  rpc UpdateGrad(UpdateGradReq) returns (UpdateGradResp) {}
  // 删除分销商等级
  rpc DeleteGrad(DeleteGradReq) returns (DeleteGradResp) {}
  // 获取可用的分销商等级列表
  rpc GetAvailableGradList(GetAvailableGradListReq) returns (GetAvailableGradListResp) {}
}
// 获取分销商等级列表请求
message GetGradListReq {
  int32 page = 1 [(validate.rules).int32.gte = 1];
  int32 limit = 2 [(validate.rules).int32 = {gte:1,lte:1000}];
}
// 获取分销商等级列表返回
message GetGradListResp {
  // 数据量
  int32 data_count = 1;
  // 分销商等级列表
  repeated ResellerGradeInfo list = 2;
}
// 分销商等级
message ResellerGradeInfo {
  int32 id = 1;
  string name = 2;
  // 商品接口调用频率
  int32 product_rate = 3;
  // 下单接口调用频率
  int32 trade_rate = 4;
  // 余额查询接口调用频率
  int32 balance_query_rate = 5;
  // 订单状态查询接口调用频率
  int32 trade_status_rate = 6;
  // 订单列表接口调用频率
  int32 trade_list_rate = 7;
  // 默认商品折扣
  double default_discount = 8;
  // 状态
  int32 status = 9;
}

// 添加分销商等级请求
message AddGradReq {
  // 分销商等级名称
  string name = 1 [(validate.rules).string = {max_len:50,min_len:1}];
  // 商品接口调用频率
  int32 product_rate = 2;
  // 下单接口调用频率
  int32 trade_rate = 3;
  // 余额查询接口调用频率
  int32 balance_query_rate = 4;
  // 订单状态查询接口调用频率
  int32 trade_status_rate = 5;
  // 订单列表接口调用频率
  int32 trade_list_rate = 6;
  // 默认商品折扣
  double default_discount = 7;
}

// 添加分销商等级返回
message AddGradResp {
}

// 修改分销商等级请求
message UpdateGradReq {
  // id
  int32 id = 1 [(validate.rules).int32.gte = 1];
  // 分销商等级名称
  string name = 2 [(validate.rules).string = {max_len:50, min_len:1}];
  // 商品接口调用频率
  int32 product_rate = 3;
  // 下单接口调用频率
  int32 trade_rate = 4;
  // 余额查询接口调用频率
  int32 balance_query_rate = 5;
  // 订单状态查询接口调用频率
  int32 trade_status_rate = 6;
  // 订单列表接口调用频率
  int32 trade_list_rate = 7;
  // 默认商品折扣
  double default_discount = 8;
}

// 修改分销商等级返回
message UpdateGradResp {
}

// 删除分销商等级请求
message DeleteGradReq {
  int32 id = 1 [(validate.rules).int32.gte = 1];
}

// 删除分销商等级返回
message DeleteGradResp {
}

// 获取可用的分销商等级列表请求
message GetAvailableGradListReq {
}
// 获取可用的分销商等级列表返回
message GetAvailableGradListResp {
  // 列表
  repeated AvailableGradInfo list = 1;
}

message AvailableGradInfo {
  // id
  int32 id = 1;
  // 分销商等级名称
  string name = 2;
}