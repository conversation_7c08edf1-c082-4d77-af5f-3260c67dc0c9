syntax = "proto3";

package reseller.resellerv1;
import "validate/validate.proto";
option go_package = "./reseller/resellerv1;resellerv1";

service AuthProduct {
  // 获取授权商品列表
  rpc GetProductList(GetProductListReq) returns (GetProductListResp) {}
  // 批量启用或者禁用
  rpc BatchEnableDisable(BatchEnableDisableReq) returns (BatchEnableDisableResp) {}
  // 批量设置卡单模式
  rpc BatchSetStuckMode(BatchSetStuckModeReq) returns (BatchSetStuckModeResp) {}
  // 批量设置延迟时间
  rpc BatchSetDelayTime(BatchSetDelayTimeReq) returns (BatchSetDelayTimeResp) {}
  // 获取分销商商品信息
  rpc GetProductInfo(GetProductInfoReq) returns (GetProductInfoResp) {}
  // 获取分销商可用商品信息
  rpc GetProductInfoV2(GetProductInfoV2Req) returns (GetProductInfoV2Resp) {}
  // 批量添加授权商品
  rpc BatchAddProduct(BatchAddProductReq) returns (BatchAddProductResp) {}
  // 更新授权商品
  rpc UpdateProduct(UpdateProductReq) returns (UpdateProductResp) {}
  // 获取分销商授权商品的收单模式
  rpc GetProductProcessMode(GetProductProcessModeReq) returns (GetProductProcessModeResp) {}
    // 获取分销商授权商品的最高价格
  rpc GetProductMaxPrice(GetProductMaxPriceReq) returns (GetProductMaxPriceResp){}
}

message GetProductMaxPriceReq {
  // 分销商ID
  int32 reseller_id = 1;
  // 我们的商品ID
  int32 ours_product_id = 2;
}

message GetProductMaxPriceResp {
  // 价格，小数保留4位,四舍五入
  double price = 1;
  // 授权商品对应的状态 0未知 1未授权 2暂停 3正常
  ProductStatus status = 2 [(validate.rules).enum = {defined_only:true}];
}

enum ProductStatus {
  // 未知
  PRODUCT_STATUS_UNKNOW = 0;
  // 未授权
  PRODUCT_STATUS_UNAUTH = 1;
  // 暂停
  PRODUCT_STATUS_DISABLE = 2;
  // 正常
  PRODUCT_STATUS_ENABLE = 3;
}

// 状态枚举
enum AuthProductStatus {
  // 暂停
  AUTH_PRODUCT_STATUS_DISABLE = 0;
  // 正常
  AUTH_PRODUCT_STATUS_ENABLE = 1;
  // 过期
  AUTH_PRODUCT_STATUS_EXPIRED = -1;
  // 删除
  AUTH_PRODUCT_STATUS_DELETED = -2;
}

// 接收订单后的处理模式枚举
enum AuthProductProcessMode {
  // 默认
  AUTH_PRODUCT_PROCESS_MODE_DITTO = 0;
  // 正常
  AUTH_PRODUCT_PROCESS_MODE_NORMAL = 1;
  // 仅收单
  AUTH_PRODUCT_PROCESS_MODE_RECEIVE = 2;
  // 分流排队
  AUTH_PRODUCT_PROCESS_MODE_SHUNT = 3;
}

// 订单失败后处理模式枚举
enum AuthProductStuckMode {
  // 直接返回失败结果
  AUTH_PRODUCT_STUCK_MODE_CLOSE = 0;
  // 失败后卡单
  AUTH_PRODUCT_STUCK_MODE_OPEN = 1;
  // 跟随分销商设置
  AUTH_PRODUCT_STUCK_MODE_FOLLOW = 2;
}

// 批量设置延迟时间请求
message BatchSetDelayTimeReq {
  // 授权产品id集合
  repeated int32 ours_product_id = 1;
  // 状态
  int32 delay_time = 2;
  // 分销商id
  int32 reseller_id = 3;
}

// 批量设置延迟时间响应
message BatchSetDelayTimeResp {

}
// 获取分销商授权商品的收单模式请求
message GetProductProcessModeReq {
  int32 reseller_id = 1;
  int32 ours_product_id = 2;
}

// 获取分销商授权商品的收单模式响应
message GetProductProcessModeResp {
  AuthProductProcessMode process_mode = 1;
}

// 更新授权商品请求
message UpdateProductReq {
  // 我们的商品id
  int32 ours_product_id = 1 [(validate.rules).int32 = {gt:0}];
  // 分销商id
  int32 reseller_id = 2 [(validate.rules).int32 = {gt:0}];
  // 折扣
  double discount = 3;
  // 价格
  double price = 4;
  // 状态 1正常 0禁用
  AuthProductStatus status = 5 [(validate.rules).enum = {defined_only:true}];
  // 授权产品信息
  string auth_product_info = 6;
  // 授权平台产品信息
  string auth_product_ids = 7;
  // 接收订单后的处理模式
  AuthProductProcessMode process_mode = 8 [(validate.rules).enum = {defined_only:true}];
  // 订单失败后处理模式：0返回失败，1今日卡单逻辑，2跟随分销商设置
  AuthProductStuckMode stuck_mode = 9 [(validate.rules).enum = {defined_only:true}];
  // 延迟时间
  int32 delay_time = 10;
}

// 更新授权商品响应
message UpdateProductResp {

}

// 批量添加授权商品请求
message BatchAddProductReq {
  // 分销商id
  int32 reseller_id = 1 [(validate.rules).int32 = {gt:0}];
  // 授权商品信息
  repeated AuthInfo auth_info = 2 [(validate.rules).repeated = {min_items:1}];
}

message AuthInfo {
  int32 ours_product_id = 1 [(validate.rules).int32 = {gt:0}];
  double discount = 2;
  double price = 3;
  double min_price = 4;
  string name = 5;
}
// 批量添加授权商品响应
message BatchAddProductResp {

}

// 获取分销商商品信息请求
message GetProductInfoReq {
  // 分销商id
  int32 reseller_id = 1 [(validate.rules).int32 = {gt:0}];
  // 授权商品id
  int32 ours_product_id = 2 [(validate.rules).int32 = {gt:0}];
}

// 获取分销商商品信息响应
message GetProductInfoResp {
  ResellerAuthProductInfo info = 1;
}

// 获取分销商商品信息请求
message GetProductInfoV2Req {
  // 分销商id
  int32 reseller_id = 1 [(validate.rules).int32 = {gt:0}];
  // 授权商品id
  int32 ours_product_id = 2 [(validate.rules).int32 = {gt:0}];
  // 是否获取所有状态的商品
  bool all_status = 3;
}

// 获取分销商商品信息响应
message GetProductInfoV2Resp {
  ResellerAuthProductInfo info = 1;
}

// 批量设置卡单模式请求
message BatchSetStuckModeReq {
  // 授权产品id集合
  repeated int32 ours_product_id = 1 [(validate.rules).repeated = {min_items:1}];
  // 卡单模式
  AuthProductStuckMode stuck_mode = 2 [(validate.rules).enum = {defined_only:true}];
  // 分销商id
  int32 reseller_id = 3 [(validate.rules).int32 = {gt:0}];
}

// 批量设置卡单模式请求
message BatchSetStuckModeResp {

}

// 批量启用或者禁用请求
message BatchEnableDisableReq {
  // 授权产品id集合
  repeated int32 ours_product_id = 1 [(validate.rules).repeated = {min_items:1}];
  // 状态
  AuthProductStatus status = 2 [(validate.rules).enum = {defined_only:true}];
  // 分销商id
  int32 reseller_id = 3 [(validate.rules).int32 = {gt:0}];
}

// 批量启用或者禁用响应
message BatchEnableDisableResp {

}

// 获取授权商品列表请求
message GetProductListReq {
  // 分销商id
  int32 reseller_id = 1;
  // 授权商品 id 集合
  repeated int32 ours_product_id = 2;
  // 卡单模式
  optional AuthProductStuckMode stuck_mode = 3;
  // 状态
  optional AuthProductStatus status = 4;
  // 授权商品名称
  bool with_reseller = 5;
}


// 获取授权商品列表响应
message GetProductListResp {
  // 数据列表
  repeated ResellerAuthProductInfo list = 1;
}

message ResellerAuthProductInfo {
  // 我们的商品id
  int32 id = 1;
  // 分销商id
  int32 reseller_id = 2;
  // 折扣
  double discount = 3;
  // 价格
  double price = 4;
  // 状态 1正常 0禁用
  AuthProductStatus status = 5;
  // 授权产品信息
  string auth_product_info = 6;
  // 授权平台产品信息
  string auth_product_ids = 7;
  // 创建时间
  int32 create_time = 8;
  // 更新时间
  int32 update_time = 9;
  // 接收订单后的处理模式
  AuthProductProcessMode process_mode = 10;
  // 订单失败后处理模式：0返回失败，1今日卡单逻辑，2跟随分销商设置
  AuthProductStuckMode stuck_mode = 11;

  ResellerSimpleData reseller = 12;
  int32 delay_time = 13;
}

message ResellerSimpleData {
  int32 id = 1;
  string name = 2;
}