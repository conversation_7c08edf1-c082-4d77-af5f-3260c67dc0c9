syntax = "proto3";

package reseller.resellerv1;
import "validate/validate.proto";
option go_package = "./reseller/resellerv1;resellerv1";


// 银联行业类目表
service Industry {
  // 搜索信息
  rpc GetList(ReqIndustryGetList) returns (RespIndustryGetList) {}
}

message ReqIndustryGetList {
  repeated string mccList = 1 [(validate.rules).repeated = {min_items:1}];
}

message RespIndustryGetList {
  repeated Item list = 1;

  message Item {
    // id
    int32 id = 1;
    // 行业类目名称
    string name = 2;
    // 行业类目编码
    string mcc = 3;

    Item parent = 4;
  }
}