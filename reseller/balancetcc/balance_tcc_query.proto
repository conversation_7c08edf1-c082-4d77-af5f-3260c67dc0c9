syntax = "proto3";

package reseller.resellerv1;
option go_package = "./reseller/balancetccquery;balancetccquery";


// 钱包更新Tcc查询GRPC
// 这里仅定义结构，目的是方便动态调用和调用直接使用
service Query {
  // 获取分销商等级列表
  rpc Query(QueryReq) returns (QueryResp) {}
}

// 请求参数
message QueryReq {
  // 原子请求id，结构为："业务标识-业务数据id-雪花ID"，如：yum-123-123456789012345678，有新业务接入时，请联系相关负责人分配业务标识
  string request_id = 1;

  // 类型，和原发起时一致
  int32 type = 2;
}

message QueryResp {
  // 查询结果，1未知，2成功，3失败，当返回失败时，会回滚余额的操作
  int32 status = 1;
}