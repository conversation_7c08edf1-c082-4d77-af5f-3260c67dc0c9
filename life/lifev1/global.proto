syntax = "proto3";

package life.api.inner;

option go_package = "./life/lifev1;lifev1";

// 公共响应参数
message Response {
	// 错误码, 200或0为成功
	int32 code = 1;
	// 错误信息
	string message = 2;
	// 错误原因
	string reason = 3;
	// 元数据
	map<string, string> metadata = 4;
}

enum GlobalStatus {
	// 全部
	GLOBAL_STATUS_ALL = 0;
	// 启用
	GLOBAL_STATUS_ENABLE = 1;
	// 禁用
	GLOBAL_STATUS_DISABLE = 2;
}

// 分页请求参数
message PageReq {
	// 页码
	int32 page = 1;
	// 每页大小
	int32 pageSize = 2;
}

// 分页响应参数
message PageReply {
	// 总数
	int32 total = 1;
	// 页码
	int32 page = 2;
	// 每页大小
	int32 pageSize = 3;
}

// 时间范围查询
message TimeRangeReq {
	// 开始时间 unix 时间戳
	int64 startAt = 1;
	// 结束时间 unix 时间戳
	int64 endAt = 2;
}

// 枚举类型
message EnumItem {
	// 枚举值
	int32 value = 1;
	// 枚举描述
	string label = 2;
}

message SupplierItem {
	uint32 id = 1;
	// 上游名称
	string name = 2;
	EnumItem status = 3;
	// 类型: 订单处理类型 1普通模式 2 降级模式
	EnumItem processType = 4;
	// 库存类型:1金额 2数量
	EnumItem stockType = 5;
	// 备注
	string remark = 6;
	// 供应商负责人id
	uint32 sponsorUserId = 7;
	// 余额
	double balance = 8;
	// 适配器编码
	string adapter_name = 9;
	// 主体
	string subjectName = 10;
	// 收款人
	string payee = 11;
	// 扩展字段
	string apiConfig = 12;
	// 供应商负责人
	string sponsorUserName = 13;
	// 超时查询时间
	int32 adapterTimeout = 14;
}