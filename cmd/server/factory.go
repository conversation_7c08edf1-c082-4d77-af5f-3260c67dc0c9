package main

import (
	"cardMall/internal/constants"
	"cardMall/internal/data"
	log2 "cardMall/internal/pkg/log"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

// 定义一些需要依赖才能生成实例的对象

// NewIdGenerate 创建一个IdGenerate实例
func NewIdGenerate(hLog *log.Helper, d *data.Data) *idgenerator.Generator {
	return idgenerator.NewGenerator(hLog, d.Rdb, constants.IdGeneratorCacheTpl.GetKey(context.Background()))
}

func NewPayLogger(logger *log2.FileLogger) *log2.PayLogger {
	return logger.PayLogHelper
}

func NewNotifyLogger(logger *log2.FileLogger) *log2.NotifyLogger {
	return logger.NotifyLogHelper
}

func NewBusinessLogger(logger *log2.FileLogger) *log2.BusinessLogger {
	return logger.BusinessLogHelper
}

func NewRechargeLogger(logger *log2.FileLogger) *log2.RechargeLogger {
	return logger.RechargeLogHelper
}
func NewMeiTuanLogger(logger *log2.FileLogger) *log2.MeiTuanLogger {
	return logger.MeiTuanLogger
}
func NewQianZhuLogger(logger *log2.FileLogger) *log2.QianZhuLogger {
	return logger.QianZhuLogger
}
