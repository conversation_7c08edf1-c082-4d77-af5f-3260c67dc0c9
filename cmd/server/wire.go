//go:build wireinject
// +build wireinject

// The build tag makes sure the stub is not built in the final build.

package main

import (
	"cardMall/internal/biz"
	"cardMall/internal/biz/ds"
	"cardMall/internal/conf"
	"cardMall/internal/data"
	"cardMall/internal/data/adminrepositoryimpl"
	"cardMall/internal/data/apprepositoryimpl"
	"cardMall/internal/data/cacheimpl"
	"cardMall/internal/data/clirepositoryimpl"
	"cardMall/internal/data/eventimpl"
	"cardMall/internal/data/excelimpl"
	"cardMall/internal/data/h5repositoryimpl"
	"cardMall/internal/data/repositoryimpl"
	"cardMall/internal/data/rpcimpl"
	"cardMall/internal/data/rpcimpl/product"
	"cardMall/internal/data/taskrepositoryimpl"
	"cardMall/internal/manager"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/clibiz"
	"cardMall/internal/module/commonbiz"
	"cardMall/internal/module/customerbiz"
	"cardMall/internal/module/h5biz"
	"cardMall/internal/module/openapibiz"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/module/taskbiz"
	"cardMall/internal/pkg"
	"cardMall/internal/server"
	"cardMall/internal/server/middleware"
	"cardMall/internal/service"
	"cardMall/internal/service/admin"
	"cardMall/internal/service/app"
	"cardMall/internal/service/cli"
	"cardMall/internal/service/common/convertor"
	"cardMall/internal/service/customer"
	"cardMall/internal/service/h5"
	"cardMall/internal/service/openapi"
	"cardMall/internal/service/supplier"
	"cardMall/internal/service/task"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/google/wire"
)

// wireApp init kratos application.
func wireApp(*conf.Server, *conf.Data, log.Logger, *log.Helper, *conf.Bootstrap) (*kratos.App, func(), error) {
	panic(wire.Build(
		data.ProviderSetData,
		server.ProviderSetServer,
		admin.ProviderSetAdminService,
		customer.ProviderSetCustomerAdminService,
		app.ProviderSetAppService,
		h5.ProviderSetH5Service,
		supplier.ProviderSetSupplierService,
		task.ProviderSetCronService,
		cli.ProviderSetCliService,
		convertor.ProviderSetCommonConvertor,

		ds.ProviderSetDs,
		adminbiz.ProviderSetAdminBiz,
		customerbiz.ProviderSetCustomerAdminBiz,
		appbiz.ProviderSetAppBiz,
		h5biz.ProviderSetH5Biz,
		taskbiz.ProviderSetTaskBiz,
		supplierbiz.ProviderSetSupplierBiz,
		clibiz.ProviderSetCliBiz,

		adminrepositoryimpl.ProviderSetAdminRepositoryImpl,
		apprepositoryimpl.ProviderSetAppRepositoryImpl,
		h5repositoryimpl.ProviderSetH5RepositoryImpl,
		rpcimpl.ProviderSetRpcImpl,
		repositoryimpl.ProviderSetRepositoryImpl,
		taskrepositoryimpl.ProviderSetTaskRepositoryImpl,
		excelimpl.ProviderSetExcelImpl,
		clirepositoryimpl.ProviderSetCliRepositoryImpl,
		eventimpl.ProviderEvent,
		middleware.ProviderSetMiddlewareSet,
		cacheimpl.ProviderSetCacheImpl,
		service.ProviderSet,

		pkg.ProviderSetPkg,
		product.ProviderSetProductRpcImpl,
		NewIdGenerate,
		biz.ProviderSetBiz,

		openapi.ProviderSetOpenapiService,
		openapibiz.ProviderSetOpenApiBiz,
		manager.ProviderSetManager,
		commonbiz.ProviderSetCommonBiz,

		//NewPayLogger,
		//NewNotifyLogger,
		//NewBusinessLogger,
		//NewRechargeLogger,
		//NewMeiTuanLogger,
		//NewQianZhuLogger,
		newApp,
	))
}
