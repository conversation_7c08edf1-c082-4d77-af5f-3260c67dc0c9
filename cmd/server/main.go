package main

import (
	"cardMall/internal/constants"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/middlewares"
	"context"
	"flag"
	"os"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/coroutine"

	"cardMall/internal/server"

	"cardMall/internal/conf"

	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"github.com/go-kratos/kratos/v2/transport/http"

	_ "go.uber.org/automaxprocs"
)

// go build -ldflags "-X main.Version=x.y.z"
var (
	// Name is the name of the compiled software.
	Name = constants.AppName
	// Version is the version of the compiled software.
	Version = "v0.0.0"
	// flagconf is the config flag.
	flagconf string

	id, _ = os.Hostname()

	isCommand = false
)

func init() {
	flag.StringVar(&flagconf, "conf", "./configs", "config path, eg: -conf config.yaml")
	middlewares.SetHostVersion(id, Version)
}

func newApp(
	logger log.Logger,
	hLog *log.Helper,
	hs *http.Server,
	appHttpServer *server.AppHttpServer,
	supplierHTTPServer *server.SupplierHTTPServer,
	taskServer *server.TaskServer,
	cliServer *server.CliServer,
	customerAdminHttpServer *server.CustomerAdminHttpServer,
	openApiHTTPServer *server.OpenApiHTTPServer,
) *kratos.App {
	if len(os.Args) > 0 {
		for _, v := range os.Args {
			if v == "-cmd" {
				isCommand = true
			}
		}
	}
	coroutineServer := coroutine.NewServer(hLog)
	serverOption := kratos.Server(
		hs, supplierHTTPServer, taskServer, coroutineServer, customerAdminHttpServer, openApiHTTPServer, appHttpServer,
	)
	if isCommand {
		serverOption = kratos.Server(
			cliServer,
		)
	}

	return kratos.New(
		kratos.ID(id),
		kratos.Name(Name),
		kratos.Version(Version),
		kratos.Metadata(map[string]string{}),
		kratos.Logger(logger),
		serverOption,
	)
}
func RequestId() log.Valuer {
	return func(ctx context.Context) interface{} {
		if requestId := ctx.Value("X-Request-ShopId"); requestId != "" && requestId != nil {
			return requestId
		}
		return ""
	}
}

func main() {
	flag.Parse()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", Name,
		"service.version", Version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", RequestId(),
	)
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()
	if err := c.Load(); err != nil {
		panic(err)
	}
	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logHelper := log.NewHelper(logger)
	//fileLogger := pkgLog.NewFileLogger(id, Name, Version)
	app, cleanup, err := wireApp(bc.Server, bc.Data, logger, logHelper, &bc)
	if err != nil {
		panic(err)
	}
	defer cleanup()

	// start and wait for stop signal
	if err := app.Run(); err != nil {
		panic(err)
	}
}
