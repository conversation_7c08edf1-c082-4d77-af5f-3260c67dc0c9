syntax = "proto3";

package platformservice.platformv1;
import "validate/validate.proto";
option go_package = "./platform/platformv1;platformv1";

service PlatformAttribute {
  // 更新上游余额
  rpc updateBalance (updateBalancePlatformAttributeRequest) returns (updateBalancePlatformAttributeReply);

  // warn_device
  rpc UpdateWarnDevice (UpdateWarnDeviceReq) returns (UpdateWarnDeviceResp);

  // 余额预警设置 updateWarn
  rpc UpdateBalanceWarn (UpdateBalanceWarnReq) returns (UpdateBalanceWarnResp);

  // 获取返利金额
  rpc GetRebate (GetRebateReq) returns (GetRebateResp);
}

message GetRebateReq{
  int32 platform_id = 1;
}

message GetRebateResp{
  double rebate = 1;
}

message UpdateWarnDeviceReq{
  int32 platform_id = 1;
  string warn_device = 2;
}
message UpdateWarnDeviceResp{}

message UpdateBalanceWarnReq{
  int32 platform_id = 1;
  float balance_warning = 2;
  int32 difference = 3;
}
message UpdateBalanceWarnResp{}

// 余额变更操作
enum BalanceOperate {
  // 减少
  BALANCE_OPERATE_DECREASE = 0;
  // 增加
  BALANCE_OPERATE_INCREASE = 1;
}

// 操作人类型
enum BalanceExecutorType {
  // 系统自动
  BALANCE_EXECUTOR_TYPE_SYSTEM = 0;
  // 后台操作
  BALANCE_EXECUTOR_TYPE_ADMIN = 1;
  // 分销商操作
  BALANCE_EXECUTOR_TYPE_RESELLER = 2;
}

// 余额变更类型
enum BalanceType {
  BALANCE_TYPE_UNKNOWN = 0;
  // 充值
  BALANCE_TYPE_RECHARGE = 1;
  // 订单消费
  BALANCE_TYPE_ORDER = 2;
  // 冲账
  BALANCE_TYPE_DEDUCTION = 3;
  // 退款
  BALANCE_TYPE_REFUND = 4;
  // 扣款
  BALANCE_TYPE_WITHHOLD = 5;
}

message updateBalancePlatformAttributeRequest {
  // 平台ID
  int32 id = 1;
  // 操作者
  int32 executor = 2;
  // 操作类型 0系统 1后台 2分销商
  BalanceExecutorType executor_type = 3 [(validate.rules).enum.defined_only = true];
  // 变动余额  传绝对值，不要负数，通过operate类型区分
  float balance = 4;
  // 操作类型 1加款 0扣款
  BalanceOperate operate = 5 [(validate.rules).enum.defined_only = true];
  // 备注
  string remark = 6;
  // 类型 1充值 2订单消费 3冲账 4充值 5扣款
  BalanceType type = 7 [(validate.rules).enum.defined_only = true];
}

message updateBalancePlatformAttributeReply {
  int32 platform_id = 1;
  int32 operate = 2;
  int32 type = 3;
  float money = 4;
  float current_balance = 5;
  string remark = 6;
  int32 executor = 7;
  int32 executor_type = 8;
  int32 create_time = 9;
  int32 id = 10;
}
