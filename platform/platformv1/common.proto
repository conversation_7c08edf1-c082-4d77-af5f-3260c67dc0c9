syntax = "proto3";
package platformservice.platformv1;
import "validate/validate.proto";
option go_package = "./platform/platformv1;platformv1";

// 空请求或返回
message Empty {
}

// 空请求
message ReqEmpty {
}

// 空返回
message RespEmpty {
}


// ReqPage 分页请求参数
message ReqPage {
  int32 num = 1 [(validate.rules).int32 = {gte:1}]; //页码
  int32 size = 2 [(validate.rules).int32 = {gte:1, lte: 10000}]; //每页数量
}
// ReqPage 分页请求参数
message RespPage {
  int32 num = 1; //页码
  int32 size = 2; //每页数量
  int32 total = 3; //总数
}