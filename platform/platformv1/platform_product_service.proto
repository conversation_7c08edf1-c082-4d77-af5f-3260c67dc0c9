syntax = "proto3";

package platformservice.platformv1;
import "google/protobuf/timestamp.proto";
import "platform/platformv1/platform_card_abolish.proto";
import "platform/platformv1/platform_product.proto";
import "platform/platformv1/platform.proto";
option go_package = "./platform/platformv1;platformv1";

// 平台(上游)
service PlatformProduct {
  // 提交上报信息
  rpc SubmitReport (ReqPlatformProductSubmitReport) returns (RespPlatformProductSubmitReport) {}
  // 获取上报信息
  rpc GetReport(ReqPlatformProductGetReport) returns (RespPlatformProductGetReport) {}

  rpc UpdateSocialCredit(ReqUpdateSocialCredit) returns (RespUpdateSocialCredit){}

  //官方卡密缓存预读 搜索列表
  rpc SearchListCardStock (SearchListCardStockReq) returns (SearchListCardStockResp){}
  //官方卡密缓存预读 更新
  rpc UpdateCardStock (UpdateCardStockReq) returns (UpdateCardStockResp){}


  //官方卡密缓存预读 搜索列表
  rpc SearchListCardSummary (SearchListCardSummaryReq) returns (SearchListCardSummaryResp){}
  //官方卡密缓存预读 更新
  rpc SaveCardSummary (SaveCardSummaryReq) returns (SaveCardSummaryResp){}


  //官方卡密缓存预读 搜索列表
  rpc SearchListCardImport (SearchListCardImportReq) returns (SearchListCardImportResp){}
  //官方卡密缓存预读 更新
  rpc BatchInvalidCardImport (BatchInvalidCardImportReq) returns (BatchInvalidCardImportResp){}
  //获取卡密缓存预读
  rpc GetCardImport (GetCardImportReq) returns (GetCardImportResp){}
  // 卡密入库
  rpc AddCardImport (AddCardImportReq) returns (AddCardImportResp) {}
  rpc CheckCardImportExcel (CheckCardImportExcelReq) returns (CheckCardImportExcelResp) {}

  //xxx官方卡密 搜索列表
  rpc SearchListPlatformCard (SearchListPlatformCardReq) returns (SearchListPlatformCardResp){}

  //官方卡密 搜索列表
  rpc SearchListPlatformProduct (SearchListPlatformProductReq) returns (SearchListPlatformProductResp){}

  // 根据official_product_id获取平台商品最低价格
  rpc GetPlatformProductMinPriceByOfficialProductId (GetPlatformProductMinPriceByOfficialProductIdReq) returns (GetPlatformProductMinPriceByOfficialProductIdResp) {}

  // 获取上游平台商品列表
  rpc GetPlatformProductList (GetPlatformProductListRequest) returns (GetPlatformProductListReply) {}

  // 获取上游平台商品
  rpc GetPlatformProduct (GetPlatformProductRequest) returns (GetPlatformProductReply) {}
  // 创建上游平台商品
  rpc CreatePlatformProduct (CreatePlatformProductRequest) returns (CreatePlatformProductReply) {}
  // 修改上游平台商品
  rpc UpdatePlatformProduct (UpdatePlatformProductRequest) returns (UpdatePlatformProductReply) {}
  // 删除上游平台商品
  rpc DeletePlatformProduct (DeletePlatformProductRequest) returns (DeletePlatformProductReply) {}
  // 修改上游平台商品状态
  rpc UpdateStatusPlatformProduct (UpdateStatusPlatformProductRequest) returns (UpdateStatusPlatformProductReply) {}
  // 获取 上游平台商品 日志
  rpc GetLogListPlatformProduct (GetLogListPlatformProductRequest) returns (GetLogListPlatformProductReply) {}

  //通过主键ID集合获取平台商品
  rpc GetPlatformProductByIds (GetPlatformProductByIdsReq) returns (GetPlatformProductByIdsResp) {}

  // 获取返利金额总和
  rpc GetSumRebatePrice(GetSumRebatePriceReq) returns (GetSumRebatePriceResp){}

  // 更新卡密状态
  rpc UpdateCardStatus(UpdateCardStatusReq) returns (UpdateCardStatusResp){}

  // 更新卡密状态
  rpc UpdateCardStatusNew(UpdateCardStatusNewReq) returns (UpdateCardStatusNewResp){}

  // 获取一个缓存卡密
  rpc GetOneCacheCard(GetOneCacheCardReq) returns (GetOneCacheCardResp){}
  // 获取一个缓存卡密
  rpc GetOneCacheCardNew(GetOneCacheCardNewReq) returns (GetOneCacheCardNewResp){}

  // 根据上游商品获取上报信息
  rpc GetSocialByProductId(GetSocialByProductIdReq) returns (GetSocialByProductIdResp){}
}

// 根据上游商品获取上报信息请求
message GetSocialByProductIdReq {
  // 接口平台商品id
  int32 platform_product_id = 1;
}

// 根据上游商品获取上报信息响应
message GetSocialByProductIdResp {
  string fv = 1;
}

// 获取一个缓存卡密请求
message GetOneCacheCardReq {
  int32 platform_product_id = 1; // 接口平台商品id
  string tag = 2; // 标签
}

// 获取一个缓存卡密响应
message GetOneCacheCardResp {
  string card_code = 1; // 卡密
}

// 获取一个缓存卡密请求
message GetOneCacheCardNewReq {
  int32 platform_product_id = 1; // 接口平台商品id
  string tag = 2; // 标签
  string serial_number = 3;
}

// 获取一个缓存卡密响应
message GetOneCacheCardNewResp {
  string card_code = 1; // 卡密
}

// 更新卡密状态请求
message UpdateCardStatusReq {
  int32 platform_product_id = 1;
  string card_code = 2;
  int32 status = 3;
  string tag = 4;
}

// 更新卡密状态响应
message UpdateCardStatusResp {

}

// 更新卡密状态请求
message UpdateCardStatusNewReq {
  int32 platform_product_id = 1;
  string card_code = 2;
  int32 status = 3;
  string tag = 4;
  string serial_number = 5;
}

// 更新卡密状态响应
message UpdateCardStatusNewResp {

}

message GetSumRebatePriceReq {
  repeated int32 platform_product_ids = 1; // 接口平台商品id集合
}

message GetSumRebatePriceResp {
  double sum_rebate_price = 1; // 返利总价格
}

message SearchListCardStockReq {
  int32 platform_product_id = 1; // 接口平台商品id
  int32 page = 2; // 页码
  int32 limit = 3;// 每页数量
}

message SearchListCardStockResp {
  int32 data_count = 1; // 数据总数

  repeated CardStockInfo list = 2; // 卡密库存列表
}

message CardStockInfo {
  CardStock card_stock = 1; // 卡密库存信息
  PlatformProductInfoV2 platform_product = 6; // 接口平台商品信息
}

message CardStock {
  int32 platform_product_id = 1; // 接口平台商品id
  int32 cache_card_stock = 2; //  缓存卡密库存
  int32 cache_stock_warning = 3;// 缓存卡密库存预警
  google.protobuf.Timestamp update_time = 4; // 更新时间
  int32 last_handler = 5;// 最后操作人
}

message UpdateCardStockReq {
  int32 platform_product_id = 1; // 接口平台商品id
  int32 cache_card_stock = 2; // 缓存卡密库存
  int32 cache_stock_warning = 3; // 缓存卡密库存预警
  int32 last_handler = 4; // 最后操作人
}

message UpdateCardStockResp {}

message SearchListCardSummaryReq {
  int32 official_product_id = 1; // 接口平台商品id
  int32 page = 2; // 页码
  int32 limit = 3;// 每页数量
}

message SearchListCardSummaryResp {
  int32 data_count = 1; // 数据总数
  repeated CardSummary list = 2; // 卡密汇总列表
}

message CardSummary {
  int32 official_product_id = 1; // 接口平台商品id
  int32 total_num = 2; // 总数量
  int32 used_num = 3; // 已使用数量
  int32 expired_num = 4; // 过期数量
  int32 unused_num = 5; // 未使用数量
  int32 cancel_num = 6; // 作废数量
  google.protobuf.Timestamp create_time = 7; // 创建时间
  google.protobuf.Timestamp update_time = 8; // 更新时间
}

message SaveCardSummaryReq {
  int32 platform_id = 1; // 平台id
  int32 platform_product_id = 2; // 接口平台商品id
  string price = 3; // 单价
  string discount = 4; // 折扣
  string total_price = 5; // 总价
  int32 import_quantity = 6; // 导入数量
  google.protobuf.Timestamp expire_time = 7; // 过期时间
  string remark = 8; // 备注
  repeated string card_ids = 9; // 卡密文件
}

message SaveCardSummaryResp {}

message SearchListCardImportReq {
  int64 start_expire_time = 1; // 过期时间开始
  int64 end_expire_time = 2; // 过期时间结束
  int64 start_create_time = 3; // 创建时间开始
  int64 end_create_time = 4; // 创建时间结束
  int32 handler = 5; // 操作人
  int32 abolisher = 6; // 作废人
  int32 platform_id = 7; // 平台id
  int32 page = 8; // 页码
  int32 limit = 9; // 每页数量
  int32 official_product_id = 10; // 接口平台商品id
  int64 timestamp = 11; // 时间戳
  int32 status = 12; // 状态

  bool withPlatform = 13;
  bool withPlatformProduct = 14;
  bool withAbolish = 15;

  SearchListCardImportSortType sortType = 16;
}
enum SearchListCardImportSortType {
  SEARCH_LIST_CARD_IMPORT_SORT_TYPE_NONE = 0;
  SEARCH_LIST_CARD_IMPORT_SORT_TYPE_ID_ASC = 1;
  SEARCH_LIST_CARD_IMPORT_SORT_TYPE_CREATE_TIME_DESC = 2;
}

message SearchListCardImportResp {
  int32 data_count = 1; // 数据总数
  repeated CardImport list = 2; // 卡密导入列表
}

message CardImport {
  int32 id = 1; // 自增长编码导入批次号
  int32 platform_id = 2; // 上游id
  int32 official_product_id = 3; // 官方产品id
  int32 platform_product_id = 4; // 商品编码
  int32 handler = 5; // 导入人编号
  int64 expire_time = 6; // 卡密过期时间
  float total_price = 7; // 总价
  float price = 8; // 单价
  float discount = 9; // 折扣
  int32 import_quantity = 10; // 导入数量
  int32 cancel_num = 11; // 作废数量
  int32 expired_num = 12; // 过期数量
  int32 used_num = 13; // 已使用数量
  int32 unused_num = 14; // 未使用数量
  string remark = 15; // 卡密说明
  int64 create_time = 16; // 导入时间
  int64 update_time = 17; // 更新时间
  int32 status = 18; // -1导入失败 0导入中 1待使用 2使用中 3使用完 4废弃完 5过期完 6完结
  string card_file = 19; // 上游卡密文件名称
  string fail_mes = 20; // 上传异常说明

  PlatformCardAbolish abolish = 21; // 作废信息
  PlatformProductInfoV2 platform_product = 22; // 接口平台商品信息
  PlatformInfo platform = 23; // 平台信息

  string status_text = 24;
}

message BatchInvalidCardImportReq {
  repeated int32 import_ids = 1; // 导入id
  string reason = 2; // 作废原因
  string tag = 3; // 作废标签
  int32 handler = 4; // 作废人
}

message BatchInvalidCardImportResp {}

message GetCardImportReq{
  int32 id = 1; // 导入id
}
message GetCardImportResp {
  CardImport card_import = 1; // 卡密导入信息
}

message AddCardImportReq{
  int32 platform_id = 1; // 平台id
  int32 platform_product_id = 2; // 接口平台商品id
  double price = 3; // 单价
  double discount = 4; // 折扣
  double total_price = 5; // 总价
  int32 import_quantity = 6; // 导入数量
  int64 expire_time = 7; // 过期时间
  string remark = 8; // 备注
  string card_file = 9; // 卡密文件

  int32  official_product_id = 10; // 官方产品id，通过product获取传递过来
  int32  handler = 11; // 操作人

  string tag = 12; // 标签，前端传入，通过platform_product_id和official_product_id获取
}
message AddCardImportResp {}

message CheckCardImportExcelReq{
  string card_file = 1; // 卡密文件
}
message CheckCardImportExcelResp{
  repeated CheckCardImportExcelData list = 1;
}
message CheckCardImportExcelData {
  int32 rowNum = 1;
  string cardCode = 2 ;
  string errMsg = 3;
}

message SearchListPlatformCardReq{
  int32 batch_number = 1;
  string tag = 2;
  int32 page = 3;
  int32 limit = 4;
  int32 status = 5;
}
message SearchListPlatformCardResp{
  int32 data_count = 1; // 数据总数
  repeated PlatformCard list = 2; // 卡密列表
}
message PlatformCard{
  string card_number = 1;
  int32 platform_product_id = 2;
  int32 batch_number = 3;
  int32 status = 4;
  int32 update_time = 5;

  string status_text = 6;
}

//官方卡密 搜索列表
message SearchListPlatformProductReq{
  int32 page = 1;
  int32 limit = 2;

  int32 platform_id = 3;
  repeated int32 official_product_ids = 4;
  int32 exchange_type = 5;
  string keyword = 6;

  // "0" 和 "disable" 禁用
  // "1" 和 "enable" 启用
  // ""  和 其他 表示不传
  string status = 7;
  bool withPlatform = 8;
  repeated int32 ids = 9;
}


message SearchListPlatformProductResp {
  int32 data_count = 1; // 数据总数
  repeated PlatformProductInfoV2 list = 2; // 卡密列表
}

// 根据official_product_id获取平台商品最低价格
message GetPlatformProductMinPriceByOfficialProductIdReq {
  repeated int32 official_product_ids = 1;
  repeated Status status = 2; // 状态
  enum Status {
    status_disable = 0;
    status_enable = 1;
  }
}

message GetPlatformProductMinPriceByOfficialProductIdResp {
  map<int32, double> min_price_map = 1;
}