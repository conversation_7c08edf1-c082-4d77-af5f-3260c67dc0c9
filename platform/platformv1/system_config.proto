syntax = "proto3";

package platformservice.platformv1;
import "platform/platformv1/common.proto";
option go_package = "./platform/platformv1;platformv1";

service SystemConfig {
  // 获取系统参数
  rpc GetConfigs (ReqEmpty) returns (SystemConfigItem);
  // 配置系统参数
  rpc Save(SystemConfigItem) returns (RespEmpty);
}

message SystemConfigItem {
  string system_name = 1;
  string system_logo = 2;
  int32 login_session_valid = 3;
  string identifying_code = 4;
  string logo_type = 5;
  string login_sms_code = 6;
}
