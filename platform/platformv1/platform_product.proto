syntax = "proto3";

package platformservice.platformv1;
import "platform/platformv1/platform.proto";
import "platform/platformv1/common.proto";
option go_package = "./platform/platformv1;platformv1";

message ReqPlatformProductSubmitReport {
  int32 is_report_info = 1;
  int32 id = 2;
  repeated SocialCredit socialCredit = 3;
}
message RespPlatformProductSubmitReport {}


message RespPlatformProductGetReport {
  int32 id = 1;
  bool is_report_info = 2;

  repeated SocialCredit social_credit = 3;
}

message SocialCredit {
  int32 platform_product_id = 1 ;
  int32 social_credit_id = 2;
  string social_credit_name = 3;
  string social_credit_code = 4;
  string fv = 5;
  int32 quantity = 6;
}


message ReqPlatformProductGetReport {
  int32 id = 1;
}

message ReqUpdateSocialCredit{
  int32 platform_product_id = 1 ;
  int32 social_credit_id = 2;
  string social_credit_name = 3;
  string social_credit_code = 4;
  int32 quantity = 5;
}

message RespUpdateSocialCredit{

}

message PlatformProductInfoV2 {
  int32 id = 1;
  int32 platformId = 2;
  int32 officialProductId = 3;
  int32 weight = 4;
  string code = 5 ;
  string name = 6;
  float discount = 7;
  float price = 8;
  string extra = 9;
  repeated string accountType = 10;
  string describe = 11;
  int32 status = 12;
  int32 createTime = 13;
  int32 updateTime = 14;
  float rebate_price = 15;
  int32 exchange_type = 16;
  int32 is_report_info = 17;

  PlatformDetail platform = 18;
}

// 获取上游平台商品列表 入参
message GetPlatformProductListRequest {
  // 分页
  ReqPage page = 1;
  // 映射的官方平台商品id
  int32 official_product_id = 3;
  // 接口平台id
  int32 platform_id = 4;
  // 模糊查询字段
  string keyword = 5;
}

// 获取上游平台商品列表 出参
message GetPlatformProductListReply {
  repeated GetPlatformProductReply list = 1;
  RespPage page = 2; // 分页
}

message GetLogListPlatformProductRequest {
  //页码
  int32 num = 1;
  //每页数量
  int32 size = 2;
  // 平台商品ID
  int32 id = 3;
}

// 获取 上游平台商品 日志 入参
message GetLogListPlatformProductReply {
  int32 count = 1;
  repeated PlatformProductLog list = 2;
}

// 获取 上游平台商品 日志 出参
message PlatformProductLog {
  string msg = 1;
  int32 handler_id = 2;
  string handler_name = 3;
  string pp_id = 4;
  int32 create_time = 5;
  int32 update_time = 6;
  string server_id = 7;
  string date_time = 8;
  string data = 9;
}

// 修改平台商品数据 入参
message UpdatePlatformProductRequest{
  // 平台商品ID
  int32 id = 1;
  // 账号类型
  int32 account_type = 2;
  // 接口平台商品编号
  string code = 3;
  // 相对于官方商品的折扣
  float discount = 4;
  // 可用账号类型
  int32 exchange_type = 5;
  // 额外必要信息 json 格式的
  string extra = 6;
  // 商品名称
  string name = 7;
  // 映射的官方平台商品id
  int32 official_product_id = 8;
  // 映射的官方平台商品价格
  float official_product_price = 9;
  // 接口平台id
  int32 platform_id = 10;
  // 购买价格
  float price = 11;
  // 返利金额，默认值0
  float rebate_price = 12;
  // 上游商品充值限制 ，0不限制。1 自然月限制1次
  int32 recharge_limit = 13;
  // 修改原因
  string remark = 14;
  // 用户ID
  int32 user_id = 15;
  // 用户名称
  string user_name = 16;
  // 商品购买日限制
  int32 day_limit = 17;
}

// 修改平台商品返回数据
message UpdatePlatformProductReply{

}

// 创建平台商品数据 入参
message CreatePlatformProductRequest{
  // 接口平台id
  int32 platform_id = 1;
  // 购买价格
  float price = 2;
  // 映射的官方平台商品id
  int32 official_product_id = 3;
  // 映射的官方平台商品价格
  float official_product_price = 4;
  // 返利金额，默认值0
  float rebate_price = 5;
  // 商品名称
  string name = 6;
  // 接口平台商品编号
  string code = 7;
  // 相对于官方商品的折扣
  float discount = 8;
  // 可用账号类型
  int32 exchange_type = 9;
  // 可用账号类型
  int32 account_type = 10;
  // 额外必要信息 json 格式的
  string extra = 11;
  // 上游商品充值限制 ，0不限制。1 自然月限制1次
  int32 recharge_limit = 12;
  // 用户ID
  int32 user_id = 13;
  // 用户名称
  string user_name = 14;
  // 商品购买日限制
  int32 day_limit = 15;
}

// 创建平台商品返回数据
message CreatePlatformProductReply{

}

// 删除平台商品数据 入参
message DeletePlatformProductRequest{
  // 接口平台id
  int32 id = 1;
  // 用户ID
  int32 user_id = 13;
  // 用户名称
  string user_name = 14;
}

// 删除平台商品返回数据
message DeletePlatformProductReply{

}

// 修改上游平台商品状态入参
message UpdateStatusPlatformProductRequest {
  // 上游平台商品ID
  int32 id = 1;
  // 用户ID
  int32 user_id = 2;
  // 用户名称
  string user_name = 3;
  // 修改状态值
  int32 status = 4;
}

// 修改上游平台商品状态返回数据
message UpdateStatusPlatformProductReply{

}

// 获取上游平台商品状态入参
message GetPlatformProductRequest {
  // 上游平台商品ID
  int32 id = 1;
}

// 获取上游平台商品状态返回数据
message GetPlatformProductReply{
  int32 id = 1;
  int32 platformId = 2;
  int32 officialProductId = 3;
  int32 weight = 4;
  string code = 5 ;
  string name = 6;
  float discount = 7;
  double price = 8;
  string extra = 9;
  repeated string accountType = 10;
  string describe = 11;
  int32 status = 12;
  int32 createTime = 13;
  int32 updateTime = 14;
  float rebate_price = 15;
  int32 exchange_type = 16;
  int32 is_report_info = 17;
  int32 recharge_limit = 18;
  int32 origin_account_type = 19;
  string template = 20;
  int32 day_limit = 21;
}

//通过主键ID集合获取平台商品入参
message GetPlatformProductByIdsReq {
  repeated int32 ids = 1;
}
//通过主键ID集合获取平台商品返回数据
message GetPlatformProductByIdsResp {
  repeated GetPlatformProductByIdsInfo data = 1;
}

message GetPlatformProductByIdsInfo {
  int32 id = 1;
  int32 platform_id = 2;
  int32 official_product_id = 3;
  int32 weight = 4;
  string code = 5 ;
  string name = 6;
  float discount = 7;
  float price = 8;
  string extra = 9;
  int32 account_type = 10;
  string describe = 11;
  int32 status = 12;
  int32 create_time = 13;
  int32 update_time = 14;
  float rebate_price = 15;
  int32 exchange_type = 16;
  int32 is_report_info = 17;
  int32 recharge_limit = 18;
  int32 day_limit = 19;
}