syntax = "proto3";

package platformservice.platformv1;
import "platform/platformv1/common.proto";
option go_package = "./platform/platformv1;platformv1";

// 余额日志列表 入参
message SearchListPlatformBalanceRequest {
  ReqPage page = 1;
  int32 platform_id = 2;
  int32 type = 3;
  optional double money_start= 4;
  optional double money_end = 5;
  repeated string create_time = 6;
}

// 余额日志列表 出参
message SearchListPlatformBalanceReply {
  RespPage page = 1;
  repeated PlatformBalanceReply list = 2;
}

message PlatformBalanceReply {
  int32 id = 1;
  string create_time = 2;
  float current_balance = 3;
  int32 executor = 4;
  string executor_name = 5;
  int32 executor_type = 6;
  float money = 7;
  int32 operate = 8;
  string platform_id = 9;
  string platform_name = 10;
  string remark = 11;
  string sign = 12;
  int32 type = 13;
}

message SearchBalanceDayReq{
  string day = 1;
  ReqPage page = 2;
}

message SearchBalanceDayResp{
  int32 data_count = 1;
  repeated PlatformBalanceDayInfo list = 2;
}

message PlatformBalanceDayInfo{
  double balance = 1;
  int32 id = 2;
  string name = 3;
  string day = 4;
}