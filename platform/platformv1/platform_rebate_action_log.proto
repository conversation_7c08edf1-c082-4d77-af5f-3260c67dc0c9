syntax = "proto3";

package platformservice.platformv1;
option go_package = "./platform/platformv1;platformv1";
import "platform/platformv1/common.proto";

service PlatformRebateActionLog {
	// Create 创建接口平台配置信息
	rpc Create (CreatePlatformRebateActionLogReq) returns (CreatePlatformRebateActionLogResp);

	// SearchList 返佣提取记录列表
	rpc SearchList (SearchListPlatformRebateActionLogReq) returns (SearchListPlatformRebateActionLogResp);
}

message CreatePlatformRebateActionLogReq {
	// 平台id
	int32 platform_id = 1;
	// 操作人id
	int32 executor = 2;
	// 提佣金额
	float money = 3;
	// 提佣备注信息
	string remark = 4;
}

message CreatePlatformRebateActionLogResp {
	// 主键id
	int32 id = 1;
	// 上游接口id
	float platform_id = 2;
	// 提佣金额
	float money = 3;
	// 操作人id
	int32 executor = 4;
	// 发生时间
	int32 create_time = 5;
	// 提佣备注信息
	string remark = 6;
}

// 返佣提取记录列表搜索
message SearchListPlatformRebateActionLogReq {
	// 分页
	ReqPage page = 2;
	// 平台ID
	int32 platform_id = 1;
	// 搜索创建开始时间
	int32 start_create_time = 3;
	// 搜索创建结束时间
	int32 end_create_time = 4;
}

// 返佣提取记录列表响应
message SearchListPlatformRebateActionLogResp {
	repeated PlatformRebateActionLogInfoResp list = 1;
	RespPage page = 2; // 分页
}

message PlatformRebateActionLogInfoResp {
	// 主键id
	int32 id = 1;
	// 上游接口id
	int32 platform_id = 2;
	// 提佣金额
	float money = 3;
	// 操作人id
	int32 executor = 4;
	// 发生时间
	int32 create_time = 5;
	// 提佣备注信息
	string remark = 6;

	rebateActionPlatform rebate_action_platform = 7;

	message rebateActionPlatform {
		// 接口平台余额信息
		float balance = 1;
		// 余额预警，低于设置的价格开始发起通知
		float balance_warning = 2;
		// 商户号
		string business_id = 3;
		// 商户秘钥
		string business_key = 4;
		// 创建时间
		int32 create_time = 5;
		// 平台ID
		int32 id = 6;
		// 独立模式截至时间
		int32 independent_deadline = 7;
		// 是否为独立模式：0不是，1是
		int32 is_independent = 8;
		// 接口平台名称
		string name = 9;
		// 查询时间
		int32 search_time = 10;
		// 状态
		int32 status = 11;
		// 余额类型，1表示金额，2表示库存
		int32 stock_type = 12;
		// 接口平台标识
		string tag = 13;
		// 更新时间
		int32 update_time = 14;
		// 权重
		int32 weight = 15;
	}
}
