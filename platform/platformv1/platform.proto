syntax = "proto3";

package platformservice.platformv1;
import "validate/validate.proto";
import "platform/platformv1/common.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/duration.proto";
option go_package = "./platform/platformv1;platformv1";

// 平台(上游)
service Platform {
  // 获取所有的启用平台数据
  rpc GetPlatformEnableAll (GetPlatformEnableAllRequest) returns (GetPlatformEnableAllReply) {}

  // 搜索平台列表
  rpc SearchList (ReqPlatformSearchList) returns (RespPlatformSearchList) {}

  // 更新状态
  rpc UpdatePlatformStatus (UpdatePlatformStatusReq) returns (UpdatePlatformStatusResp) {}

  // 搜索平台列表
  rpc GetPlatformDetail (GetPlatformDetailReq) returns (GetPlatformDetailResp) {}

  // 根据 tag 获取平台详情
  rpc GetPlatformDetailByTag (GetPlatformDetailByTagReq) returns (GetPlatformDetailResp) {}

  // 根据id 获取用户 map 结构信息
  rpc GetMapsByIds(ReqPlatformGetMaps) returns (RespPlatformGetMaps) {}

  // 参数设置
  rpc UpdatePlatformInfo (UpdatePlatformInfoReq) returns (UpdatePlatformInfoResp);

  // 预警值设置
  rpc UpdatePlatformWarning (UpdatePlatformWarningReq) returns (UpdatePlatformWarningResp);

  //获取上游供货商商品信息列表
  rpc GetPlatformProductBatch (ReqPlatformProductBatch) returns (RespPlatformProductBatch){}

  //新增上游供货商商品信息
  rpc AddPlatformProduct (AddPlatformProductRequest) returns (AddPlatformProductResponse){}

  // 上游平台余额数据列表
  rpc BalanceList (BalanceListPlatformRequest) returns (BalanceListPlatformReply){}

  // 获取接口平台tag信息
  rpc GetPlatformTag (GetPlatformTagRequest) returns (GetPlatformTagReply){}

  // UpdateByCrontab
  rpc UpdateByCrontab (UpdateByCrontabReq) returns (UpdateByCrontabResp){}

  // FindPlatform
  rpc FindPlatform(FindPlatformReq) returns (FindPlatformResp){}

  // 设置平台信息
  rpc SetInfo (SetInfoReq) returns (SetInfoResp){}

  // 创建平台
  rpc Create (CreateReq) returns (CreateResp){}

  // 更新平台配置信息
  rpc UpdateAdapterConfig (UpdateAdapterConfigReq) returns (UpdateAdapterConfigResp){}
}


message SetInfoReq {
  int32 id = 1;
  string summary = 2;
  int32 aftermarket = 3;
}

message SetInfoResp {
  int32 count = 1;
}

message FindPlatformReq {
  repeated int32 id = 1;
  int32 stock_type = 2;
  string tag = 3;
}

message FindPlatformResp {
  repeated PlatformDetail data = 1;
}

// 获取接口平台tag信息 入参
message GetPlatformTagRequest {
  int32 platform_id = 1;
}

// 获取接口平台tag信息 出参
message GetPlatformTagReply {
  string tag = 1;
}

// 获取所有的启用平台数据 入参
message GetPlatformEnableAllRequest {
  string name = 1;
  int32 stock_type = 2;
}

// 获取所有的启用平台数据 出参
message GetPlatformEnableAllReply {
  repeated platformEnableInfo data = 1;
  message platformEnableInfo {
    int32 id = 1;
    string name = 2;
  }
}

message BalanceListPlatformRequest {
  int32 page = 1;
  int32 limit = 2;
  int32 platform_id = 3;
  string keyword = 4;
}


message BalanceListPlatformReply {
  // 数据量
  int32 data_count = 1;
  // 列表数据
  repeated BalancePlatformInfo list = 2;
}

message GetPlatformDetailReq {
  int32 id = 1;
}
message GetPlatformDetailResp {
  PlatformDetail data = 1;
}

message GetPlatformDetailByTagReq {
  string tag = 1;
}
message PlatformDetail {
  int32 id = 1;
  string tag = 2;
  string name = 3;
  string business_id = 4;
  string business_key = 5;
  int32 status = 6;
  int32 weight = 7;
  int64 create_time = 8;
  int64 update_time = 9;
  int64 search_time = 10;
  float balance = 11;
  float balance_warning = 12;
  int32 stock_type = 13;
  int32 is_independent = 14;
  int64 independent_deadline = 15;
  google.protobuf.Struct config = 16;
  CircuitBreaker circuit = 17;
  int32 dd_warning = 18;
  int32 independent_warning = 19;

}

message BalancePlatformInfo {
  int32 id = 1;
  string tag = 2;
  string name = 3;
  int32 status = 4;
  int32 weight = 5;
  int32 createTime = 6;
  int32 updateTime = 7;
  int32 searchTime = 8;
  float balance = 9;
  float balanceWarning = 10;
  int32 stockType = 11;
  int32 isIndependent = 12;
  int32 independentDeadline = 13;
  PlatformAttributeInfo platformAttribute = 15;
  string businessId = 16;
  string businessKey = 17;
}

message ReqPlatformGetMaps {
  repeated int32 ids = 1 [(validate.rules).repeated = {min_items:1, unique:true, items:{int32:{gt:0}}}];
}

message RespPlatformGetMaps {
  map<int32, RespPlatformInfo> maps = 1;
}

// 搜索平台列表请求
message ReqPlatformSearchList {
  // 关键字
  string keyword = 1;
  // 分页
  ReqPage page = 2;
  int32 stockType = 3;
  optional int32 status = 4;
  // 排序类型 0:默认排序 1:搜索时间倒序 2:创建时间倒序
  PlatformSortType sortType = 5;

  bool withCallLog = 6;
  bool withProduct = 7;
  bool withAttribute = 8;
}
enum PlatformSortType {
  PLATFORM_SORT_TYPE_NONE = 0;
  PLATFORM_SORT_TYPE_SEARCH_TIME = 1;
  PLATFORM_SORT_TYPE_CREATE_TIME = 2;
}


//搜索平台列表响应
message RespPlatformSearchList {
  int32 dataCount = 1;
  repeated RespPlatformInfo list = 2;
}

message RespPlatformInfo {
  int32 id = 1;
  string tag = 2;
  string name = 3;
  int32 status = 4;
  int32 weight = 5;
  int32 createTime = 6;
  int32 updateTime = 7;
  int32 searchTime = 8;
  float balance = 9;
  float balanceWarning = 10;
  StockType stockType = 11;
  int32 isIndependent = 12;
  int32 independentDeadline = 13;

  string businessId = 14;
  string businessKey = 15;


  repeated PlatformProductInfo products = 16;
  PlatformAttributeInfo platform_attribute = 17;
  repeated PlatformCallLog call_logs = 18;
  string summary = 19;
  int32 dd_warning = 20;
  int32 independent_warning = 21;

  google.protobuf.Struct config = 22;
  CircuitBreaker circuit = 23;
  int32 aftermarket = 24;

  //余额类型，，
  enum StockType {
    TYPE_UNKNOWN = 0; //0
    TYPE_BALANCE = 1; //1表示金额
    TYPE_NUM = 2; //2表示库存
  }
}

message PlatformCallLog {
  int32 id = 1;
  int32 platform_product_id = 2;
  string order_direct_serial_number = 3;
  int32 platform_id = 4;
  string method_name = 5;
  string data = 6;
  int32 status = 7;
  int32 create_time = 8;
}

message PlatformProductInfo {
  int32 Id = 1;
  int32 platformId = 2;
  int32 officialProductId = 3;
  int32 weight = 4;
  string code = 5 ;
  string name = 6;
  float discount = 7;
  float price = 8;
  string extra = 9;
  repeated string accountType = 10;
  string describe = 11;
  int32 status = 12;
  int32 createTime = 13;
  int32 updateTime = 14;
  float rebate_price = 15;
  int32 exchange_type = 16;
  int32 is_report_info = 17;
  int32 recharge_limit = 18;
}

message PlatformAttributeInfo {
  uint32 id = 1;
  float balance = 2; // 接口平台余额信息
  float balance_warning = 3; // 余额预警，低于设置的价格开始发起通知
  float rebate = 4; // 返利金额
  int32 create_time = 5; // 创建时间
  int32 update_time = 6; // 更新时间
  string warn_device = 7; // 余额预警设备，可以设置多个手机号
  int32 difference = 8; // 允许的差额
}

message UpdatePlatformStatusReq {
  int32 id = 1;
}
message UpdatePlatformStatusResp {}

message UpdatePlatformInfoReq {
  int32 id = 1;
  string name = 2;
  int32 stock_type = 3;
  int32 is_independent = 4;
  int64 independent_deadline = 5;
  int32 dd_warning = 6;
  int32 independent_warning = 7;
}
message UpdatePlatformInfoResp{
  int32 num = 1;
}

message UpdatePlatformWarningReq {
  int32 id = 1;
  int32 dd_warning = 2;
  int32 independent_warning = 3;
}
message UpdatePlatformWarningResp{
  int32 num = 1;
}

message PlatformInfo {
  int32 id = 1;
  string tag = 2;
  string name = 3;
  string business_id = 4;
  string business_key = 5;
  int32 status = 6;
  int32 weight = 7;
  int32 create_time = 8;
  int32 update_time = 9;
  int32 search_time = 10;
  float balance = 11;
  float balance_warning = 12;
  int32 stock_type = 13;
  int32 is_independent = 14;
  int64 independent_deadline = 15;
}

message OfficialInfo {
  int32 id = 1;
  string name = 2;
  string describe = 3;
  int32 num = 4;
  int32 status = 5;
  string web_url = 6;
  string recharge_url = 7;
  string create_time = 8;
  string update_time = 9;
  int32 type = 10;
  string tag = 11;
}

message OfficialProduct {
  int32 id = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  string price = 6;
  int32 status = 7;
  string create_time = 8;
  string update_time = 9;
  int32 type = 10;
  int32 daies = 11;
  OfficialInfo official = 12;
}



message RespPlatformProductBatch {
  int32 dataCount = 1;
  repeated PlatformProduct platformProducts = 2;

  message PlatformProduct {
    int32 id = 1;
    int32 platform_id = 2;
    int32 official_product_id = 3;
    int32 weight = 4;
    string code = 5;
    string name = 6;
    float discount = 7;
    float price = 8;
    string extra = 9;
    int32 account_type = 10;
    string describe = 11;
    int32 status = 12;
    int32 create_time = 13;
    int32 update_time = 14;
    float rebate_price = 15;
    int32 exchange_type = 16;
    int32 is_report_info = 17;
    int32 call_logs_count = 18;
    PlatformInfo platform = 19;
    OfficialProduct official_product = 20;
  }
}

message ReqPlatformProductBatch {
  int32 official_id = 1;
  string  keyword = 2;
  int32 limit = 3;
  int32 official_product_id = 4;
  int32 page = 5;
  int32 platform_id = 6;
  float price = 7;
}

message AddPlatformProductRequest {
  int32 platform_id = 1; // 接口平台id
  int32 official_product_id = 2; // 映射的官方平台商品id
  string code = 3; // 接口平台商品编号
  string name = 4; // 商品名称
  float discount = 5; // 相对于官方商品的折扣
  float price = 6; // 购买价格
  string extra = 7; // 额外必要信息
  uint32 account_type = 8; // 可用账号类型
  string describe = 9; // 描述
  int32 status = 10; // 状态
  float rebate_price = 11; // 返利金额，默认值0
  uint32 exchange_type = 12; // 商品兑换类型，1直冲 2卡密转直冲
  int32 recharge_limit = 13; // 上游商品充值限制 ，0不限制。1 自然月限制1次
  int32 is_report_info = 14; // 是否上报主体信息：0不上报，1上报
  int32 day_limit = 15; // 商品购买日限制
}

message AddPlatformProductResponse {
  bool result = 1;
}

message UpdateByCrontabReq{
  int32 user_id = 1;
  string realname = 2;
  string exec_event = 3;
}
message UpdateByCrontabResp{}


message CreateReq {
  string tag = 1;
  string name = 2;
  string adapter = 3;
  int32 aftermarket = 4;
}

message CreateResp {}

message CircuitBreaker {
  bool close = 1;
  float success = 2;
  int64 request = 3;
  int32 bucket = 4;
  google.protobuf.Duration window = 5;
}

message UpdateAdapterConfigReq {
  int32 id = 1;
  google.protobuf.Struct config = 2;
  CircuitBreaker circuit = 3;
}

message UpdateAdapterConfigResp {}
