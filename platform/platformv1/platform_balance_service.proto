syntax = "proto3";

package platformservice.platformv1;
import "platform/platformv1/platform_balance.proto";
option go_package = "./platform/platformv1;platformv1";

service PlatformBalance {
	// SearchListPlatformBalance 余额日志列表
	rpc SearchListPlatformBalance (SearchListPlatformBalanceRequest) returns (SearchListPlatformBalanceReply);

	// 平台日志指定余额
	rpc SearchBalanceDay(SearchBalanceDayReq) returns (SearchBalanceDayResp) {}
}