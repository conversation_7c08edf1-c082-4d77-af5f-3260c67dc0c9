# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ""
    version: 0.0.1
paths:
    /admin/v1/login:
        get:
            tags:
                - Login
            description: 登录
            operationId: Login_Login
            parameters:
                - name: account
                  in: query
                  schema:
                    type: string
                - name: password
                  in: query
                  schema:
                    type: string
                - name: verificationCode
                  in: query
                  schema:
                    type: string
                - name: verificationToken
                  in: query
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/cardMall.admin.v1.LoginRsp'
    /helloworld/{name}:
        get:
            tags:
                - Greeter
            description: Sends a greeting
            operationId: Greeter_SayHello
            parameters:
                - name: name
                  in: path
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/helloworld.v1.HelloReply'
components:
    schemas:
        cardMall.admin.v1.LoginRsp:
            type: object
            properties:
                adminId:
                    type: integer
                    format: int32
                name:
                    type: string
                token:
                    type: string
        helloworld.v1.HelloReply:
            type: object
            properties:
                message:
                    type: string
            description: The response message containing the greetings
tags:
    - name: Greeter
      description: The greeting service definition.
    - name: Login
