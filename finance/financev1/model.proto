syntax = "proto3";

package finance.api.inner;

import "finance/financev1/enum.proto";
import "buf/validate/validate.proto";

option go_package = "./finance/financev1;financev1";

// 用于下拉框数据
message SelectOption {
  string label = 1;
  int32 value = 2;
  bool disabled = 3;
}

// 用于下拉框数据
message SelectOptionWithCode {
  string label = 1;
  int32 value = 2;
  bool disabled = 3;
  string code = 4;
}

message Empty {}

// 发票项目明细
message InvoiceProjectItem {
  // 编号
  int32 id = 1;
  // 发票项目名称
  string name = 2;
  // 发票税率(整数， 例如6.12% = 612), 最大值100% = 10000
  int32 taxRate = 3;
  // 创建时间
  string createdAt = 4;
  // 更新时间
  string updatedAt = 5;
  // 税收分类编码
  string code = 6;
  // 项目主体
  string subject = 7;
}

// 对账单明细
message ReconciliationItem {
  // 编号
  int32 id = 1;
  // 下游商户ID
  int64 resellerId = 2;
  // 下游商户名称
  string resellerName = 3;
  // 期初
  string startTime = 4;
  // 期末
  string endTime = 5;
  // 账单文件名称
  string fileName = 6;
  // 账单文件地址
  string fileUrl = 7;
  // 账单文件大小
  int64 fileSize = 8;
  // 账单状态
  EnumItem status = 9;
  // 账单类型
  repeated EnumItem categories = 10;
  // 账单创建时间
  string createdAt = 11;
  // 账单更新时间
  string updatedAt = 12;
  // 对账单类型
  EnumItem reconciliationType = 13;
}

message InvoiceIssueItem {
  int32 id = 1;
  // 下游商户ID
  int32 resellerId = 2;
  // 发票抬头
  string title = 3;
  // 纳税人识别号
  string taxpayerNumber = 4;
  // 公司地址
  string address = 5;
  // 公司电话
  string mobile = 6;
  // 开户行
  string bank = 7;
  // 银行账户
  string account = 8;
}

message InvoiceReceiptItem {
  int32 id = 1;
  // 下游商户ID
  int32 resellerId = 2;
  // 开票性质 1电子 2纸质
  EnumItem property = 3;
  // 收票人姓名
  string userName = 4;
  // 收票人电话
  string mobile = 5;
  // 收货邮箱
  string email = 6;
  // 收货地址
  string address = 7;
}

// 发票项目金额
message InvoiceProjectWithAmount {
  int32 id = 1;
  string name = 2;
  int32 taxRate = 3;
  double amount = 4;
  string code = 5;
}

// 发票明细
message InvoiceItem {
  int32 id = 1;
  string createdAt = 2;
  // 抬头
  string title = 3;
  // 下游商户
  ResellerItem reseller = 4;
  // 申请编号
  string invoiceNumber = 5;
  // 发票状态
  EnumItem status = 6;
  // 发票类型
  EnumItem type = 7;
  // 发票性质
  EnumItem property = 8;
  // 开票金额
  double amount = 9;
  // 税额
  double taxAmount = 10;
  // 备注
  string remark = 11;
  // 开票商品
  repeated string products = 12;
  // 驳回原因
  string reason = 13;
  // 纳税人识别号
  string taxpayerNumber = 14;
  // 开票方主体
  string subject = 15;
  // 钉钉审批id
  string approveNo = 16;
}

message ResellerItem {
  int32 id = 1;
  // 下游商户名称
  string name = 2;
  // 全称
  string fullName = 3;
  // 联系人
  string contacts = 4;
  // 主体
  string subject = 5;
  // 商户号
  string accountTag = 6;
}

message InvoiceMoneyLogItem {
  int32 id = 1;
  // 创建时间
  string createdAt = 2;
  // 变化金额
  double amount = 3;
  // 下游id
  int32 resellerId = 4;
  // 变动类型
  EnumItem type = 5;
  // 操作类型
  EnumItem operation = 6;
  // 变动前金额
  double beforeAmount = 7;
  // 变动后金额
  double afterAmount = 8;
  // 备注
  string remark = 9;
  // 关联单号
  string invoiceNumber = 10;
  // 下游分销商全名
  string resellerName = 11;
  // 变动后冻结金额
  double afterFreezeAmount = 12;
  // 类型 1结算单 2发票
  EnumItem numberType = 13;
  // 关联id
  int32 numberId = 14;
}

message SettlementItem {
  int32 id = 1;
  // 结算单编号
  string number = 2;
  // 结算单周期开始时间
  string startAt = 3;
  // 结算单周期结束时间
  string endAt = 4;
  // 备注
  string remark = 5;
  // 创建时间
  string createdAt = 6;
  // 状态  1待确认 2已确认 3已驳回 4已作废 5待审核 6审核中 7结算中
  EnumItem status = 7;
  // 结算金额
  double amount = 8;
  // 主体
  string subject = 9;
  // 驳回原因
  string reason = 10;
  repeated SettlementInfoItem items = 11;
  // 下游商户
  ResellerItem reseller = 12;
  string approveNo = 13;
}

// 结算单明细
message SettlementInfoItem {
  int32 id = 1;
  string createdAt = 2;
  string updatedAt = 3;
  // 商品编码
  string productCode = 4;
  // 商品名称
  string productName = 5;
  // 来源系统说明
  string fromSystemDesc = 6;
  // 单价
  double unitPrice = 7;
  // 数量
  int32 num = 8;
  // 面值
  string faceValue = 9;
  // 来源系统
  int32 fromSystem = 10;
  // 总金额
  double totalPrice = 11;
}

message TaxpayerNumber {
  // 公司名
  string name = 1;
  // 纳税人识别号
  string taxpayerNumber = 2;
}

message BankItem {
  // 银行编号
  string bnknbr = 1;
  // 联行号
  string brdnbr = 2;
  // 联行类型
  string brdtyp = 3;
  // 城市代码
  string ctycod = 4;
  // 内部编号
  string innnbr = 5;
  // 更新日期
  string upddat = 6;
  // 银行名称
  string whlnam = 7;
}

message PaymentOrderDetailInfoItem {
  // 金额
  double money = 1 [(buf.validate.field).cel = {
    message: "金额必须大于0"
    expression: "this > 0"
  }];
  // 备注
  string remark = 2 [(buf.validate.field).cel = {
    message: "备注最多支持30个字符"
    expression: "this.size() <= 30"
  }];
}

message PaymentOrderItem {
  // 编号
  uint32 id = 1;
  // 创建时间
  string createdAt = 2;
  // 更新时间
  string updatedAt = 3;
  // 完成时间
  string finishAt = 4;
  // 备注
  string remark = 5;
  // 状态
  EnumItem status = 6;
  // 供应商ID
  uint32 supplierId = 7;
  // 供应商名称
  string supplierName = 8;
  // 金额
  string amount = 9;
  // 主体ID
  uint32 subjectId = 10;
  // 主体名称
  string subjectName = 11;
  // 记账账户
  string creditedAccount = 12;
  // 主体账户
  string subjectAccount = 13;
  // 审批编号
  string approvalNo = 14;
  // 审批备注
  string approvalRemark = 15;
  // 用户ID
  uint32 userId = 16;
  // 用户名
  string username = 17;
  // 明细
  repeated PaymentOrderDetailInfoItem items = 18;
  // 标签
  uint32 tag = 19;
  // 记账账户银行明细
  BankItem creditedBank = 20;
  // 支付账号银行明细
  BankItem subjectBank = 21;
  // 用途
  string purpose = 22;
  // 摘要
  string summary = 23;
  // 回单状态
  EnumItem receiptStatus = 24;
}

message SubjectBankItem {
  // 账户类型
  EnumItem category = 1;
  // 状态
  EnumItem status = 2;
  // 银行名称
  string bankName = 3;
  // 银行账号
  string bankAccount = 4;
  // 银行开户名
  string accountName = 5;
  // 备注
  string remark = 6;
  // ID
  uint32 id = 7;
  // 开户行
  string bankAddress = 8;
  BankItem bank = 9;
}

message SupplierItem {
  uint32 id = 1;
  string name = 2;
  int32 status = 3;
  // 余额
  string balance = 4;
  // 额度
  double limitAmount = 5;
}

message SupplierBankItem {
  // 银行名称
  string bankName = 3;
  // 银行账号
  string bankAccount = 4;
  // 银行开户名
  string accountName = 5;
  // 备注
  string remark = 6;
  // ID
  uint32 id = 7;
  // 开户行
  string bankAddress = 8;
  BankItem bank = 9;
}

message SupplierTreeItem {
  uint32 value   = 1;
  string title = 2;
  bool disabled = 3;
  repeated SupplierTreeItem children = 4;
  string key = 5;
  int32 tag = 6;
}

message SupplierSelectItem {
  uint32 value   = 1;
  string label = 2;
  bool disabled = 3;
  int32 tag = 4;
}

message SupplierSettlementItem {
  int32 id = 1;
  // 结算单编号
  string number = 2;
  // 结算单周期开始时间
  string startAt = 3;
  // 结算单周期结束时间
  string endAt = 4;
  // 备注
  string remark = 5;
  // 创建时间
  string createdAt = 6;
  // 状态  1待确认 2已确认 3已驳回 4已作废 5待审核 6审核中 7结算中
  EnumItem status = 7;
  // 结算金额
  double amount = 8;
  string approveNo = 9;
  // 驳回原因
  string reason = 10;
  repeated SupplierSettlementInfoRespItem items = 11;
  string supplierName = 12;
  string fromSystem = 13;
}

message SupplierSettlementInfoRespItem {
  int32   id = 1;
  string  productName = 2;
  double  unitPrice = 3;
  int32   num = 4;
  string  faceValue = 5;
  double  totalPrice = 6;
  int32  fromSystem = 7;
  int32  productId = 8;
  string fromSystemDesc = 9;
}

message SupplierSettlementInfoItem {
  int32   id = 1;
  string  productName = 2;
  double  unitPrice = 3;
  int32   num = 4;
  double  faceValue = 5;
  double  totalPrice = 6;
  int32  fromSystem = 7;
  string fromSystemDesc = 9;
  int32 productId = 8;
}