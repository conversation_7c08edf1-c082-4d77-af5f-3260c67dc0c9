syntax = "proto3";

package finance.api.inner;

import "buf/validate/validate.proto";

option go_package = "./finance/financev1;financev1";

// 公共响应参数
message Response {
  // 错误码, 200或0为成功
  int32 code = 1;
  // 错误信息
  string message = 2;
  // 错误原因
  string reason = 3;
  // 元数据
  map<string, string> metadata = 4;
}

// 分页请求参数
message PageReq {
  // 页码
  int32 page = 1;
  // 每页大小
  int32 pageSize = 2;

  option (buf.validate.message).cel = {
    id: "PageReq.page",
    message: "页码必须大于0",
    expression: "this.page > 0"
  };

  option (buf.validate.message).cel = {
    id: "PageReq.pageSize",
    message: "分页数量必须大于0",
    expression: "this.pageSize > 0"
  };
}

// 分页响应参数
message PageReply {
  // 总数
  int32 total = 1;
  // 页码
  int32 page = 2;
  // 每页大小
  int32 pageSize = 3;
}

// 时间范围查询
message TimeRangeReq {
  // 开始时间 unix 时间戳
  int64 startAt = 1;
  // 结束时间 unix 时间戳
  int64 endAt = 2;
}
