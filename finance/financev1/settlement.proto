syntax = "proto3";

package finance.api.inner;

import "buf/validate/validate.proto";
import "finance/financev1/model.proto";
import "finance/financev1/enum.proto";
import "finance/financev1/global.proto";

option go_package = "./finance/financev1;financev1";

service Settlement {
  rpc Create(CreateSettlementRequest) returns (CreateSettlementReply) {}

  rpc Apply(CreateSettlementRequest) returns (Empty);

  // 修改结算单
  rpc UpdateInfo(UpdateSettlementInfoRequest) returns (UpdateSettlementInfoReply){}

  rpc List(ListSettlementRequest) returns (ListSettlementReply) {}

  rpc Detail(SettlementIdRequest) returns (SettlementItem){}

  rpc Cancel(SettlementIdRequest) returns (Empty) {}

  // 提交审核
  rpc SubmitApprove(SettlementIdRequest) returns (Empty) {}

  // 驳回
  rpc Rejected(RejectedRequest) returns (Empty) {}

  rpc Download(DownloadRequest) returns (DownloadReply) {}

  // 确认
  rpc Confirm(SettlementIdRequest) returns (Empty) {}

  rpc RefreshStatus(SettlementIdRequest) returns (Empty) {}

  // 创建上游结算单
  rpc SupplierApply(CreateSupplierSettlementRequest) returns (Empty) {}

  // 上游结算单列表
  rpc SupplierList(ListSupplierSettlementRequest) returns (ListSupplierSettlementReply) {}

  // 上游结算单详情
  rpc SupplierDetail(IdRequest) returns (SupplierSettlementItem) {}

  // 修改上游结算单
  rpc UpdateSupplierSettlementInfo(UpdateSupplierSettlementInfoRequest) returns (UpdateSettlementInfoReply) {}

  // 修改上游结算单状态
  rpc UpdateSupplierSettlementStatus(UpdateSupplierSettlementStatusRequest) returns (Empty) {}

  // 下载上游结算单
  rpc SupplierDownload(SupplierDownloadRequest) returns (DownloadReply) {}

  // 下载下游结算单
  rpc ResellerDownload(ResellerDownloadRequest) returns (DownloadReply) {}
}

message SupplierDownloadRequest {
  uint32 id = 1;
}

message ResellerDownloadRequest {
  uint32 id = 1;
}

message UpdateSupplierSettlementStatusRequest {
  // 上游结算单id
  uint32 id = 1;
  // 驳回原因
  string reason = 3;
  SettlementStatus status = 4 [(buf.validate.field).required = true,(buf.validate.field).enum.defined_only = true];
}

message RejectedRequest {
  uint32 id = 1;
  int32 resellerId = 2;
  string reason = 3 [(buf.validate.field).required = true];
}

message DownloadRequest {
  int32 id = 1;
  int32 resellerId = 2;
}

message DownloadReply {
  string file = 1;
  string fileName = 2;
}

message SettlementIdRequest {
  uint32 id = 1;
  repeated uint32 ids = 2;
  int32 resellerId = 3;
  int32 from = 4;
}

message IdRequest {
  uint32 id = 1;
}

message ListSettlementRequest {
  PageReq page = 1 [(buf.validate.field).required = true];
  // 下游商户ID
  int64 resellerId = 2;
  // 账单创建时间
  repeated string createdAt = 3;
  // 金额区间
  repeated double amountRange = 4;
  // 1 财务系统 2商户中心
  int32 from = 5;
  // 结算周期
  repeated string startAt = 6;
  // 0 全部 1待确认 2已确认 3已驳回 4已作废 5待审核 6审核中 7结算中
  SettlementStatus status = 7;
}

message ListSettlementReply {
  PageReply page = 1;
  repeated SettlementItem list = 2;
}

message UpdateSettlementInfoRequest {
  int32 id = 1 [(buf.validate.field).required = true];
  repeated SettlementInfoItem items = 2;
  // 0 全部 1待确认 2已确认 3已驳回 4已作废 5待审核 6审核中 7结算中
  SettlementStatus status = 3;
}

message UpdateSettlementInfoReply{}

message CreateSettlementRequest {
  int32 resellerId = 1 [(buf.validate.field).required = true];
  string startAt = 2 [(buf.validate.field).required = true];
  string endAt = 3 [(buf.validate.field).required = true];
  string remark = 4;
}

message CreateSettlementReply {}

message CreateSupplierSettlementRequest {
  int32 supplierId = 1 [(buf.validate.field).required = true];
  // 开始日期
  string startAt = 2 [(buf.validate.field).required = true];
  // 结束日期
  string endAt = 3 [(buf.validate.field).required = true];
  string remark = 4;
  OrderType tag = 5[(buf.validate.field).required = true,(buf.validate.field).enum.defined_only = true];
}

message ListSupplierSettlementRequest {
  PageReq page = 1 [(buf.validate.field).required = true];
  // 上游商户ID
  int64 supplierId = 2;
  // 账单创建时间
  repeated string createdAt = 3;
  // 金额区间
  repeated double amountRange = 4;
  // 1 财务系统 2商户中心
  int32 from = 5;
  // 结算周期
  repeated string startAt = 6;
  // 0 全部 1待确认 2已确认 3已驳回 4已作废 5待审核 6审核中 7结算中
  SettlementStatus status = 7;
  SettlementFromSystem tag = 8;
}

message ListSupplierSettlementReply {
  PageReply page = 1;
  repeated SupplierSettlementItem list = 2;
}

message UpdateSupplierSettlementInfoRequest {
  int32 id = 1 [(buf.validate.field).required = true];
  repeated SupplierSettlementInfoItem items = 2;
  // 0 全部 1待确认 2已确认 3已驳回 4已作废 5待审核 6审核中 7结算中
  SettlementStatus status = 3;
}