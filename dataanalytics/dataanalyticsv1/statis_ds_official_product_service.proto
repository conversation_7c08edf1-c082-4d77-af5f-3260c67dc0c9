syntax = "proto3";

package dataanalytics.dataanalyticsv1;

option go_package = "./dataanalytics/dataanalyticsv1;dataanalyticsv1";

//电商销量同比分析
service StatisDsOfficialProductService{
  rpc GetStatisDsOfficialProductSum(GetStatisDsOfficialProductSumRequest) returns (GetStatisDsOfficialProductSumResponse);
  rpc GetStatisDsOfficialProductList(GetStatisDsOfficialProductListRequest) returns (GetStatisDsOfficialProductListResponse);
  //rpc GetStatisDsOfficialProductPlatform(GetStatisDsOfficialProductListDayRequest) returns (GetStatisDsOfficialProductListDayResponse);
}

message GetStatisDsOfficialProductListRequest{
  repeated string ct = 1;
  repeated string ho = 2;
  int32 page = 5;
  int32 limit = 6;
  int32 aggregation_type = 7;
  int32 official_product_id = 8;
  int32 official_id = 9;
}

message GetStatisDsOfficialProductListResponse{
  repeated StatisDsOfficialProductListResponse list = 1;
  int32 data_count = 2;
}

message StatisDsOfficialProductListResponse {
  string official_product_name = 1;
  double face_value = 2;
  int32 current_num = 3;
  double current_amount  = 4;
  int32 history_num = 5;
  double history_amount  = 6;
  int32 history_num_diff = 7;
  int32 platform_id = 8;
  string platform_name = 9;
  int32 official_product_id = 10;
  double platform_price = 11;
}


message GetStatisDsOfficialProductSumRequest {
  repeated string ct = 1;
  int32 platform_id = 2;
  int32 page = 5;
  int32 limit = 6;
}

message GetStatisDsOfficialProductSumResponse {
  repeated GetStatisDsOfficialProductSum ds_official_product_sum = 1;
  int32 data_count = 2;
}

message GetStatisDsOfficialProductSum {
  int32 platform_product_id = 1;
  string platform_product_name = 2;
  int32 current_num = 3;
  int32 history_one_num = 4;
  int32 history_two_num = 5;
  int32 history_one_diff = 6;
  int32 history_two_diff = 7;
  float face_value = 8;
  double platform_price = 9;
}