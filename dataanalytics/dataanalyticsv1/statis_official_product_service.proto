syntax = "proto3";

package dataanalytics.dataanalyticsv1;

option go_package = "./dataanalytics/dataanalyticsv1;dataanalyticsv1";

//销量同比分析
service StatisOfficialProductService {
  rpc GetStatisOfficialProductSum(GetStatisOfficialProductSumRequest) returns (GetStatisOfficialProductSumResponse);
  rpc GetStatisOfficialProductSumDecline(GetStatisOfficialProductSumRequest) returns (GetStatisOfficialProductSumDeclineResponse);
}


message GetStatisOfficialProductSumRequest {
  repeated string ct = 1;
  int32 downward_value = 4;
  int32 page = 5;
  int32 limit = 6;
  repeated int32 official_product_id = 7;
}

message GetStatisOfficialProductSumResponse {
  repeated GetStatisOfficialProductSum official_product_sum = 1;
  int32 data_count = 2;
}

message GetStatisOfficialProductSum {
  int32 official_product_id = 1;
  string official_product_name = 2;
  int32 current_num = 3;
  int32 history_one_num = 4;
  int32 history_two_num = 5;
  int32 history_one_diff = 6;
  int32 history_two_diff = 7;
}

message GetStatisOfficialProductSumDeclineResponse {
  repeated GetStatisOfficialProductSumDecline official_product_sum_decline = 1;
  int32 data_count = 2;
}

message GetStatisOfficialProductSumDecline {
  int32 reseller_id = 1;
  int32 official_product_id = 2;
  string official_product_name = 3;
  string reseller_name = 4;
  int32 current_num = 5;
  int32 history_one_num = 6;
  int32 history_two_num = 7;
  int32 history_one_diff = 8;
  int32 history_two_diff = 9;
}