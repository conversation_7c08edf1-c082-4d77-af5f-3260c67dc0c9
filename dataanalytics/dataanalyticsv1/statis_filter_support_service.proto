syntax = "proto3";

package dataanalytics.dataanalyticsv1;

option go_package = "./dataanalytics/dataanalyticsv1;dataanalyticsv1";

service StatisFilterSupportService {
  rpc GetStatisFilterReseller(GetStatisFilterResellerRequest) returns (GetStatisFilterResellerResponse);
 // rpc GetStatisFilterOfficialProduct(GetStatisFilterOfficialProductRequest) returns (GetStatisFilterOfficialProductResponse);
 // rpc GetStatisFilterPlatformProduct(GetStatisFilterPlatformProductRequest) returns (GetStatisFilterPlatformProductResponse);
  rpc GetStatisFilterPlatform(GetStatisFilterPlatformRequest) returns (GetStatisFilterPlatformResponse);
  rpc GetStatisFilterOursProduct(GetStatisFilterOursProductRequest) returns (GetStatisFilterOursProductResponse);

  rpc GetStatisFilterOfficial(GetStatisFilterOfficialRequest) returns (GetStatisFilterOfficialResponse);

  rpc GetStatisFilterOfficialProduct(GetStatisFilterOfficialProductRequest) returns (GetStatisFilterOfficialProductResponse);
}

// 分销商
message GetStatisFilterResellerRequest {
  int32 reseller_id = 1;
  int32 aftermarket = 2;
}
message GetStatisFilterResellerResponse {
  repeated StatisFilterResellerResponse list = 1;
}

message StatisFilterResellerResponse {
  int32 reseller_id = 1;
  string reseller_name = 2;
}

// 平台
message GetStatisFilterPlatformRequest {
  int32 platform_id = 1;
}
message GetStatisFilterPlatformResponse {
  repeated StatisFilterPlatformResponse list = 1;
}
message StatisFilterPlatformResponse {
  int32 platform_id = 1;
  string platform_name = 2;
}

// 我们的商品
message GetStatisFilterOursProductRequest {
  int32 ours_product_id = 1;
}
message GetStatisFilterOursProductResponse {
  repeated StatisFilterOursProductResponse list = 1;
}

message StatisFilterOursProductResponse {
  int32 ours_product_id = 1;
  string ours_product_name = 2;
}

// 品牌
message GetStatisFilterOfficialRequest {
  int32 official_id = 1;
}

message GetStatisFilterOfficialResponse {
  repeated StatisFilterOfficialResponse list = 1;
}

message StatisFilterOfficialResponse {
  int32 official_id = 1;
  string official_name = 2;
}

message GetStatisFilterOfficialProductRequest {
  int32 official_product_id = 1;
}
message GetStatisFilterOfficialProductResponse {
  repeated StatisFilterOfficialProductResponse list = 1;
}
message StatisFilterOfficialProductResponse {
  int32 official_product_id = 1;
  string official_product_name = 2;
}