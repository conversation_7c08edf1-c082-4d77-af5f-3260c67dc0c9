syntax = "proto3";
package tradeservice.tradev1;
import "trade/tradev1/order.proto";
option go_package = "./trade/tradev1;tradev1";

// 充值相关服务
service Direct {
  // 手动处理的订单
  rpc DirectOrder (DirectOrderReq) returns (DirectOrderResp) {};
  // 订单充值队列日志
  rpc DirectLog (DirectLogReq) returns (DirectLogResp) {};
  // 订单充值队列
  rpc DirectList (DirectListReq) returns (DirectListResp) {};
  // 订单充值导出
  rpc DirectExport (DirectListReq) returns (DirectExportResp) {};
  // 历史订单充值列表
  rpc DirectHistoryList (DirectHistoryListReq) returns (DirectHistoryListResp) {};
  // 历史订单充值导出
  rpc DirectHistoryExport (DirectHistoryExportReq) returns (DirectHistoryExportResp) {};
  // 订单充值队列
  rpc DirectAll(DirectListReq) returns (DirectAllResp){};
  // 上游通知列表
  rpc DirectNotifyList (DirectNotifyListReq) returns (DirectNotifyListResp) {};
  // 将充值信息手动修改为成功
  rpc DirectMarkSuccess (DirectMarkSuccessReq) returns (DirectMarkSuccessResp) {};
  // 关闭充值
  rpc DirectClose (DirectCloseReq) returns (DirectCloseResp) {};
  // 查询充值队列状态
  rpc DirectQuery(DirectQueryReq) returns (DirectQueryResp) {};
  // 充值账户充值
  rpc DirectRetry(DirectRetryReq) returns (DirectRetryResp) {};
  // 批量重试
  rpc BatchRetry(BatchRetryReq) returns (BatchRetryResp) {};
  // 失败消息重试
  rpc FailMsgRetry(FailMsgRetryReq) returns (FailMsgRetryResp) {};
}

// 批量重试请求
message BatchRetryReq {
  // 交易流水号
  repeated string serial_number = 1;
  // 重试类型
  string type = 2;
  // 备注
  string remark = 3;
  // 处理人id
  int32 handle_id = 4;
  // 处理人名称
  string handle_name = 5;
  // 是否是特权用户
  bool privileged = 6;
  // 是否为运营角色
  bool operator_role = 7;
}

// 批量重试响应
message BatchRetryResp {
  int32 success = 1;
  int32 error = 2;
  map<string, string> err_msg = 3;
}

// 失败消息重试请求
message FailMsgRetryReq {
  int32 id = 1;
}

// 失败消息重试响应
message FailMsgRetryResp {

}

// 重试账户充值请求
message DirectRetryReq {
  // 交易流水号
  string serial_number = 1;
  // 重试类型
  string type = 2;
  // 备注
  string remark = 3;
  // 处理人id
  int32 handle_id = 4;
  // 处理人名称
  string handle_name = 5;
  // 是否是特权用户
  bool privileged = 6;
  // 是否为运营角色
  bool operator_role = 7;
}

// 重试账户充值响应
message DirectRetryResp {

}

// 查询充值队列状态请求
message DirectQueryReq {
  // 交易流水号
  string serial_number = 1;
  // 处理人id
  int32 handle_id = 2;
  // 处理人名称
  string handle_name = 3;
}

// 查询充值队列状态响应
message DirectQueryResp {
  int32 status = 1;
  string mes = 2;
}

// 关闭充值请求
message DirectCloseReq {
  // 交易流水号
  string serial_number = 1;
  // 处理人id
  int32 handle_id = 2;
  // 处理人名称
  string handle_name = 3;
  // 备注
  string remark = 4;
}

// 关闭充值响应
message DirectCloseResp {
  // 充值订单信息
  OrderDirect order = 1;
}

// 将充值信息手动修改为成功请求
message DirectMarkSuccessReq {
  // 交易流水号
  string serial_number = 1;
  // 处理人id
  int32 handle_id = 2;
  // 处理人名称
  string handle_name = 3;
  // 备注
  string remark = 4;
}

// 将充值信息手动修改为成功响应
message DirectMarkSuccessResp {
  // 充值订单信息
  OrderDirect order = 1;
}

// 上游通知列表请求
message DirectNotifyListReq {
  repeated int64 create_time = 1;
  repeated int32 status = 2;
  int32 platform_product_id = 3;
}

// 上游通知列表响应
message DirectNotifyListResp {
  message DirectNotify {
    string serial_number = 1;
    string terminal_account = 2;
    int32 status = 3;
    int32 execute_time = 4;
    int32 platform_product_id = 5;
  }
  repeated DirectNotify list = 1;
}

// 历史订单充值导出请求
message DirectHistoryExportReq {
  string scroll_id = 1;
  repeated string fields = 2;
  DirectHistoryListReq query_data = 3;
}

// 历史订单充值导出响应
message DirectHistoryExportResp {
  repeated DirectHistoryInfo list = 1;
  int32 data_count = 2;
  string scroll_id = 3;
}

// 历史订单充值列表请求
message DirectHistoryListReq {
  repeated int32 create_time = 1;
  repeated string status = 2;
  string terminal_account = 3;
  string order_number = 4;
  repeated int32 ours_product_id = 5;
  string platform_id = 6;
  int32 reseller_id = 7;
  repeated string platform_product_id = 8;
  int32 page = 9;
  int32 limit = 10;
  string serial_number = 11;
  string reseller_order_number = 12;
  int32 aftermarket = 13;
}

message DirectAllResp {
  repeated DirectWithOrderInfo list = 1;
}

// 历史订单充值列表响应
message DirectHistoryListResp {
  repeated DirectHistoryInfo list = 1;
  int32 data_count = 2;
  bool more = 3;
}

message DirectHistoryInfo {
  int32 ours_product_id = 1;
  string terminal_account = 2;
  double platform_price = 3;
  string order_number = 4;
  int32 platform_product_id = 5;
  string reseller_order_number = 6;
  string execute_time = 7;
  string platform_name = 8;
  int32 order_type = 9;
  int32 reseller_id = 10;
  string order_remark = 11;
  int32 identifier = 12;
  double trade_price = 13;
  string create_time = 14;
  string platform_product_name = 15;
  string serial_number = 16;
  string ours_product_title = 17;
  string platform_product_code = 18;
  string reseller_name = 19;
  int32 order_quantity = 20;
  string platform_id = 21;
  string status = 22;
  string platform_order_number = 23;
  string fail_message = 24;
  int32 aftermarket = 25;
}

// 订单充值队列请求
message DirectListReq {
  repeated int32 create_time = 1;
  repeated int32 status = 2;
  string terminal_account = 3;
  repeated string order_order_number = 4;
  repeated int32 ours_product_id = 5;
  repeated int32 platform_product_ids = 6;
  string serial_number = 7;
  int32 page = 8;
  int32 limit = 9;
  int32 reseller_id = 10;
  int32 user_id = 11;

  // 导出用
  string cursor = 12;
  bool with_remark = 13;
  bool with_order = 14;
  bool with_batch = 15;
  bool with_item = 16;
}

// 订单充值队列导出响应
message DirectExportResp {
  int32 data_count = 1;
  repeated DirectExport lists = 2;
  string cursor = 3;
}
// 订单充值队列响应
message DirectListResp {
  int32 data_count = 1;
  repeated DirectWithOrder lists = 2;
}

message DirectExport {
  string serial_number = 1;
  string order_order_number = 2;
  string terminal_account = 3;
  // 商品ID
  int32 ours_product_id = 4;
  // 状态
  int32 status = 5;
  // 成交价格
  double trade_price = 6;
  // 本次使用的平台商品id
  int32 platform_product_id = 7;
  // 平台产品价格缓存信息
  double platform_price = 8;
  int32 create_time = 9;
  int32 execute_time = 10;
  // 序号
  int32 identifier = 11;
  // 修改版本
  int32 version = 12;
  // 充值类型：0订单模式1叠加模式2批量模式
  int32 type = 13;
  // 当前使用的接口备选项序号
  int32 position = 14;
  OrderInfo order = 15;
  ExportBatchDirect batch = 16;
  string status_text = 17;
}

message ExportBatchDirect {
  string batch_id = 1;
  // 客户名称
  string customer = 2;
  // 我们的产品id
  int32 ours_product_id = 3;
  // 单价
  double price = 4;
  // 数量
  int32 quantity = 5;
  // 总价
  double total = 6;
  // 充值使用渠道
  string platform = 7;
  // 创建时间
  int32 create_time = 8;
  // 批量充值状态：0创建，-1关闭，1充值中，2充值完成
  int32 status = 9;
  // 成功充值数量
  int32 success = 10;
  // 失败充值数量
  int32 fail = 11;
  // 操作人
  int32 operator = 12;
  // 完成时间
  int32 finish_time = 13;
  // 文件版本
  int32 version = 14;
  // 备注
  string remark = 15;
  // 分销商id
  int32 reseller_id = 16;
  // 利润
  double profit = 17;
  // 订单审批编号
  string ding_talk_sn = 18;
  // 所属商务
  int32 aftermarket = 19;
}

message DirectWithOrder {
  string serial_number = 1;
  string order_order_number = 2;
  string terminal_account = 3;
  // 商品ID
  int32 ours_product_id = 4;
  // 状态
  int32 status = 5;
  // 成交价格
  double trade_price = 6;
  // 本次使用的平台商品id
  int32 platform_product_id = 7;
  // 平台产品价格缓存信息
  double platform_price = 8;
  int32 create_time = 9;
  int32 execute_time = 10;
  // 序号
  int32 identifier = 11;
  // 修改版本
  int32 version = 12;
  // 充值类型：0订单模式1叠加模式2批量模式
  int32 type = 13;
  // 当前使用的接口备选项序号
  int32 position = 14;
  OrderDirectFailReason reason = 16;
  int32 reseller_id = 17;
  int32 aftermarket = 18;
}

message DirectWithOrderInfo {
  string serial_number = 1;
  string order_order_number = 2;
  string terminal_account = 3;
  // 商品ID
  int32 ours_product_id = 4;
  // 状态
  int32 status = 5;
  // 成交价格
  double trade_price = 6;
  // 本次使用的平台商品id
  int32 platform_product_id = 7;
  // 平台产品价格缓存信息
  double platform_price = 8;
  int32 create_time = 9;
  int32 execute_time = 10;
  // 序号
  int32 identifier = 11;
  // 修改版本
  int32 version = 12;
  // 充值类型：0订单模式1叠加模式2批量模式
  int32 type = 13;
  // 当前使用的接口备选项序号
  int32 position = 14;
  DirectOrderInfo order = 15;
  OrderDirectFailReason reason = 16;
}

message OrderDirectFailReason {
  string serial_number = 1;
  string message = 2;
}

// 订单信息
message DirectOrderInfo {
  // 订单id/订单编号order_number
  string id = 1;
  // 分销商id
  int32 reseller_id = 2;
  // 经销商的职员id
  optional int32 reseller_member_id = 3;
  // 订单状态【-2全部失败-1关闭订单0    下单中1  订单完成2  部分成功3  充值处理中】
  int32 status = 4;
  // 支付状态 【0未支付 1支付中 2支付完成 -1支付失败】
  int32 pay_status = 5;
  // 创建时间
  int32 create_time = 6;
  // 支付时间
  optional int32 pay_time = 7;
  // 直冲，卡密交易 【1直冲 2卡密】
  int32 type = 8;
  // 1API，2web，3wechat ，4seller
  int32 mode = 9;
  // 充值账号类型
  optional int32 account_type = 10;
  // 充值账户
  string account = 11;
  // 发起IP
  int64 create_ip = 12;
  // 成交数量
  uint32 quantity = 13;
  // 子订单数量
  int32 leaf_count = 14;
  // 成交成功数量
  int32 success_number = 15;
  // 成交金额
  double amount = 16;
  // 支付金额
  double pay_amount = 17;
  // 分销订单号
  string reseller_order_number = 18;
  // 回调通知地址
  string notifyUrl = 19;
  // 我们商品的定价
  double price = 20;
  // 回调通知时间
  optional int32 notify_time = 21;
  double profit = 22;
  optional int32 finish_time = 23;
  // 归属商务id
  int32 aftermarket = 24;
  // 备注
  OrderRemarkInfo remark = 25;
  // 订单商品快照
  OrderItemInfo item = 26;
  // 订单状态描述
  string status_desc = 27;
  // 支付状态描述
  string pay_status_desc = 28;
}

// 订单充值队列日志请求
message DirectLogReq {
  string order_number = 1;
  string serial_number = 2;
}

// 订单充值队列日志响应
message DirectLogResp {
  repeated DirectLogInfo logs = 1;
}

message DirectLogInfo {
  string datetime = 1;
  string server_id = 2;
  string mes = 3;
  string data = 4;
}

// 手动处理的订单请求
message DirectOrderReq {
  repeated int32 create_time = 1;
  optional int32 platform_status = 2;
  string serial_number = 3;
  optional int32 status = 4;
  optional int32 old_status = 5;
  string order_order_number = 6;
  int32 page = 7;
  int32 limit = 8;
}

// 手动处理的订单响应
message DirectOrderResp {
  int32 data_count = 1;
  repeated OrderDirectManualInfo lists = 2;
}

message OrderDirectManualInfo {
  // 充值序号
  string serial_number = 1;
  // 手动处理时间
  int32 create_time = 2;
  // 平台最终状态处理时间
  int32 execute_time = 3;
  // 处理前订单状态
  int32 old_status = 4;
  // 手动处理状态，-1失败，1成功
  int32 status = 5;
  // 平台最终状态，2充值中，-1失败，1成功
  int32 platform_status = 6;
  // 平台成本价格，用来计算亏损使用
  double cost = 7;
  // 备注
  string remark = 8;
  // 手动处理人
  int32 handle = 9;
  OrderDirect direct = 10;
}

message OrderDirect {
  string serial_number = 1;
  string order_order_number = 2;
  string terminal_account = 3;
  // 商品ID
  int32 ours_product_id = 4;
  // 状态
  int32 status = 5;
  // 成交价格
  double trade_price = 6;
  // 本次使用的平台商品id
  int32 platform_product_id = 7;
  // 平台产品价格缓存信息
  double platform_price = 8;
  int32 create_time = 9;
  int32 execute_time = 10;
  // 序号
  int32 identifier = 11;
  // 修改版本
  int32 version = 12;
  // 充值类型：0订单模式1叠加模式2批量模式
  int32 type = 13;
  // 当前使用的接口备选项序号
  int32 position = 14;
}