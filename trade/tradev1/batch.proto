syntax = "proto3";
package tradeservice.tradev1;
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
option go_package = "./trade/tradev1;tradev1";

// 批量充值服务
service Batch {
  // 获取批量充值列表
  rpc BatchList (BatchListReq) returns (BatchListResp);
  // 获取批量充值详情
  rpc BatchDetail (BatchDetailReq) returns (BatchDetailResp);
  // 获取批量充值日志
  rpc BatchLog (BatchLogReq) returns (BatchLogResp);
  // 批量充值订单导出
  rpc BatchOrderExport (BatchOrderExportReq) returns (BatchOrderExportResp);
  // 批量充值
  rpc DirectBatch (DirectBatchReq) returns (DirectBatchResp) {};
  // 获取批量充值状态
  rpc GetBatchInfo (GetBatchInfoReq) returns (GetBatchInfoResp) {};
}

// 获取批量充值状态请求
message GetBatchInfoReq {
  string id = 1;
}

// 获取批量充值状态返回
message GetBatchInfoResp {
  BatchDirectInfo info = 1;
}

// 批量充值订单导出请求
message BatchOrderExportReq {
  string order_number = 1;
}

// 批量充值订单导出返回
message BatchOrderExportResp {
  repeated BatchOrderDirect direct = 1;
  repeated ESOrderDirect direct_es = 2;
}

message ESOrderDirect {
  int32 ours_product_id = 1;
  string terminal_account = 2;
  double platform_price = 3;
  string order_number = 4;
  int32 platform_product_id = 5;
  string reseller_order_number = 6;
  string execute_time = 7;
  string platform_name = 8;
  int32 order_type = 9;
  int32 reseller_id = 10;
  string order_remark = 11;
  int32 identifier = 12;
  double trade_price = 13;
  string create_time = 14;
  string platform_product_name = 15;
  string serial_number = 16;
  string ours_product_title = 17;
  string platform_product_code = 18;
  string reseller_name = 19;
  int32 order_quantity = 20;
  string platform_id = 21;
  string status = 22;
  string platform_order_number = 23;
  string fail_message = 24;
}

// 获取批量充值日志请求
message BatchLogReq {
  // 充值订单号
  string order_number = 1;
}

// 获取批量充值日志返回
message BatchLogResp {
  repeated BatchLogInfo logs = 1;
}

message BatchLogInfo {
  string datetime = 1;
  string server_id = 2;
  string mes = 3;
  string data = 4;
}

// 批量充值详情请求
message BatchDetailReq {
  // 充值订单号
  string order_number = 1;
}

// 批量充值详情返回
message BatchDetailResp {
  string batch_id = 1;
  // 客户名称
  string customer = 2;
  // 我们的产品id
  int32 ours_product_id = 3;
  // 单价
  double price = 4;
  // 数量
  int32 quantity = 5;
  // 总价
  double total = 6;
  // 充值使用渠道
  string platform = 7;
  // 创建时间
  int32 create_time = 8;
  // 批量充值状态：0创建，-1关闭，1充值中，2充值完成
  int32 status = 9;
  // 成功充值数量
  int32 success = 10;
  // 失败充值数量
  int32 fail = 11;
  // 操作人
  int32 operator = 12;
  // 完成时间
  int32 finish_time = 13;
  // 文件版本
  int32 version = 14;
  // 备注
  string remark = 15;
  // 分销商id
  int32 reseller_id = 16;
  // 利润
  double profit = 17;
  // 订单审批编号
  string ding_talk_sn = 18;
  // 所属商务
  int32 aftermarket = 19;
  repeated BatchOrderBenefit benefits = 20;
  repeated BatchOrderDirect directs = 21;
  bool is_more = 22;
}

message BatchOrderBenefit {
  // 主键
  int32 id = 1;
  // order_number 或者 batch_id
  string order_number = 2;
  // 1订单，2批量充值
  int32 type = 3;
  // 分销商
  int32 reseller_id = 4;
  // 效益，正数为盈利，负数为亏损
  double benefit = 5;
  // 备注
  string remark = 6;
  // 创建时间
  int32 create_time = 7;
  // 完成时间
  int32 update_time = 8;
}

message BatchOrderDirect {
  string serial_number = 1;
  string order_order_number = 2;
  string terminal_account = 3;
  // 商品ID
  int32 ours_product_id = 4;
  // 状态
  int32 status = 5;
  // 成交价格
  double trade_price = 6;
  // 本次使用的平台商品id
  int32 platform_product_id = 7;
  // 平台产品价格缓存信息
  double platform_price = 8;
  google.protobuf.Timestamp create_time = 9;
  google.protobuf.Timestamp execute_time = 10;
  // 序号
  int32 identifier = 11;
  // 修改版本
  int32 version = 12;
  // 充值类型：0订单模式1叠加模式2批量模式
  int32 type = 13;
  // 当前使用的接口备选项序号
  int32 position = 14;
  BatchDirectInfo batch = 25;
}

// 批量充值列表请求
message BatchListReq {
  // 充值订单号
  string batch_id = 1;
  // 分销商 id
  int32 reseller_id = 2;
  // 商品 id
  int32 ours_product_id = 3;
  // 创建时间
  repeated int32 create_time = 4;
  // 状态
  repeated int32 status = 5;
  // 页码
  int32 page = 6;
  // 每页条数
  int32 limit = 7;
  string customer = 8;
  int32 aftermarket = 9;
}

// 批量充值列表返回
message BatchListResp {
  int32 count = 1;
  double total_amount = 2;
  repeated BatchDirectInfo list = 3;
}

message BatchDirectInfo {
  string batch_id = 1;
  // 客户名称
  string customer = 2;
  // 我们的产品id
  int32 ours_product_id = 3;
  // 单价
  double price = 4;
  // 数量
  int32 quantity = 5;
  // 总价
  double total = 6;
  // 充值使用渠道
  string platform = 7;
  // 创建时间
  int32 create_time = 8;
  // 批量充值状态：0创建，-1关闭，1充值中，2充值完成
  int32 status = 9;
  // 成功充值数量
  int32 success = 10;
  // 失败充值数量
  int32 fail = 11;
  // 操作人
  int32 operator = 12;
  // 完成时间
  int32 finish_time = 13;
  // 文件版本
  int32 version = 14;
  // 备注
  string remark = 15;
  // 分销商id
  int32 reseller_id = 16;
  // 利润
  double profit = 17;
  // 订单审批编号
  string ding_talk_sn = 18;
  // 所属商务
  int32 aftermarket = 19;
}


message DirectBatchReq {
  int32 reseller_id = 1;
  int32 ours_product_id = 2;
  double price = 3; // api 由string转成float64
  int32 quantity = 4;
  double total = 5;
  string account = 6;
  string ding_talk_sn = 7;
  string remark = 8;

  int32 operator = 9  [(validate.rules).int32 = {gte:1}]; // api层传递的操作人
  int32 aftermarket = 10 [(validate.rules).int32 = {gte:1}]; // api层传递的商务
}
message DirectBatchResp {}