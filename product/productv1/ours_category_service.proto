syntax = "proto3";

package productservice.productv1;

import "product/productv1/common.proto";
import "product/productv1/ours_category.proto";

option go_package = "./product/productv1;productv1";

service OursCategory {
  rpc GetList (ReqPage) returns (GetListResponse);

  rpc AddCategory(AddCategoryRequest) returns(RespEmpty);

  rpc GetCategoryEnableTree(GetCategoryEnableTreeReq) returns(GetCategoryEnableTreeResp);

  // 获取所有分类和分类下的商品
  rpc GetAllCategoryAndProducts(GetAllCategoryAndProductsReq) returns(GetAllCategoryAndProductsResp);
  //通过ID获取商品分类
  rpc GetCategoryByIds(GetCategoryByIdsReq) returns (GetCategoryByIdsResp);

  // 获取分类列表和子集
  rpc GetCategoryList (GetCategoryListReq) returns (GetCategoryListResp) {}

  // 更新
  rpc UpdateCategory (CategoryUpdateReq) returns (CategoryUpdateResp) {}

  //添加
  rpc CreateCategory (CategoryCreateReq) returns (CategoryCreateResp) {}

  // 软删除
  rpc DeleteCategory (CategoryDelReq) returns (CategoryDelResp) {}

}