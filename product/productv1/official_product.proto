syntax = "proto3";

package productservice.productv1;
import "product/productv1/official.proto";
import "product/productv1/common.proto";
import "validate/validate.proto";
option go_package = "./product/productv1;productv1";

message FindRelaOfficialRequest {
  int32  id = 1;
}
message FindRelaOfficialResponse {
  int32 id = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  float price = 6;
  int32 status = 7;
  int32 create_time = 8;
  int32 update_time = 9;
  int32 type = 10;
  int32 daies = 11;
  Official official = 12;
}

//商品规格
message RespOfficialProduct {
  int32 id = 1;
  string name = 2;
  float price = 3;
}

//多个商品商品规格
message RespOfficialProducts {
  repeated RespOfficialProduct OfficialProducts = 1;
}

// 分类信息
message RespOfficialProductCategory {
  int32 id = 1;
  string name = 2;
  repeated RespOfficialProduct officialProduct = 3;
}

//按分类返回所有规格
message RespOfficialProductInfos {
  repeated RespOfficialProductCategory allList = 1;
}

// 商品规格ids
message ReqOfficialProductIds {
  //ids
  repeated int32 ids = 1;
}

// 商品规格ids
message ReqOfficialProductId {
  int32 id = 1;
}

// official_product
message OfficialProductInfo{
  int32 id = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  float price = 6;
  int32 status = 7;
  int32 create_time = 8;
  int32 update_time = 9;
  int32 type = 10;
  int32 daies = 11;
}

message FindOfficialProductReq {
  // @validate:"required"
  repeated int32 official_product_ids = 1;
  bool with_official = 2;
}

message FindOfficialProductResp {
  repeated OfficialProductDetail items = 1;
}

message OfficialProductDetail {
  OfficialProductInfo official_product = 1;
  Official official = 2;
}

message SearchListOfficialReq {
  int32 type = 2; // 默认 1直冲 2卡密 3(直冲+卡密)
}
message SearchListOfficialResp {
  repeated OfficialSimple data = 1;
  int32 cache_time = 2;
  message OfficialSimple {
    int32 id = 1;
    string name = 2;
    repeated OfficialProductSimple products = 3;
    int32 type = 4;
  }
  message OfficialProductSimple {
    int32 id = 1;
    string name = 2;
    float price = 3;
  }
}

message OfficialDetail {
  Official official = 1;
  repeated OfficialProductInfo products = 2;
}

message GetMultiOfficialProductReq {
  int32 official_product_id = 1;
}
message GetMultiOfficialProductResp {
  repeated OursOfficialProductSimple list = 1;
  message OursOfficialProductSimple {
    int32 official_product_id = 1;
    int32 id = 2;
    string name = 3;
  }
}

message GetMapsOfficialProductByIdsRequest {
  repeated int32 ids = 1 [(validate.rules).repeated = {min_items:1, unique:true, items:{int32:{gt:0}}}];
}

message GetMapsOfficialProductByIdsReply {
  map<int32, OfficialProductInfo> maps = 1;
}

message GetOfficialProductRequest {
  int32 id = 1;
}

message GetOfficialProductReply {
  int32 id = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  float price = 6;
  int32 status = 7;
  int32 create_time = 8;
  int32 update_time = 9;
  int32 type = 10;
  int32 daies = 11;
}

message SearchOfficialProductReq {
  ReqPage page = 1;
  int32 official_id = 2;
  int32 type = 3; // 默认 1直冲 2卡密 3(直冲+卡密)
  string keyword = 4;// 关键字
}

message SearchOfficialProductResp {
  int32 data_count = 1;
  repeated FindRelaOfficialResponse list = 2;
}

message CreateOfficialProductReq {
  int32 daies = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  double price = 6;
  int32 status = 7;
  int32 type = 8;
}

message UpdateOfficialProductReq {
  int32 id = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  double price = 6;
  int32 status = 7;
  int32 type = 8;
  int32 daies = 9;
}

message OfficialProductId {
  int32 id = 1;
}