syntax = "proto3";

package productservice.productv1;
import "validate/validate.proto";
import "product/productv1/common.proto";

option go_package = "./product/productv1;productv1";

message GetOursProductListRequest {
  int32 official_id = 1;
  int32 official_product_id = 2;
  int32 platform_id = 3;
  string keyword = 4;
  message ReqPage {
    int32 num = 1 [(validate.rules).int32 = {gte:1, lte: 100}]; //页码
    int32 size = 2 [(validate.rules).int32 = {gte:1, lte: 10000}]; //每页数量
  }
  ReqPage page = 5;

  bool with_category = 6;
  bool with_official_product = 7;
  bool with_official_product_official = 8;

  // 0:无排序 1:'ours_product_category_id', 'wight'
  GetOursProductListSortType sortType = 9;

  bool with_statistics = 10;

  repeated int32 ours_product_ids = 11;
}

enum  GetOursProductListSortType {
  OURS_PRODUCT_LIST_SORT_TYPE_NONE = 0;
  OURS_PRODUCT_LIST_SORT_TYPE_CATEGORY = 1;
}

message GetOursProductListResponse {
  int32 data_count = 1;
  repeated GetOursProductList list = 2;
}

message GetOursProductList {
  int32 id = 1;
  int32 ours_product_category_id = 2;
  int32 official_product_id = 3;
  string tag = 4;
  string name = 5;
  int32 type = 6;
  float discount = 7;
  string preview = 8;
  string describe = 9;
  float price = 10;
  int32 status = 11;
  int32 create_time = 12;
  int32 update_time = 13;
  string extend = 14;
  int32 wight = 15;
  int32 property = 16;
  string auth_product_info = 17;
  string auth_product_ids = 18;
  Category category = 19;
  OursOfficialProduct official_product = 20;
  Statistics statistics = 21 [json_name = "statistics"];
}
message Statistics {
  int32 ours_product_id = 1 [json_name = "ours_product_id"];
  float total_price = 2 [json_name = "total_price"];
  int32 total = 3 [json_name = "total"];
}
message Category {
  int32 id = 1;
  string name = 2;
  int32 status = 3;
  int32 create_time = 4;
  int32 pid = 5;
}

message AddOursProductRequest {
  int32 ours_product_category_id = 1;
  int32 official_product_id = 2;
  int32 official_id = 3;
  string name = 4;
  string describe = 5;
  float official_product_price = 6;
  float price = 7;
  float discount = 8;
  string auth_product_info = 9;
  string auth_product_ids = 10;
  int32 status = 11;
  string preview = 12;
  int32 type = 13;

  // 更新时使用
  int32 id = 14;
  // 操作用户
  int32 user_id = 15 [(validate.rules).int32 = {gte:1}];
}

message AddOursProductResponse {
  int32 ours_product_category_id = 1;
  int32 official_product_id = 2;
  int32 official_id = 3;
  string name = 4;
  string describe = 5;
  float official_product_price = 6;
  float price = 7;
  string discount = 8;
  int32 auth_product_info = 9;
  string auth_product_ids = 10;
  string status = 11;
  string preview = 12;
  string type = 13;
  int32 id = 14;
}

message DeleteOursProductReq {
  // 删除的商品id
  int32 id = 1 [(validate.rules).int32 = {gte:1}];
  // 操作用户
  int32 user_id = 2 [(validate.rules).int32 = {gte:1}];
}

message DeleteOursProductResp {}

message SearchListOursProductReq {
  int32 ours_product_category_id = 1;
  string keyword = 2;
  repeated int32 ids = 4;
  ReqPage page = 5;
  repeated int32 status = 6;
}

message OursProductData {
  int32 id = 1;
  int32 ours_product_category_id = 2;
  int32 official_product_id = 3;
  string tag = 4;
  string name = 5;
  int32 type = 6;
  float discount = 7;
  string preview = 8;
  string describe = 9;
  float price = 10;
  int32 status = 11;
  int32 create_time = 12;
  int32 update_time = 13;
  string extend = 14;
  int32 wight = 15;
  int32 property = 16;
  string auth_product_info = 17;
  string auth_product_ids = 18;
}

message SearchListOursProductResp {
  repeated OursProductData list = 1;
  int32 data_count = 2;
}

message GetOursProductListWithOfficialReq {
  repeated int32 ids = 1;
}

message GetOursProductListWithOfficialResp {
  repeated  OursProductListWithOfficial list = 1;
}

message OursProductListWithOfficial {
  int32 id = 1;
  int32 ours_product_category_id = 2;
  int32 official_product_id = 3;
  string tag = 4;
  string name = 5;
  int32 type = 6;
  float discount = 7;
  string preview = 8;
  string describe = 9;
  float price = 10;
  int32 status = 11;
  int32 create_time = 12;
  int32 update_time = 13;
  string extend = 14;
  int32 wight = 15;
  int32 property = 16;
  string auth_product_info = 17;
  string auth_product_ids = 18;
  OursOfficialProduct official_product = 19;
}

message OursOfficialProduct {
  int32 id = 1;
  int32 official_id = 2;
  string name = 3;
  string describe = 4;
  string preview = 5;
  float price = 6;
  int32 status = 7;
  int32 create_time = 8;
  int32 update_time = 9;
  int32 type = 10;
  int32 daies = 11;
  OursOfficial official = 12;
}

message OursOfficial {
  int32 id = 1;
  string name = 2;
  string describe = 3;
  int32 num = 4;
  int32 status = 5;
  string web_url = 6;
  string recharge_url = 7;
  int32 create_time = 8;
  int32 update_time = 9;
  int32 type = 10;
  string tag = 11;
}

message GetOursProductIdsReq {
  int32 ours_product_category_id = 1;
}

message GetOursProductIdsResp {
  repeated int32 our_product_ids = 1;
}

message GetOursProductInfoReq {
  int32 our_product_id = 1;
  bool with_category = 2;
  bool with_official_product = 3;
  bool with_official = 4; // 需要 official_product 为 true
}

message GetOursProductInfoResp {
  int32 id = 1;
  int32 ours_product_category_id = 2;
  int32 official_product_id = 3;
  string tag = 4;
  string name = 5;
  int32 type = 6;
  float discount = 7;
  string preview = 8;
  string describe = 9;
  float price = 10;
  int32 status = 11;
  int32 create_time = 12;
  int32 update_time = 13;
  string extend = 14;
  int32 wight = 15;
  int32 property = 16;
  string auth_product_info = 17;
  string auth_product_ids = 18;

  Category category = 19;
  OursOfficialProduct official_product = 20;
}

message UpdateByCrontabReq{
  int32 user_id = 1;
  string realname = 2;
  string exec_event = 3;
  string ids = 4;
  int32 ours_status = 5;
  double price = 6;
  double discount = 7;
  int32 ours_product_id = 8;
}
message UpdateByCrontabResp{}

// 获取产品列表请求
message GetProductListReq {
  repeated int32 ids = 1;
}

// 获取产品列表响应
message GetProductListResp {
  repeated OursProductData list = 1;
}