syntax = "proto3";

package productservice.productv1;

import "product/productv1/ours_product.proto";
option go_package = "./product/productv1;productv1";

message GetListResponse {
  repeated OursCategoryInfo category = 1;
}

message OursCategoryInfo {
  int32 pid = 1;
  string name = 2;
  int32 id = 3;
  int32 status = 4;
}

message GetCategoryEnableTreeReq{}

message GetCategoryEnableTreeResp{
  repeated Category data = 1;
  int32 cache_time = 2 [json_name = "cache_time"];

  message Product {
    int32 ours_product_category_id = 1 [json_name = "ours_product_category_id"];
    int32 id = 2 [json_name = "id"];
    string name = 3 [json_name = "name"];
    int32 official_product_id = 4 [json_name = "official_product_id"];
    int32 status = 5 [json_name = "status"];
    message OfficialProduct {
      string price = 1 [json_name = "price"];
    }
    OfficialProduct official_product = 6 [json_name = "official_product"];
    double min_price = 7 [json_name = "min_price"];
  }

  message Category {
    int32 id = 1 [json_name = "id"];
    string name = 2 [json_name = "name"];
    int32 pid = 3 [json_name = "pid"];
    repeated Product products = 4 [json_name = "products"];
    repeated Category child = 5 [json_name = "child"];
  }
}

message AddCategoryRequest {
  int32 pid = 1;
  string name = 2;
  int32 status = 4;
}

message GetAllCategoryAndProductsReq {

}

message GetAllCategoryAndProductsResp {
  repeated CategoryAndProducts items = 1;
  message CategoryAndProducts {
    OursCategoryInfo category = 1;
    repeated OursProductData products = 2;
  }
}

message GetCategoryByIdsReq {
  repeated int32 ids = 1;
}

message GetCategoryByIdsResp {
  repeated OursCategoryInfo data = 1;
}

message GetCategoryListReq{
  int32 page = 1;
  int32 limit = 2;
}
message GetCategoryListResp{
  repeated CategoryList data = 1;
}
message  CategoryList {
  int32 pid = 1;
  string name = 2;
  int32 id = 3;
  int32 status = 4;
  repeated CategoryList child = 5;
}

message CategoryUpdateReq{
  string name = 1;
  int32 id = 2;
  int32 status = 3;
}

message CategoryCreateReq{
  string name = 1;
  int32 pid = 2;
  int32 status = 3;
}

message CategoryDelReq{
  int32 id = 1;
}
message CategoryCreateResp{}
message CategoryDelResp{}
message CategoryUpdateResp{}
