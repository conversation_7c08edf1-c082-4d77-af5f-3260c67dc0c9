GOHOSTOS:=$(shell go env GOHOSTOS)
GOPATH:=$(shell go env GOPATH)
VERSION=$(shell git describe --tags --always)

ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	#Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
#	Git_Bash=$(subst \,/,$(subst cmd\,bin\bash.exe,$(dir $(shell where git | grep cmd))))
	G_Pth = $(subst Program Files,"Program Files", $(shell where git | grep cmd))
    Git_Bash=$(subst \,/,$(subst cmd\git.exe,bin\bash.exe,$(G_Pth)))
	INTERNAL_PROTO_FILES=$(shell $(Git_Bash) -c "find -L internal -name *.proto")
	API_PROTO_FILES=$(shell $(Git_Bash) -c "find -L api -name *.proto")
else
	INTERNAL_PROTO_FILES=$(shell find -L internal -name *.proto)
	API_PROTO_FILES=$(shell find -L api -name *.proto)
endif

.PHONY: init
# init env
init:
	go install google.golang.org/protobuf/cmd/protoc-gen-go@latest
	go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@latest
	go install github.com/go-kratos/kratos/cmd/kratos/v2@latest
	go install github.com/go-kratos/kratos/cmd/protoc-gen-go-http/v2@latest
	go install github.com/google/gnostic/cmd/protoc-gen-openapi@latest
	go install github.com/google/wire/cmd/wire@latest

.PHONY: config
# generate internal proto
config:
	protoc --proto_path=./internal \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./internal \
	       $(INTERNAL_PROTO_FILES)

#.PHONY: validate
# generate validate proto
#validate:
#	protoc --proto_path=./api \
#           --proto_path=./third_party \
#           --go_out=paths=source_relative:. \
#            --validate_out=paths=source_relative,lang=go:. \
#           $(API_PROTO_FILES)

.PHONY: wire
# generate wire
wire:
	cd ./cmd/server && wire

.PHONY: build
# build
build:
	make generate;
	mkdir -p bin/ && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags "-X main.Version=$(VERSION)" -o ./bin/ ./cmd/...

.PHONY: api
# generate api proto
api:
	protoc --proto_path=./api \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./api \
 	       --go-http_out=paths=source_relative:./api \
 	       --go-grpc_out=paths=source_relative:./api \
 	       --validate_out=paths=source_relative,lang=go:./api \
 	       --go-errors_out=paths=source_relative:./api \
	       --openapi_out=fq_schema_naming=true,default_response=false:./third_party/swagger_ui \
	       $(API_PROTO_FILES)

.PHONY: fmt-proto
# format proto
fmt-proto:
	go install github.com/bufbuild/buf/cmd/buf@latest;
	buf format -w --path ./api;
	buf format -w --path ./internal/conf;

.PHONY: delgen
# delgen
delgen:
	find "./internal/data/ent/" -mindepth 1 -maxdepth 1 -type d ! -name "template" ! -name "schema" -exec rm -rf {} +;
	find "./api" -type f -name "*.go" -delete;
	find "./cmd" -type f -name "wire_gen.go" -delete
	find "./internal/biz/valobj" -type f -name "*_string.go" -delete;
	find "./internal/data/ent/" -mindepth 1 -maxdepth 1 ! -name "template" ! -name "schema" -type f -name "*.go" -exec rm -rf {} +;

.PHONY: generate
# generate
generate:
	make grpc;
	make api;
	make config;
	make generateStringer;
	make ent;
	make wire;

.PHONY: grpc
# generate api proto
grpc:
	cd ./grpc && make generate

.PHONY: generateStringer
generateStringer:
	go install codeup.aliyun.com/5f9118049cffa29cfdd3be1c/tools/cmd/stringer@latest;
	go generate ./internal/biz/valobj/; #生成值对象

.PHONY: all
# generate all
all:
	make generate;

.PHONY: errors
# errors
#errors:
#	protoc --proto_path=./api \
#           --proto_path=./third_party \
#           --go_out=paths=source_relative:. \
#           --go-errors_out=paths=source_relative:. \
#           $(API_PROTO_FILES)

# show help
help:
	@echo ''
	@echo 'Usage:'
	@echo ' make [target]'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
	helpMessage = match(lastLine, /^# (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 2, RLENGTH); \
			printf "\033[36m%-22s\033[0m %s\n", helpCommand,helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

.PHONY: ent
# 生成 ent 代码，t=表名
ent:
	@echo '生成 ent 模型，命令格式：make ent t=表名，当前生成中...'
	sh ./sync_table_entgo.sh $(t)

.PHONY: clean
clean:
	rm -rf ./internal/biz/valobj/*_string.go

.DEFAULT_GOAL := help

.PHONY: dev
dev:
	go run cmd/server/factory.go cmd/server/main.go cmd/server/wire_gen.go
