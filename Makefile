GOHOSTOS:=$(shell go env GOHOSTOS)
ifeq ($(GOHOSTOS), windows)
	#the `find.exe` is different from `find` in bash/shell.
	#to see https://docs.microsoft.com/en-us/windows-server/administration/windows-commands/find.
	#changed to use git-bash.exe to run find cli or other cli friendly, caused of every developer has a Git.
	#Git_Bash= $(subst cmd\,bin\bash.exe,$(dir $(shell where git)))
#	Git_Bash=$(subst \,/,$(subst cmd\,bin\bash.exe,$(dir $(shell where git | grep cmd))))
	G_Pth = $(subst Program Files,"Program Files", $(shell where git | grep cmd))
	Git_Bash=$(subst \,/,$(subst cmd\git.exe,bin\bash.exe,$(G_Pth)))

	GRPC_PROTO_FILES=$(shell $(Git_Bash) -c "find . -path './third_party' -prune -o -type f -name '*.proto' -print")
	PB_FILES=$(shell $(Git_Bash) -c "find . -name *.pb.*")
else
	GRPC_PROTO_FILES=$(shell find . -path "./third_party" -prune -o -type f -name "*.proto" -print)
	PB_FILES=$(shell find . -name *.pb.*)
endif

.PHONY: generate
# generate api proto
generate:
	protoc --proto_path=./ \
		   --experimental_allow_proto3_optional \
	       --proto_path=./third_party \
 	       --go_out=paths=source_relative:./ \
 	       --go-http_out=paths=source_relative:./ \
 	       --go-grpc_out=paths=source_relative:./ \
 	       --validate_out=paths=source_relative,lang=go:./ \
 	       --go-errors_out=paths=source_relative:./ \
	       $(GRPC_PROTO_FILES)

.PHONY: remove
#删除已生成的 pb 文件
remove:
	rm -f ${PB_FILES}
