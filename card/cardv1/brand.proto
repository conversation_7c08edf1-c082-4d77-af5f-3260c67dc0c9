syntax = "proto3";

package api.inner;

import "card/cardv1/global.proto";

option go_package = "./card/cardv1;cardv1";

service Brand {
	// 获取品牌明细
	rpc GetBrand (GetBrandRequest) returns (GetBrandReply);

	// 获取品牌列表
	rpc ListBrand (ListBrandRequest) returns (ListBrandReply);

	// 获取品牌列表 用于筛选
	rpc ListBrandSelect (ListBrandSelectRequest) returns (ListBrandSelectReply);

	// 获取品牌商品明细
	rpc GetBrandGoods (GetBrandGoodsRequest) returns (GetBrandGoodsReply);

	// 获取品牌商品列表
	rpc ListBrandGoods (ListBrandGoodsRequest) returns (ListBrandGoodsReply);

	// 获取品牌商品下拉列表
	rpc ListBrandGoodsSelect (ListBrandGoodsSelectRequest) returns (ListBrandGoodsSelectReply);
}

// 获取品牌请求参数
message GetBrandRequest {
	uint32 brandId = 1;
}
// 获取品牌响应参数
message GetBrandReply {
	// 品牌明细
	BrandItem detail = 1;
}

// 获取品牌列表请求参数
message ListBrandRequest {
	// 分页参数， 必传
	PageReq page = 1;
	// 品牌名称，根据名称模糊查询
	string keyword = 2;
	// 品牌状态
	BrandStatus status = 3;
	// 主数据ID列表
	repeated uint32 brandIds = 4;
}
// 获取品牌列表响应参数
message ListBrandReply {
	// 分页参数
	PageReply page = 1;
	// 品牌明细列表
	repeated BrandItem list = 2;
}

// 获取品牌列表请求参数
message ListBrandSelectRequest {
	// 分页参数， 必传
	PageReq page = 1;
	// 品牌名称，根据名称模糊查询
	string keyword = 2;
	// 品牌状态
	BrandStatus status = 3;
	// 主数据ID列表
	repeated uint32 brandIds = 4;
}

// 获取品牌列表响应参数
message ListBrandSelectReply {
	// 分页参数
	PageReply page = 1;
	// 品牌明细列表
	repeated SelectOption list = 2;
}

// 获取品牌商品请求参数
message GetBrandGoodsRequest {
	// 品牌商品ID gt: 0
	uint32 brandGoodsId = 1;
}
// 获取品牌商品响应参数
message GetBrandGoodsReply {
	// 品牌商品明细
	BrandGoodsItem detail = 1;
}

// 获取品牌商品列表请求参数
message ListBrandGoodsRequest {
	// 所属品牌
	uint32 brandId = 1;
	// 分页参数， 必传
	PageReq page = 2;
	// 商品名称，根据名称模糊查询
	string keyword = 3;
	// 品牌商品类型
	BrandGoodsType type = 4;
	// 品牌商品状态
	BrandGoodsStatus status = 5;
	// 主数据ID列表
	repeated uint32 brandGoodsIds = 6;
}
message ListBrandGoodsReply {
	// 分页参数
	PageReply page = 1;
	// 品牌商品明细列表
	repeated BrandGoodsItem list = 2;
}

message ListBrandGoodsSelectRequest {
	string keyword = 1;
	PageReq page = 2;
	BrandGoodsType type = 3;
	uint32 brandId = 4;
	BrandGoodsStatus status = 5;
	repeated uint32 brandGoodsIds = 6;
}
message ListBrandGoodsSelectReply {
	PageReply page = 1;
	repeated SelectOptionWithCode list = 2;
}