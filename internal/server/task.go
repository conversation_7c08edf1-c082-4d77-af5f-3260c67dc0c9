package server

import (
	"cardMall/internal/conf"
	"cardMall/internal/module/taskbiz"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/service/task"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/robfig/cron/v3"
	"time"
)

type TaskServiceInterface interface {
	Run(ctx context.Context) error
	GetCommend(ctx context.Context) (string, error)
	GetName() string
	GetTaskName() string
}

type TaskServer struct {
	transport.Server
	c       *conf.Bootstrap
	job     *cron.Cron
	log     *log.Helper
	taskBiz *taskbiz.TaskBiz
	taskIds map[string]cron.EntryID
}

func NewTaskServer(
	log *log.Helper,
	c *conf.Bootstrap,
	taskBiz *taskbiz.TaskBiz,
	orderCancel *task.OrderCancelService,
	rechargeOrderReceiveService *task.RechargeOrderReceiveService,
	meiTuanOrderReceiveService *task.MeiTuanOrderReceiveService,
	thirdOrderUnpaidSyncService *task.ThirdOrderUnpaidSyncService,
	logisticsQueryService *task.LogisticsQueryService,
	entityOrderReceiveService *task.EntityOrderReceiveService,
	StockAlarmService *task.StockAlarmService,
	integralExpireService *task.IntegralExpireService,
	baseGoodsRefreshService *task.BaseGoodsRefreshService,
	thirdOrderCancelService *task.ThirdOrderCancelService,
	hytAfterSaleService *task.HytAfterSaleService,
	syncHytGoodsService *task.SyncHytGoodsService,
	startBucksTakeOutReceivedService *task.StartBucksTakeOutReceivedService,
) *TaskServer {
	taskServer := &TaskServer{
		log:     log,
		c:       c,
		taskBiz: taskBiz,
	}
	taskServer.job = cron.New(cron.WithSeconds())
	taskServer.taskIds = make(map[string]cron.EntryID)

	ctx := context.Background()
	if helper.IsTest(taskServer.c.GetServer().GetEnv()) || helper.IsProduction(taskServer.c.GetServer().GetEnv()) {
		for _, db := range c.GetSharding() {
			cpCtx := isolationcustomer.WithCustomerAndDisableShopCtx(ctx, int(db.GetCustomerId().GetMin()))
			taskServer.register(cpCtx, orderCancel)
			taskServer.register(cpCtx, rechargeOrderReceiveService)
			taskServer.register(cpCtx, meiTuanOrderReceiveService)
			taskServer.register(cpCtx, thirdOrderUnpaidSyncService)
			taskServer.register(cpCtx, logisticsQueryService)
			taskServer.register(cpCtx, entityOrderReceiveService)
			taskServer.register(cpCtx, StockAlarmService)
			taskServer.register(cpCtx, integralExpireService)
			//直连天下虚拟商品只需要刷新官方库
			if db.GetCustomerId().GetMin() == isolationcustomer.SaasPlatformCustomer {
				taskServer.register(cpCtx, baseGoodsRefreshService)
				taskServer.register(cpCtx, hytAfterSaleService) // 售后审核提交到货易通
				taskServer.register(cpCtx, syncHytGoodsService) // 同步货易通商品到官方库
			}
			taskServer.register(cpCtx, thirdOrderCancelService)
			taskServer.register(cpCtx, startBucksTakeOutReceivedService)
		}
	}
	return taskServer
}

func (s *TaskServer) Start(ctx context.Context) error {
	s.job.Start()
	return nil
}

func (s *TaskServer) Stop(ctx context.Context) error {
	c := s.job.Stop()
	s.deleteLock()
	<-c.Done()
	s.log.Info("自动任务服务成功关闭")
	return nil
}

func (s *TaskServer) register(ctx context.Context, task TaskServiceInterface) {
	spec, err := task.GetCommend(ctx)
	if err != nil {
		s.log.Errorf("获取自动任务命令行错误: %v", err)
		return
	}
	if spec == "" {
		return
	}
	taskName := s.getTaskName(ctx, task)

	taskId, err := s.job.AddFunc(spec, func() {
		//捕获自动任务的panic
		defer func() {
			if msg := recover(); msg != nil {
				err = fmt.Errorf("%v", msg)
				s.errHandler(ctx, err, task)
			}
		}()
		//被锁定，说明已经有进程在跑，直接返回
		if s.taskBiz.IsLock(ctx, taskName) {
			//s.log.Errorf("自动任务 [%s] 已被锁定", taskName)
			return
		}
		//上锁
		err = s.taskBiz.Lock(ctx, taskName)
		if err != nil {
			s.log.Errorf("自动任务 [%s] 加锁失败: %v", taskName, err)
			return
		}
		startTime := time.Now()
		err = task.Run(ctx)
		if err != nil {
			s.errHandler(ctx, err, task)
		}
		//解锁
		_ = s.taskBiz.UnLock(ctx, taskName)
		s.log.Infof("自动任务 [%s] 执行成功,启动时间：%s，耗时: %d秒", task.GetName(), startTime.Format("2006-01-02 15:04:05"), int(time.Since(startTime).Seconds()))
	})
	if err != nil {
		s.log.Errorf("自动任务 [%s] 注册失败: %v", taskName, err)
		return
	}
	s.taskIds[taskName] = taskId
	s.log.Infof("自动任务 [%s] 注册成功", taskName)
}

func (s *TaskServer) errHandler(ctx context.Context, err error, task TaskServiceInterface) {
	taskName := s.getTaskName(ctx, task)
	s.log.Errorf("自动任务 [%s] 执行失败: %v", taskName, err)
	//if taskId, ok := s.taskIds[taskName]; ok {
	//	s.job.Remove(taskId)
	//	delete(s.taskIds, taskName)
	//删除锁
	_ = s.taskBiz.UnLock(ctx, taskName)
	//}
}

func (s *TaskServer) getTaskName(ctx context.Context, task TaskServiceInterface) string {
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	return fmt.Sprintf("card-mall:task-lock:%s:%d", task.GetTaskName(), customerId)
}

func (s *TaskServer) deleteLock() {
	for taskName, _ := range s.taskIds {
		//删除锁
		_ = s.taskBiz.UnLock(context.Background(), taskName)
	}
}
