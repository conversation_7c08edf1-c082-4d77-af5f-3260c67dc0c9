package authtools

import (
	"context"

	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
)

// LoginContextOpts 登录用户信息
type LoginContextOpts struct {
	UserId     int
	Username   string // 使用account 弃用 表中的username
	AdminType  valobj.AdminTypeObj
	SupplierId int
	RoleId     int
	CustomerId int
	ShopId     int
	IsRoot     valobj.AdminIsRootObj
	SaasUser   *LoginContextSaasOpts
}

type LoginContextSaasOpts struct {
	UserId int
	Mobile string
}

// GetRoleId 获取角色ID
func (l *LoginContextOpts) GetRoleId() int {
	if l == nil {
		return 0
	}
	return l.RoleId
}

// GetUserId 获取用户ID
func (l *LoginContextOpts) GetUserId() int {
	if l == nil {
		return 0
	}
	return l.UserId
}
func (l *LoginContextOpts) GetUserName() string {
	if l == nil {
		return ""
	}
	return l.Username
}

func (l *LoginContextOpts) GetAdminType() valobj.AdminTypeObj {
	if l == nil {
		return valobj.AdminTypeObj(0)
	}
	return l.AdminType
}

func (l *LoginContextOpts) GetSupplierId() int {
	if l == nil {
		return 0
	}
	return l.SupplierId
}

func (l *LoginContextOpts) GetCustomerId() int {
	if l == nil {
		return 0
	}
	return l.CustomerId
}
func (l *LoginContextOpts) GetShopId() int {
	if l == nil {
		return 0
	}
	return l.ShopId
}

func (l *LoginContextOpts) GetSaasUserId() int {
	if l == nil {
		return 0
	}
	return l.SaasUser.UserId
}

func (l *LoginContextOpts) GetSaasMobile() string {
	if l == nil {
		return ""
	}
	return l.SaasUser.Mobile
}

// ToLoginInfo 转换为登录信息
func (l *LoginContextOpts) ToLoginInfo() *bo.AdminLoginInfoBo {
	return NewLoginInfoByOpts(l)
}

func NewLoginInfoByOpts(loginInfo *LoginContextOpts) *bo.AdminLoginInfoBo {
	return &bo.AdminLoginInfoBo{
		SupplierId: loginInfo.SupplierId,
		AdminType:  loginInfo.AdminType,
		OperaId:    loginInfo.UserId,
		OperaName:  loginInfo.Username,
		ShopId:     loginInfo.ShopId,
		CustomerId: loginInfo.CustomerId,
		IsRoot:     loginInfo.IsRoot,
	}
}

func GetLoginInfoX(ctx context.Context) *LoginContextOpts {
	loginOpts, err := GetLoginInfoWithError(ctx)
	if err != nil {
		panic(err)
	}
	return loginOpts
}

func GetLoginInfoWithError(ctx context.Context) (*LoginContextOpts, error) {
	val := GetLoginInfo(ctx)
	if val.GetUserId() <= 0 {
		return nil, apierr.ErrorDbNotFound("获取登录信息失败")
	}
	return val, nil
}
