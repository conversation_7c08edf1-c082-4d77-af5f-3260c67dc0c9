package authtools

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	pkgJwt "cardMall/internal/pkg/jwt"
	"context"
)

var loginContextOptsKey LoginContextOpts

// GetLoginInfo 获取用户信息
func GetLoginInfo(ctx context.Context) *LoginContextOpts {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	if !ok {
		return nil
	}
	return c
}

// GetLoginUserId 获取用户信息
func GetLoginUserId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetUserId()
}

// GetLoginAdminType 获取用户信息
func GetLoginAdminType(ctx context.Context) valobj.AdminTypeObj {
	return GetLoginInfo(ctx).GetAdminType()
}

// GetLoginUserName 获取用户信息
func GetLoginUserName(ctx context.Context) string {
	return GetLoginInfo(ctx).GetUserName()
}

// GetLoginRoleId 获取用户信息
func GetLoginRoleId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetRoleId()
}

// GetSupplierId 获取当前登录供应商ID
func GetSupplierId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetSupplierId()
}

// GetCustomerId 获取当前登录供应商ID
func GetCustomerId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetCustomerId()
}

// GetShopId 获取当前登录供应商ID
func GetShopId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetShopId()
}

// GetSaasUserId 获取当前登录saas用户ID
func GetSaasUserId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetSaasUserId()
}

// GetSaasUserMobile 获取当前登录saas用用户手机号
func GetSaasUserMobile(ctx context.Context) string {
	return GetLoginInfo(ctx).GetSaasMobile()
}

// WithLoginUserInfo 设置登录用户信息
func WithLoginUserInfo(ctx context.Context, adminDo *do.AdminDo, supplierIds ...int) context.Context {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	var supplierId int
	if len(supplierIds) > 0 {
		supplierId = supplierIds[0]
	}
	if !ok {
		return context.WithValue(ctx, loginContextOptsKey, &LoginContextOpts{
			UserId:     adminDo.Id,
			Username:   adminDo.Account,
			AdminType:  adminDo.Type,
			SupplierId: supplierId,
			RoleId:     adminDo.RoleID,
			CustomerId: adminDo.CustomerId,
			ShopId:     adminDo.ShopId,
			IsRoot:     adminDo.IsRoot,
		})
	}
	c.UserId = adminDo.Id
	c.Username = adminDo.Account
	c.AdminType = adminDo.Type
	c.SupplierId = supplierId
	c.RoleId = adminDo.RoleID
	c.CustomerId = adminDo.CustomerId
	c.ShopId = adminDo.ShopId
	c.IsRoot = adminDo.IsRoot
	return ctx
}

// WithShopInfo 设置商城信息
func WithShopInfo(ctx context.Context, shopId, customerId int) context.Context {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	if !ok {
		return context.WithValue(ctx, loginContextOptsKey, &LoginContextOpts{
			UserId:     0,
			Username:   "",
			AdminType:  0,
			SupplierId: 0,
			RoleId:     0,
			CustomerId: customerId,
			ShopId:     shopId,
		})
	}
	c.ShopId = shopId
	c.CustomerId = customerId
	return ctx
}

// WithSaasUserInfo 设置saas越级登录信息
func WithSaasUserInfo(ctx context.Context, adminClaims *pkgJwt.AdminClaims) context.Context {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	if !ok {
		return context.WithValue(ctx, loginContextOptsKey, &LoginContextOpts{
			UserId:     0,
			Username:   "",
			AdminType:  0,
			SupplierId: 0,
			RoleId:     0,
			CustomerId: 0,
			ShopId:     0,
		})
	}
	c.SaasUser = &LoginContextSaasOpts{
		Mobile: adminClaims.SaasUser.Mobile,
		UserId: adminClaims.SaasUser.UserId,
	}
	return ctx
}
