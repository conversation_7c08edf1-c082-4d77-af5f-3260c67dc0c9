package apiauthtools

import (
	"cardMall/internal/pkg/jwt"
	"context"
)

var loginContextOptsKey LoginContextOpts

// GetLoginInfo 获取用户信息
func GetLoginInfo(ctx context.Context) *LoginContextOpts {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	if !ok {
		return nil
	}
	return c
}

// GetLoginUserId 获取用户信息
func GetLoginUserId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetUserId()
}

// GetLoginUserName 获取用户信息
func GetLoginUserName(ctx context.Context) string {
	return GetLoginInfo(ctx).GetUserName()
}

// GetCustomerId 获取当前登录供应商ID
func GetCustomerId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetCustomerId()
}

// GetShopId 获取当前登录供应商ID
func GetShopId(ctx context.Context) int {
	return GetLoginInfo(ctx).GetShopId()
}

// WithLoginClaim 设置登录用户信息
func WithLoginClaim(ctx context.Context, claim *jwt.ClientClaims) context.Context {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	if !ok {
		return context.WithValue(ctx, loginContextOptsKey, &LoginContextOpts{
			UserId:      claim.GetId(),
			Username:    claim.NickName,
			WxOpenId:    claim.OpenId,
			AvatarUrl:   claim.Avatar,
			PhoneNumber: claim.PhoneNumber,
			ShopId:      claim.ShopId,
			CustomerId:  claim.CustomerId,
		})
	}
	c.UserId = claim.GetId()
	c.Username = claim.NickName
	c.WxOpenId = claim.OpenId
	c.AvatarUrl = claim.Avatar
	c.PhoneNumber = claim.PhoneNumber
	c.ShopId = claim.ShopId
	c.CustomerId = claim.CustomerId
	return ctx
}

// WithLoginShop 设置商城信息
func WithLoginShop(ctx context.Context, shopId, customerId int) context.Context {
	c, ok := ctx.Value(loginContextOptsKey).(*LoginContextOpts)
	if !ok {
		return context.WithValue(ctx, loginContextOptsKey, &LoginContextOpts{
			UserId:      0,
			Username:    "",
			WxOpenId:    "",
			AvatarUrl:   "",
			PhoneNumber: "",
			ShopId:      shopId,
			CustomerId:  customerId,
		})
	}
	c.ShopId = shopId
	c.CustomerId = customerId
	return ctx
}
