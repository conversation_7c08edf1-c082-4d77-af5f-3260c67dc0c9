package apiauthtools

import (
	"cardMall/internal/biz/bo"
)

// LoginContextOpts 登录用户信息
type LoginContextOpts struct {
	UserId      int
	Username    string
	WxOpenId    string
	AvatarUrl   string
	PhoneNumber string
	ShopId      int
	CustomerId  int
}

// GetUserId 获取用户ID
func (l *LoginContextOpts) GetUserId() int {
	if l == nil {
		return 0
	}
	return l.UserId
}
func (l *LoginContextOpts) GetUserName() string {
	if l == nil {
		return ""
	}
	return l.Username
}

func (l *LoginContextOpts) GetCustomerId() int {
	if l == nil {
		return 0
	}
	return l.CustomerId
}
func (l *LoginContextOpts) GetShopId() int {
	if l == nil {
		return 0
	}
	return l.ShopId
}

// ToApiLoginInfo 转换为登录信息
func (l *LoginContextOpts) ToApiLoginInfo() *bo.ApiLoginInfoBo {
	return NewApiLoginInfoByOpts(l)
}

func NewApiLoginInfoByOpts(loginInfo *LoginContextOpts) *bo.ApiLoginInfoBo {
	return &bo.ApiLoginInfoBo{
		UserId:      loginInfo.UserId,
		UserName:    loginInfo.Username,
		WxOpenId:    loginInfo.WxOpenId,
		AvatarUrl:   loginInfo.AvatarUrl,
		PhoneNumber: loginInfo.PhoneNumber,
		ShopId:      loginInfo.ShopId,
		CustomerId:  loginInfo.CustomerId,
	}
}
