package middleware

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/conf"
	"cardMall/internal/module/openapibiz"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"errors"
	errors2 "github.com/go-kratos/kratos/v2/errors"
	"io"
	"strconv"
	"strings"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type OpenApiMiddleware struct {
	c            *conf.Bootstrap
	hLog         *log.Helper
	checkSignBiz *openapibiz.CheckSignBiz
}

const openapiMchIdCtx = "mchid"

// GetOpenApiShopMchIdCtx 获取分销商ID
func GetOpenApiShopMchIdCtx(ctx context.Context) string {
	mchId := ctx.Value(openapiMchIdCtx)
	if mchId == nil {
		return ""
	}
	s, _ := mchId.(string)
	return s
}

func NewOpenApiMiddleware(c *conf.Bootstrap, hLog *log.Helper, checkSignBiz *openapibiz.CheckSignBiz) *OpenApiMiddleware {
	return &OpenApiMiddleware{c: c, hLog: hLog, checkSignBiz: checkSignBiz}
}
func (o *OpenApiMiddleware) HttpMiddleware() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			tr, isSContext := transport.FromServerContext(ctx)
			if !isSContext {
				return nil, apierr.ErrorNotAllow("请求错误，请检查请求是否符合标准的http协议")
			}
			ht, isTransport := tr.(*http.Transport)
			if !isTransport {
				return nil, apierr.ErrorNotAllow("请求错误，请检查请求是否符合标准的http协议")
			}
			// 获取要检查的参数
			openApiCheckSignBo, err := o.GetCheckParams(ht)
			if err != nil {
				return nil, err
			}
			// 设置ctx
			ctx = context.WithValue(ctx, openapiMchIdCtx, openApiCheckSignBo.MchId)
			customerId, shopId, err := isolationcustomer.DecodeC(openApiCheckSignBo.MchId)
			if err != nil {
				return nil, apierr.ErrorNotAllow("无效的mchid")
			}
			ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, customerId, shopId)

			if !o.isWhiteSign(openApiCheckSignBo.Signature) {
				// 检查时间
				timeout := int(o.c.OpenApi.GetTimestampDuration())
				if timeout == 0 {
					timeout = 30
				}
				nowUnix := int(time.Now().Unix())
				if openApiCheckSignBo.Timestamp < (nowUnix - timeout) {
					return nil, apierr.ErrorNotAllow("请求时间戳超时或大于当前时间")
				}

				// 检查签名
				err = o.checkSignBiz.Check(ctx, openApiCheckSignBo)
			}

			if err != nil {
				var errExcept *errors2.Error
				if errors.As(err, &errExcept) {
					return nil, errExcept
				}
				return nil, apierr.ErrorSystemError(err.Error())
			}
			return handler(ctx, req)
		}
	}
}

// isWhiteSign 检查是否白名单
func (o *OpenApiMiddleware) isWhiteSign(signature string) bool {
	return o.c.OpenApi.WhitelistSign != "" && o.c.OpenApi.WhitelistSign == signature
}

func (o *OpenApiMiddleware) GetCheckParams(ht *http.Transport) (*bo.OpenApiCheckSignBo, error) {
	// 检查Content-Type
	if ht.Request().Header.Get("Content-Type") != "application/json" {
		return nil, apierr.ErrorNotAllow("Content-Type必须是application/json")
	}

	authorization := ht.Request().Header.Get("Authorization")
	reqBody, err := io.ReadAll(ht.Request().Body)
	if err != nil {
		return nil, err
	}
	if authorization == "" {
		return nil, apierr.ErrorParamError("请求头Authorization不能为空")
	}

	algorithmName, signature, mchId, timestamp, err := o.parseAuthHeader(authorization)
	if err != nil {
		return nil, err
	}

	if algorithmName != "MD5" {
		return nil, apierr.ErrorParam("请求头Authorization的加密算法名称不支持，当前仅支持MD5")
	}

	if mchId == "" {
		return nil, apierr.ErrorParamError("请求头Authorization中的mchid不能为空")
	}
	if timestamp <= 0 {
		return nil, apierr.ErrorParamError("请求头Authorization中的timestampInt必须是大于0的整型")
	}

	if signature == "" {
		return nil, apierr.ErrorParamError("请求头Authorization中的signature不能为空")
	}
	urlPath := ht.PathTemplate()
	return &bo.OpenApiCheckSignBo{
		AlgorithmType: bo.OpenApiCheckSignAlgorithmTypeBoMd5,
		Path:          urlPath,
		Body:          string(reqBody),
		MchId:         mchId,
		Signature:     signature,
		Timestamp:     timestamp,
	}, nil
}

// parseAuthHeader 解析Authorization
func (o *OpenApiMiddleware) parseAuthHeader(auth string) (algorithmName, signature, mchId string, timestamp int, err error) {
	//Authorization: MD5 mchid=这里是mchId,timestamp=这里是timestamp,signature=这里是md5Str
	spaceSegments := strings.Split(auth, " ")
	if len(spaceSegments) != 2 {
		err = apierr.ErrorParamError("请求头Authorization的格式不正确，请查阅文档进行调整")
		return
	}
	algorithmName = strings.ToUpper(spaceSegments[0])
	if algorithmName == "" {
		err = apierr.ErrorParamError("请求头Authorization的加密算法名称不能为空")
		return
	}

	//todo:先兼容下带"号的
	spaceSegments[1] = strings.ReplaceAll(spaceSegments[1], `"`, "")

	items := strings.Split(spaceSegments[1], ",")
	if len(items) != 3 {
		err = apierr.ErrorParamError("请求头Authorization的格式不正确，请查阅文档进行调整")
		return
	}
	m := make(map[string]string, 3)
	for _, item := range items {
		kv := strings.Split(item, "=")
		k := kv[0]
		v := kv[1]
		m[k] = v
	}
	timestamp, _ = strconv.Atoi(m["timestamp"])
	signature = m["signature"]
	mchId = m["mchid"]
	return
}
