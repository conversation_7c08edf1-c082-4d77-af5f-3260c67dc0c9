package server

import (
	"cardMall/api/apierr"
	openapi2 "cardMall/api/openapi"
	"cardMall/api/ping"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/protovalidate"
	middleware2 "cardMall/internal/server/middleware"
	"cardMall/internal/service"
	"cardMall/internal/service/openapi"
	middlewares2 "codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/middlewares"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
	http2 "net/http"
)

type OpenApiHTTPServer struct {
	*http.Server
}

// NewOpenApiHTTPServer new an HTTP server.
func NewOpenApiHTTPServer(
	b *conf.Server,
	hLog *log.Helper,
	conf *conf.Bootstrap,
	openApiMiddleware *middleware2.OpenApiMiddleware,
	unionLoginService *openapi.UnionLoginService,
	orderService *openapi.OrderService,
	pingService *service.PingService,
) *OpenApiHTTPServer {
	var opts []http.ServerOption
	if b.Http.GetOpenapi().Network != "" {
		opts = append(opts, http.Network(b.Http.GetOpenapi().Network))
	}
	if b.Http.GetOpenapi().Addr != "" {
		opts = append(opts, http.Address(b.Http.GetOpenapi().Addr))
	}
	if b.Http.GetOpenapi().Timeout != nil {
		opts = append(opts, http.Timeout(b.Http.GetOpenapi().Timeout.AsDuration()))
	}

	// 允许跨域
	filters := []http.FilterFunc{responseCROSHandler, handlers.CORS(
		// 域名配置
		handlers.AllowedOrigins([]string{"*"}),
		handlers.AllowedHeaders([]string{constants.AdminAuthHeaderKey, constants.AdminMallHeaderKey, "Content-Type", "Accept", "Referer", "User-Agent", "X-Requested-With", "X-Request-Id"}),
		handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS", "DELETE"}),
	)}
	opts = append(opts, http.Filter(filters...))

	middlewares := make([]middleware.Middleware, 0)
	middlewares = append(
		middlewares,
		copyContext(),
		middlewares2.AddHeaderServer(),
		openApiMiddleware.HttpMiddleware(),
		// validate.Validator(),
		protovalidate.Validator(),
	)
	//拦截恢复异常
	middlewares = append(middlewares, recovery.Recovery(recovery.WithHandler(func(ctx context.Context, req, err interface{}) error {
		// 做一些panic处理
		newErr, isOk := err.(*errors.Error)
		if isOk {
			// 记录panic
			// 如果是自己定义的类型，则直接抛出去，目的是支持 panic(myErr) 类型的写法，提升开发速度
			return newErr
		}
		return fmt.Errorf("系统Panic了：%+v", err)
	})))
	opts = append(opts, http.Middleware(middlewares...), http.ErrorEncoder(newErrorEncoderForOpenAPI(hLog)))

	srv := http.NewServer(opts...)

	ping.RegisterPingHTTPServer(srv, pingService)
	openapi2.RegisterUnionLoginHTTPServer(srv, unionLoginService)
	openapi2.RegisterOrderServiceHTTPServer(srv, orderService)

	if helper.IsDev(conf.Server.GetEnv()) {
		srv.HandlePrefix("/openapi/", http2.StripPrefix("/openapi/", http2.FileServer(http2.Dir("./sources/openapi/"))))
	}
	return &OpenApiHTTPServer{Server: srv}
}

// newErrorEncoderForOpenAPI 错误
func newErrorEncoderForOpenAPI(hLog *log.Helper) http.EncodeErrorFunc {
	return func(w http.ResponseWriter, r *http.Request, err error) {
		if se := new(errors.Error); errors.As(err, &se) {
			if se.Reason == "CODEC" {
				err = apierr.ErrorParamError("请求参数无效，请检查参数类型或JSON格式是否正确")
			} else if apierr.IsSystemError(err) || apierr.IsException(err) || apierr.IsSystemPanic(err) {
				// 这个日志报错
				hLog.Errorf("系统异常：%+v", err)
				if !apierr.IsSystemError(err) {
					err = apierr.ErrorSystemError("系统内部错误，请重试")
				}
			}
		} else {
			hLog.Errorf("非预定定义错误：error: %+v", err)
			err = apierr.ErrorSystemError("系统内部错误，请重试")
		}
		http.DefaultErrorEncoder(w, r, err)
	}
}
