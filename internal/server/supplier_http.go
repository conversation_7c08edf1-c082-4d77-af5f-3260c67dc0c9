package server

import (
	"cardMall/api/ping"
	"cardMall/api/supplierv1"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	middleware2 "cardMall/internal/server/middleware"
	"cardMall/internal/service"
	"cardMall/internal/service/supplier"
	middlewares2 "codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/middlewares"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/middleware/recovery"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/gorilla/handlers"
	http2 "net/http"
)

type SupplierHTTPServer struct {
	*http.Server
}

// NewSupplierHTTPServer new an HTTP server.
func NewSupplierHTTPServer(
	b *conf.Server,
	hLog *log.Helper,
	conf *conf.<PERSON>tra<PERSON>,
	adminAuthMiddleware *middleware2.AdminAuthMiddleware,

	goodsService *supplier.GoodsService,
	specService *supplier.SpecService,
	orderService *supplier.OrderService,
	areaService *supplier.AreaService,
	transportService *supplier.TransportService,
	dashboardService *supplier.DashboardService,
	baseGoodsService *supplier.BaseGoodsService,
	afterSaleService *supplier.AfterSaleService,
	platformSupplierService *supplier.PlatformSupplierService,
	siteService *supplier.SiteService,
	fileUploadService *supplier.FileUploadService,
	hytService *supplier.HytService,
	hytNotifyService *supplier.HytNotifyService,
	pingService *service.PingService,
) *SupplierHTTPServer {
	opts := make([]http.ServerOption, 0, 20)
	if b.Http.GetSupplier().Network != "" {
		opts = append(opts, http.Network(b.Http.GetSupplier().Network))
	}
	if b.Http.GetSupplier().Addr != "" {
		opts = append(opts, http.Address(b.Http.GetSupplier().Addr))
	}
	if b.Http.GetSupplier().Timeout != nil {
		opts = append(opts, http.Timeout(b.Http.GetSupplier().Timeout.AsDuration()))
	}

	// 允许跨域
	filters := []http.FilterFunc{responseCROSHandler, handlers.CORS(
		// 域名配置
		handlers.AllowedOrigins([]string{"*"}),
		handlers.AllowedHeaders([]string{constants.AdminAuthHeaderKey, constants.AdminMallHeaderKey, "Content-Type", "Accept", "Referer", "User-Agent", "X-Requested-With", "X-Request-Id"}),
		handlers.AllowedMethods([]string{"GET", "POST", "PUT", "HEAD", "OPTIONS", "DELETE"}),
	)}

	//拦截恢复异常
	middlewares := make([]middleware.Middleware, 0)

	middlewares = append(
		middlewares,
		copyContext(),
		recovery.Recovery(),
		middlewares2.AddHeaderServer(),
		adminAuthMiddleware.HttpMiddleware(),
	)
	opts = append(opts, http.Middleware(middlewares...))

	//统一返回结构
	opts = append(opts, http.ResponseEncoder(newResponseEncoder(hLog, conf)))
	opts = append(opts, http.ErrorEncoder(newErrorEncoder(hLog, conf)))

	opts = append(opts, http.Filter(filters...))

	srv := http.NewServer(opts...)

	ping.RegisterPingHTTPServer(srv, pingService)
	supplierv1.RegisterGoodsSrvHTTPServer(srv, goodsService)
	supplierv1.RegisterSpecSrvHTTPServer(srv, specService)
	supplierv1.RegisterOrderSrvHTTPServer(srv, orderService)
	supplierv1.RegisterAreaSrvHTTPServer(srv, areaService)
	supplierv1.RegisterTransportSrvHTTPServer(srv, transportService)
	supplierv1.RegisterDashboardSrvHTTPServer(srv, dashboardService)
	supplierv1.RegisterBaseGoodsHTTPServer(srv, baseGoodsService)
	supplierv1.RegisterAfterSaleSrvHTTPServer(srv, afterSaleService)
	supplierv1.RegisterPlatformSupplierSrvHTTPServer(srv, platformSupplierService)
	supplierv1.RegisterSiteHTTPServer(srv, siteService)
	supplierv1.RegisterFileUploadHTTPServer(srv, fileUploadService)
	supplierv1.RegisterHytSrvHTTPServer(srv, hytService)

	adminRouter := srv.Route("/supplier")
	adminRouter.GET("/v1/goods/export", authHandler(goodsService.ExportGoods))
	adminRouter.GET("/v1/order/export", authHandler(orderService.ExportOrder))

	adminRouter.GET("/v1/sync", hytService.Sync)
	adminRouter.GET("/v1/syncDealAfterSale", hytService.DealAfterSale)

	adminRouter.POST("/v1/hyt/sendOrderFinish", hytNotifyService.DeliverFinishNotify)
	adminRouter.POST("/v1/hyt/finishOrderAfter", hytNotifyService.FinishOrderAfterNotify)
	adminRouter.POST("/v1/hyt/handleOrderAfter", hytNotifyService.HandleOrderAfter)
	adminRouter.POST("/v1/hyt/goodsStatus", hytNotifyService.GoodsStatusNotify)
	adminRouter.POST("/v1/hyt/goodsUpdate", hytNotifyService.GoodsUpdateNotify)
	adminRouter.POST("/v1/hyt/orderClose", hytNotifyService.OrderCloseNotify)

	if helper.IsDev(conf.Server.GetEnv()) {
		srv.HandlePrefix("/supplier/", http2.StripPrefix("/supplier/", http2.FileServer(http2.Dir("./sources/supplier/"))))
	}

	return &SupplierHTTPServer{Server: srv}
}
