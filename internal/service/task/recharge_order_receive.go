package task

import (
	"cardMall/internal/biz/valobj"
	taskbiz2 "cardMall/internal/module/taskbiz"
	"cardMall/internal/module/taskbiz/vo"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
)

type RechargeOrderReceiveService struct {
	biz     *taskbiz2.OrderBiz
	taskBiz *taskbiz2.TaskBiz
	log     *log.Helper
}

func NewRechargeOrderReceiveService(biz *taskbiz2.OrderBiz, taskBiz *taskbiz2.TaskBiz, log *log.Helper) *RechargeOrderReceiveService {
	return &RechargeOrderReceiveService{
		biz:     biz,
		taskBiz: taskBiz,
		log:     log,
	}
}

func (r *RechargeOrderReceiveService) Run(ctx context.Context) error {
	return r.biz.RechargeReceived(ctx, valobj.PayOrderReceiveTime)
}

func (r *RechargeOrderReceiveService) GetCommend(ctx context.Context) (string, error) {
	task, _ := r.taskBiz.FindByTaskName(ctx, r.GetTaskName())
	if task == nil {
		return "", errors.New("任务不存在")
	}
	if task.Status != vo.TaskStatusAble {
		return "", nil
	}
	return task.TaskCommend, nil
}

func (r *RechargeOrderReceiveService) GetName() string {
	return "直充订单自动收货"
}

func (r *RechargeOrderReceiveService) GetTaskName() string {
	return "RechargeOrderReceive"
}
