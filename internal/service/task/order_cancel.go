package task

import (
	"cardMall/internal/biz/valobj"
	taskbiz2 "cardMall/internal/module/taskbiz"
	"cardMall/internal/module/taskbiz/vo"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
)

type OrderCancelService struct {
	biz     *taskbiz2.OrderBiz
	taskBiz *taskbiz2.TaskBiz
	log     *log.Helper
}

func NewOrderCancelService(biz *taskbiz2.OrderBiz, taskBiz *taskbiz2.TaskBiz, log *log.Helper) *OrderCancelService {
	return &OrderCancelService{
		biz:     biz,
		taskBiz: taskBiz,
		log:     log,
	}
}

func (o *OrderCancelService) Run(ctx context.Context) error {
	return o.biz.Cancel(ctx, valobj.PayOrderExpireTime)
}

func (o *OrderCancelService) GetCommend(ctx context.Context) (string, error) {
	task, _ := o.taskBiz.FindByTaskName(ctx, o.GetTaskName())
	if task == nil {
		return "", errors.New("任务不存在")
	}
	if task.Status != vo.TaskStatusAble {
		return "", nil
	}
	return task.TaskCommend, nil
}

func (o *OrderCancelService) GetName() string {
	return "待支付订单超时取消"
}

func (o *OrderCancelService) GetTaskName() string {
	return "OrderCancel"
}
