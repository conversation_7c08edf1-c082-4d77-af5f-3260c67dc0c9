package task

import (
	"cardMall/internal/conf"
	"cardMall/internal/manager"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/taskbiz"
	"cardMall/internal/module/taskbiz/vo"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	errors2 "github.com/pkg/errors"
)

type BaseGoodsRefreshService struct {
	customerResellerBiz       *taskbiz.CustomerResellerBiz
	baseGoodsBiz              *adminbiz.BaseGoodsBiz
	taskBiz                   *taskbiz.TaskBiz
	conf                      *conf.Bootstrap
	log                       *log.Helper
	goodsPriceAutoSyncManager *manager.GoodsPriceAutoSyncManager
}

func NewBaseGoodsRefreshService(customerResellerBiz *taskbiz.CustomerResellerBiz, baseGoodsBiz *adminbiz.BaseGoodsBiz, taskBiz *taskbiz.TaskBiz, conf *conf.Bootstrap, log *log.Helper, goodsPriceAutoSyncManager *manager.GoodsPriceAutoSyncManager) *BaseGoodsRefreshService {
	return &BaseGoodsRefreshService{customerResellerBiz: customerResellerBiz, baseGoodsBiz: baseGoodsBiz, taskBiz: taskBiz, conf: conf, log: log, goodsPriceAutoSyncManager: goodsPriceAutoSyncManager}
}

func (b *BaseGoodsRefreshService) Run(ctx context.Context) error {
	ctx = isolationcustomer.WithCustomerAndDisableShopCtx(ctx, isolationcustomer.SaasPlatformCustomer)
	// 接取授权商品
	changeSupplierSkuDos, err := b.baseGoodsBiz.PullV2(ctx, &bo.BaseGoodsPullBo{
		IsProd: b.conf.GetRecharge().GetIsProd(),
		Time:   b.conf.GetRecharge().GetTimeout().AsDuration(),
		ApiUrl: b.conf.GetRecharge().GetApiUrl(),
	})
	if err != nil {
		return errors2.WithMessage(err, "自动更新官方虚拟商品失败")
	}
	if len(changeSupplierSkuDos) > 0 {
		for _, sku := range changeSupplierSkuDos {
			b.log.Infof("自动更新官方虚拟商品成功，即将联动修改变更，商品skuId:%d,名称：%s,供应价格：%.4f", sku.ID, sku.Name, sku.SupplierPrice)
		}
		// 联动处理
		err = b.goodsPriceAutoSyncManager.OfficialSupplierSync(ctx, changeSupplierSkuDos, true)
		if err != nil {
			return errors2.WithMessage(err, "联动处理价格变更时异常")
		}
	}
	return nil
}

func (b *BaseGoodsRefreshService) GetCommend(ctx context.Context) (string, error) {
	task, _ := b.taskBiz.FindByTaskName(ctx, b.GetTaskName())
	if task == nil {
		return "", errors.New("任务不存在")
	}
	if task.Status != vo.TaskStatusAble {
		return "", nil
	}
	return task.TaskCommend, nil
}

func (b *BaseGoodsRefreshService) GetName() string {
	return "分销商刷新授权商品与成本价"
}

func (b *BaseGoodsRefreshService) GetTaskName() string {
	return "BaseGoodsRefresh"
}
