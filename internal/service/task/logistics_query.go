package task

import (
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/taskbiz"
	"cardMall/internal/module/taskbiz/vo"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
)

type LogisticsQueryService struct {
	biz     *appbiz.OrderLogisticsBiz
	taskBiz *taskbiz.TaskBiz
	log     *log.Helper
}

func NewLogisticsQueryService(biz *appbiz.OrderLogisticsBiz, taskBiz *taskbiz.TaskBiz, log *log.Helper) *LogisticsQueryService {
	return &LogisticsQueryService{
		biz:     biz,
		taskBiz: taskBiz,
		log:     log,
	}
}

func (l *LogisticsQueryService) Run(ctx context.Context) error {
	return l.biz.Query(ctx)
}

func (l *LogisticsQueryService) GetCommend(ctx context.Context) (string, error) {
	task, _ := l.taskBiz.FindByTaskName(ctx, l.GetTaskName())
	if task == nil {
		return "", errors.New("任务不存在")
	}
	if task.Status != vo.TaskStatusAble {
		return "", nil
	}
	return task.TaskCommend, nil
}

func (l *LogisticsQueryService) GetName() string {
	return "订单物流查询"
}

func (l *LogisticsQueryService) GetTaskName() string {
	return "LogisticsQueryService"
}
