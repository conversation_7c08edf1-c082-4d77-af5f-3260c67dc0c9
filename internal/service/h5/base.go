package h5

import (
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/server/middleware/apiauthtools"
	"cardMall/internal/service"
	"context"
)

type base struct {
	service.ApiBase
}

func (b *base) UserInfo(ctx context.Context) (*do.UserDo, error) {
	logInfo := apiauthtools.GetLoginInfo(ctx).ToApiLoginInfo()
	return &do.UserDo{
		Id:          logInfo.UserId,
		WxOpenId:    logInfo.WxOpenId,
		NickName:    logInfo.UserName,
		AvatarUrl:   logInfo.AvatarUrl,
		PhoneNumber: logInfo.PhoneNumber,
	}, nil
}
