package h5

import (
	"cardMall/api/apierr"
	"cardMall/api/h5"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/h5biz"
	"cardMall/internal/module/h5biz/bo"
	"context"
)

type PayService struct {
	h5.UnimplementedPayServer
	biz *h5biz.PayBiz
}

func NewPayService(biz *h5biz.PayBiz) *PayService {
	return &PayService{biz: biz}
}

func (p *PayService) PrePay(ctx context.Context, req *h5.PrePayReq) (*h5.PrePayRsp, error) {
	in := &bo.PrePayBo{
		OrderNumber: req.OrderNumber,
		PayType:     valobj.PayOrderPayTypeObj(req.PayType),
	}
	res, err := p.biz.PrePay(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("支付失败:%s", err)
	}
	return &h5.PrePayRsp{PayInfo: res.PayInfo}, nil
}
