package supplier

import (
	"cardMall/api/supplierv1"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/data/ent/order"
	"cardMall/internal/data/ent/orderreturnaddress"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/pkg/jwt"
	"cardMall/internal/service"
	commonconvertor "cardMall/internal/service/common/convertor"
	"cardMall/internal/service/supplier/convertor"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

type AfterSaleService struct {
	service.AdminBase
	supplierv1.UnimplementedAfterSaleSrvServer

	log                   *log.Helper
	j                     *jwt.JWT
	afterSaleBiz          *supplierbiz.AfterSaleBiz
	orderReturnAddressBiz *supplierbiz.OrderReturnAddressBiz
	afterSaleConvertor    *convertor.AfterSaleConvertor
	pageConvertor         *commonconvertor.PageConvertor
}

func NewAfterSaleService(log *log.Helper, j *jwt.JWT, afterSaleBiz *supplierbiz.AfterSaleBiz, orderReturnAddressBiz *supplierbiz.OrderReturnAddressBiz, afterSaleConvertor *convertor.AfterSaleConvertor, pageConvertor *commonconvertor.PageConvertor) *AfterSaleService {
	return &AfterSaleService{log: log, j: j, afterSaleBiz: afterSaleBiz, orderReturnAddressBiz: orderReturnAddressBiz, afterSaleConvertor: afterSaleConvertor, pageConvertor: pageConvertor}
}

func (a *AfterSaleService) List(ctx context.Context, req *supplierv1.AfterSaleListReq) (*supplierv1.AfterSaleListResp, error) {
	var reqBo *commonbo.OrderAfterSaleSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	if len(req.Date) == 2 {
		start, err := helper.DateStringToTime(req.Date[0])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateStart = int(start.Unix())
		end, err := helper.DateStringToTime(req.Date[1])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateEnd = int(end.Unix())
	}

	reqBo.SupplierId = a.GetSupplierIdX(ctx)
	reqBo.SortData = []*commonbo.CommonSortBo{
		{Asc: false, Field: order.FieldID},
	}
	reqBo.Edges = &commonbo.OrderAfterSaleEdgesBo{
		WithOrderAfterSaleGoods: true,
		WithOrderAfterSaleLog:   true,
	}

	dos, pageInfo := a.afterSaleBiz.SearchList(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	list := make([]*supplierv1.AfterSaleData, 0, len(dos))
	var tmpData *supplierv1.AfterSaleData
	for _, itemDo := range dos {
		tmpData = &supplierv1.AfterSaleData{
			Id:                  int32(itemDo.ID),
			OrderNumber:         itemDo.OrderNumber,
			AfterSaleType:       int32(itemDo.Type),
			AfterSaleTypeText:   itemDo.Type.String(),
			ApplyTime:           helper.GetTimeDate(itemDo.CreateTime, helper.DateFormat),
			AfterSaleStatus:     int32(itemDo.Status),
			AfterSaleStatusText: itemDo.GetStatusText(),
			AfterSaleReason:     itemDo.Reason,
			AfterSaleImages:     itemDo.GetImages(),
			RefundAmount:        itemDo.RefundAmount,
			AfterSaleNo:         itemDo.AfterSaleNo,
			Refund: &supplierv1.AfterSaleData_RefundDetail{
				GoodsAmount: itemDo.RefundGoodsAmount,
				FreightFee:  itemDo.RefundFreightFee,
			},
			AfterSalePlatformStatus:     int32(itemDo.PlatformStatus),
			AfterSalePlatformStatusText: itemDo.PlatformStatus.GetSupplierText(),
			RefuseReason:                itemDo.GetPlatformRefuseReason(),
			//RefundCardGiftAmount: itemDo.RefundCardGiftAmount,
			RefundCardGiftFreightFee: itemDo.RefundCardGiftFreightFee,
			//Goods:               nil,
			//ExchangeGoods:       nil,
			//RefundGoodsAmount:   0,
			//ExchangeGoodsAmount: 0,
		}
		tmpData.Goods, tmpData.RefundGoodsAmount = a.afterSaleConvertor.TransferReturnGoodsItems(itemDo)
		tmpData.ExchangeGoods, tmpData.ExchangeGoodsAmount = a.afterSaleConvertor.TransferExchangeGoodsItems(itemDo)
		list = append(list, tmpData)
	}

	return &supplierv1.AfterSaleListResp{
		List: list,
		Page: a.pageConvertor.ToRespPage(pageInfo),
	}, nil
}

func (a *AfterSaleService) Detail(ctx context.Context, req *supplierv1.AfterSaleDetailReq) (*supplierv1.AfterSaleDetailResp, error) {
	var (
		itemDo *do.OrderAfterSaleDo
		err    error
	)
	if a.GetLoginInfoX(ctx).AdminType.IsSaasSupplier() {
		itemDo, err = a.afterSaleBiz.GetWithEdgesBySaas(isolationcustomer.WithDisableShopCtx(ctx), int(req.Id), req.OrderNo, &commonbo.OrderAfterSaleEdgesBo{
			WithOrderAfterSaleDeliver: true,
			WithOrderAfterSaleGoods:   true,
			WithOrderAfterSaleLog:     true,
		})
	} else {
		itemDo, err = a.afterSaleBiz.GetWithEdges(isolationcustomer.WithDisableShopCtx(ctx), int(req.Id), &commonbo.OrderAfterSaleEdgesBo{
			WithOrderAfterSaleDeliver: true,
			WithOrderAfterSaleGoods:   true,
			WithOrderAfterSaleLog:     true,
		})
	}

	if err != nil {
		return nil, err
	}

	goodsRsp := make([]*supplierv1.AfterSaleGoods, 0, len(itemDo.OrderAfterSaleGoods))
	for _, goodsItem := range itemDo.OrderAfterSaleGoods {
		goodsRsp = append(goodsRsp, &supplierv1.AfterSaleGoods{
			SkuNo:     goodsItem.SkuNo,
			GoodsName: goodsItem.GoodsName,
			GoodsNum:  int32(goodsItem.GoodsNum),
		})
	}
	err = a.afterSaleBiz.AfterSaleOrderGoods(ctx, []*do.OrderAfterSaleDo{itemDo})
	if err != nil {
		return nil, err
	}
	rsp := &supplierv1.AfterSaleDetailResp{
		Id:                  int32(itemDo.ID),
		OrderNumber:         itemDo.OrderNumber,
		AfterSaleType:       int32(itemDo.Type),
		AfterSaleTypeText:   itemDo.Type.String(),
		ApplyTime:           helper.GetTimeDate(itemDo.CreateTime, helper.DateFormat),
		AfterSaleStatus:     int32(itemDo.Status),
		AfterSaleStatusText: itemDo.GetStatusText(),
		AfterSaleReason:     itemDo.Reason,
		AfterSaleImages:     itemDo.GetImages(),
		Log:                 a.afterSaleConvertor.TransferLogItems(itemDo),
		BuyerDeliver:        a.afterSaleConvertor.TransferDeliverInfo(itemDo.GetBuyerDeliver()),
		ExchangeOrderNo:     itemDo.ExchangeOrderNo,
		RefundAmount:        itemDo.RefundAmount,
		AfterSaleNo:         itemDo.AfterSaleNo,
		AfterSaleRemark:     itemDo.Remark,
		ReceiveStatus:       int32(itemDo.ReceiveStatus),
		ReceiveStatusText:   itemDo.ReceiveStatus.String(),
		Refund: &supplierv1.AfterSaleDetailResp_RefundDetail{
			GoodsAmount: itemDo.RefundGoodsAmount,
			FreightFee:  itemDo.RefundFreightFee,
		},
		RefuseReason: itemDo.GetPlatformRefuseReason(),
		//RefundCardGiftAmount: itemDo.RefundCardGiftAmount,
		RefundCardGiftFreightFee: itemDo.RefundCardGiftFreightFee,
		//Goods:               nil,
		//ExchangeGoods:       nil,
		//RefundGoodsAmount:   0,
		//ExchangeGoodsAmount: 0,
	}
	rsp.Goods, rsp.RefundGoodsAmount = a.afterSaleConvertor.TransferReturnGoodsItems(itemDo)
	rsp.ExchangeGoods, rsp.ExchangeGoodsAmount = a.afterSaleConvertor.TransferExchangeGoodsItems(itemDo)

	return rsp, nil
}

func (a *AfterSaleService) getAfterSaleOperaBo(ctx context.Context, id int) *bo.AfterSaleOperaBo {
	return &bo.AfterSaleOperaBo{
		Id:               id,
		AdminLoginInfoBo: a.GetLoginInfoX(ctx).ToLoginInfo(),
	}
}

func (a *AfterSaleService) Approve(ctx context.Context, req *supplierv1.AfterSaleApproveReq) (*supplierv1.AfterSaleApproveResp, error) {
	var reqBo *bo.AfterSaleApproveBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if err := reqBo.Validate(); err != nil {
		return nil, err
	}
	if reqBo.AdminType.IsSaasSupplier() {
		if err := reqBo.CheckOrderNo(); err != nil {
			return nil, err
		}
	}
	err := a.afterSaleBiz.Approve(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleApproveResp{}, nil
}

func (a *AfterSaleService) Refund(ctx context.Context, req *supplierv1.AfterSaleRefundReq) (*supplierv1.AfterSaleRefundResp, error) {
	var reqBo *bo.AfterSaleRefundBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if reqBo.AdminType.IsSaasSupplier() {
		if err := reqBo.CheckOrderNo(); err != nil {
			return nil, err
		}
	}

	err := a.afterSaleBiz.Refund(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleRefundResp{}, nil
}

func (a *AfterSaleService) SetReceiveAddress(ctx context.Context, req *supplierv1.AfterSaleSetReceiveAddressReq) (*supplierv1.AfterSaleSetReceiveAddressResp, error) {
	var reqBo *bo.AfterSaleSetReceiveAddressBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if reqBo.AdminType.IsSaasSupplier() {
		if err := reqBo.CheckOrderNo(); err != nil {
			return nil, err
		}
	}

	err := a.afterSaleBiz.SetReceiveAddress(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleSetReceiveAddressResp{}, nil
}

func (a *AfterSaleService) Refuse(ctx context.Context, req *supplierv1.AfterSaleRefuseReq) (*supplierv1.AfterSaleRefuseResp, error) {
	var reqBo *bo.AfterSaleRefuseBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if reqBo.AdminType.IsSaasSupplier() {
		if err := reqBo.CheckOrderNo(); err != nil {
			return nil, err
		}
	}

	if err := reqBo.Validate(); err != nil {
		return nil, err
	}
	err := a.afterSaleBiz.Refuse(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleRefuseResp{}, nil
}

// Receive 确认收货
func (a *AfterSaleService) Receive(ctx context.Context, req *supplierv1.AfterSaleReceiveReq) (*supplierv1.AfterSaleReceiveResp, error) {
	var reqBo *bo.AfterSaleConfirmReceiveBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if reqBo.AdminType.IsSaasSupplier() {
		if err := reqBo.CheckOrderNo(); err != nil {
			return nil, err
		}
	}

	err := a.afterSaleBiz.ConfirmReceive(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleReceiveResp{}, nil
}

func (a *AfterSaleService) Deliver(ctx context.Context, req *supplierv1.AfterSaleDeliverReq) (*supplierv1.AfterSaleDeliverResp, error) {
	var reqBo *bo.AfterSaleDeliverBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if err := a.afterSaleBiz.Deliver(ctx, reqBo); err != nil {
		return nil, err
	}

	return &supplierv1.AfterSaleDeliverResp{}, nil
}

func (a *AfterSaleService) CreateExchangeOrder(ctx context.Context, req *supplierv1.CreateExchangeOrderReq) (*supplierv1.CreateExchangeOrderResp, error) {
	reqBo := &bo.CreateExchangeOrderBo{
		AfterSaleOperaBo: &bo.AfterSaleOperaBo{},
	}
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	reqBo.OrderNo = req.OrderNo
	if reqBo.AdminType.IsSaasSupplier() {
		if err := reqBo.CheckOrderNo(); err != nil {
			return nil, err
		}
	}

	exchangeOrderNumber, err := a.afterSaleBiz.CreateExchangeOrder(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.CreateExchangeOrderResp{
		OrderNumber: exchangeOrderNumber,
	}, nil
}

func (a *AfterSaleService) AddressList(ctx context.Context, req *supplierv1.AfterSaleAddressListReq) (*supplierv1.AfterSaleAddressListResp, error) {
	var reqBo *commonbo.OrderReturnAddressSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AdminLoginInfoBo = a.GetLoginInfoX(ctx).ToLoginInfo()
	reqBo.SupplierId = a.GetSupplierIdX(ctx)

	reqBo.SortData = append(reqBo.SortData, &commonbo.CommonSortBo{
		Field: orderreturnaddress.FieldID, Asc: false,
	})

	dos, pageInfo := a.orderReturnAddressBiz.SearchList(ctx, reqBo)
	list := make([]*supplierv1.AfterSaleAddressData, 0, len(dos))
	for _, itemDo := range dos {
		list = append(list, &supplierv1.AfterSaleAddressData{
			Id:           int32(itemDo.ID),
			ContactName:  itemDo.ContactName,
			ContactPhone: itemDo.ContactPhone,
			Address:      itemDo.Address,
			CreateTime:   helper.GetTimeDate(itemDo.CreateTime),
			Title:        itemDo.Title,
		})
	}

	return &supplierv1.AfterSaleAddressListResp{
		List: list,
		Page: a.pageConvertor.ToRespPage(pageInfo),
	}, nil
}

func (a *AfterSaleService) AddressDetail(ctx context.Context, req *supplierv1.AfterSaleAddressDetailReq) (*supplierv1.AfterSaleAddressData, error) {
	itemDo, err := a.orderReturnAddressBiz.Get(ctx, int(req.Id), a.GetSupplierIdX(ctx))
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleAddressData{
		Id:           int32(itemDo.ID),
		ContactName:  itemDo.ContactName,
		ContactPhone: itemDo.ContactPhone,
		Address:      itemDo.Address,
		CreateTime:   helper.GetTimeDate(itemDo.CreateTime),
		Title:        itemDo.Title,
	}, nil
}

func (a *AfterSaleService) AddressSave(ctx context.Context, data *supplierv1.AfterSaleAddressData) (*supplierv1.AfterSaleAddressSaveResp, error) {
	var reqBo *bo.OrderReturnAddressSaveBo
	_ = mapstructure.Decode(data, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(data.Id))
	reqBo.SupplierId = a.GetSupplierIdX(ctx)

	if err := reqBo.Validate(); err != nil {
		return nil, err
	}

	err := a.orderReturnAddressBiz.Save(ctx, reqBo)
	if err != nil {
		return nil, err
	}

	return &supplierv1.AfterSaleAddressSaveResp{}, nil
}

func (a *AfterSaleService) AddressDelete(ctx context.Context, req *supplierv1.AfterSaleAddressDeleteReq) (*supplierv1.AfterSaleAddressDeleteResp, error) {
	err := a.orderReturnAddressBiz.Delete(ctx, a.GetSupplierIdX(ctx), int(req.Id))
	if err != nil {
		return nil, err
	}
	return &supplierv1.AfterSaleAddressDeleteResp{}, nil
}
