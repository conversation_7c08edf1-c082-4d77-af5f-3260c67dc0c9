package supplier

import (
	"cardMall/api/apierr"
	"cardMall/api/supplierv1"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/data/ent/order"
	"cardMall/internal/data/ent/orderdeliver"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/service"
	commonconvertor "cardMall/internal/service/common/convertor"
	"cardMall/internal/service/supplier/convertor"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

type PlatformSupplierService struct {
	service.AdminBase
	supplierv1.UnimplementedPlatformSupplierSrvServer

	log                 *log.Helper
	platformSupplierBiz *supplierbiz.PlatformSupplierBiz
	pageConvertor       *commonconvertor.PageConvertor
	orderConvertor      *convertor.OrderConvertor
	afterSaleConvertor  *convertor.AfterSaleConvertor
}

func NewPlatformSupplierService(log *log.Helper, platformSupplierBiz *supplierbiz.PlatformSupplierBiz, pageConvertor *commonconvertor.PageConvertor, orderConvertor *convertor.OrderConvertor, afterSaleConvertor *convertor.AfterSaleConvertor) *PlatformSupplierService {
	return &PlatformSupplierService{log: log, platformSupplierBiz: platformSupplierBiz, pageConvertor: pageConvertor, orderConvertor: orderConvertor, afterSaleConvertor: afterSaleConvertor}
}

func (o *PlatformSupplierService) PlatformOrderList(ctx context.Context, req *supplierv1.OrderListReq) (*supplierv1.OrderListResp, error) {
	if !o.GetLoginInfoX(ctx).AdminType.IsSaasSupplier() {
		return nil, apierr.ErrorNotAllow("非saas供应商，没有权限")
	}
	var reqBo *commonbo.OrderSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	if len(req.OrderDate) == 2 {
		start, err := helper.DateStringToTime(req.OrderDate[0])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateStart = int(start.Unix())
		end, err := helper.DateStringToTime(req.OrderDate[1])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateEnd = int(end.Unix())
	}
	reqBo.Edges = &commonbo.OrderWithEdgeBo{
		WithGoods:       true,
		WithUserAddress: true,
		WithCardGift:    true,
	}
	reqBo.OnlyEntity = true
	reqBo.SupplierId = o.GetSupplierIdX(ctx)
	reqBo.SortData = []*commonbo.CommonSortBo{
		{Asc: false, Field: order.FieldID},
	}

	orderDos, pageInfo, err := o.platformSupplierBiz.OrderList(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	var list []*supplierv1.OrderData
	for _, orderDo := range orderDos {
		list = append(list, o.orderConvertor.ToOrderData(orderDo))
	}

	return &supplierv1.OrderListResp{
		List: list,
		Page: o.pageConvertor.ToRespPage(pageInfo),
	}, nil
}

func (o *PlatformSupplierService) PlatformDeliverList(ctx context.Context, req *supplierv1.DeliverListReq) (*supplierv1.DeliverListResp, error) {
	if !o.GetLoginInfoX(ctx).AdminType.IsSaasSupplier() {
		return nil, apierr.ErrorNotAllow("非saas供应商，没有权限")
	}
	var reqBo *commonbo.OrderDeliverSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	if len(req.Date) == 2 {
		start, err := helper.DateStringToTime(req.Date[0])
		if err != nil {
			return nil, err
		}
		reqBo.DateStart = int(start.Unix())
		end, err := helper.DateStringToTime(req.Date[1])
		if err != nil {
			return nil, err
		}
		reqBo.DateEnd = int(end.Unix())
	}
	reqBo.Edges = &commonbo.OrderDeliverWithEdgeBo{
		WithDeliverGoods: true,
	}
	reqBo.SupplierId = o.GetSupplierIdX(ctx)
	reqBo.SortData = []*commonbo.CommonSortBo{
		{Asc: false, Field: orderdeliver.FieldID},
	}

	deliverDos, pageInfo, err := o.platformSupplierBiz.OrderDeliverSearchList(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	if err != nil {
		return nil, err
	}

	resp := &supplierv1.DeliverListResp{
		List: make([]*supplierv1.DeliverItem, 0, len(deliverDos)),
		Page: o.pageConvertor.ToRespPage(pageInfo),
	}

	for _, deliverDo := range deliverDos {
		tmpGoods := make([]*supplierv1.DeliverGoods, 0, len(deliverDo.OrderDeliverGoods))
		for _, deliverGoodsDo := range deliverDo.OrderDeliverGoods {
			tmpGoods = append(tmpGoods, &supplierv1.DeliverGoods{
				GoodsId:      int32(deliverGoodsDo.GoodsID),
				GoodsName:    deliverGoodsDo.GoodsName,
				SkuId:        int32(deliverGoodsDo.GoodsSkuID),
				SkuName:      deliverGoodsDo.GoodsSkuName,
				Quantity:     int32(deliverGoodsDo.Quantity),
				ThirdBarcode: deliverGoodsDo.SupplierBarcode,
				Barcode:      deliverGoodsDo.Barcode,
				SkuNo:        deliverGoodsDo.SkuNo,
			})
		}
		resp.List = append(resp.List, &supplierv1.DeliverItem{
			OrderNumber:      deliverDo.OrderNumber,
			ConsigneeName:    deliverDo.GetOrderUserAddress().GetName(),
			ConsigneePhone:   deliverDo.GetOrderUserAddress().GetPhoneNumber(),
			KdName:           deliverDo.KdName,
			ExpressNo:        deliverDo.LogisticsNo,
			KdCode:           deliverDo.KdCode,
			CreateTime:       helper.GetTimeDate(deliverDo.CreateTime),
			Goods:            tmpGoods,
			ConsigneeAddress: deliverDo.GetOrderUserAddress().GetAddress(),
			Id:               int32(deliverDo.ID),
			Enable:           int32(deliverDo.Enable),
			EnableText:       deliverDo.Enable.GetName(),
		})
	}
	return resp, nil
}

func (o *PlatformSupplierService) PlatformAfterSaleList(ctx context.Context, req *supplierv1.AfterSaleListReq) (*supplierv1.AfterSaleListResp, error) {
	if !o.GetLoginInfoX(ctx).AdminType.IsSaasSupplier() {
		return nil, apierr.ErrorNotAllow("非saas供应商，没有权限")
	}

	var reqBo *commonbo.OrderAfterSaleSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	if len(req.Date) == 2 {
		start, err := helper.DateStringToTime(req.Date[0])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateStart = int(start.Unix())
		end, err := helper.DateStringToTime(req.Date[1])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateEnd = int(end.Unix())
	}

	reqBo.SupplierId = o.GetSupplierIdX(ctx)
	reqBo.SortData = []*commonbo.CommonSortBo{
		{Asc: false, Field: order.FieldID},
	}
	reqBo.Edges = &commonbo.OrderAfterSaleEdgesBo{
		WithOrderAfterSaleGoods: true,
		WithOrderAfterSaleLog:   true,
	}

	dos, pageInfo := o.platformSupplierBiz.AfterSaleSearchList(isolationcustomer.WithDisableShopCtx(ctx), reqBo)
	list := make([]*supplierv1.AfterSaleData, 0, len(dos))
	var tmpData *supplierv1.AfterSaleData
	for _, itemDo := range dos {
		tmpData = &supplierv1.AfterSaleData{
			Id:                  int32(itemDo.ID),
			OrderNumber:         itemDo.OrderNumber,
			AfterSaleType:       int32(itemDo.Type),
			AfterSaleTypeText:   itemDo.Type.String(),
			ApplyTime:           helper.GetTimeDate(itemDo.CreateTime, helper.DateFormat),
			AfterSaleStatus:     int32(itemDo.Status),
			AfterSaleStatusText: itemDo.GetStatusText(),
			AfterSaleReason:     itemDo.Reason,
			AfterSaleImages:     itemDo.GetImages(),
			RefundAmount:        itemDo.RefundAmount,
			AfterSaleNo:         itemDo.AfterSaleNo,
			Refund: &supplierv1.AfterSaleData_RefundDetail{
				GoodsAmount: itemDo.RefundGoodsAmount,
				FreightFee:  itemDo.RefundFreightFee,
			},
			AfterSalePlatformStatus:     int32(itemDo.PlatformStatus),
			AfterSalePlatformStatusText: itemDo.PlatformStatus.GetSupplierText(),
			RefuseReason:                itemDo.GetPlatformRefuseReason(),
			RefundCardGiftFreightFee:    itemDo.RefundCardGiftFreightFee,
			//Goods:               nil,
			//ExchangeGoods:       nil,
			//RefundGoodsAmount:   0,
			//ExchangeGoodsAmount: 0,
		}
		tmpData.Goods, tmpData.RefundGoodsAmount = o.afterSaleConvertor.TransferReturnGoodsItems(itemDo)
		tmpData.ExchangeGoods, tmpData.ExchangeGoodsAmount = o.afterSaleConvertor.TransferExchangeGoodsItems(itemDo)
		list = append(list, tmpData)
	}

	return &supplierv1.AfterSaleListResp{
		List: list,
		Page: o.pageConvertor.ToRespPage(pageInfo),
	}, nil
}
