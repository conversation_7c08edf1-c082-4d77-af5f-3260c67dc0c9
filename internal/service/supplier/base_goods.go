package supplier

import (
	"cardMall/api/common"
	"cardMall/api/supplierv1"
	"cardMall/grpc/product/productv1"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/conf"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/service"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"fmt"
	"strconv"
)

type BaseGoodsService struct {
	service.AdminBase
	supplierv1.UnimplementedBaseGoodsServer
	conf           *conf.Bootstrap
	baseGoodsBiz   *supplierbiz.BaseGoodsBiz
	oursProductRpc rpc.OursProductRpc
}

func NewBaseGoodsService(conf *conf.Bootstrap, baseGoodsBiz *supplierbiz.BaseGoodsBiz, oursProductRpc rpc.OursProductRpc) *BaseGoodsService {
	return &BaseGoodsService{conf: conf, baseGoodsBiz: baseGoodsBiz, oursProductRpc: oursProductRpc}
}

func (b *BaseGoodsService) BaseGoodsList(ctx context.Context, req *supplierv1.BaseGoodsListReq) (*supplierv1.BaseGoodsListRsp, error) {
	var reqBo *commonbo.BaseGoodsSearchBo
	_ = mapstructure.Decode(req, &reqBo)

	dos, pageInfo := b.baseGoodsBiz.SearchList(ctx, reqBo)

	var rspPage *common.RespPage
	if req.Page != nil {
		rspPage = &common.RespPage{
			Page:     int32(pageInfo.Page),
			PageSize: int32(pageInfo.PageSize),
			Total:    int32(pageInfo.Total),
		}
	}

	list := make([]*supplierv1.BaseGoodsListItem, 0)
	var rsp = &supplierv1.BaseGoodsListRsp{
		List: list,
		Page: rspPage,
	}

	for _, val := range dos {
		item := &supplierv1.BaseGoodsListItem{
			Id:             int32(val.ID),
			ProductId:      val.ProductID,
			ChannelPrice:   helper.Float64ToString(val.ChannelPrice, 4),
			OriginalPrice:  helper.Float64ToString(val.OriginalPrice, 4),
			ProductName:    val.ProductName,
			CreateTime:     helper.GetTimeDate(val.CreateTime),
			Status:         int32(val.Status),
			Type:           int32(val.Type),
			CardExpireTime: int32(val.CardExpireTime),
		}
		rsp.List = append(rsp.List, item)
	}

	return rsp, nil
}

func (b *BaseGoodsService) BaseGoodsListV2(ctx context.Context, req *supplierv1.BaseGoodsListV2Req) (*supplierv1.BaseGoodsListV2Rsp, error) {
	ids := make([]int32, 0)
	if req.ProductId != "" {
		id, _ := strconv.Atoi(req.ProductId)
		ids = append(ids, int32(id))
	}
	reqBo := &productv1.SearchListOursProductReq{
		// OursProductCategoryId: 0,
		Keyword: req.ProductName,
		Ids:     ids,
		Page:    nil,
		Status:  []int32{1},
	}
	if req.Page != nil {
		reqBo.Page = &productv1.ReqPage{
			Num:  req.Page.GetPage(),
			Size: req.Page.GetPageSize(),
		}
	}

	res, err := b.oursProductRpc.SearchListOursProduct(ctx, reqBo)
	if err != nil {
		return nil, err
	}

	rsp := &supplierv1.BaseGoodsListV2Rsp{
		List: make([]*supplierv1.BaseGoodsListV2Rsp_BaseGoodsListItem, 0),
		Page: nil,
	}
	if req.Page != nil {
		rsp.Page = &common.RespPage{}
		rsp.Page.Page = req.Page.GetPage()
		rsp.Page.PageSize = req.Page.GetPageSize()
		rsp.Page.Total = res.DataCount
	}

	for _, item := range res.List {
		rsp.List = append(rsp.List, &supplierv1.BaseGoodsListV2Rsp_BaseGoodsListItem{
			ProductId:   fmt.Sprintf("%d", item.Id),
			ProductName: item.Name,
			Status:      item.Status,
			Type:        item.Type,
		})
	}

	return rsp, nil
}
