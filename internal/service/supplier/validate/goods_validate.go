package validate

import (
	"context"
	"errors"
	"fmt"
	"math"
	"regexp"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"cardMall/api/supplierv1"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/excel"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/httpx"
	"github.com/xuri/excelize/v2"
)

type ValidateGoodsBo struct {
	ID int `json:"id"`
	// 序号
	Index string `json:"index"`
	// 品牌
	Brand string `json:"brand"`
	// 商品类目 类目一/类目二/类目三
	Category [3]string `json:"category"`
	// 商品名称
	GoodsName string `json:"goodsName"`
	// 商品图片
	GoodsImage         string `json:"goodsImage"`
	GoodsImageBytes    []byte `json:"goodsImageBytes"`
	GoodsImageBytesExt string `json:"goodsImageBytesExt"`
	GoodsImageOss      string `json:"goodsImageOss"`
	// 税率
	TaxRate float64 `json:"taxRate"`
	// 税率备注
	TaxRateRemark string `json:"taxRateRemark"`
	// 规格 颜色：颜色1；尺寸：尺寸1；内存：内存1；
	Spec string `json:"spec"`
	// 规格图片
	SpecImage         string `json:"specImage"`
	SpecImageBytes    []byte `json:"specImageBytes"`
	SpecImageBytesExt string `json:"specImageBytesExt"`
	SpecImageOss      string `json:"specImageOss"`
	// 原厂编码
	OriginalCode string `json:"originalCode"`
	// 供应价
	SupplyPrice float64 `json:"supplyPrice"`
	// 市场价
	MarketPrice float64 `json:"marketPrice"`
	// 库存
	Stock int `json:"stock"`
	// 物流模板
	LogisticsTemplate string `json:"logisticsTemplate"`
	// 禁售地区
	BanSaleAreas []string `json:"banSaleAreas"`
	// 描述信息
	Remark string `json:"remark"`
	// 是否上架
	IsOnSale bool `json:"isOnSale"`
	// 错误信息
	Errors []error `json:"errors"`
	// 商品类型 2-实物 1-虚拟
	Type valobj.SupplierGoodsTypeObj `json:"type"`
	// 关联商品名称
	RelationGoodsName string `json:"relationGoodsName"`
	// 关联商品ID
	RelationGoodsId string `json:"relationGoodsId"`

	// 规格解析出来的数据
	specs []*bo.Spec
}

// ParseCategory 解析excel类目数据
func (v *ValidateGoodsBo) ParseCategory(category string) *ValidateGoodsBo {
	category = strings.TrimSpace(category)
	if category == "" {
		v.Errors = append(v.Errors, errors.New("商品类目不能为空"))
		return v
	}
	categories := strings.Split(category, "/")
	if len(categories) != 3 {
		v.Errors = append(v.Errors, errors.New("商品类目格式不正确"))
		return v
	}
	v.Category = [3]string{
		strings.TrimSpace(categories[0]),
		strings.TrimSpace(categories[1]),
		strings.TrimSpace(categories[2]),
	}
	return v
}

// BindRow 解析excel行数据
func (v *ValidateGoodsBo) BindRow(goodsType valobj.SupplierGoodsTypeObj, line int, row []string, headerMap map[string]int, excelFile *excelize.File) *ValidateGoodsBo {
	v.Type = goodsType
	v.Index = strconv.Itoa(line)
	v.Brand = strings.TrimSpace(row[headerMap["品牌"]])
	v.ParseCategory(row[headerMap["商品类目"]])
	v.GoodsName = strings.TrimSpace(row[headerMap["商品名称"]])
	v.GoodsImage = strings.TrimSpace(row[headerMap["商品图片"]])

	if !helper.IsBool(row[headerMap["是否上架"]]) {
		v.Errors = append(v.Errors, errors.New("是否上架必须为是/否"))
	}
	v.IsOnSale = helper.StringToBoolX(row[headerMap["是否上架"]])

	if !helper.IsFloat(row[headerMap["税率"]]) {
		v.Errors = append(v.Errors, errors.New("税率必须为数字或者百分比"))
	}
	v.TaxRate = helper.StringToFloat64X(row[headerMap["税率"]]) * 100

	v.TaxRateRemark = row[headerMap["税率备注"]]
	v.Spec = strings.TrimSpace(row[headerMap["规格"]])
	v.SpecImage = strings.TrimSpace(row[headerMap["规格图片"]])

	if !helper.IsInt(row[headerMap["库存"]]) {
		v.Errors = append(v.Errors, errors.New("库存必须为大于等于0的整数数字"))
	}
	v.Stock = helper.StringToIntX(row[headerMap["库存"]])

	if goodsType == valobj.SupplierGoodsTypeEntity {
		v.OriginalCode = strings.TrimSpace(row[headerMap["原厂编码"]])
		v.SupplyPrice = helper.StringToFloat64X(row[headerMap["供应价"]])
		v.MarketPrice = helper.StringToFloat64X(row[headerMap["市场价"]])
		v.LogisticsTemplate = strings.TrimSpace(row[headerMap["物流模板"]])
		if len(row) > headerMap["禁售地区"] {
			banSaleAreas := strings.ReplaceAll(strings.TrimSpace(row[headerMap["禁售地区"]]), ";", ",")
			banSaleAreas = strings.ReplaceAll(banSaleAreas, "；", ",")
			banSaleAreas = strings.ReplaceAll(banSaleAreas, "，", ",")
			v.BanSaleAreas = helper.SliceTo(strings.Split(banSaleAreas, ","), func(areaStr string) (string, bool) {
				a := strings.TrimSpace(areaStr)
				return a, len(a) > 0
			})
		}
	}
	if goodsType == valobj.SupplierGoodsTypeVirtual {
		v.RelationGoodsName = strings.TrimSpace(row[headerMap["关联商品名称"]])
		v.RelationGoodsId = strings.TrimSpace(row[headerMap["关联商品ID"]])
	}

	if len(row) > headerMap["描述信息"] {
		v.Remark = row[headerMap["描述信息"]]
	}

	if excel.IsDispImg(v.GoodsImage) {
		pictures, err := excelFile.GetPictures("Sheet1", fmt.Sprintf("E%d", line))
		if err != nil {
			v.Errors = append(v.Errors, err)
			return v
		}
		if len(pictures) > 0 {
			v.GoodsImageBytes = pictures[0].File
			v.GoodsImageBytesExt = pictures[0].Extension
		}
	}

	if excel.IsDispImg(v.SpecImage) {
		pictures, err := excelFile.GetPictures("Sheet1", fmt.Sprintf("J%d", line))
		if err != nil {
			v.Errors = append(v.Errors, err)
			return v
		}
		if len(pictures) > 0 {
			v.SpecImageBytes = pictures[0].File
			v.SpecImageBytesExt = pictures[0].Extension
		}
	}

	return v
}

// BindApi 校验api数据
func (v *ValidateGoodsBo) BindApi(goodsApi *supplierv1.UploadGoodsData) *ValidateGoodsBo {
	var category [3]string
	copy(category[:], goodsApi.Category[:3])
	return &ValidateGoodsBo{
		Index:              goodsApi.Index,
		Brand:              goodsApi.Brand,
		Category:           category,
		GoodsName:          goodsApi.GoodsName,
		GoodsImage:         goodsApi.GoodsImage,
		GoodsImageBytes:    excel.Base64ToBytes(goodsApi.GetGoodsImageBytesB64()),
		GoodsImageBytesExt: goodsApi.GoodsImageBytesB64Ext,
		GoodsImageOss:      goodsApi.GoodsOssImage,
		TaxRate:            goodsApi.TaxRate,
		TaxRateRemark:      goodsApi.TaxRateRemark,
		Spec:               goodsApi.Spec,
		SpecImage:          goodsApi.SpecImage,
		SpecImageBytes:     excel.Base64ToBytes(goodsApi.GetSpecImageBytesB64()),
		SpecImageBytesExt:  goodsApi.SpecImageBytesB64Ext,
		SpecImageOss:       goodsApi.SpecOssImage,
		OriginalCode:       goodsApi.OriginalCode,
		SupplyPrice:        goodsApi.SupplyPrice,
		MarketPrice:        goodsApi.MarketPrice,
		Stock:              int(goodsApi.Stock),
		LogisticsTemplate:  goodsApi.LogisticsTemplate,
		BanSaleAreas:       goodsApi.BanSaleAreas,
		Remark:             goodsApi.Remark,
		IsOnSale:           goodsApi.IsOnSale,
		Errors:             nil,
		Type:               valobj.SupplierGoodsTypeObj(goodsApi.GoodsType),
		RelationGoodsName:  goodsApi.GoodsNameRelation,
		RelationGoodsId:    goodsApi.RelationGoodsId,
	}
}

// IsError 判断是否有错误信息
func (v *ValidateGoodsBo) IsError() bool {
	return helper.SlicesIsExist(v.Errors, func(err error) bool { return err != nil })
}

// Validate 校验商品数据
func (v *ValidateGoodsBo) Validate(validator *ValidateGoodsBoDepth) *ValidateGoodsBo {
	return v.ValidateBrand(validator.BrandMap).
		ValidateCategory(validator.CategoryMap).
		ValidateName(validator.GoodsMap).
		ValidateArea(validator.AreaMap).
		ValidateTransport(validator.TransportMap).
		ValidateGoodsImgUrl(validator.Upload).
		ValidateGoodsSpecImgUrl(validator.Upload).
		ValidatePrice().
		ValidateTaxRate().
		ValidateRelationGoods(validator.RelationGoodsMap).
		ValidateSpec(validator.GoodsMap).
		ValidateRemark().
		ValidateStock()
}

// ValidateRemark 验证描述信息
func (v *ValidateGoodsBo) ValidateRemark() *ValidateGoodsBo {
	//if len(v.Remark) == 0 {
	//	v.Errors = append(v.Errors, errors.New("描述信息不能为空"))
	//	return v
	//}
	return v
}

// ValidateStock 验证库存
func (v *ValidateGoodsBo) ValidateStock() *ValidateGoodsBo {
	if v.Stock < 0 {
		v.Errors = append(v.Errors, errors.New("库存不能小于0"))
		return v
	}
	// mysql int(11)
	if v.Stock > math.MaxInt32 {
		v.Errors = append(v.Errors, errors.New(fmt.Sprintf("库存不能大于%d最大值", math.MaxInt32)))
		return v
	}
	return v
}

// ValidateArea 验证禁售地区
func (v *ValidateGoodsBo) ValidateArea(areaMap map[string]*do.AreaDo) *ValidateGoodsBo {
	if v.Type != valobj.SupplierGoodsTypeEntity {
		return v
	}
	areas := v.BanSaleAreas
	notExist := make([]string, 0, len(areas))
	for _, area := range areas {
		if _, ok := areaMap[area]; !ok {
			notExist = append(notExist, area)
		}
	}
	if len(notExist) > 0 {
		v.Errors = append(v.Errors, errors.New(fmt.Sprintf("禁售地区(%s)不存在", strings.Join(notExist, ","))))
	}
	return v
}

// ValidateTransport 验证物流模板
func (v *ValidateGoodsBo) ValidateTransport(transportMap map[string]*do.SupplierTransportDo) *ValidateGoodsBo {
	if v.Type != valobj.SupplierGoodsTypeEntity {
		return v
	}
	if strings.TrimSpace(v.LogisticsTemplate) == "" {
		v.Errors = append(v.Errors, errors.New("物流模板不能为空"))
		return v
	}
	if _, ok := transportMap[v.LogisticsTemplate]; !ok {
		v.Errors = append(v.Errors, errors.New("物流模板不存在"))
	}
	return v
}

// ValidatePrice 验证价格
func (v *ValidateGoodsBo) ValidatePrice() *ValidateGoodsBo {
	if v.Type != valobj.SupplierGoodsTypeEntity {
		return v
	}
	if v.SupplyPrice <= 0 {
		v.Errors = append(v.Errors, errors.New("供应价必须大于0"))
	}
	if v.MarketPrice <= 0 {
		v.Errors = append(v.Errors, errors.New("市场价必须大于0"))
	}
	return v
}

// ValidateTaxRate 验证税率
func (v *ValidateGoodsBo) ValidateTaxRate() *ValidateGoodsBo {
	if v.TaxRate < 0 || v.TaxRate > 100 {
		v.Errors = append(v.Errors, errors.New("税率必须在0-100%之间"))
	}
	if utf8.RuneCountInString(v.TaxRateRemark) > 200 {
		v.Errors = append(v.Errors, errors.New("税率备注不能超过200个字符"))
	}
	return v
}

// ValidateRelationGoods 验证关联商品
func (v *ValidateGoodsBo) ValidateRelationGoods(relationGoodsMap map[string]*do.BaseGoodsDo) *ValidateGoodsBo {
	if v.Type == valobj.SupplierGoodsTypeEntity {
		return v
	}
	if strings.TrimSpace(v.RelationGoodsName) == "" {
		v.Errors = append(v.Errors, errors.New("关联商品不能为空"))
		return v
	}

	if strings.TrimSpace(v.RelationGoodsId) == "" {
		v.Errors = append(v.Errors, errors.New("关联商品ID不能为空"))
		return v
	}
	if _, ok := relationGoodsMap[v.RelationGoodsId+"--"+v.RelationGoodsName]; !ok {
		v.Errors = append(v.Errors, errors.New("关联商品不存在"))
		return v
	}
	return v
}

// getSpecNames 获取规格名称
func (v *ValidateGoodsBo) getSpecNames(goodsMap map[string]*do.SupplierGoodsDo) []string {
	goodsDo, ok := goodsMap[v.GoodsName]
	if !ok {
		return nil
	}

	return helper.SliceTo(goodsDo.GetSpecBo(), func(spec *bo.Spec) (string, bool) {
		return spec.Name, true
	})
}

func (v *ValidateGoodsBo) GetSpecsMap() map[string]string {
	specs := convertToSpecs(v.Spec)
	specMap := make(map[string]string)
	for _, spec := range specs {
		for _, item := range spec.Items {
			specMap[spec.Name] = item.Name
		}
	}
	return specMap
}

// ValidateSpec 验证规格
func (v *ValidateGoodsBo) ValidateSpec(goodsMap map[string]*do.SupplierGoodsDo) *ValidateGoodsBo {
	if len(strings.TrimSpace(v.Spec)) == 0 {
		v.Errors = append(v.Errors, errors.New("规格不能为空"))
		return v
	}
	specs := convertToSpecs(v.Spec)
	if len(specs) == 0 {
		v.Errors = append(v.Errors, errors.New("规格格式错误"))
		return v
	}
	// 获取原始规格顺序
	specNames := v.getSpecNames(goodsMap)
	// 对比两组顺序是否一致
	newSpecNames := helper.SliceTo(specs, func(spec *bo.Spec) (string, bool) {
		return spec.Name, true
	})
	if len(specNames) > 0 && !helper.SliceEqual(specNames, newSpecNames) {
		v.Errors = append(v.Errors, errors.New("规格顺序不一致"))
		return v
	}
	v.specs = specs
	maxCount := 20
	for _, spec := range specs {
		if utf8.RuneCountInString(spec.Name) > maxCount {
			v.Errors = append(v.Errors, errors.New("规格名称不能超过20个字符"))
		}
		if len(spec.Items) == 0 {
			v.Errors = append(v.Errors, errors.New("规格项不能为空"))
		}
		for _, item := range spec.Items {
			if utf8.RuneCountInString(item.Name) > maxCount {
				v.Errors = append(v.Errors, errors.New("规格项名称不能超过20个字符"))
			}
		}
	}
	return v
}

// ValidateBrand 验证品牌
func (v *ValidateGoodsBo) ValidateBrand(brandMap map[string]*do.GoodsBrandDo) *ValidateGoodsBo {
	if strings.TrimSpace(v.Brand) == "" {
		v.Errors = append(v.Errors, errors.New("品牌不能为空"))
		return v
	}
	brandDo, ok := brandMap[v.Brand]
	if !ok {
		v.Errors = append(v.Errors, errors.New("品牌不存在"))
		return v
	}
	if !brandDo.Status.IsEnable() {
		v.Errors = append(v.Errors, errors.New("品牌已禁用"))
		return v
	}
	return v
}

const imageUrlExpr = `^(http|https):\/\/[^\s]+$`

var imageExt = []string{"png", "jpg", "jpeg"}

// 500K
var maxImageSize1024K = 1024 * 1024

func (v *ValidateGoodsBo) ValidateGoodsSpecImgUrl(upload UploadFunc) *ValidateGoodsBo {
	if strings.TrimSpace(v.SpecImage) == "" {
		v.Errors = append(v.Errors, errors.New("规格图片不能为空"))
		return v
	}
	if v.SpecImageOss != "" {
		return v
	}

	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Second)
	defer cancel()

	fileBytes := v.SpecImageBytes
	filename := v.SpecImage + v.SpecImageBytesExt
	if len(fileBytes) == 0 {
		// 校验必须满足http或者https结构
		compile, err := regexp.Compile(imageUrlExpr)
		if err != nil {
			panic(err)
		}
		if !compile.MatchString(v.SpecImage) {
			v.Errors = append(v.Errors, errors.New("商品规格图片链接格式不正确"))
			return v
		}

		// 下载图片上传到oss
		fileBytes, filename, err = httpx.DownloadFile(ctx, v.SpecImage)
		if err != nil {
			v.Errors = append(v.Errors, errors.New("商品规格图片下载失败"))
			return v
		}
	}
	// 校验图片格式
	if !helper.InSlice(helper.GetFileExt(filename), imageExt) {
		v.Errors = append(v.Errors, errors.New(fmt.Sprintf("商品规格图片格式不正确, 只允许 %s 格式", strings.Join(imageExt, ","))))
		return v
	}

	// 限制文件大小
	if len(fileBytes) > maxImageSize1024K {
		v.Errors = append(v.Errors, errors.New("商品规格图片大小不能超过1024K"))
		return v
	}

	if upload == nil {
		return v
	}

	fileUrl, err := upload(ctx, filename, fileBytes)
	if err != nil {
		v.Errors = append(v.Errors, errors.New("商品规格图片上传失败"))
		return v
	}
	v.SpecImageOss = fileUrl
	if len(v.SpecImageOss) == 0 {
		v.Errors = append(v.Errors, errors.New("商品规格图片不允许为空"))
		return v
	}
	return v
}

func (v *ValidateGoodsBo) ValidateGoodsImgUrl(upload UploadFunc) *ValidateGoodsBo {
	if strings.TrimSpace(v.GoodsImage) == "" {
		v.Errors = append(v.Errors, errors.New("商品图片不能为空"))
		return v
	}

	if v.GoodsImageOss != "" {
		return v
	}

	ctx, cancel := context.WithTimeout(context.Background(), 50*time.Second)
	defer cancel()

	fileBytes := v.GoodsImageBytes
	filename := v.GoodsImage + v.GoodsImageBytesExt
	if len(fileBytes) == 0 {
		// 校验必须满足http或者https结构
		compile, err := regexp.Compile(imageUrlExpr)
		if err != nil {
			panic(err)
		}
		if !compile.MatchString(v.GoodsImage) {
			v.Errors = append(v.Errors, errors.New("商品图片链接格式不正确"))
			return v
		}

		if upload == nil {
			return v
		}

		// 下载图片上传到oss
		fileBytes, filename, err = httpx.DownloadFile(ctx, v.GoodsImage)
		if err != nil {
			v.Errors = append(v.Errors, errors.New("商品图片下载失败"))
			return v
		}
	}

	// 校验图片格式
	if !helper.InSlice(helper.GetFileExt(filename), imageExt) {
		v.Errors = append(v.Errors, errors.New(fmt.Sprintf("商品图片格式不正确, 只允许 %s 格式", strings.Join(imageExt, ","))))
		return v
	}

	// 限制文件大小
	if len(fileBytes) > maxImageSize1024K {
		v.Errors = append(v.Errors, errors.New("商品图片大小不能超过1024K"))
		return v
	}

	if upload == nil {
		return v
	}

	fileUrl, err := upload(ctx, filename, fileBytes)
	if err != nil {
		v.Errors = append(v.Errors, errors.New("商品图片上传失败"))
		return v
	}
	v.GoodsImageOss = fileUrl
	if v.GoodsImageOss == "" {
		v.Errors = append(v.Errors, errors.New("商品图片不允许为空"))
		return v
	}
	return v
}

func (v *ValidateGoodsBo) ValidateCategory(categoryMap map[string]*do.GoodsCategoryDo) *ValidateGoodsBo {
	category1Name := v.Category[0]
	category2Name := v.Category[1]
	category3Name := v.Category[2]
	if strings.TrimSpace(category1Name) == "" {
		v.Errors = append(v.Errors, errors.New("一级类目不能为空"))
		return v
	}

	category1, ok := categoryMap[category1Name]
	if !ok {
		v.Errors = append(v.Errors, errors.New("一级类目不存在"))
		return v
	}
	if !category1.Status.IsEnable() {
		v.Errors = append(v.Errors, errors.New("一级类目已禁用"))
		return v
	}
	if strings.TrimSpace(category2Name) == "" {
		v.Errors = append(v.Errors, errors.New("二级类目不能为空"))
		return v
	}
	categoryMap = helper.SliceToMap(category1.Children, func(item *do.GoodsCategoryDo) (string, *do.GoodsCategoryDo, bool) {
		return item.Name, item, item.Pid == category1.Id
	})
	category2, ok := categoryMap[category2Name]
	if !ok {
		v.Errors = append(v.Errors, errors.New("二级类目不存在"))
		return v
	}
	if !category2.Status.IsEnable() {
		v.Errors = append(v.Errors, errors.New("二级类目已禁用"))
		return v
	}
	if category2.Pid != category1.Id {
		v.Errors = append(v.Errors, errors.New("二级类目不属于一级类目"))
		return v
	}
	if strings.TrimSpace(category3Name) == "" {
		v.Errors = append(v.Errors, errors.New("三级类目不能为空"))
		return v
	}
	categoryMap = helper.SliceToMap(category2.Children, func(item *do.GoodsCategoryDo) (string, *do.GoodsCategoryDo, bool) {
		return item.Name, item, item.Pid == category2.Id
	})
	category3, ok := categoryMap[category3Name]
	if !ok {
		v.Errors = append(v.Errors, errors.New("三级类目不存在"))
		return v
	}
	if !category3.Status.IsEnable() {
		v.Errors = append(v.Errors, errors.New("三级类目已禁用"))
		return v
	}
	if category3.Pid != category2.Id {
		v.Errors = append(v.Errors, errors.New("三级类目不属于二级类目"))
		return v
	}
	return v
}

// ValidateName 验证
func (v *ValidateGoodsBo) ValidateName(nameMap map[string]*do.SupplierGoodsDo) *ValidateGoodsBo {
	if strings.TrimSpace(v.GoodsName) == "" {
		v.Errors = append(v.Errors, errors.New("商品名称不能为空"))
		return v
	}
	// 30字符
	if utf8.RuneCountInString(v.GoodsName) > 30 {
		v.Errors = append(v.Errors, errors.New("商品名称不能超过30个字符"))
		return v
	}
	if existGoods, ok := nameMap[v.GoodsName]; ok {
		v.ID = existGoods.ID
	}
	return v
}

func NewValidateGoodsBos(list []*ValidateGoodsBo, opts ...ValidateGoodsBosOption) *ValidateGoodsBos {
	dos := &ValidateGoodsBos{
		list: list,
	}
	for _, opt := range opts {
		opt(dos)
	}
	return dos
}

type UploadFunc func(ctx context.Context, fileName string, content []byte) (string, error)

type ValidateGoodsBoDepth struct {
	GoodsMap         map[string]*do.SupplierGoodsDo
	RelationGoodsMap map[string]*do.BaseGoodsDo
	CategoryMap      map[string]*do.GoodsCategoryDo
	AreaMap          map[string]*do.AreaDo
	BrandMap         map[string]*do.GoodsBrandDo
	TransportMap     map[string]*do.SupplierTransportDo

	ExistGoodsImages map[string]map[string]struct{}

	Upload UploadFunc
}

type ValidateGoodsBos struct {
	list []*ValidateGoodsBo

	ValidateGoodsBoDepth
}

// GetList 获取商品列表
func (g *ValidateGoodsBos) GetList() []*ValidateGoodsBo {
	return g.list
}

// GetCategory 获取分类
func (g *ValidateGoodsBos) GetCategory(categoryNames [3]string) (*do.GoodsCategoryDo, error) {
	category1Name := categoryNames[0]
	category2Name := categoryNames[1]
	category3Name := categoryNames[2]
	if strings.TrimSpace(category1Name) == "" {
		return nil, errors.New("一级类目不能为空")
	}

	category1, ok := g.CategoryMap[category1Name]
	if !ok {
		return nil, errors.New("一级类目不存在")
	}
	if strings.TrimSpace(category2Name) == "" {
		return nil, errors.New("二级类目不能为空")
	}
	categoryMap := helper.SliceToMap(category1.Children, func(item *do.GoodsCategoryDo) (string, *do.GoodsCategoryDo, bool) {
		return item.Name, item, item.Pid == category1.Id
	})
	category2, ok := categoryMap[category2Name]
	if !ok {
		return nil, errors.New("二级类目不存在")
	}
	if category2.Pid != category1.Id {
		return nil, errors.New("二级类目不属于一级类目")
	}
	if strings.TrimSpace(category3Name) == "" {
		return nil, errors.New("三级类目不能为空")
	}
	categoryMap = helper.SliceToMap(category2.Children, func(item *do.GoodsCategoryDo) (string, *do.GoodsCategoryDo, bool) {
		return item.Name, item, item.Pid == category2.Id
	})
	category3, ok := categoryMap[category3Name]
	if !ok {
		return nil, errors.New("三级类目不存在")
	}
	if category3.Pid != category2.Id {
		return nil, errors.New("三级类目不属于二级类目")
	}
	return category3, nil
}

// Validate 校验商品数据
func (g *ValidateGoodsBos) Validate() *ValidateGoodsBos {
	specMap := g.ConvertToSpecs()
	goodsSpecExistMap := make(map[string]int)
	goodsNameSpecMap := make(map[string][]string)
	waringGoodsNameMap := make(map[string]struct{})
	for _, goodsBo := range g.list {
		goodsBo.Validate(&g.ValidateGoodsBoDepth)
		if len(goodsBo.specs) == 0 {
			continue
		}
		goodsSpecNames := helper.SliceTo(goodsBo.specs, func(item *bo.Spec) (string, bool) {
			return item.Name, true
		})
		if _, ok := goodsNameSpecMap[goodsBo.GoodsName]; !ok {
			goodsNameSpecMap[goodsBo.GoodsName] = goodsSpecNames
		}
		goodsNameSpecNames := goodsNameSpecMap[goodsBo.GoodsName]
		if !helper.SliceEqual(goodsNameSpecNames, goodsSpecNames) {
			waringGoodsNameMap[goodsBo.GoodsName] = struct{}{}
			goodsBo.Errors = append(goodsBo.Errors, errors.New("同一商品存在规格名称不一致情况"))
		}
		// 校验规格
		goodsUK := buildSpecsIndex(goodsBo.GoodsName, goodsBo.specs)
		if goodsSpecExistIndex, ok := goodsSpecExistMap[goodsUK]; ok {
			goodsBo.Errors = append(goodsBo.Errors, errors.New(fmt.Sprintf("此商品规格与 %d 行相同", goodsSpecExistIndex)))
		}

		// 验证规格是否合法
		specList, ok := specMap[goodsBo.GoodsName]
		if !ok {
			goodsBo.Errors = append(goodsBo.Errors, errors.New("同一商品存在规格为空情况"))
			continue
		}
		existGoodsDo, ok := g.GoodsMap[goodsBo.GoodsName]
		if ok {
			// 合并specList
			_, err := g.MergeSpecList(specList, existGoodsDo)
			if err != nil {
				goodsBo.Errors = append(goodsBo.Errors, err)
			}
		}
	}
	for _, goodsBo := range g.list {
		_, ok := waringGoodsNameMap[goodsBo.GoodsName]
		if len(goodsBo.Errors) == 0 && ok {
			goodsBo.Errors = append(goodsBo.Errors, errors.New("同一商品存在规格名称不一致情况"))
		}
	}
	return g
}

// buildSpecsIndex 根据specs构建唯一标识
func buildSpecsIndex(goodsName string, specs []*bo.Spec) string {
	specNames := make([]string, 0, len(specs))
	for _, spec := range specs {
		specNames = append(specNames, spec.Name)
		for _, item := range spec.Items {
			specNames = append(specNames, item.Name)
		}
	}
	specNames = append(specNames, goodsName)
	sort.Slice(specNames, func(i, j int) bool {
		return specNames[i] < specNames[j]
	})
	return strings.Join(specNames, "-")
}

// 转换函数
func convertToSpecs(rules string) []*bo.Spec {
	// 兼容中文分号和冒号
	rules = strings.ReplaceAll(rules, "；", ";")
	rules = strings.ReplaceAll(rules, "：", ":")
	// 定义一个切片用于存储 Spec
	var specs []*bo.Spec

	// 按照分号分割规则字段
	rulePairs := strings.Split(rules, ";")
	specMap := make(map[string]*bo.Spec)

	// 遍历所有规则字段
	sortSpecNames := make([]string, 0, 3)
	for _, rule := range rulePairs {
		// 按冒号分割规格名称和规格项
		parts := strings.Split(rule, ":")
		if len(parts) != 2 {
			continue
		}

		specName := strings.TrimSpace(parts[0])
		specValue := strings.TrimSpace(parts[1])

		// 如果该规格名称未在map中存在，创建新的Spec对象
		if _, exists := specMap[specName]; !exists {
			specMap[specName] = &bo.Spec{
				Id:       0, // 自增的ID
				Name:     specName,
				AddImage: false, // 默认值
			}
			sortSpecNames = append(sortSpecNames, specName)
		}

		// 创建新的SpecItems并添加到该Spec的Items中
		specItem := &bo.SpecItems{
			Id:       0, // 自增的ID
			SpecId:   specMap[specName].Id,
			Name:     specValue,
			SpecName: specName,
			Image:    "", // 默认不带图片
		}

		specMap[specName].Items = append(specMap[specName].Items, specItem)
	}

	// 将map中的Spec转换为切片
	for _, specName := range sortSpecNames {
		specs = append(specs, specMap[specName])
	}

	return specs
}

// ConvertToSpecItems 转换规则为 SpecItems 列表
func ConvertToSpecItems(specs []*bo.Spec, rules string) []*bo.SpecItems {
	var specItemsList []*bo.SpecItems

	// 兼容中文分号和冒号
	rules = strings.ReplaceAll(rules, "；", ";")
	rules = strings.ReplaceAll(rules, "：", ":")

	// 按照分号拆分规则
	rulePairs := strings.Split(rules, ";")

	// 处理每一条规则
	for _, rule := range rulePairs {
		// 分割规格名称和值
		parts := strings.Split(rule, ":")
		if len(parts) != 2 {
			continue
		}

		specName := strings.TrimSpace(parts[0])
		specValue := strings.TrimSpace(parts[1])

		// 查找对应的 Spec 和 SpecItems
		for _, spec := range specs {
			if spec.Name == specName {
				// 在 SpecItems 中查找匹配的项
				for _, specItem := range spec.Items {
					if specItem.Name == specValue {
						specItemsList = append(specItemsList, specItem)
					}
				}
			}
		}
	}

	return specItemsList
}

func (g *ValidateGoodsBos) MergeSpecList(specList []*bo.Spec, existGoodsDo *do.SupplierGoodsDo) ([]*bo.Spec, error) {
	if existGoodsDo == nil {
		return specList, nil
	}
	newSpecMap := make(map[string]*bo.Spec, len(specList))
	for _, spec := range specList {
		if _, ok := newSpecMap[spec.Name]; ok {
			newSpecMap[spec.Name].Items = append(newSpecMap[spec.Name].Items, spec.Items...)
			continue
		}
		newSpecMap[spec.Name] = spec
	}
	existGoodsDoSpecs := existGoodsDo.GetSpecBo()
	oldGoodsSpecMap := make(map[string]*bo.Spec, len(existGoodsDoSpecs))
	oldGoodsSpecNames := make([]string, 0, len(existGoodsDoSpecs))
	for _, spec := range existGoodsDoSpecs {
		if _, ok := oldGoodsSpecMap[spec.Name]; ok {
			oldGoodsSpecMap[spec.Name].Items = append(oldGoodsSpecMap[spec.Name].Items, spec.Items...)
			continue
		}
		oldGoodsSpecNames = append(oldGoodsSpecNames, spec.Name)
		oldGoodsSpecMap[spec.Name] = spec
	}
	if len(newSpecMap) != len(oldGoodsSpecMap) {
		return nil, errors.New("新旧规格不同, 请调整规格后重试")
	}
	respSpecList := make([]*bo.Spec, 0, len(specList))
	for _, specName := range oldGoodsSpecNames {
		oldSpec, ok := oldGoodsSpecMap[specName]
		if !ok {
			continue
		}
		oldSpecItemMap := make(map[string]*bo.SpecItems, len(oldSpec.Items))
		for _, specItem := range oldSpec.Items {
			oldSpecItemMap[specItem.Name] = specItem
		}
		newGoodsSpec, ok := newSpecMap[specName]
		if !ok {
			// 新旧规格不同，为异常数据，直接返回空
			return nil, errors.New("新旧规格不同, 请调整规格后重试")
		}
		respSpecItems := make([]*bo.SpecItems, 0, len(oldSpec.Items)+len(newGoodsSpec.Items))
		respSpecItems = append(respSpecItems, helper.SliceSortUnique(oldSpec.Items, func(v *bo.SpecItems) string { return v.Name })...)
		for _, newSpecItem := range helper.SliceSortUnique(newGoodsSpec.Items, func(v *bo.SpecItems) string { return v.Name }) {
			if _, ok := oldSpecItemMap[newSpecItem.Name]; !ok {
				respSpecItems = append(respSpecItems, newSpecItem)
				continue
			}
		}
		oldSpec.Items = respSpecItems
		oldSpec.AddImage = newGoodsSpec.AddImage
		respSpecList = append(respSpecList, oldSpec)
	}
	return respSpecList, nil
}

// ConvertToSpecs 将商品数据转换为Spec结构
func (g *ValidateGoodsBos) ConvertToSpecs() map[string][]*bo.Spec {
	goodNameMap := make(map[string][]*bo.Spec, len(g.list))
	goodsRulesMap := make(map[string][]string, len(g.list))
	for _, goodsBo := range g.list {
		goodsRulesMap[goodsBo.GoodsName] = append(goodsRulesMap[goodsBo.GoodsName], goodsBo.Spec)
	}

	for goodsName, rules := range goodsRulesMap {
		// 定义一个切片用于存储 Spec
		var specs []*bo.Spec
		specMap := make(map[string]*bo.Spec)
		sortSpecNames := make([]string, 0)
		for _, rule := range rules {
			specsTmp := convertToSpecs(rule)

			for _, spec := range specsTmp {
				if _, ok := specMap[spec.Name]; ok {
					specMap[spec.Name].Items = helper.SliceSortUnique(append(specMap[spec.Name].Items, spec.Items...), func(v *bo.SpecItems) string { return v.Name })
					continue
				}
				specMap[spec.Name] = spec
				sortSpecNames = append(sortSpecNames, spec.Name)
			}
		}

		for _, name := range sortSpecNames {
			specs = append(specs, specMap[name])
		}
		goodNameMap[goodsName] = specs
	}

	return goodNameMap
}

type ValidateGoodsBosOption func(v *ValidateGoodsBos)

// WithValidateGoodsBosGoods 设置商品依赖
func WithValidateGoodsBosGoods(goodsMap map[string]*do.SupplierGoodsDo) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.GoodsMap = goodsMap
	}
}

// WithValidateGoodsBosCategory 设置分类依赖
func WithValidateGoodsBosCategory(categoryMap map[string]*do.GoodsCategoryDo) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.CategoryMap = categoryMap
	}
}

// WithValidateGoodsBosArea 设置地区依赖
func WithValidateGoodsBosArea(areaMap map[string]*do.AreaDo) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.AreaMap = areaMap
	}
}

// WithValidateGoodsBosBrand 设置品牌依赖
func WithValidateGoodsBosBrand(brandMap map[string]*do.GoodsBrandDo) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.BrandMap = brandMap
	}
}

// WithValidateGoodsBosTransport 设置运费模板依赖
func WithValidateGoodsBosTransport(transportMap map[string]*do.SupplierTransportDo) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.TransportMap = transportMap
	}
}

// WithValidateGoodsBosUpload 设置上传依赖
func WithValidateGoodsBosUpload(upload UploadFunc) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.Upload = upload
	}
}

// WithValidateRelationGoods 设置关联商品依赖
func WithValidateRelationGoods(relationGoodsMap map[string]*do.BaseGoodsDo) ValidateGoodsBosOption {
	return func(o *ValidateGoodsBos) {
		o.RelationGoodsMap = relationGoodsMap
	}
}
