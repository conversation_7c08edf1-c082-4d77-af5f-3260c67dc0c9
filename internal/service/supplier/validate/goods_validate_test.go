package validate

import (
	"encoding/json"
	"testing"

	"cardMall/internal/biz/bo"
	"cardMall/internal/pkg/helper"
)

func Test_convertToSpecs(t *testing.T) {
	// 示例商品规则
	rules := "颜色: 红色; 尺寸: 大; 内存: 8GB; 颜色: 蓝色; 尺寸: 小; 内存: 16GB"

	// 转换规则为Spec结构体
	specs := convertToSpecs(rules)

	t.Log(helper.SliceJsonString(specs))
}

func Test_ConvertToSpecItems(t *testing.T) {
	specsJson := `[{"id":1,"name":"颜色","addImage":false,"items":[{"id":0,"specId":1,"name":"红色","specName":"颜色","image":""},{"id":0,"specId":1,"name":"蓝色","specName":"颜色","image":""}]},{"id":2,"name":"尺寸","addImage":false,"items":[{"id":0,"specId":2,"name":"大","specName":"尺寸","image":""},{"id":0,"specId":2,"name":"小","specName":"尺寸","image":""}]},{"id":3,"name":"内存","addImage":false,"items":[{"id":0,"specId":3,"name":"8GB","specName":"内存","image":""},{"id":0,"specId":3,"name":"16GB","specName":"内存","image":""}]}]`
	var specs []*bo.Spec
	_ = json.Unmarshal([]byte(specsJson), &specs)
	rules := "颜色: 红色; 尺寸: 大; 内存: 8GB;"
	specItems := ConvertToSpecItems(specs, rules)
	t.Log(helper.SliceJsonString(specItems))
}
