package supplier

import (
	"cardMall/api/apierr"
	"cardMall/api/common"
	"cardMall/api/supplierv1"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/service"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

type TransportService struct {
	service.AdminBase
	transportBiz *supplierbiz.TransportBiz
	log          *log.Helper
}

func NewTransportService(transportBiz *supplierbiz.TransportBiz, log *log.Helper) *TransportService {
	return &TransportService{transportBiz: transportBiz, log: log}
}

func (t *TransportService) DelTransport(ctx context.Context, req *supplierv1.DelTransportReq) (*supplierv1.DelTransportResp, error) {
	if req.Id <= 0 {
		return nil, apierr.ErrorParam("ID 不能为空")
	}
	return &supplierv1.DelTransportResp{}, t.transportBiz.Delete(ctx, t.GetSupplierIdX(ctx), int(req.Id))
}

func (t *TransportService) ListTransport(ctx context.Context, req *supplierv1.ListTransportReq) (*supplierv1.ListTransportResp, error) {
	var reqBo *commonbo.SupplierTransportSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.SupplierId = t.GetSupplierIdX(ctx)

	transportDos, pageInfo := t.transportBiz.List(ctx, reqBo)
	var list []*supplierv1.TransportData
	for _, transportDo := range transportDos {
		list = append(list, &supplierv1.TransportData{
			Id:          int32(transportDo.ID),
			Name:        transportDo.Name,
			Description: transportDo.Description,
			UpdateTime:  helper.GetTimeDate(transportDo.UpdateTime),
			CreateTime:  helper.GetTimeDate(transportDo.CreateTime),
		})
	}

	var rspPage *common.RespPage
	if req.Page != nil {
		rspPage = &common.RespPage{
			Page:     int32(pageInfo.Page),
			PageSize: int32(pageInfo.PageSize),
			Total:    int32(pageInfo.Total),
		}
	}

	return &supplierv1.ListTransportResp{
		List: list,
		Page: rspPage,
	}, nil
}

func (t *TransportService) SaveTransport(ctx context.Context, req *supplierv1.SaveTransportReq) (*supplierv1.SaveTransportResp, error) {
	var reqBo *bo.SaveTransportBo
	_ = mapstructure.Decode(req, &reqBo)
	if err := reqBo.Validate(); err != nil {
		return nil, err
	}
	err := t.transportBiz.Save(ctx, t.GetSupplierIdX(ctx), reqBo)
	if err != nil {
		return nil, err
	}
	return &supplierv1.SaveTransportResp{}, nil
}

func (t *TransportService) GetTransport(ctx context.Context, req *supplierv1.GetTransportReq) (*supplierv1.GetTransportResp, error) {
	transportDo, err := t.transportBiz.Get(ctx, t.GetSupplierIdX(ctx), int(req.Id))
	if err != nil {
		return nil, err
	}
	rsp := &supplierv1.GetTransportResp{
		Id:           int32(transportDo.ID),
		Name:         transportDo.Name,
		Description:  transportDo.Description,
		KdId:         int32(transportDo.KdID),
		KdName:       transportDo.KdName,
		KdCode:       transportDo.KdCode,
		PricingMode:  int32(transportDo.PricingMode),
		DefaultNum:   transportDo.DefaultNum,
		DefaultPrice: transportDo.DefaultPrice,
		AddNum:       transportDo.AddNum,
		AddPrice:     transportDo.AddPrice,
		Items:        make([]*supplierv1.TransportItem, 0, len(transportDo.SupplierTransportItem)),
	}

	for _, item := range transportDo.SupplierTransportItem {
		cityItems := make([]*supplierv1.TransportCity, 0, len(item.SupplierTransportCity))
		for _, city := range item.SupplierTransportCity {
			cityItems = append(cityItems, &supplierv1.TransportCity{
				CityId:   int32(city.CityID),
				CityName: city.CityName,
			})
		}

		rsp.Items = append(rsp.Items, &supplierv1.TransportItem{
			DefaultNum:   item.DefaultNum,
			DefaultPrice: item.DefaultPrice,
			AddNum:       item.AddNum,
			AddPrice:     item.AddPrice,
			Items:        cityItems,
		})
	}

	return rsp, nil
}
