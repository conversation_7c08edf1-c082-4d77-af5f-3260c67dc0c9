package convertor

import (
	"cardMall/api/supplierv1"
	"cardMall/internal/biz/do"
	"cardMall/internal/pkg/build"
	"cardMall/internal/pkg/helper"
)

type OrderConvertor struct {
}

func NewOrderConvertor() *OrderConvertor {
	return &OrderConvertor{}
}

func (o *OrderConvertor) ToOrderData(orderDo *do.OrderDo) *supplierv1.OrderData {
	rspGoods := make([]*supplierv1.OrderGoods, len(orderDo.OrderGoods))
	for i, orderGoodsDo := range orderDo.OrderGoods {
		rspGoods[i] = &supplierv1.OrderGoods{
			GoodsSkuId:         int32(orderGoodsDo.GoodsSkuID),
			GoodsId:            int32(orderGoodsDo.GoodsID),
			GoodsName:          build.GenGoodsNameWithSKU(orderGoodsDo.GoodsName, orderGoodsDo.GoodsSkuName),
			GoodsSkuName:       orderGoodsDo.GoodsSkuName,
			Quantity:           int32(orderGoodsDo.Quantity),
			GoodsImage:         orderGoodsDo.GoodsImage,
			DeliverQuantity:    int32(orderGoodsDo.DeliverQuantity),
			SkuNo:              orderGoodsDo.SkuNo,
			CanDeliverQuantity: int32(orderGoodsDo.GetNeedDeliverQuantity()),
		}
	}
	rspLog := make([]*supplierv1.OrderOperationLog, len(orderDo.OrderOperatorLog))
	for i, logDo := range orderDo.OrderOperatorLog {
		rspLog[i] = &supplierv1.OrderOperationLog{
			Content:          logDo.Content,
			CreateTime:       helper.GetTimeDate(int(logDo.CreateTime)),
			OperatorUserName: logDo.OperatorUserName,
			OrderStatus:      int32(logDo.OrderStatus),
			OrderStatusText:  logDo.OrderStatus.GetName(),
			Id:               0,
		}
	}

	rspOrder := &supplierv1.OrderData{
		Id:                 int32(orderDo.ID),
		OrderNumber:        orderDo.OrderNumber,
		CreateTime:         helper.GetTimeDate(orderDo.CreateTime),
		DelayDeliverRemark: orderDo.DelayDeliverRemark,
		OrderType:          int32(orderDo.OrderType),
		OrderTypeText:      orderDo.OrderType.GetName(),
		OrderStatus:        int32(orderDo.Status),
		OrderStatusText:    orderDo.Status.GetName(),
		DeliverStatus:      int32(orderDo.DeliverStatus),
		DeliverStatusText:  orderDo.GetDeliverStatusText(),
		ConsigneeName:      orderDo.GetOrderUserAddress().GetName(),
		ConsigneePhone:     orderDo.GetOrderUserAddress().GetPhoneNumber(),
		ConsigneeAddress:   orderDo.GetOrderUserAddress().GetAddress(),
		DeliverAddress:     "",
		Goods:              rspGoods,
		Operation:          rspLog,
		FreightFee:         orderDo.FreightFee,
		IsTimeout:          orderDo.GetIsTimeout(),
		DeliverExpireTime:  helper.GetTimeDate(orderDo.DeliverExpireTime),
		CardGiftFreightFee: orderDo.GetCardGiftFreightFee(),
	}
	return rspOrder
}
