package convertor

import (
	"context"
	"encoding/json"
	"sort"

	"cardMall/api/supplierv1"
	"cardMall/internal/biz"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/excel"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/server/middleware/authtools"
	"cardMall/internal/service/supplier/validate"
	"github.com/xuri/excelize/v2"
)

type GoodsConvertor struct {
	categoryBiz      *supplierbiz.CategoryBiz
	areaBaseBiz      *supplierbiz.AreaBaseBiz
	supplierGoodsBiz *supplierbiz.GoodsBiz
	brandBiz         *supplierbiz.BrandBiz
	transportBiz     *supplierbiz.TransportBiz
	uploadBiz        *biz.FileUploadBiz
	specBiz          *supplierbiz.SpecBiz
	baseGoodsBiz     *supplierbiz.BaseGoodsBiz
}

func NewGoodsConvertor(
	categoryBiz *supplierbiz.CategoryBiz,
	areaBaseBiz *supplierbiz.AreaBaseBiz,
	supplierGoodsBiz *supplierbiz.GoodsBiz,
	brandBiz *supplierbiz.BrandBiz,
	transportBiz *supplierbiz.TransportBiz,
	uploadBiz *biz.FileUploadBiz,
	specBiz *supplierbiz.SpecBiz,
	baseGoodsBiz *supplierbiz.BaseGoodsBiz,
) *GoodsConvertor {
	return &GoodsConvertor{
		categoryBiz:      categoryBiz,
		areaBaseBiz:      areaBaseBiz,
		supplierGoodsBiz: supplierGoodsBiz,
		brandBiz:         brandBiz,
		transportBiz:     transportBiz,
		uploadBiz:        uploadBiz,
		specBiz:          specBiz,
		baseGoodsBiz:     baseGoodsBiz,
	}
}

// GoodsDoToGoodsDetailData .
func (g *GoodsConvertor) GoodsDoToGoodsDetailData(ctx context.Context, goodsDo *do.SupplierGoodsDo) (*supplierv1.GoodsDetailData, error) {
	specRsp := make([]*supplierv1.Spec, 0)
	skuItemsRsp := make([]*supplierv1.SkuItems, 0)
	banSkuItemsRsp := make([]*supplierv1.SkuItems, 0, len(goodsDo.BanSkuJson))

	_ = json.Unmarshal([]byte(goodsDo.SpecJson), &specRsp)

	specIdMap := make(map[int32]*supplierv1.SpecItems)
	for _, spec := range specRsp {
		for _, item := range spec.Items {
			specIdMap[item.Id] = item
		}
	}

	existSkuMap := make(map[string]struct{})
	for _, skuDo := range goodsDo.GoodsSku {
		specIds := make([]int32, 0)
		specItemIds := helper.SliceConvertSlice[int32](skuDo.GetSpecItemIds())
		for _, specId := range specItemIds {
			specItem, ok := specIdMap[specId]
			if ok {
				specIds = append(specIds, specItem.SpecId)
			}
		}
		sort.Slice(specItemIds, func(i, j int) bool {
			return specItemIds[i] < specItemIds[j]
		})
		if skuDo.Status == valobj.SupplierGoodsStatusDraft {
			continue
		}
		existSkuMap[helper.SliceJoin(specItemIds, ",")] = struct{}{}
		skuItemsRsp = append(skuItemsRsp, &supplierv1.SkuItems{
			SpecItemIds:     specItemIds,
			SpecIds:         specIds,
			Barcode:         skuDo.Barcode,
			SupplierBarcode: skuDo.SupplierBarcode,
			MarketPrice:     skuDo.MarketPrice,
			// SalePrice:        skuDo.SalePrice,
			// FreePostagePrice: skuDo.FreePostagePrice,
			Stock:           int32(skuDo.Stock),
			ProductId:       skuDo.ProductID,
			SupplierPrice:   skuDo.SupplierPrice,
			Id:              int32(skuDo.ID),
			ProductType:     int32(skuDo.ProductType),
			ProductTypeText: skuDo.ProductType.GetName(),
			ProductName:     skuDo.ProductName,
			SkuNo:           skuDo.SkuNo,
		})
	}

	for _, skuDo := range goodsDo.BanSkuJson {
		specIds := make([]int32, 0)
		specItemIds := helper.SliceConvertSlice[int32](skuDo.GetSpecItemIds())
		for _, specId := range specItemIds {
			specItem, ok := specIdMap[specId]
			if ok {
				specIds = append(specIds, specItem.SpecId)
			}
		}
		sort.Slice(specItemIds, func(i, j int) bool {
			return specItemIds[i] < specItemIds[j]
		})
		if _, ok := existSkuMap[helper.SliceJoin(specItemIds, ",")]; ok {
			continue
		}
		banSkuItemsRsp = append(banSkuItemsRsp, &supplierv1.SkuItems{
			SpecItemIds:     specItemIds,
			SpecIds:         specIds,
			Barcode:         skuDo.Barcode,
			SupplierBarcode: skuDo.SupplierBarcode,
			MarketPrice:     skuDo.MarketPrice,
			Stock:           int32(skuDo.Stock),
			ProductId:       skuDo.ProductID,
			SupplierPrice:   skuDo.SupplierPrice,
			ProductType:     int32(skuDo.ProductType),
			ProductTypeText: skuDo.ProductType.GetName(),
			ProductName:     skuDo.ProductName,
			SkuNo:           skuDo.SkuNo,
		})
	}

	categoryIds, err := g.categoryBiz.GetCategoryIdWithParent(ctx, goodsDo.CategoryID)
	if err != nil {
		return nil, err
	}

	notAreaIds := goodsDo.GetNotSaleAreaSlice()

	areaDos, err := g.areaBaseBiz.FindAreaByIds(ctx, helper.SliceConvertSlice[int](notAreaIds))
	if err != nil {
		return nil, err
	}

	notSaleAreaData := make([]*supplierv1.AreaItem, 0)
	for _, areaDo := range areaDos {
		notSaleAreaData = append(notSaleAreaData, &supplierv1.AreaItem{
			Id:   int32(areaDo.ID),
			Name: areaDo.Name,
		})
	}

	rsp := &supplierv1.GoodsDetailData{
		Type:        int32(goodsDo.Type),
		Name:        goodsDo.Name,
		CategoryId:  int32(goodsDo.CategoryID),
		BrandId:     int32(goodsDo.BrandID),
		Images:      goodsDo.ImagesToSlice(),
		Spec:        specRsp,
		SkuItems:    skuItemsRsp,
		TaxRate:     goodsDo.TaxRate,
		TaxRemark:   goodsDo.TaxRemark,
		BanSkuItems: banSkuItemsRsp,
		Detail:      goodsDo.Detail,
		Online:      goodsDo.IsOnline(),
		Id:          int32(goodsDo.ID),
		NotSaleArea: helper.SliceConvertSlice[int32](notAreaIds),
		// DeliverAddr:     helper.SliceConvertSlice[int32](goodsDo.GetDeliverAddrSlice()),
		// SpecJson:        goodsDo.GetSpecJsonStruct(),
		CategoryIds:     helper.SliceConvertSlice[int32](categoryIds),
		NotSaleAreaJson: notSaleAreaData,
		TransportId:     int32(goodsDo.TransportID),
		DeliverTimeline: int32(goodsDo.DeliverTimeline),
	}
	return rsp, nil
}

// ValidateGoodsRow 校验商品信息
func (g *GoodsConvertor) ValidateGoodsRow(ctx context.Context, goodsType valobj.SupplierGoodsTypeObj, goodsRows [][]string, headerMap map[string]int, excelFile *excelize.File) *validate.ValidateGoodsBos {
	list := make([]*validate.ValidateGoodsBo, 0, len(goodsRows))
	categoryNames := make([]string, 0, len(goodsRows))
	goodsNames := make([]string, 0, len(goodsRows))
	brandNames := make([]string, 0, len(goodsRows))
	transportNames := make([]string, 0, len(goodsRows))
	areaNames := make([]string, 0, len(goodsRows))
	relationGoodsIds := make([]string, 0, len(goodsRows))
	for index, goodsRow := range goodsRows {
		if goodsType == valobj.SupplierGoodsTypeEntity && len(goodsRow) <= headerMap["物流模板"] {
			continue
		}
		if goodsType == valobj.SupplierGoodsTypeVirtual && len(goodsRow) <= headerMap["库存"] {
			continue
		}
		item := new(validate.ValidateGoodsBo).BindRow(goodsType, index+3+1, goodsRow, headerMap, excelFile)
		list = append(list, item)
		categoryNames = append(categoryNames, item.Category[:]...)
		goodsNames = append(goodsNames, item.GoodsName)
		brandNames = append(brandNames, item.Brand)
		transportNames = append(transportNames, item.LogisticsTemplate)
		areaNames = append(areaNames, item.BanSaleAreas...)
		relationGoodsIds = append(relationGoodsIds, item.RelationGoodsId)
	}
	opts := []validate.ValidateGoodsBosOption{
		validate.WithValidateGoodsBosCategory(g.categoryBiz.GetCategoryMap(ctx, helper.SliceSortUnique(categoryNames, func(v string) string { return v }))),
		validate.WithValidateGoodsBosGoods(g.supplierGoodsBiz.GetGoodsMap(ctx, helper.SliceSortUnique(goodsNames, func(v string) string { return v }))),
		validate.WithValidateGoodsBosBrand(g.brandBiz.GetBrandMap(ctx, helper.SliceSortUnique(brandNames, func(v string) string { return v }))),
		validate.WithValidateGoodsBosUpload(nil),
	}
	if goodsType.IsEntity() {
		opts = append(opts,
			validate.WithValidateGoodsBosTransport(g.transportBiz.GetTransportMap(ctx, helper.SliceSortUnique(transportNames, func(v string) string { return v }))),
			validate.WithValidateGoodsBosArea(g.areaBaseBiz.GetAreaMap(ctx, helper.SliceSortUnique(areaNames, func(v string) string { return v }))),
		)
	}
	if goodsType.IsVirtual() {
		opts = append(opts, validate.WithValidateRelationGoods(g.baseGoodsBiz.GetRelationGoodsMap(ctx, helper.SliceSortUnique(relationGoodsIds, func(v string) string { return v }))))
	}
	return validate.NewValidateGoodsBos(list, opts...).Validate()
}

// ValidateGoodsBo 校验商品信息
func (g *GoodsConvertor) ValidateGoodsBo(ctx context.Context, goodsApis []*supplierv1.UploadGoodsData) *validate.ValidateGoodsBos {
	if len(goodsApis) == 0 {
		return nil
	}
	list := make([]*validate.ValidateGoodsBo, 0, len(goodsApis))
	categoryNames := make([]string, 0, len(goodsApis))
	goodsNames := make([]string, 0, len(goodsApis))
	brandNames := make([]string, 0, len(goodsApis))
	transportNames := make([]string, 0, len(goodsApis))
	areaNames := make([]string, 0, len(goodsApis))
	goodsType := valobj.SupplierGoodsTypeObj(goodsApis[0].GoodsType)
	relationGoodsIds := make([]string, 0, len(goodsApis))
	for _, goodsApi := range goodsApis {
		if goodsType != valobj.SupplierGoodsTypeObj(goodsApi.GoodsType) {
			continue
		}
		item := new(validate.ValidateGoodsBo).BindApi(goodsApi)
		list = append(list, item)
		categoryNames = append(categoryNames, item.Category[:]...)
		goodsNames = append(goodsNames, item.GoodsName)
		brandNames = append(brandNames, item.Brand)
		transportNames = append(transportNames, item.LogisticsTemplate)
		areaNames = append(areaNames, item.BanSaleAreas...)
		relationGoodsIds = append(relationGoodsIds, item.RelationGoodsId)
	}
	opts := []validate.ValidateGoodsBosOption{
		validate.WithValidateGoodsBosCategory(g.categoryBiz.GetCategoryMap(ctx, helper.SliceSortUnique(categoryNames, func(v string) string { return v }))),
		validate.WithValidateGoodsBosGoods(g.supplierGoodsBiz.GetGoodsMap(ctx, helper.SliceSortUnique(goodsNames, func(v string) string { return v }))),
		validate.WithValidateGoodsBosBrand(g.brandBiz.GetBrandMap(ctx, helper.SliceSortUnique(brandNames, func(v string) string { return v }))),
		validate.WithValidateGoodsBosUpload(g.uploadBiz.Upload),
	}
	if goodsType.IsEntity() {
		opts = append(opts,
			validate.WithValidateGoodsBosTransport(g.transportBiz.GetTransportMap(ctx, helper.SliceSortUnique(transportNames, func(v string) string { return v }))),
			validate.WithValidateGoodsBosArea(g.areaBaseBiz.GetAreaMap(ctx, helper.SliceSortUnique(areaNames, func(v string) string { return v }))),
		)
	}
	if goodsType.IsVirtual() {
		opts = append(opts, validate.WithValidateRelationGoods(g.baseGoodsBiz.GetRelationGoodsMap(ctx, helper.SliceSortUnique(relationGoodsIds, func(v string) string { return v }))))
	}
	return validate.NewValidateGoodsBos(list, opts...).Validate()
}

// GoodsBoToUploadGoodsData 转换商品数据为api层所需数据
func (g *GoodsConvertor) GoodsBoToUploadGoodsData(_ context.Context, goodsBos *validate.ValidateGoodsBos) []*supplierv1.UploadGoodsData {
	goodsData := make([]*supplierv1.UploadGoodsData, 0, len(goodsBos.GetList()))
	for _, goodsBo := range goodsBos.GetList() {
		goodsData = append(goodsData, &supplierv1.UploadGoodsData{
			Index:             goodsBo.Index,
			Brand:             goodsBo.Brand,
			Category:          goodsBo.Category[:],
			GoodsName:         goodsBo.GoodsName,
			GoodsImage:        goodsBo.GoodsImage,
			TaxRate:           goodsBo.TaxRate,
			TaxRateRemark:     goodsBo.TaxRateRemark,
			Spec:              goodsBo.Spec,
			SpecImage:         goodsBo.SpecImage,
			OriginalCode:      goodsBo.OriginalCode,
			SupplyPrice:       goodsBo.SupplyPrice,
			MarketPrice:       goodsBo.MarketPrice,
			Stock:             int32(goodsBo.Stock),
			LogisticsTemplate: goodsBo.LogisticsTemplate,
			BanSaleAreas:      goodsBo.BanSaleAreas,
			Remark:            goodsBo.Remark,
			IsOnSale:          goodsBo.IsOnSale,
			Errors: helper.SliceTo(goodsBo.Errors, func(err error) (string, bool) {
				if err == nil {
					return "", false
				}
				return err.Error(), true
			}),
			GoodsOssImage:         goodsBo.GoodsImageOss,
			SpecOssImage:          goodsBo.SpecImageOss,
			GoodsImageBytesB64:    excel.BytesToBase64(goodsBo.GoodsImageBytes),
			GoodsImageBytesB64Ext: goodsBo.GoodsImageBytesExt,
			SpecImageBytesB64:     excel.BytesToBase64(goodsBo.SpecImageBytes),
			SpecImageBytesB64Ext:  goodsBo.SpecImageBytesExt,
			GoodsNameRelation:     goodsBo.RelationGoodsName,
			RelationGoodsId:       goodsBo.RelationGoodsId,
			GoodsType:             int32(goodsBo.Type),
		})
	}
	return goodsData
}

// GoodsBoToSaveGoodsBo 转换商品数据为保存商品所需
func (g *GoodsConvertor) GoodsBoToSaveGoodsBo(ctx context.Context, goodsBos *validate.ValidateGoodsBos) []*commonbo.SaveGoodsBo {
	adminLoginInfoBo := authtools.GetLoginInfoX(ctx).ToLoginInfo()
	list := goodsBos.GetList()

	specMap := goodsBos.ConvertToSpecs()
	goodsOnlineMap := make(map[string]bool)

	// 不合法商品
	warnGoodsNameMap := make(map[string]struct{})
	for goodsName, specList := range specMap {
		specDataList := make([]*bo.SpecData, 0, len(list))
		existGoodsDo, ok := goodsBos.GoodsMap[goodsName]
		if ok {
			// 合并specList
			specList, _ = goodsBos.MergeSpecList(specList, existGoodsDo)
		}
		if len(specList) == 0 {
			// 存在不合法数据
			warnGoodsNameMap[goodsName] = struct{}{}
			continue
		}
		specItemNamesMap := make(map[string][]string)
		for index, spec := range specList {
			itemIndex := 0
			if _, ok := specItemNamesMap[spec.Name]; !ok {
				specItemNamesMap[spec.Name] = make([]string, 0, len(spec.Items))
			}
			specDataList = append(specDataList, &bo.SpecData{
				Id:   spec.Id,
				Key:  index + 1,
				Name: spec.Name,
				Items: helper.SliceTo(spec.Items, func(item *commonbo.SpecItems) (*bo.SpecItemsData, bool) {
					itemIndex++
					specItemNamesMap[spec.Name] = append(specItemNamesMap[spec.Name], item.Name)
					return &bo.SpecItemsData{
						Id:       item.Id,
						Key:      itemIndex,
						Name:     item.Name,
						SpecId:   item.SpecId,
						SpecName: item.SpecName,
					}, true
				}),
			})
		}
		// 创建 spec
		resData, err := g.specBiz.CreateGoodsSpec(ctx, adminLoginInfoBo.SupplierId, &bo.CreateGoodsSpecBo{
			SpecData: specDataList,
		})
		if err != nil {
			panic(err)
		}

		saveOKSpecDataMap := make(map[string]*bo.SpecData)
		for _, spec := range resData.SpecData {
			saveOKSpecDataMap[spec.Name] = spec
		}

		specTmp := helper.SliceTo(specList, func(spec *commonbo.Spec) (*commonbo.Spec, bool) {
			saveOKSpecData, ok := saveOKSpecDataMap[spec.Name]
			if !ok {
				return nil, false
			}
			specItemsMap := make(map[string]*bo.SpecItemsData)
			for _, item := range saveOKSpecData.Items {
				specItemsMap[item.Name] = item
			}
			specItemNames, ok := specItemNamesMap[spec.Name]
			if !ok {
				return nil, false
			}
			return &commonbo.Spec{
				Id:       saveOKSpecData.Id,
				Name:     saveOKSpecData.Name,
				AddImage: false,
				Items: helper.SliceSortUnique(helper.SliceTo(specItemNames, func(name string) (*commonbo.SpecItems, bool) {
					item, ok := specItemsMap[name]
					if !ok {
						return nil, false
					}
					return &commonbo.SpecItems{
						Id:       item.Id,
						SpecId:   item.SpecId,
						Name:     item.Name,
						SpecName: item.SpecName,
						Image:    "",
					}, true
				}), func(item *commonbo.SpecItems) string {
					return item.Name
				}),
			}, true
		})
		specMap[goodsName] = helper.SliceSortUnique(specTmp, func(spec *commonbo.Spec) string { return spec.Name })
	}
	goodsMap := make(map[string]*commonbo.SaveGoodsBo, len(list))
	for _, goodsBo := range list {
		if _, ok := goodsOnlineMap[goodsBo.GoodsName]; !ok {
			goodsOnlineMap[goodsBo.GoodsName] = goodsBo.IsOnSale
		}

		_, exist := warnGoodsNameMap[goodsBo.GoodsName]
		if exist {
			// 在异常商品列表里面，直接跳过该商品
			continue
		}
		if goodsBo.IsError() {
			continue
		}
		goodsSpecMap := goodsBo.GetSpecsMap()
		specs := specMap[goodsBo.GoodsName]
		if len(specs) == 0 || len(specs[0].Items) == 0 {
			continue
		}
		specs[0].AddImage = goodsBo.SpecImageOss != "" || specs[0].AddImage
		for index := range specs[0].Items {
			specItemName, ok := goodsSpecMap[specs[0].Name]
			if !ok {
				continue
			}
			if specItemName == specs[0].Items[index].Name {
				specs[0].Items[index].Image = goodsBo.SpecImageOss
				continue
			}
		}
		dbGoods, ok := goodsBos.GoodsMap[goodsBo.GoodsName]
		if ok {
			// 合并规格图片 没有图片则不更新， 保留原始图片
			specList := dbGoods.GetSpecBo()
			existSpecMap := make(map[string]string)
			for _, spec := range specList {
				for _, item := range spec.Items {
					existSpecMap[item.SpecName+"--"+item.Name] = item.Image
				}
			}
			for index := range specs[0].Items {
				if specs[0].Items[index].Image != "" {
					continue
				}
				specs[0].Items[index].Image = existSpecMap[specs[0].Items[index].SpecName+"--"+specs[0].Items[index].Name]
			}
		}

		categoryDetail, err := goodsBos.GetCategory(goodsBo.Category)
		if err != nil {
			continue
		}
		specItems := validate.ConvertToSpecItems(specs, goodsBo.Spec)
		specItemIds := helper.SliceSortUnique(helper.SliceTo(specItems, func(spec *commonbo.SpecItems) (int, bool) {
			return spec.Id, true
		}), func(item int) int { return item })
		sort.Ints(specItemIds)
		commonBoSkuItem := &commonbo.SkuItems{
			SpecItemIds:      specItemIds,
			Barcode:          goodsBo.OriginalCode,
			SupplierBarcode:  "",
			MarketPrice:      goodsBo.MarketPrice,
			SalePrice:        0,
			FreePostagePrice: 0,
			Stock:            goodsBo.Stock,
			ProductId:        "",
			ProductType:      0,
			ProductName:      goodsBo.GoodsName,
			SupplierPrice:    goodsBo.SupplyPrice,
		}
		if goodsBo.Type == valobj.SupplierGoodsTypeVirtual {
			relationGoodsDo, ok := goodsBos.RelationGoodsMap[goodsBo.RelationGoodsId+"--"+goodsBo.RelationGoodsName]
			if !ok {
				continue
			}
			commonBoSkuItem = &commonbo.SkuItems{
				SpecItemIds:      commonBoSkuItem.SpecItemIds,
				Barcode:          "",
				SupplierBarcode:  "",
				MarketPrice:      relationGoodsDo.OriginalPrice,
				SalePrice:        relationGoodsDo.OriginalPrice,
				FreePostagePrice: 0,
				Stock:            commonBoSkuItem.Stock,
				ProductId:        relationGoodsDo.ProductID,
				ProductType:      valobj.VirtualProductTypeObj(relationGoodsDo.Type),
				ProductName:      relationGoodsDo.ProductName,
				SupplierPrice:    relationGoodsDo.ChannelPrice,
			}
		}

		goodsItem := &commonbo.SaveGoodsBo{
			Id:         goodsBo.ID,
			Type:       goodsBo.Type,
			Name:       goodsBo.GoodsName,
			CategoryId: categoryDetail.GetID(),
			BrandId:    goodsBos.BrandMap[goodsBo.Brand].GetID(),
			Images:     []string{},
			Spec:       specs,
			SkuItems:   []*commonbo.SkuItems{commonBoSkuItem},
			TaxRate:    goodsBo.TaxRate,
			TaxRemark:  goodsBo.TaxRateRemark,
			SaleArea:   nil,
			NotSaleArea: helper.SliceTo(goodsBo.BanSaleAreas, func(areaStr string) (int, bool) {
				area, ok := goodsBos.AreaMap[areaStr]
				if !ok || area == nil {
					return 0, false
				}
				return area.ID, true
			}),
			DeliverAddr:      nil,
			Detail:           goodsBo.Remark,
			Online:           goodsOnlineMap[goodsBo.GoodsName],
			DraftId:          0,
			TransportId:      goodsBos.TransportMap[goodsBo.LogisticsTemplate].GetID(),
			AdminLoginInfoBo: adminLoginInfoBo,
		}
		if goodsBos.ExistGoodsImages == nil {
			goodsBos.ExistGoodsImages = make(map[string]map[string]struct{})
		}
		_, ok = goodsBos.ExistGoodsImages[goodsBo.GoodsName]
		if !ok {
			goodsBos.ExistGoodsImages[goodsBo.GoodsName] = make(map[string]struct{})
		}
		goodsImageMap := goodsBos.ExistGoodsImages[goodsBo.GoodsName]
		if _, ok := goodsImageMap[goodsBo.GoodsImage]; !ok {
			if goodsBo.GoodsImageOss != "" {
				goodsBos.ExistGoodsImages[goodsBo.GoodsName][goodsBo.GoodsImage] = struct{}{}
				goodsItem.Images = append(goodsItem.Images, goodsBo.GoodsImageOss)
			}
		}

		existGoods, ok := goodsMap[goodsBo.GoodsName]
		if !ok {
			goodsMap[goodsBo.GoodsName] = goodsItem
			continue
		}
		// 添加spec等信息
		existGoods.Images = append(existGoods.Images, goodsItem.Images...)

		existGoods.SkuItems = helper.SliceSortUnique(append(existGoods.SkuItems, goodsItem.SkuItems...), func(skuItem *commonbo.SkuItems) string {
			sItemIds := helper.SliceSortUnique(skuItem.SpecItemIds, func(specItemId int) int { return specItemId })
			sort.Ints(sItemIds)
			return helper.SliceJoin(sItemIds, ",")
		})
		existGoods.NotSaleArea = append(existGoods.NotSaleArea, goodsItem.NotSaleArea...)
		goodsMap[goodsBo.GoodsName] = existGoods
	}

	goodsRespList := make([]*commonbo.SaveGoodsBo, 0, len(goodsMap))
	for goodsName, goods := range goodsMap {
		if len(goods.Images) == 0 {
			// 没有主图， 直接跳过
			continue
		}
		existGoodsDo, ok := goodsBos.GoodsMap[goodsName]
		if ok {
			for _, skuDo := range existGoodsDo.GoodsSku {
				specItemIds := helper.SliceSortUnique(skuDo.GetSpecItemIds(), func(specItemId int) int { return specItemId })
				sort.Ints(specItemIds)
				goods.SkuItems = append(goods.SkuItems, &commonbo.SkuItems{
					SpecItemIds:      specItemIds,
					Barcode:          skuDo.Barcode,
					SupplierBarcode:  skuDo.SupplierBarcode,
					MarketPrice:      skuDo.MarketPrice,
					SalePrice:        skuDo.SalePrice,
					FreePostagePrice: skuDo.FreePostagePrice,
					Stock:            skuDo.Stock,
					ProductId:        skuDo.ProductID,
					ProductType:      skuDo.ProductType,
					ProductName:      skuDo.ProductName,
					SupplierPrice:    skuDo.SupplierPrice,
				})
			}
		}
		goods.SkuItems = helper.SliceSortUnique(goods.SkuItems, func(skuItem *commonbo.SkuItems) string {
			return helper.SliceJoin(skuItem.SpecItemIds, ",")
		})
		goodsRespList = append(goodsRespList, goods)
	}
	return goodsRespList
}
