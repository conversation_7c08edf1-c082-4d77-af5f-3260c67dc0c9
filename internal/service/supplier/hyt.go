package supplier

import (
	"cardMall/api/apierr"
	"cardMall/api/common"
	"cardMall/api/supplierv1"
	"cardMall/internal/biz"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/conf"
	"cardMall/internal/manager"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/pkg/hyt"
	"cardMall/internal/service"
	"cardMall/internal/service/common/convertor"
	"cardMall/internal/service/task"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"strings"
	"time"
)

type HytService struct {
	service.AdminBase
	supplierv1.UnimplementedHytSrvServer

	conf                      *conf.Bootstrap
	log                       *log.Helper
	hytClient                 *hyt.Client
	huoYiTongBiz              *biz.HuoYiTongBiz
	pageConvertor             *convertor.PageConvertor
	goodsPriceAutoSyncManager *manager.GoodsPriceAutoSyncManager
	hytGoodsRepo              repository.HytGoodsRepo
	afterSaleBiz              *supplierbiz.AfterSaleBiz
	syncHytGoodsService       *task.SyncHytGoodsService
}

func NewHytService(conf *conf.Bootstrap, log *log.Helper, hytClient *hyt.Client, huoYiTongBiz *biz.HuoYiTongBiz, pageConvertor *convertor.PageConvertor, goodsPriceAutoSyncManager *manager.GoodsPriceAutoSyncManager, hytGoodsRepo repository.HytGoodsRepo, afterSaleBiz *supplierbiz.AfterSaleBiz, syncHytGoodsService *task.SyncHytGoodsService) *HytService {
	return &HytService{conf: conf, log: log, hytClient: hytClient, huoYiTongBiz: huoYiTongBiz, pageConvertor: pageConvertor, goodsPriceAutoSyncManager: goodsPriceAutoSyncManager, hytGoodsRepo: hytGoodsRepo, afterSaleBiz: afterSaleBiz, syncHytGoodsService: syncHytGoodsService}
}

func (h HytService) SyncBrand(ctx context.Context, empty *common.ReqEmpty) (*common.RespEmpty, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	loginInfo := h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	err := h.huoYiTongBiz.SyncBrand(ctx, loginInfo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, err
}

func (h HytService) SyncCategory(ctx context.Context, empty *common.ReqEmpty) (*common.RespEmpty, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	loginInfo := h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	err := h.huoYiTongBiz.SyncCategory(ctx, loginInfo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, err
}

func (h HytService) BindBrand(ctx context.Context, request *supplierv1.BindBrandRequest) (*common.RespEmpty, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	reqBo := new(bo.BindBrandRequest)
	_ = mapstructure.Decode(request, &reqBo)
	if err := request.Validate(); err != nil {
		return nil, err
	}
	reqBo.AdminLoginInfoBo = h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()

	err := h.huoYiTongBiz.BindBrand(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, err
}

func (h HytService) BindCategory(ctx context.Context, request *supplierv1.BindCategoryRequest) (*common.RespEmpty, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	reqBo := new(bo.BindCategoryRequest)
	_ = mapstructure.Decode(request, &reqBo)
	if err := request.Validate(); err != nil {
		return nil, err
	}
	reqBo.AdminLoginInfoBo = h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	err := h.huoYiTongBiz.BindCategory(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, err
}

func (h HytService) ListBrand(ctx context.Context, request *supplierv1.ListBrandRequest) (*supplierv1.ListBrandResponse, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	reqBo := new(bo.HytBrandSearchBo)
	_ = mapstructure.Decode(request, &reqBo)
	if err := request.Validate(); err != nil {
		return nil, err
	}
	reqBo.AdminLoginInfoBo = h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	dos, pageInfo := h.huoYiTongBiz.ListBrand(ctx, reqBo)
	return &supplierv1.ListBrandResponse{
		Page:  h.pageConvertor.ToRespPage(pageInfo),
		Items: h.toListBrand(dos),
	}, nil
}
func (h HytService) toListBrand(dos []*do.HytBrandDo) []*supplierv1.HytBrand {
	res := make([]*supplierv1.HytBrand, 0)
	for _, item := range dos {
		res = append(res, &supplierv1.HytBrand{
			Name:     item.HytName,
			IsBind:   item.IsBind,
			BindId:   int32(item.BindId),
			BindName: item.BindName,
		})
	}
	return res
}

func (h HytService) ListCategory(ctx context.Context, request *supplierv1.ListCategoryRequest) (*supplierv1.ListCategoryResponse, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	reqBo := new(bo.HytCategorySearchBo)
	_ = mapstructure.Decode(request, &reqBo)
	if err := request.Validate(); err != nil {
		return nil, err
	}
	reqBo.AdminLoginInfoBo = h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	dos, pageInfo := h.huoYiTongBiz.ListCategory(ctx, reqBo)
	return &supplierv1.ListCategoryResponse{
		Page:  h.pageConvertor.ToRespPage(pageInfo),
		Items: h.toListCategory(dos),
	}, nil
}

func (h HytService) toListCategory(dos []*do.HytCategoryDo) []*supplierv1.HytCategory {
	res := make([]*supplierv1.HytCategory, 0)
	for _, item := range dos {
		res = append(res, &supplierv1.HytCategory{
			Id:       int32(item.HytID),
			Name:     item.HytName,
			IsBind:   item.IsBind,
			BindId:   int32(item.BindId),
			BindName: item.BindName,
		})
	}
	return res
}
func (h HytService) UnbindBrand(ctx context.Context, request *supplierv1.UnbindBrandRequest) (*common.RespEmpty, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	reqBo := new(bo.UnbindBrandRequest)
	_ = mapstructure.Decode(request, &reqBo)
	if err := request.Validate(); err != nil {
		return nil, err
	}
	reqBo.AdminLoginInfoBo = h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	err := h.huoYiTongBiz.UnbindBrand(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, err
}
func (h HytService) DealAfterSale(httpCtx http.Context) error {
	query := httpCtx.Request().URL.Query()
	if query.Get("token") != fmt.Sprintf("xx%sxx", time.Now().Format("2006010215")) {
		return apierr.ErrorException("token error")
	}
	return h.afterSaleBiz.ListWaitSupplierApproveToApply(context.Background())
}
func (h HytService) Sync(httpCtx http.Context) error {
	query := httpCtx.Request().URL.Query()
	if query.Get("token") != fmt.Sprintf("xx%sxx", time.Now().Format("2006010215")) {
		return apierr.ErrorException("token error")
	}

	goodsNumsStr := query.Get("goods_nums")
	var goodsNums []string
	if goodsNumsStr != "" {
		goodsNums = strings.Split(goodsNumsStr, ",")
	}
	//if err := h.syncHytGoodsService.SyncBrand(); err != nil {
	//	h.log.Errorf("同步货易通商品时，同步品牌错误 err:%v", err)
	//}
	if err := h.syncHytGoodsService.SyncCategory(); err != nil {
		h.log.Errorf("同步货易通商品时，同步分类错误 err:%v", err)
	}
	return h.syncHytGoodsService.SyncGoods(goodsNums)
}
func (h HytService) UnbindCategory(ctx context.Context, request *supplierv1.UnbindCategoryRequest) (*common.RespEmpty, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂未开放")
	reqBo := new(bo.UnbindCategoryRequest)
	_ = mapstructure.Decode(request, &reqBo)
	if err := request.Validate(); err != nil {
		return nil, err
	}
	reqBo.AdminLoginInfoBo = h.AdminBase.GetLoginInfoX(ctx).ToLoginInfo()
	err := h.huoYiTongBiz.UnbindCategory(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, err
}
