package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/helper"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

type AccountService struct {
	adminv1.UnimplementedAccountServer
	biz        *adminbiz.AdminBiz
	sysRoleBiz *adminbiz.SysRoleBiz
	sysMenuBiz *adminbiz.SysMenuBiz
	base
}

func NewAccountService(biz *adminbiz.AdminBiz, sysRoleBiz *adminbiz.SysRoleBiz, sysMenuBiz *adminbiz.SysMenuBiz) *AccountService {
	return &AccountService{biz: biz, sysRoleBiz: sysRoleBiz, sysMenuBiz: sysMenuBiz}
}

func (a *AccountService) Add(ctx context.Context, req *adminv1.AccountAddReq) (*adminv1.AccountAddRsp, error) {
	_, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	//if admin.IsSupperAdmin() {
	//	return nil, apierr.ErrorNotAllow("账号无此操作权限")
	//}
	in := &bo.AdminAddBo{
		Name:     req.GetName(),
		Account:  req.GetAccount(),
		Status:   valobj.AdminStatusObj(req.GetStatus()),
		Password: req.GetPassword(),
		RoleId:   int(req.RoleId),
		Mobile:   req.Mobile,
		Type:     valobj.AdminTypeShopAdmin,
	}
	if err = in.Validate(); err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	id, err := a.biz.AddAdmin(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &adminv1.AccountAddRsp{EffectRow: int32(id)}, nil
}
func (a *AccountService) Detail(ctx context.Context, req *adminv1.AccountDetailReq) (*adminv1.AccountDetailRsp, error) {
	if req.Id < 1 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	accountInfo, err := a.biz.AccountInfo(ctx, int(req.Id))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.AccountDetailRsp{
		Id:         int32(accountInfo.Id),
		Name:       accountInfo.Name,
		Account:    accountInfo.Account,
		Status:     int32(accountInfo.Status),
		CreateTime: helper.GetTimeDate(accountInfo.CreateTime),
		UpdateTime: helper.GetTimeDate(accountInfo.UpdateTime),
		Mobile:     accountInfo.Mobile,
		RoleId:     int32(accountInfo.RoleID),
		IsRoot:     int32(accountInfo.IsRoot),
	}, nil
}

func (a *AccountService) List(ctx context.Context, req *adminv1.AccountListReq) (*adminv1.AccountListRsp, error) {
	in := &bo.AdminListBo{
		Name:    req.GetName(),
		Account: req.GetAccount(),
		RoleId:  int(req.GetRoleId()),
		Status:  valobj.AdminStatusObj(req.GetStatus()),
		Mobile:  req.Mobile,
		ReqPageBo: bo.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		Type: valobj.AdminTypeShopAdmin,
	}
	rsp := &adminv1.AccountListRsp{
		Page: int32(in.GetPage()),
	}
	total, list, err := a.biz.ListAdmin(ctx, in)
	rsp.Count = int32(total)
	if total == 0 {
		return rsp, err
	}

	accountRole, err := a.getAccountRole(ctx, list)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	rsp.List = make([]*adminv1.AccountListRsp_AccountListItem, 0, len(list))
	for _, v := range list {
		item := &adminv1.AccountListRsp_AccountListItem{
			Id:         int32(v.Id),
			Name:       v.Name,
			Account:    v.Account,
			Status:     int32(v.Status),
			CreateTime: helper.GetTimeDate(v.CreateTime),
			UpdateTime: helper.GetTimeDate(v.UpdateTime),
			Mobile:     v.Mobile,
			IsRoot:     int32(v.IsRoot),
		}
		roleInfo, ok := accountRole[v.RoleID]
		if ok {
			item.RoleId = int32(roleInfo.Id)
			item.RoleName = roleInfo.RoleName
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, err
}
func (s *AccountService) getAccountRole(ctx context.Context, adminDos []*do.AdminDo) (map[int]*do.SysRoleDo, error) {

	roleIds := slice.Map(adminDos, func(_ int, item *do.AdminDo) int {
		return item.RoleID
	})
	sysRoleDos, err := s.sysRoleBiz.FindByRoleIds(ctx, roleIds)
	if err != nil {
		return nil, err
	}
	roleMap := slice.KeyBy(sysRoleDos, func(item *do.SysRoleDo) int {
		return item.Id
	})
	return roleMap, nil
}
func (a *AccountService) Update(ctx context.Context, req *adminv1.AccountUpdateReq) (*adminv1.AccountUpdateRsp, error) {
	admin, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	adminDo, err := a.biz.AccountInfo(ctx, admin.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if adminDo.Id == int(req.Id) {
		if !valobj.AdminStatusObj(req.GetStatus()).IsValid() {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己状态")
		}
		if req.GetRoleId() != 0 {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己角色")
		}
	}
	updateAdminDo, err := a.biz.AccountInfo(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	in := new(bo.AdminUpdateBo)
	in.Id = int(req.GetId())
	if req.GetPassword() != "" {
		in.Password = req.GetPassword()
	}
	if req.GetAccount() != "" {
		in.Account = req.GetAccount()
	}
	if req.GetName() != "" {
		in.Name = req.GetName()
	}
	if !valobj.AdminStatusObj(req.GetStatus()).IsValid() {
		if updateAdminDo.IsRoot.IsYes() {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管状态")
		}
		in.Status = valobj.AdminStatusObj(req.GetStatus())
	}
	if req.GetRoleId() != 0 {
		if updateAdminDo.IsRoot.IsYes() {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管角色")
		}
		in.RoleId = int(req.RoleId)
	}
	if req.Mobile != "" {
		in.Mobile = req.Mobile
	}
	if err = in.Validate(); err != nil {
		return nil, err
	}
	row, err := a.biz.Update(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}

	err = a.sysMenuBiz.RefreshUserRole(ctx, in.Id)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &adminv1.AccountUpdateRsp{EffectRow: int32(row)}, nil
}

func (a *AccountService) Del(ctx context.Context, req *adminv1.AccountDelReq) (*adminv1.AccountDelRsp, error) {
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	admin, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	adminDo, err := a.biz.AccountInfo(ctx, admin.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if adminDo.Id == int(req.Id) {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己")
	}

	updateAdminDo, err := a.biz.AccountInfo(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if updateAdminDo.IsRoot == valobj.AdminIsRootYes {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管")
	}

	row, err := a.biz.Del(ctx, id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.AccountDelRsp{EffectRow: int32(row)}, nil
}

func (a *AccountService) UpdateStatus(ctx context.Context, req *adminv1.AccountUpdateStatusReq) (*adminv1.AccountUpdateStatusRsp, error) {

	admin, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	adminDo, err := a.biz.AccountInfo(ctx, admin.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if adminDo.Id == int(req.Id) {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己")
	}
	updateAdminDo, err := a.biz.AccountInfo(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if updateAdminDo.IsRoot == valobj.AdminIsRootYes {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管")
	}

	in := &bo.AdminUpdateStatusBo{
		Id:     int(req.GetId()),
		Status: valobj.AdminStatusObj(req.GetStatus()),
	}
	if err = in.Validate(); err != nil {
		return nil, apierr.ErrorParam("%s", err.Error())
	}

	row, err := a.biz.UpdateStatus(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &adminv1.AccountUpdateStatusRsp{EffectRow: int32(row)}, nil
}
func (a *AccountService) SendSMS(ctx context.Context, req *adminv1.AccountSendSMSReq) (*adminv1.AccountSendSMSRsp, error) {
	adminDo, err := a.base.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	res, err := a.biz.SendSMS(ctx, adminDo)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.AccountSendSMSRsp{
		VerifyId:   res.VerifyId,
		ExpireTime: int32(res.ExpireTime),
	}, nil
}
func (a *AccountService) SMSCheck(ctx context.Context, req *adminv1.AccountSMSCheckReq) (*adminv1.AccountSMSCheckRsp, error) {
	adminDo, err := a.base.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	err = a.biz.AdminCheckSMS(ctx, adminDo, &bo.AdminCheckSmsBo{
		VerifyCode: req.VerifyCode,
		VerifyId:   req.VerifyId,
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.AccountSMSCheckRsp{
		Result: true,
	}, nil
}
