package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/service/task"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util"
	"context"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
)

type BaseGoodsService struct {
	adminv1.UnimplementedBaseGoodsServer
	conf                    *conf.Bootstrap
	biz                     *adminbiz.BaseGoodsBiz
	baseGoodsRefreshService *task.BaseGoodsRefreshService
}

func NewBaseGoodsService(conf *conf.Bootstrap, biz *adminbiz.BaseGoodsBiz, baseGoodsRefreshService *task.BaseGoodsRefreshService) *BaseGoodsService {
	return &BaseGoodsService{conf: conf, biz: biz, baseGoodsRefreshService: baseGoodsRefreshService}
}

// Pull 同步授权商品数据
func (b *BaseGoodsService) Pull(ctx context.Context, req *adminv1.BaseGoodsPullReq) (*adminv1.BaseGoodsPullRsp, error) {
	ctx = util.CopyValueCtx(ctx)
	err := b.baseGoodsRefreshService.Run(ctx)
	return &adminv1.BaseGoodsPullRsp{}, err
}

func (b *BaseGoodsService) List(ctx context.Context, req *adminv1.BaseGoodsListReq) (*adminv1.BaseGoodsListRsp, error) {
	var in = &bo.BaseGoodsQueryBo{
		ProductName: req.GetProductName(),
		ProductId:   req.GetProductId(),
		Status:      nil,
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		Type: make([]int, 0, len(req.GetType())),
	}
	if req.Status != nil {
		status := int(req.GetStatus())
		in.Status = &status
	}

	list := make([]*adminv1.BaseGoodsListRsp_BaseGoodsListItem, 0, in.GetPageSize())
	var rsp = &adminv1.BaseGoodsListRsp{
		List:  list,
		Page:  int32(in.GetPage()),
		Count: 0,
	}

	count, data, err := b.biz.List(ctx, in)
	if err != nil {
		return rsp, err
	}

	if count == 0 {
		return rsp, nil
	}
	rsp.Count = int32(count)

	for _, val := range data {
		item := &adminv1.BaseGoodsListRsp_BaseGoodsListItem{
			Id:             int32(val.Id),
			ProductId:      val.ProductId,
			ChannelPrice:   helper.Float64ToString(val.ChannelPrice, 4),
			OriginalPrice:  helper.Float64ToString(val.OriginalPrice, 4),
			ProductName:    val.ProductName,
			CreateTime:     helper.GetTimeDate(val.CreateTime),
			Status:         int32(val.Status),
			Type:           int32(val.Type),
			CardExpireTime: int32(val.CardExpireTime),
			AccountType:    helper.SliceConvertSlice[int32, int](val.ToAccountTypeInt()),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

// CardExpire 设置授权卡密商品的过期时间
func (b *BaseGoodsService) CardExpire(ctx context.Context, req *adminv1.BaseGoodsCardExpireReq) (*adminv1.BaseGoodsCardExpireRsp, error) {
	expireTime, err := helper.GetTimestamp(req.CardExpireTime)
	if err != nil {
		return nil, apierr.ErrorParam("参数错误:%d", req.CardExpireTime)
	}
	var in = &bo.BaseGoodsCardExpireBo{
		ProductId:      req.ProductId,
		CardExpireTime: int(expireTime),
	}

	row, err := b.biz.SetCardExpire(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.BaseGoodsCardExpireRsp{EffectRow: int32(row)}, nil
}

// UpdateStatus 启用禁用
func (b *BaseGoodsService) UpdateStatus(ctx context.Context, req *adminv1.BaseGoodsUpdateStatusReq) (*adminv1.BaseGoodsUpdateStatusRsp, error) {
	var in = &bo.BaseGoodsUpdateStatusBo{}
	_ = mapstructure.Decode(req, &in)

	row, err := b.biz.UpdateStatus(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.BaseGoodsUpdateStatusRsp{EffectRow: int32(row)}, nil
}

func (b *BaseGoodsService) All(ctx context.Context, req *adminv1.BaseGoodsAllReq) (*adminv1.BaseGoodsAllRsp, error) {
	var in = &bo.BaseGoodsQueryBo{
		ProductName: req.GetProductName(),
		ProductId:   req.GetProductId(),
		Status:      nil,
	}
	if req.Status != nil {
		status := int(req.GetStatus())
		in.Status = &status
	}

	list := make([]*adminv1.BaseGoodsAllRsp_BaseGoodsAllItem, 0, in.GetPageSize())
	var rsp = &adminv1.BaseGoodsAllRsp{
		Data: list,
	}

	data, err := b.biz.All(ctx, in)
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}

	if data == nil {
		return rsp, nil
	}

	for _, val := range data {
		if val.Type == valobj.BaseGoodsTypeUnknown {
			continue
		}
		item := &adminv1.BaseGoodsAllRsp_BaseGoodsAllItem{
			Id:             int32(val.Id),
			ProductId:      val.ProductId,
			ChannelPrice:   helper.Float64ToString(val.ChannelPrice, 4),
			OriginalPrice:  helper.Float64ToString(val.OriginalPrice, 4),
			ProductName:    val.ProductName,
			CreateTime:     helper.GetTimeDate(val.CreateTime),
			Status:         int32(val.Status),
			Type:           int32(val.Type),
			CardExpireTime: int32(val.CardExpireTime),
		}
		rsp.Data = append(rsp.Data, item)
	}
	return rsp, nil
}
