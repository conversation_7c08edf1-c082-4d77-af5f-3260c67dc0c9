package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/service"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
	"io"
	"os"
	"strings"
	"time"
)

type UserService struct {
	base
	adminv1.UnimplementedUserServer
	biz          *adminbiz.UserBiz
	sysConfigBiz *adminbiz.SysConfigBiz
	sysTaskBiz   *adminbiz.SysTaskBiz
	authHandler  *service.AuthHandler
}

func NewUserService(biz *adminbiz.UserBiz, sysConfigBiz *adminbiz.SysConfigBiz, sysTaskBiz *adminbiz.SysTaskBiz, authHandler *service.AuthHandler) *UserService {
	return &UserService{biz: biz, sysConfigBiz: sysConfigBiz, sysTaskBiz: sysTaskBiz, authHandler: authHandler}
}

func (u *UserService) List(ctx context.Context, req *adminv1.UserListReq) (*adminv1.UserListRsp, error) {
	var in = &bo.UserListBo{
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.PageSize),
			Page:     int(req.Page),
		},
		WxNickName:  req.NickName,
		WxOpenId:    req.WxOpenId,
		Id:          int(req.GetId()),
		PhoneNumber: req.GetPhoneNumber(),
		ClientType:  valobj.UserClientTypeObj(req.GetClientType()),
		Username:    req.Username,
	}
	if req.GetCreateTimeStart() != "" {
		createTimeStart, err := helper.GetTimestamp(req.GetCreateTimeStart())
		if err != nil {
			return nil, apierr.ErrorParam("参数错误")
		}
		in.CreateTimeStart = int(createTimeStart)
	}
	if req.GetCreateTimeEnd() != "" {
		createTimeEnd, err := helper.GetTimestamp(req.GetCreateTimeEnd())
		if err != nil {
			return nil, apierr.ErrorParam("参数错误")
		}
		in.CreateTimeEnd = int(createTimeEnd)
	}

	list := make([]*adminv1.UserListRspUser, 0, in.GetPageSize())
	var rsp = &adminv1.UserListRsp{
		List:  list,
		Count: 0,
		Page:  int32(in.GetPage()),
	}

	count, data, err := u.biz.List(ctx, in)
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}
	rsp.Count = int32(count)

	for _, val := range data {
		batchGiftList, _ := u.biz.FindCardBatchGift(ctx, val.ID)
		cardGiftBalance := decimal.Zero
		for _, batchGift := range batchGiftList {
			if batchGift.Status == valobj.CardBatchGiftStatusUnUsed {
				if batchGift.UseExpireEnd > int(time.Now().Unix()) {
					cardGiftBalance = cardGiftBalance.Add(decimal.NewFromFloat(batchGift.CardGiftBalance))
				}
			}
			if batchGift.Status == valobj.CardBatchGiftStatusUsed {
				if batchGift.UseExpireEnd > int(time.Now().Unix()) {
					cardGiftBalance = cardGiftBalance.Add(decimal.NewFromFloat(batchGift.CardGiftBalance))
				}
			}
		}

		item := &adminv1.UserListRspUser{
			Id:               int32(val.ID),
			WxOpenId:         val.WxOpenID,
			NickName:         val.NickName,
			AvatarUrl:        val.AvatarURL,
			Integral:         int32(val.Integral),
			FreezeIntegral:   int32(val.FreezeIntegral),
			CreateTime:       helper.GetTimeDate(val.CreateTime),
			PhoneNumber:      val.PhoneNumber,
			Username:         val.Username,
			WxOfficialOpenId: val.WxOfficialOpenID,
			AlipayOpenId:     val.AlipayOpenID,
			CardGiftBalance:  cardGiftBalance.String(),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (u *UserService) Del(ctx context.Context, req *adminv1.UserDelReq) (*adminv1.UserDelRsp, error) {
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorParam("参数错误")
	}
	row, err := u.biz.Del(ctx, id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.UserDelRsp{
		EffectRow: int32(row),
	}, nil
}

func (u *UserService) All(ctx context.Context, req *adminv1.UserAllReq) (*adminv1.UserAllRsp, error) {
	in := &bo.UserAllBo{
		UserId:          int(req.GetUserId()),
		CreateTimeStart: 0,
		CreateTimeEnd:   0,
		PhoneNumber:     req.GetPhoneNumber(),
		ClientType:      []valobj.UserClientTypeObj{valobj.UserClientTypeObj(req.GetClientType())},
	}
	if createTimeStart := req.GetCreateTimeStart(); createTimeStart != "" {
		createTimeStartInt, err := helper.GetTimestamp(createTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", err)
		}
		in.CreateTimeStart = int(createTimeStartInt)
	}
	if createTimeEnd := req.GetCreateTimeEnd(); createTimeEnd != "" {
		createTimeEndInt, err := helper.GetTimestamp(createTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", err)
		}
		in.CreateTimeStart = int(createTimeEndInt)
	}
	if err := in.Validate(true); err != nil {
		return nil, err
	}
	data, err := u.biz.All(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if len(data) == 0 {
		return nil, nil
	}
	rsp := &adminv1.UserAllRsp{
		List: make([]*adminv1.UserAllRsp_User, 0, len(data)),
	}
	for _, v := range data {
		rsp.List = append(rsp.List, &adminv1.UserAllRsp_User{
			Id:       int32(v.ID),
			NickName: v.NickName,
		})
	}
	return rsp, nil
}

type AccountCheckRsp struct {
	ErrMsg        string `json:"errMsg"`
	SysTaskNumber string `json:"sysTaskNumber"`
}

func (u *UserService) ImportAccount1(ctx http.Context) error {
	file, handler, err := ctx.Request().FormFile("account_list_file")
	println(file, handler, err)

	adminDo, err := u.authHandler.ParseAuthorization(ctx)
	println(adminDo, err)

	return nil
}
func (u *UserService) ImportAccount(ctx http.Context) error {
	rsp := &AccountCheckRsp{}
	file, handler, err := ctx.Request().FormFile("account_list_file")
	if err != nil {
		rsp.ErrMsg = errors.New("文件上传失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer file.Close()

	adminDo, err := u.authHandler.ParseAuthorization(ctx)
	if err != nil {
		return apierr.ErrorParam("操作失败:%s", err)
	}
	ctx1 := context.Background()
	ctx1 = isolationcustomer.WithShopIdCtx(ctx1, helper.AnyToPtr(adminDo.ShopId))
	ctx1 = isolationcustomer.WithCustomerIdCtx(ctx1, adminDo.CustomerId)

	sysConfigDo, err := u.sysConfigBiz.FindByConfigKey(ctx1, valobj.SysConfigLoginType)
	if err != nil {
		rsp.ErrMsg = errors.New("文件上传失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	if sysConfigDo.GetLoginTypeType() == valobj.ConfigLoginTypeWechat {
		rsp.ErrMsg = errors.New("微信OpenID静默登录不支持当前导入方式").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	// 保存上传的文件
	f, err := os.OpenFile(handler.Filename, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		rsp.ErrMsg = errors.New("文件保存失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer f.Close()
	_, _ = io.Copy(f, file)

	// 读取 Excel 文件
	xlFile, err := excelize.OpenFile(handler.Filename)
	if err != nil {
		rsp.ErrMsg = errors.New("文件打开失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer func() {
		if err = f.Close(); err != nil {
			log.Errorf("导入用户文件关闭失败: %v", err)
		}
		err = os.Remove(handler.Filename)
		if err != nil {
			log.Errorf("导入用户文件删除失败: %v", err)
		}
	}()

	//获取Sheet1的总数据行数
	rows, err := xlFile.GetRows("Sheet1")
	if err != nil {
		rsp.ErrMsg = errors.New("文件读取失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	rowCount := len(rows)
	if rowCount < 2 {
		rsp.ErrMsg = errors.New("文件数据不能少于1条").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	if rowCount > 10001 {
		rsp.ErrMsg = errors.New("文件数据不能超过10000条").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	var result []string
	for _, subArray := range rows {
		result = append(result, strings.Join(subArray, ","))
	}

	finalStr := strings.Join(result, ";")

	sysTask, err := u.sysTaskBiz.GenerateTask(ctx1, &bo2.SysTaskExecuteBo{
		SysTaskName:   "导入用户",
		SysTaskType:   valobj.SysTaskTypeImport,
		SysTaskSource: valobj.SysTaskSourceImportAccount,
		Parameter:     finalStr,
		Remark:        "导入用户",
		UserID:        adminDo.Id,
		UserName:      adminDo.Name,
		RelatedNo:     "",
	})
	if err != nil {
		return apierr.ErrorParam("操作失败:%s", err)
	}
	rsp.SysTaskNumber = sysTask.SysTaskNumber
	_ = u.responseJson(ctx, rsp)
	return nil
}
