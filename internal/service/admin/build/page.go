package build

import (
	"cardMall/api/common"
	"cardMall/internal/biz/bo"
)

func ToPageReq(page *common.ReqPage) *bo.ReqPageBo {
	if page == nil {
		return nil
	}
	return &bo.ReqPageBo{
		PageSize: int(page.GetPageSize()),
		Page:     int(page.GetPage()),
	}
}

func ToPageRsp(pageInfo *bo.RespPageBo) *common.RespPage {
	if pageInfo == nil {
		return nil
	}
	return &common.RespPage{
		Page:     int32(pageInfo.Page),
		PageSize: int32(pageInfo.PageSize),
		Total:    int32(pageInfo.Total),
	}
}
