package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/helper"
	"context"
	"time"
)

type SysRoleService struct {
	biz        *adminbiz.SysRoleBiz
	sysMenuBiz *adminbiz.SysMenuBiz
}

func NewSysRoleService(biz *adminbiz.SysRoleBiz, sysMenuBiz *adminbiz.SysMenuBiz) *SysRoleService {
	return &SysRoleService{biz: biz, sysMenuBiz: sysMenuBiz}
}
func (s *SysRoleService) Select(ctx context.Context, req *adminv1.ReqSysRoleEmpty) (*adminv1.RespSysRoleSelectList, error) {
	in := &bo.SysRoleListBo{
		ReqPageBo: bo.ReqPageBo{
			PageSize: 100000,
			Page:     1,
		},
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	count, data, err := s.biz.GetSysRoleList(ctx, in)

	list := make([]*adminv1.RespSysRoleSelectList_RespSysRoleSelectInfo, 0, in.GetPageSize())
	var rsp = &adminv1.RespSysRoleSelectList{
		List: list,
	}
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}
	for _, val := range data {
		item := &adminv1.RespSysRoleSelectList_RespSysRoleSelectInfo{
			Id:       int32(val.Id),
			RoleName: val.RoleName,
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

// Create 新增
func (s *SysRoleService) Create(ctx context.Context, req *adminv1.ReqSysRoleSave) (*adminv1.RespSysRoleInfo, error) {
	in := &bo.SysRoleBo{
		//ShopId:       0,
		RoleName: req.RoleName,
		MenuIds:  helper.Int32ToInt(req.MenuIds),
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	sysRoleId, err := s.biz.AddSysRole(ctx, in)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RespSysRoleInfo{
		Id:         int32(sysRoleId),
		MenuIds:    req.MenuIds,
		RoleName:   req.RoleName,
		UpdateTime: helper.GetTimeDateInt64(time.Now().Unix()),
	}, nil
}

// Delete 删除
func (s *SysRoleService) Delete(ctx context.Context, req *adminv1.ReqSysRoleChange) (*adminv1.RespSysRoleEmpty, error) {
	if req.GetId() <= 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	effectRow, err := s.biz.DeleteSysRole(ctx, int(req.Id))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.RespSysRoleEmpty{
		EffectRow: int32(effectRow),
	}, nil
}

// Info 详情
func (s *SysRoleService) Info(ctx context.Context, req *adminv1.ReqSysRoleInfo) (*adminv1.RespSysRoleInfo, error) {
	if req.GetId() <= 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	sysRoleDetail, err := s.biz.SysRoleDetail(ctx, int(req.Id))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.RespSysRoleInfo{
		Id:         int32(sysRoleDetail.Id),
		MenuIds:    helper.IntToInt32(sysRoleDetail.MenuIds),
		RoleName:   sysRoleDetail.RoleName,
		UpdateTime: helper.GetTimeDate(sysRoleDetail.UpdateTime),
	}, nil
}

// List 列表
func (s *SysRoleService) List(ctx context.Context, req *adminv1.ReqSysRoleList) (*adminv1.RespSysRoleList, error) {
	in := &bo.SysRoleListBo{
		RoleName: req.RoleName,
		ReqPageBo: bo.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	count, data, err := s.biz.GetSysRoleList(ctx, in)

	list := make([]*adminv1.RespSysRoleInfo, 0, in.GetPageSize())
	var rsp = &adminv1.RespSysRoleList{
		List:  list,
		Page:  int32(in.GetPage()),
		Count: int32(count),
	}
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}
	for _, val := range data {
		item := &adminv1.RespSysRoleInfo{
			Id: int32(val.Id),
			//Menus:      nil,
			RoleName:   val.RoleName,
			UpdateTime: helper.GetTimeDate(val.UpdateTime),
			CreateTime: helper.GetTimeDate(val.CreateTime),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

// Update 更新
func (s *SysRoleService) Update(ctx context.Context, req *adminv1.ReqSysRoleSave) (*adminv1.RespSysRoleInfo, error) {
	in := &bo.SysRoleBo{
		Id:       int(req.Id),
		RoleName: req.RoleName,
		MenuIds:  helper.Int32ToInt(req.MenuIds),
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	_, err = s.biz.UpdateSysRole(ctx, in)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	err = s.sysMenuBiz.RefreshRoleAuthMenu(ctx, in.Id)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RespSysRoleInfo{
		Id:         req.Id,
		MenuIds:    req.MenuIds,
		RoleName:   req.RoleName,
		UpdateTime: helper.GetTimeDateInt64(time.Now().Unix()),
	}, nil

}
