package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	do2 "cardMall/internal/biz/do"
	"cardMall/internal/data"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/helper"
	"context"
	"github.com/jinzhu/copier"
)

type SysMenuService struct {
	base
	adminv1.UnimplementedSiteServer
	biz  *adminbiz.SysMenuBiz
	data *data.Data
}

func NewSysMenuService(biz *adminbiz.SysMenuBiz, data *data.Data) *SysMenuService {
	return &SysMenuService{biz: biz, data: data}
}

// Create 创建权限
func (s *SysMenuService) Create(ctx context.Context, req *adminv1.ReqSysMenuCreate) (*adminv1.RespSysMenu, error) {

	var in = &bo.SysMenuBo{
		//ShopId:             0,
		MenuName:       req.MenuName,
		ParentID:       int(req.ParentId),
		MenuPath:       req.MenuPath,
		MenuIcon:       req.MenuIcon,
		MenuSort:       int(req.MenuSort),
		MenuType:       int(req.MenuType),
		APIPath:        req.ApiPath,
		APIMethod:      int(req.ApiMethod),
		FrontComponent: req.FrontComponent,
		FrontURL:       req.FrontUrl,
		//ProcessType:    0,
	}
	if len(req.ProcessTypeValues) > 0 {
		mPart, err := in.ProcessTypeValuesToMPart(req.ProcessTypeValues)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
		in.ConvertProcessType(mPart)
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	sysMenuId, err := s.biz.AddSysMenu(ctx, in)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RespSysMenu{
		Id: int32(sysMenuId),
	}, nil
}

// Delete 删除权限
func (s *SysMenuService) Delete(ctx context.Context, req *adminv1.ReqSysMenuDelete) (*adminv1.RespSysMenuEmpty, error) {
	if len(req.Ids) <= 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	effectRow, err := s.biz.DeleteSysMenu(ctx, helper.Int32ToInt(req.Ids))
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RespSysMenuEmpty{
		EffectRow: int32(effectRow),
	}, nil
}

// GetInfo 获取权限详情
func (s *SysMenuService) GetInfo(ctx context.Context, req *adminv1.ReqSysMenuGetInfo) (*adminv1.RespSysMenu, error) {
	if req.GetId() <= 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	sysMenuDetailDo, err := s.biz.SysMenuDetail(ctx, int(req.Id))
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RespSysMenu{
		Id:                int32(sysMenuDetailDo.Id),
		MenuName:          sysMenuDetailDo.MenuName,
		ParentId:          int32(sysMenuDetailDo.ParentId),
		MenuPath:          sysMenuDetailDo.MenuPath,
		MenuIcon:          sysMenuDetailDo.MenuIcon,
		MenuSort:          int32(sysMenuDetailDo.MenuSort),
		MenuType:          int32(sysMenuDetailDo.MenuType),
		ApiPath:           sysMenuDetailDo.ApiPath,
		ApiMethod:         int32(sysMenuDetailDo.ApiMethod),
		FrontComponent:    sysMenuDetailDo.FrontComponent,
		FrontUrl:          sysMenuDetailDo.FrontUrl,
		ProcessType:       int32(sysMenuDetailDo.ProcessType),
		ProcessTypeValues: sysMenuDetailDo.ProcessTypeMPartToValues(sysMenuDetailDo.ParseProcessType()),
		Children:          nil,
	}, nil
}

// GetTree 获取权限树
func (s *SysMenuService) GetTree(ctx context.Context, req *adminv1.ReqSysMenuGetTree) (*adminv1.RespSysMenuTree, error) {
	sysMenuDos, err := s.biz.GetSysMenuList(ctx)
	list := make([]*adminv1.RespSysMenuTree_Item, 0)
	rsp := &adminv1.RespSysMenuTree{
		List: list,
	}
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if len(sysMenuDos) == 0 {
		return rsp, nil
	}
	for _, sysMenuDo := range sysMenuDos {
		item := &adminv1.RespSysMenuTree_Item{
			Id:                int32(sysMenuDo.Id),
			MenuName:          sysMenuDo.MenuName,
			ParentId:          int32(sysMenuDo.ParentId),
			MenuPath:          sysMenuDo.MenuPath,
			MenuIcon:          sysMenuDo.MenuIcon,
			MenuSort:          int32(sysMenuDo.MenuSort),
			MenuType:          int32(sysMenuDo.MenuType),
			ApiPath:           sysMenuDo.ApiPath,
			ApiMethod:         int32(sysMenuDo.ApiMethod),
			FrontComponent:    sysMenuDo.FrontComponent,
			FrontUrl:          sysMenuDo.FrontUrl,
			ProcessType:       int32(sysMenuDo.ProcessType),
			ProcessTypeValues: sysMenuDo.ProcessTypeMPartToValues(sysMenuDo.ParseProcessType()),
			Children:          nil,
		}
		list = append(list, item)
	}
	result := buildMenuTree(list, 0)
	rsp = &adminv1.RespSysMenuTree{
		List: result,
	}
	return rsp, nil
}

// Update 更新权限
func (s *SysMenuService) Update(ctx context.Context, req *adminv1.ReqSysMenuUpdate) (*adminv1.RespSysMenuEmpty, error) {

	var in = &bo.SysMenuBo{
		Id:             int(req.Id),
		MenuName:       req.MenuName,
		ParentID:       int(req.ParentId),
		MenuPath:       req.MenuPath,
		MenuIcon:       req.MenuIcon,
		MenuSort:       int(req.MenuSort),
		MenuType:       int(req.MenuType),
		APIPath:        req.ApiPath,
		APIMethod:      int(req.ApiMethod),
		FrontComponent: req.FrontComponent,
		FrontURL:       req.FrontUrl,
		//ProcessType:    0,
	}
	if len(req.ProcessTypeValues) > 0 {
		mPart, err := in.ProcessTypeValuesToMPart(req.ProcessTypeValues)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
		in.ConvertProcessType(mPart)
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := s.biz.UpdateSysMenu(ctx, in)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RespSysMenuEmpty{
		EffectRow: int32(effectRow),
	}, nil
}
func buildMenuTree(items []*adminv1.RespSysMenuTree_Item, parentID int32) []*adminv1.RespSysMenuTree_Item {
	var tree []*adminv1.RespSysMenuTree_Item

	for _, item := range items {
		if item.ParentId == parentID {
			children := buildMenuTree(items, item.Id)
			if len(children) > 0 {
				item.Children = children
			}
			tree = append(tree, item)
		}
	}
	return tree
}
func (s *SysMenuService) GetAuthMenu(ctx context.Context, operation string) (sysMenuDo *do.SysMenuDo, err error) {
	sysMenuDo, err = s.biz.GetCacheByApiPath(ctx, operation)
	if err != nil {
		return
	}
	return
}
func (s *SysMenuService) GetAuthAdminInfo(ctx context.Context) (adminInfo *do2.AdminLoginDo, err error) {
	adminInfo, err = s.adminInfo(ctx)
	if err != nil {
		return
	}
	return
}
func (s *SysMenuService) Auth(ctx context.Context, adminId int, operation string) (result bool, err error) {
	result, err = s.biz.Auth(ctx, adminId, operation)
	if err != nil {
		return
	}
	return
}
func (s *SysMenuService) AddOperateLog(ctx context.Context, in *bo.SysOperateLogBo) (err error) {
	do := &do.SysOperateLogDo{}
	err = copier.Copy(do, in)
	if err != nil {
		return
	}
	return s.biz.AddOperateLog(ctx, do)
}
