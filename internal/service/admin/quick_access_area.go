package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/common"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/helper"
	"context"
	"sort"
)

type QuickAccessAreaService struct {
	biz          *adminbiz.QuickAccessAreaBiz
	porcelainBiz *adminbiz.PorcelainAreaBiz
	adminv1.UnimplementedQuickAccessAreaServer
}

func NewQuickAccessAreaService(
	biz *adminbiz.QuickAccessAreaBiz,
	porcelainBiz *adminbiz.PorcelainAreaBiz,
) *QuickAccessAreaService {
	return &QuickAccessAreaService{
		biz:          biz,
		porcelainBiz: porcelainBiz,
	}
}

func (q *QuickAccessAreaService) All(ctx context.Context, _ *adminv1.QuickAccessAreaAllReq) (*adminv1.QuickAccessAreaAllRsp, error) {
	d, _ := q.biz.All(ctx)
	rsp := &adminv1.QuickAccessAreaAllRsp{All: make([]*adminv1.QuickAccessAreaAllRsp_QuickAccessAreaItem, 0, len(d))}
	for _, v := range d {
		rsp.All = append(rsp.All, &adminv1.QuickAccessAreaAllRsp_QuickAccessAreaItem{
			Id:            int32(v.Id),
			Name:          v.Name,
			Status:        int32(v.Status),
			RelationType:  int32(v.RelationType),
			RelationValue: v.RelationValue,
			Icon:          v.Icon,
			Sort:          int32(v.Sort),
		})
	}
	return rsp, nil
}

func (q *QuickAccessAreaService) AddOrUpdate(ctx context.Context, req *adminv1.QuickAccessAreaAddOrUpdateReq) (*adminv1.QuickAccessAreaAddOrUpdateRsp, error) {
	in := &bo.QuickAccessAreaAddOrUpdateBo{
		Id:               int(req.GetId()),
		Name:             req.GetName(),
		Status:           valobj.QuickAccessAreaStatusObj(req.GetStatus()),
		RelationType:     valobj.QuickAccessAreaRelationTypeObj(req.GetRelationType()),
		RelationValue:    req.GetRelationValue(),
		Icon:             req.GetIcon(),
		Sort:             int(req.GetSort()),
		RelationValueIds: helper.Int32ToInt(req.GetRelationValueIds()),
	}

	err := in.Validate()
	if err != nil {
		return nil, err
	}
	err = q.biz.AddOrUpdate(ctx, in)
	return nil, err
}

func (q *QuickAccessAreaService) Del(ctx context.Context, req *adminv1.QuickAccessAreaDelReq) (*adminv1.QuickAccessAreaDelRsp, error) {
	row, err := q.biz.Del(ctx, int(req.GetId()))
	return &adminv1.QuickAccessAreaDelRsp{EffectedRow: int32(row)}, err
}

func (q *QuickAccessAreaService) SavePorcelain(ctx context.Context, req *adminv1.QuickAccessAreaSavePorcelainReq) (*common.RespEmpty, error) {
	params := &bo.QuickAccessAreaSavePorcelainReq{
		Id:               int(req.GetId()),
		Name:             req.GetName(),
		Icon:             req.GetIcon(),
		Sort:             int(req.GetSort()),
		RelationValueIds: helper.Int32ToInt(req.GetRelationValueIds()),
		RelationType:     valobj.QuickAccessAreaRelationTypeObj(req.GetRelationType()),
		RelationValue:    req.GetRelationValue(),
		Status:           valobj.QuickAccessAreaStatusObj(req.GetStatus()),
	}
	if err := params.Validate(); err != nil {
		return nil, err
	}
	if err := q.porcelainBiz.SavePorcelain(ctx, params); err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, nil
}

func (q *QuickAccessAreaService) Porcelains(ctx context.Context, _ *common.ReqEmpty) (*adminv1.QuickAccessAreaPorcelainsRsp, error) {
	porcelains, err := q.porcelainBiz.Porcelains(ctx)
	if err != nil {
		return nil, err
	}
	return &adminv1.QuickAccessAreaPorcelainsRsp{
		All: q.ToPorcelainItems(porcelains),
	}, nil
}

func (q *QuickAccessAreaService) DelPorcelain(ctx context.Context, req *adminv1.QuickAccessAreaDelReq) (*common.RespEmpty, error) {
	if err := q.porcelainBiz.DelPorcelain(ctx, int(req.GetId())); err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, nil
}

func (q *QuickAccessAreaService) ToPorcelainItem(item *bo.PorcelainItem) *adminv1.QuickAccessAreaPorcelainsRsp_PorcelainItem {
	if item == nil {
		return nil
	}
	relationValueIds := make([]int32, 0, len(item.RelationValueIds))
	for _, v := range item.RelationValueIds {
		relationValueIds = append(relationValueIds, int32(v))
	}
	return &adminv1.QuickAccessAreaPorcelainsRsp_PorcelainItem{
		Id:               int32(item.Id),
		Name:             item.Name,
		Icon:             item.Icon,
		Sort:             int32(item.Sort),
		Status:           int32(item.Status),
		RelationValueIds: relationValueIds,
		RelationType:     int32(item.RelationType),
		RelationValue:    item.RelationValue,
	}
}

func (q *QuickAccessAreaService) ToPorcelainItems(items []*bo.PorcelainItem) []*adminv1.QuickAccessAreaPorcelainsRsp_PorcelainItem {
	list := helper.SlicesTo(items, q.ToPorcelainItem)
	// 降序
	sort.Slice(list, func(i, j int) bool {
		return list[i].Sort > list[j].Sort
	})
	return list
}
