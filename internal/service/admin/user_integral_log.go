package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/service"
	"context"
	"errors"
	"fmt"
	"io"
	"os"
	"strconv"
	"strings"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/xuri/excelize/v2"
)

type UserIntegralLogService struct {
	base
	adminv1.UnimplementedUserIntegralLogServer
	biz               *adminbiz.UserIntegralLogBiz
	userBiz           *adminbiz.UserBiz
	log               *log.Helper
	integralConfigBiz *adminbiz.IntegralConfigBiz
	sysConfigDs       *ds.SysConfigDs
	authHandler       *service.AuthHandler
}

func NewUserIntegralLogService(biz *adminbiz.UserIntegralLogBiz, userBiz *adminbiz.UserBiz, log *log.Helper, integralConfigBiz *adminbiz.IntegralConfigBiz, sysConfigDs *ds.SysConfigDs, authHandler *service.AuthHandler) *UserIntegralLogService {
	return &UserIntegralLogService{biz: biz, userBiz: userBiz, log: log, integralConfigBiz: integralConfigBiz, sysConfigDs: sysConfigDs, authHandler: authHandler}
}

type UserIntegralLogImportAddRsp struct {
	Success int                                `json:"success"`
	Fail    int                                `json:"fail"`
	ErrMsg  string                             `json:"errMsg"`
	ErrData []*UserIntegralLogImportAddErrItem `json:"errData"`
}

type UserIntegralLogImportAddErrItem struct {
	PhoneNumber string `json:"phoneNumber"`
	Integral    string `json:"integral"`
	Remark      string `json:"remark"`
	ErrMsg      string `json:"errMsg"`
}

func (u *UserIntegralLogService) List(ctx context.Context, req *adminv1.UserIntegralLogListReq) (*adminv1.UserIntegralLogListRsp, error) {
	var in = &bo.UserIntegralListBo{
		UserId:      int(req.UserId),
		Status:      valobj.UserIntegralLogStatusFinish,
		NickName:    req.GetNickName(),
		PhoneNumber: req.GetPhoneNumber(),
		IncrSource:  valobj.UserIntegralLogIncrSourceObj(req.GetIncrSource()),
		Type:        valobj.UserIntegralLogTypeObj(req.GetType()),
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.PageSize),
			Page:     int(req.Page),
		},
	}
	if req.GetCreateTimeStart() != "" {
		crateTimeStart, err := helper.GetTimestamp(req.GetCreateTimeStart())
		if err != nil {
			return nil, apierr.ErrorParam("起始时间格式错误")
		}
		in.CreateTimeStart = int(crateTimeStart)
	}
	if req.GetCreateTimeEnd() != "" {
		crateTimeEnd, err := helper.GetTimestamp(req.GetCreateTimeEnd())
		if err != nil {
			return nil, apierr.ErrorParam("结束时间格式错误")
		}
		in.CreateTimeEnd = int(crateTimeEnd)
	}

	list := make([]*adminv1.UserIntegralLogListRsp_UserIntegralLogListItem, 0, in.GetPageSize())
	var rsp = &adminv1.UserIntegralLogListRsp{
		List:  list,
		Count: 0,
		Page:  int32(in.GetPage()),
	}

	count, data, err := u.biz.List(ctx, in)
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}
	rsp.Count = int32(count)

	for _, val := range data {
		item := &adminv1.UserIntegralLogListRsp_UserIntegralLogListItem{
			Id:          int32(val.Id),
			UserId:      int32(val.UserId),
			Remark:      val.Remark,
			Type:        int32(val.Type),
			Integral:    int32(val.Integral),
			Status:      int32(val.Status),
			CreateTime:  helper.GetTimeDate(val.CreateTime),
			NickName:    val.NickName,
			PhoneNumber: val.PhoneNumber,
			UpdateTime:  helper.GetTimeDate(val.UpdateTime),
		}
		rsp.List = append(rsp.List, item)
	}

	return rsp, nil
}

func (u *UserIntegralLogService) Add(ctx context.Context, req *adminv1.UserIntegralAddReq) (*adminv1.UserIntegralAddRsp, error) {
	if !u.sysConfigDs.IsSceneIntegralEnable(ctx) {
		return nil, apierr.ErrorNotAllow("积分功能未开启")
	}
	shopId := isolationcustomer.GetShopIdZero(ctx)
	if shopId == 0 {
		return nil, apierr.ErrorNotAllow("获取商城信息失败")
	}
	conf, _ := u.integralConfigBiz.Query(ctx, shopId)
	if conf == nil {
		return nil, apierr.ErrorParam("未找到积分配置")
	}
	var in = &bo.UserIntegralAddBo{
		UserId:     int(req.UserId),
		Remark:     req.Remark,
		Type:       valobj.UserIntegralLogTypeObj(req.Type),
		Integral:   int(req.Integral),
		Status:     valobj.UserIntegralLogStatusFinish,
		ExpireTime: conf.GetExpireTime(),
	}
	if len(in.Remark) == 0 {
		if in.Type == valobj.UserIntegralLogTypeIncr {
			in.Remark = "系统赠送"
			in.IncrSource = valobj.UserIntegralLogIncrSourceSystem
		} else {
			in.Remark = "系统扣除"
		}

	}

	id, err := u.biz.Add(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.UserIntegralAddRsp{Id: int32(id)}, nil
}

func (u *UserIntegralLogService) Del(ctx context.Context, req *adminv1.UserIntegralDelReq) (*adminv1.UserIntegralDelRsp, error) {
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorSystemPanic("参数错误")
	}
	row, err := u.biz.Del(ctx, id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.UserIntegralDelRsp{EffectRow: int32(row)}, nil
}

func (u *UserIntegralLogService) BatchAdd(ctx context.Context, req *adminv1.UserIntegralBatchAddReq) (*adminv1.UserIntegralBatchAddRsp, error) {
	if !u.sysConfigDs.IsSceneIntegralEnable(ctx) {
		return nil, apierr.ErrorNotAllow("积分功能未开启")
	}
	shopId := isolationcustomer.GetShopIdZero(ctx)
	if shopId == 0 {
		return nil, apierr.ErrorNotAllow("获取商城信息失败")
	}
	conf, _ := u.integralConfigBiz.Query(ctx, shopId)
	if conf == nil {
		return nil, apierr.ErrorParam("未找到积分配置")
	}

	rsp := &adminv1.UserIntegralBatchAddRsp{ErrorData: make([]*adminv1.UserIntegralBatchAddRsp_ErrData, 0)}
	userIn := &bo.UserAllBo{
		UserId:          int(req.GetUserId()),
		CreateTimeStart: 0,
		CreateTimeEnd:   0,
		PhoneNumber:     req.GetPhoneNumber(),
		ClientType:      make([]valobj.UserClientTypeObj, 0),
	}
	if len(req.GetClientType()) > 0 {
		for _, clientType := range req.GetClientType() {
			if valobj.UserClientTypeObj(clientType).IsValid() {
				userIn.ClientType = append(userIn.ClientType, valobj.UserClientTypeObj(clientType))
			}
		}
	}
	if createTimeStart := req.GetCreateTimeStart(); createTimeStart != "" {
		createTimeStartInt, err := helper.GetTimestamp(createTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", err)
		}
		userIn.CreateTimeStart = int(createTimeStartInt)
	}
	if createTimeEnd := req.GetCreateTimeEnd(); createTimeEnd != "" {
		createTimeEndInt, err := helper.GetTimestamp(createTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", err)
		}
		userIn.CreateTimeEnd = int(createTimeEndInt)
	}
	if err := userIn.Validate(false); err != nil {
		return nil, apierr.ErrorParam("参数错误:%s", err)
	}
	users, _ := u.userBiz.All(ctx, userIn)
	if len(users) == 0 {
		return nil, apierr.ErrorDbNotFound("未找到任何符合条件的用户")
	}
	if req.GetIntegral() <= 0 {
		return nil, apierr.ErrorDbNotFound("发放积分不能小于等于0")
	}
	if len(users) > 100000 {
		return nil, apierr.ErrorDbNotFound("发放用户数量过大，请联系开发人员")
	}

	for _, user := range users {
		_, err := u.biz.Add(ctx, &bo.UserIntegralAddBo{
			UserId:     user.ID,
			Remark:     req.GetRemark(),
			Type:       valobj.UserIntegralLogTypeIncr,
			Integral:   int(req.GetIntegral()),
			Status:     valobj.UserIntegralLogStatusFinish,
			IncrSource: valobj.UserIntegralLogIncrSourceSystem,
			ExpireTime: conf.GetExpireTime(),
		})
		if err != nil {
			rsp.ErrorData = append(rsp.ErrorData, &adminv1.UserIntegralBatchAddRsp_ErrData{
				PhoneNumber: user.PhoneNumber,
				Integral:    req.GetIntegral(),
				Remark:      req.GetRemark(),
				ErrorMsg:    err.Error(),
			})
			rsp.Fail++
			continue
		}
		rsp.Success++
	}

	return rsp, nil
}

func (u *UserIntegralLogService) ImportBatchAddCheck(ctx http.Context) error {
	rsp := &UserIntegralLogImportAddRsp{ErrData: make([]*UserIntegralLogImportAddErrItem, 0)}

	if !u.sysConfigDs.IsSceneIntegralEnable(ctx) {
		rsp.ErrMsg = errors.New("积分功能未开启").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	file, handler, err := ctx.Request().FormFile("batch_integral_add")
	if err != nil {
		rsp.ErrMsg = errors.New("文件上传失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer file.Close()

	adminDo, err := u.authHandler.ParseAuthorization(ctx)
	if err != nil {
		return apierr.ErrorParam("操作失败:%s", err)
	}

	ctx1 := context.Background()
	ctx1 = isolationcustomer.WithShopIdCtx(ctx1, helper.AnyToPtr(adminDo.ShopId))
	ctx1 = isolationcustomer.WithCustomerIdCtx(ctx1, adminDo.CustomerId)
	// 校验积分功能是否开启
	shopId := isolationcustomer.GetShopIdZero(ctx1)
	if shopId == 0 {
		rsp.ErrMsg = errors.New("获取商城信息失败").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	conf, _ := u.integralConfigBiz.Query(ctx1, shopId)
	if conf == nil {
		rsp.ErrMsg = errors.New("未找到积分配置").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	// 保存上传的文件
	f, err := os.OpenFile(handler.Filename, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		rsp.ErrMsg = errors.New("文件保存失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer f.Close()
	_, _ = io.Copy(f, file)

	// 读取 Excel 文件
	xlFile, err := excelize.OpenFile(handler.Filename)
	if err != nil {
		rsp.ErrMsg = errors.New("文件打开失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer func() {
		if err = f.Close(); err != nil {
			u.log.Errorf("批量发放积分文件关闭失败: %v", err)
		}
		err = os.Remove(handler.Filename)
		if err != nil {
			u.log.Errorf("批量发放积分文件删除失败: %v", err)
		}
	}()

	//获取Sheet1的总数据行数
	rows, err := xlFile.GetRows("Sheet1")
	if err != nil {
		rsp.ErrMsg = errors.New("文件读取失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	rowCount := len(rows)
	if rowCount < 2 {
		rsp.ErrMsg = errors.New("文件数据不能少于1条").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	if rowCount > 1001 {
		rsp.ErrMsg = errors.New("文件数据不能超过1000条").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	for i := 1; i < rowCount; i++ {
		errData := &UserIntegralLogImportAddErrItem{}
		if len(rows[i]) < 2 {
			if len(rows[i]) == 1 {
				errData.PhoneNumber = rows[i][0]
			}
			errData.ErrMsg = fmt.Errorf("第%d行数据格式错误", i).Error()
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}

		phoneNumber := strings.Trim(rows[i][0], " ")
		integral := 0
		integral, err = strconv.Atoi(strings.Trim(rows[i][1], " "))
		remark := ""
		if len(rows[i]) >= 3 {
			remark = strings.Trim(rows[i][2], "")
		}

		errData.PhoneNumber = phoneNumber
		errData.Integral = strings.Trim(rows[i][1], " ")
		errData.Remark = remark
		if !helper.IsAccountPhone(phoneNumber) {
			errData.ErrMsg = "手机号格式错误"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		if err != nil {
			errData.ErrMsg = "发放积分数量格式错误"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		if integral <= 0 {
			errData.ErrMsg = "发放积分不能小于等于0"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		if len(remark) > 200 {
			errData.ErrMsg = "备注不能超过100个字符"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		rsp.Success++
	}
	_ = u.responseJson(ctx, rsp)
	return nil
}

func (u *UserIntegralLogService) ImportBatchAdd(ctx http.Context) error {
	rsp := &UserIntegralLogImportAddRsp{ErrData: make([]*UserIntegralLogImportAddErrItem, 0)}

	file, handler, err := ctx.Request().FormFile("batch_integral_add")
	if err != nil {
		rsp.ErrMsg = errors.New("文件上传失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer file.Close()

	adminDo, err := u.authHandler.ParseAuthorization(ctx)
	if err != nil {
		return apierr.ErrorParam("操作失败:%s", err)
	}
	ctx1 := context.Background()
	ctx1 = isolationcustomer.WithShopIdCtx(ctx1, helper.AnyToPtr(adminDo.ShopId))
	ctx1 = isolationcustomer.WithCustomerIdCtx(ctx1, adminDo.CustomerId)
	// 校验积分功能是否开启
	shopId := isolationcustomer.GetShopIdZero(ctx1)
	if shopId == 0 {
		rsp.ErrMsg = errors.New("获取商城信息失败").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	if !u.sysConfigDs.IsSceneIntegralEnable(ctx1) {
		rsp.ErrMsg = errors.New("积分功能未开启").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	conf, _ := u.integralConfigBiz.Query(ctx1, shopId)
	if conf == nil {
		rsp.ErrMsg = errors.New("未找到积分配置").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	// 保存上传的文件
	f, err := os.OpenFile(handler.Filename, os.O_WRONLY|os.O_CREATE, 0666)
	if err != nil {
		rsp.ErrMsg = errors.New("文件保存失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer f.Close()
	_, _ = io.Copy(f, file)

	// 读取 Excel 文件
	xlFile, err := excelize.OpenFile(handler.Filename)
	if err != nil {
		rsp.ErrMsg = errors.New("文件打开失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	defer func() {
		if err = f.Close(); err != nil {
			u.log.Errorf("批量发放积分文件关闭失败: %v", err)
		}
		err = os.Remove(handler.Filename)
		if err != nil {
			u.log.Errorf("批量发放积分文件删除失败: %v", err)
		}
	}()

	//获取Sheet1的总数据行数
	rows, err := xlFile.GetRows("Sheet1")
	if err != nil {
		rsp.ErrMsg = errors.New("文件读取失败:" + err.Error()).Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	rowCount := len(rows)
	if rowCount < 2 {
		rsp.ErrMsg = errors.New("文件数据不能少于1条").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}
	if rowCount > 1001 {
		rsp.ErrMsg = errors.New("文件数据不能超过1000条").Error()
		_ = u.responseJson(ctx, rsp)
		return nil
	}

	for i := 1; i < rowCount; i++ {
		errData := &UserIntegralLogImportAddErrItem{}
		if len(rows[i]) < 2 {
			if len(rows[i]) == 1 {
				errData.PhoneNumber = rows[i][0]
			}
			errData.ErrMsg = fmt.Errorf("第%d行数据格式错误", i).Error()
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}

		phoneNumber := strings.Trim(rows[i][0], " ")
		integral := 0
		integral, err = strconv.Atoi(strings.Trim(rows[i][1], " "))
		remark := ""
		if len(rows[i]) >= 3 {
			remark = strings.Trim(rows[i][2], "")
		}

		errData.PhoneNumber = phoneNumber
		errData.Integral = strings.Trim(rows[i][1], " ")
		errData.Remark = remark
		if !helper.IsAccountPhone(phoneNumber) {
			errData.ErrMsg = "手机号格式错误"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		if err != nil {
			errData.ErrMsg = "发放积分数量格式错误"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		if integral <= 0 {
			errData.ErrMsg = "发放积分不能小于等于0"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		if len(remark) > 200 {
			errData.ErrMsg = "备注不能超过100个字符"
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		item := &bo.UserIntegralImportBatchAddBo{
			PhoneNumber: rows[i][0],
			Integral:    integral,
			Remark:      remark,
		}
		err = u.biz.ImportAdd(ctx1, item, conf)
		if err != nil {
			errData.ErrMsg = err.Error()
			rsp.ErrData = append(rsp.ErrData, errData)
			rsp.Fail++
			continue
		}
		rsp.Success++
	}
	_ = u.responseJson(ctx, rsp)
	return nil
}
