package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/conf"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"fmt"
	"net/url"
	"strconv"
	"time"
)

type CustomerSupportService struct {
	base
	adminv1.UnimplementedCustomerSupportServer
	conf *conf.Bootstrap
}

func NewCustomerSupportService(conf *conf.Bootstrap) *CustomerSupportService {
	return &CustomerSupportService{
		conf: conf,
	}
}

// GetUrl 获取客服系统链接
func (c *CustomerSupportService) GetUrl(ctx context.Context, req *adminv1.CustomerSupportGetUrlReq) (*adminv1.CustomerSupportGetUrlRsp, error) {
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	shopId := isolationcustomer.GetShopIdZero(ctx)
	if customerId <= 0 || shopId <= 0 {
		return nil, apierr.ErrorNotAllow("未获取到已登录的商城信息，请重试")
	}

	kf := fmt.Sprintf("saas_%d_%d", customerId, shopId)
	t := int(time.Now().Unix())
	token := helper.GetKeFuToken(c.conf.KeFu.SecretKey, kf, strconv.Itoa(t))

	queryParams := make(url.Values)
	queryParams.Add("kf", kf)
	queryParams.Add("timestamp", strconv.Itoa(t))
	queryParams.Add("token", token)
	gotoUrl := fmt.Sprintf("%s?%s", c.conf.KeFu.BackendUrl, queryParams.Encode())

	return &adminv1.CustomerSupportGetUrlRsp{
		Url: gotoUrl,
	}, nil
}
