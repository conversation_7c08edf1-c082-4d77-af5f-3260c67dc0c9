package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"context"
)

type GoodsSpecService struct {
	*adminv1.UnimplementedGoodsSpecServer
	biz *adminbiz.GoodsSpecBiz
}

func (g GoodsSpecService) Add(ctx context.Context, req *adminv1.GoodsSpecAddReq) (*adminv1.GoodsSpecAddRsp, error) {
	in := &bo.GoodsSpecAddBo{
		Name:   req.GetName(),
		Remark: "",
		Format: valobj.GoodsSpecFormatString,
		Sort:   0,
		Type:   valobj.GoodsSpecTypeVir,
	}
	row, err := g.biz.Add(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("规格添加失败：%", err)
	}
	return &adminv1.GoodsSpecAddRsp{EffectRow: int32(row)}, nil
}

func (g GoodsSpecService) All(ctx context.Context, req *adminv1.GoodsSpecAllReq) (*adminv1.GoodsSpecAllRsp, error) {
	in := &bo.GoodsSpecQueryBo{
		Id:   0,
		Name: req.GetName(),
		Type: valobj.GoodsSpecTypeVir,
	}
	specs, err := g.biz.Get(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("规格查询失败：%", err)
	}

	rsp := &adminv1.GoodsSpecAllRsp{
		List: make([]*adminv1.GoodsSpecAllRsp_GoodsSpecAllItem, 0, len(specs)),
	}
	for _, spec := range specs {
		rsp.List = append(rsp.List, &adminv1.GoodsSpecAllRsp_GoodsSpecAllItem{
			Id:   int32(spec.Id),
			Name: spec.Name,
		})
	}
	return rsp, nil
}
