package admin

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bizBo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/helper"

	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
)

type GoodsService struct {
	adminv1.UnimplementedGoodsServer
	biz             *adminbiz.GoodsBiz
	goodsCategoryDs *ds.GoodsCategoryDs
	areaRepoDs      *ds.AreaRepoDs
}

func NewGoodsService(biz *adminbiz.GoodsBiz,
	areaRepoDs *ds.AreaRepoDs,
	goodsCategoryDs *ds.GoodsCategoryDs) *GoodsService {
	return &GoodsService{biz: biz, goodsCategoryDs: goodsCategoryDs, areaRepoDs: areaRepoDs}
}

// PoolGoodsList 已选商品池查询-分页
func (g *GoodsService) PoolGoodsList(ctx context.Context, req *adminv1.PoolGoodsListReq) (*adminv1.PoolGoodsListRsp, error) {
	var in = &bizBo.PoolGoodsListBo{
		SkuNo:          req.SkuNo,
		GoodsSkuID:     req.GoodsSkuId,
		GoodsSkuName:   req.GoodsSkuName,
		GoodsName:      req.GoodsName,
		GoodsId:        req.GoodsId,
		CategoryId:     g.goodsCategoryDs.FindLeafNodeId(ctx, int(req.CategoryId)),
		Barcode:        req.Barcode,
		BrandName:      req.BrandName,
		Status:         req.Status,
		SettlementType: req.SettlementType,
		ReqPageBo: bizBo.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		ReqSortBo: bizBo.ReqSortBo{
			OrderBy:   req.OrderBy,
			OrderType: req.OrderType,
		},
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	count, data, err := g.biz.PoolGoodsList(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	totalNum, disable, err := g.biz.GoodsNum(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	list := make([]*adminv1.PoolGoodsListItem, 0, in.GetPageSize())
	var rsp = &adminv1.PoolGoodsListRsp{
		GoodsNum:        int32(totalNum),
		GoodsDisableNum: int32(disable),
		List:            list,
		Page:            int32(in.GetPage()),
		Count:           int32(count),
	}
	if count == 0 {
		return rsp, nil
	}
	for _, val := range data {
		saleProfit := decimal.Zero
		if val.SalePrice.GreaterThan(decimal.Zero) {
			saleProfit = (val.SalePrice.Sub(val.SupplierPrice)).Div(val.SalePrice)
		}
		item := &adminv1.PoolGoodsListItem{
			Type:               int32(val.Type),
			SkuNo:              val.SkuNo,
			GoodsName:          val.GoodsName,
			GoodsId:            int64(val.GoodsID),
			MarketPrice:        val.MarketPrice.String(),
			SalePrice:          val.SalePrice.String(),
			SaleIntegral:       int32(val.SaleIntegral),
			SupplierPrice:      val.SupplierPrice.String(),
			SaleProfit:         saleProfit.Mul(decimal.NewFromInt(100)).Round(2).String(),
			Stock:              int32(val.Stock),
			LogisticsType:      "",
			Status:             int32(val.Status),
			NumLimit:           int32(val.NumLimit),
			CreateTime:         helper.GetTimeDateInt64(val.CreateTime),
			GoodsSkuId:         int64(val.GoodsSkuID),
			GoodsSkuName:       val.GoodsSkuName,
			Recommend:          int32(val.Recommend),
			Image:              val.Image,
			GoodsSkuImage:      val.GoodsSkuImage,
			PurchaseLimitCycle: int32(val.PurchaseLimitCycle),
			PurchaseLimitStart: helper.GetTimeDateInt64(val.PurchaseLimitStart),
			PurchaseLimitEnd:   helper.GetTimeDateInt64(val.PurchaseLimitEnd),
			TransportId:        int32(val.TransportID),
			TransportName:      val.TransportName,
			SettlementType:     int32(val.SettlementType),
			Sort:               int32(val.Sort),
		}
		if val.GoodsCategoryDo != nil {
			item.CategoryName = val.GoodsCategoryDo.Name
			item.CategoryId = int32(val.GoodsCategoryDo.Id)
		}
		if val.GoodsBrandDo != nil {
			item.BrandName = val.GoodsBrandDo.Name
			item.BrandId = int32(val.GoodsBrandDo.Id)
		}

		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}
func (g *GoodsService) PoolGoodsDetail(ctx context.Context, req *adminv1.PoolGoodsDetailReq) (*adminv1.PoolGoodsDetailRsp, error) {
	if req.GetGoodsSkuId() <= 0 {
		return nil, apierr.ErrorParam("操作失败:商品id不能为空")
	}
	poolGoodsDetail, err := g.biz.PoolGoodsDetail(ctx, int(req.GoodsSkuId))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	var (
		categoryName      = ""
		saleProfit        = decimal.Zero
		images            []string
		detailImages      []string
		notSaleAreaInfo   []*adminv1.PoolGoodsDetailRspAreaInfo
		notSaleAreaInfoDB []*bizdo.AreaDo
	)
	if poolGoodsDetail.SalePrice.GreaterThan(decimal.Zero) {
		saleProfit = (poolGoodsDetail.SalePrice.Sub(poolGoodsDetail.SupplierPrice)).Div(poolGoodsDetail.SalePrice)
	}
	categoryName, err = g.goodsCategoryDs.GetTreeLineJoin(ctx, poolGoodsDetail.CategoryId, "/")
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	images, err = poolGoodsDetail.GetImages()
	if err != nil {
		return nil, apierr.ErrorSystemPanic("图片解析失败:%s", err)
	}
	detailImages, err = poolGoodsDetail.GetDetailImages()
	if err != nil {
		return nil, apierr.ErrorSystemPanic("图片解析失败:%s", err)
	}
	if len(poolGoodsDetail.GetNotSaleArea()) > 0 {
		notSaleAreaInfoDB, err = g.areaRepoDs.GetAreaByIds(ctx, poolGoodsDetail.GetNotSaleArea())
		if err != nil {
			return nil, apierr.ErrorSystemPanic("获取禁销区域失败:%s", err)
		}
		for _, val := range notSaleAreaInfoDB {
			notSaleAreaInfo = append(notSaleAreaInfo, &adminv1.PoolGoodsDetailRspAreaInfo{
				Id:        int32(val.ID),
				Pid:       int32(val.Pid),
				Name:      val.Name,
				Level:     int32(val.Level),
				Mergename: val.Mergename,
			})
		}
	}
	return &adminv1.PoolGoodsDetailRsp{
		SkuNo:         poolGoodsDetail.SkuNo,
		GoodsName:     poolGoodsDetail.GoodsName,
		GoodsId:       int64(poolGoodsDetail.GoodsID),
		MarketPrice:   poolGoodsDetail.MarketPrice.String(),
		SalePrice:     poolGoodsDetail.SalePrice.String(),
		SaleIntegral:  int32(poolGoodsDetail.SaleIntegral),
		SupplierPrice: poolGoodsDetail.SupplierPrice.String(),
		SaleProfit:    saleProfit.Mul(decimal.NewFromInt(100)).Round(2).String(),
		Stock:         int32(poolGoodsDetail.Stock),
		LogisticsType: "",
		Status:        int32(poolGoodsDetail.Status),
		NumLimit:      int32(poolGoodsDetail.NumLimit),
		CreateTime:    helper.GetTimeDateInt64(poolGoodsDetail.CreateTime),
		GoodsSkuId:    int64(poolGoodsDetail.GoodsSkuID),
		GoodsSkuName:  poolGoodsDetail.GoodsSkuName,
		CategoryId:    int64(poolGoodsDetail.CategoryId),
		CategoryName:  categoryName,
		BrandId:       int64(poolGoodsDetail.BrandId),
		BrandName:     poolGoodsDetail.BrandName,
		Image:         poolGoodsDetail.Image,
		Images:        images,
		DetailImage:   detailImages,
		GoodsSkuImage: poolGoodsDetail.GoodsSkuImage,
		SupplierId:    int64(poolGoodsDetail.SupplierId),
		SupplierName:  poolGoodsDetail.SupplierName,
		NotSaleArea:   notSaleAreaInfo,
	}, nil
}
func (g *GoodsService) PoolGoodsPurchaseLimit(ctx context.Context, req *adminv1.PoolGoodsPurchaseLimitReq) (*adminv1.PoolGoodsPurchaseLimitRsp, error) {
	var (
		purchaseLimitStart int64 = 0
		purchaseLimitEnd   int64 = 0
		err                error
	)
	if req.PurchaseLimitCycle == 1 {

		purchaseLimitStart, err = helper.GetTimestamp(req.PurchaseLimitStart)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
		purchaseLimitEnd, err = helper.GetTimestamp(req.PurchaseLimitEnd)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
	}
	var in = &bizBo.PoolGoodsPurchaseLimitBo{
		GoodsSkuIds:        req.GoodsSkuIds,
		NumLimit:           req.NumLimit,
		PurchaseLimitCycle: req.PurchaseLimitCycle,
		PurchaseLimitStart: purchaseLimitStart,
		PurchaseLimitEnd:   purchaseLimitEnd,
	}
	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := g.biz.PoolGoodsPurchaseLimit(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.PoolGoodsPurchaseLimitRsp{EffectRow: int64(effectRow)}, nil
}
func (g *GoodsService) PoolGoodsChangeStatus(ctx context.Context, req *adminv1.PoolGoodsChangeStatusReq) (*adminv1.PoolGoodsChangeStatusRsp, error) {
	status := int(req.Status)
	var in = &bizBo.PoolGoodsChangeStatusBo{
		GoodsSkuIds: req.GoodsSkuIds,
		Status:      &status,
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := g.biz.PoolGoodsChangeStatus(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.PoolGoodsChangeStatusRsp{EffectRow: int64(effectRow)}, nil
}
func (g *GoodsService) PoolGoodsChangePrice(ctx context.Context, req *adminv1.PoolGoodsChangePriceReq) (*adminv1.PoolGoodsChangePriceRsp, error) {
	var in = &bizBo.PoolGoodsChangePriceBoV2{
		SkuNo:         req.GetSkuNo(),
		ChangeType:    req.ChangeType,
		PriceType:     valobj.ProductAuthorizePriceChangeTypeObj(req.PriceType),
		PriceStatus:   req.PriceStatus,
		PriceRate:     decimal.NewFromFloat(req.PriceRate),
		PriceRateUnit: req.PriceRateUnit,
		SaleIntegral:  req.SaleIntegral,
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := g.biz.PoolGoodsChangePrice(ctx, in)
	if err != nil {
		return nil, err
	}
	return &adminv1.PoolGoodsChangePriceRsp{EffectRow: int64(effectRow)}, nil
}

func (g *GoodsService) PoolGoodsChangeRecommend(ctx context.Context, req *adminv1.PoolGoodsChangeRecommendReq) (*adminv1.PoolGoodsChangeRecommendRsp, error) {
	var in = &bizBo.PoolGoodsChangeRecommendBo{
		GoodsSkuIds: req.GoodsSkuIds,
		Recommend:   valobj.GoodsSkuRecommendObj(req.Recommend),
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := g.biz.PoolGoodsChangeRecommend(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.PoolGoodsChangeRecommendRsp{EffectRow: int64(effectRow)}, nil
}
func (g *GoodsService) PoolGoodsExport(ctx http.Context, ctx1 context.Context) error {
	params := ctx.Request().URL.Query()

	var in = &bizBo.ExportPoolGoodsListBo{
		//GoodsSkuID:   params.Get("goodsSkuId"),
		GoodsSkuName: params.Get("goodsSkuName"),
		GoodsName:    params.Get("goodsName"),
		//GoodsId:      params.Get("goodsId"),
		//CategoryId:   params.Get("categoryId"),
		Barcode:   params.Get("barcode"),
		BrandName: params.Get("brandName"),
		SkuNo:     params.Get("skuNo"),
		//Status:       params.Get("status"),
		ReqSortBo: bizBo.ReqSortBo{
			OrderBy: params.Get("orderBy"),
			//OrderType: params.Get("orderType"),
		},
	}
	if params.Get("orderType") != "" {
		orderType, err := helper.StringToInt32(params.Get("orderType"))
		if err != nil {
			return fmt.Errorf("goodsSkuID参数解析失败:%s", err)
		}
		in.OrderType = orderType
	}
	if params.Get("goodsSkuId") != "" {
		goodsSkuID, err := helper.StringToInt64(params.Get("goodsSkuId"))
		if err != nil {
			return fmt.Errorf("goodsSkuID参数解析失败:%s", err)
		}
		in.GoodsSkuID = goodsSkuID
	}
	if params.Get("goodsId") != "" {
		goodsId, err := helper.StringToInt64(params.Get("goodsId"))
		if err != nil {
			return fmt.Errorf("goodsId参数解析失败:%s", err)
		}
		in.GoodsId = goodsId
	}
	if params.Get("categoryId") != "" {
		categoryId, err := helper.StringToInt64(params.Get("categoryId"))
		if err != nil {
			return fmt.Errorf("categoryId参数解析失败:%s", err)
		}
		in.CategoryId = g.goodsCategoryDs.FindLeafNodeId(ctx, int(categoryId))
	}
	if len(params.Get("status")) != 0 {
		status, err := helper.StringToInt32(params.Get("status"))
		if err != nil {
			return fmt.Errorf("status参数解析失败:%s", err)
		}
		in.Status = &status
	}
	err := in.Validate()
	if err != nil {
		return apierr.ErrorParam("操作失败:%s", err)
	}
	data, err := g.biz.ExportPoolGoodsList(ctx1, in)
	if err != nil {
		return err
	}
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return err
	}
	_ = f.SetCellStr("sheet1", "A1", "商品名称")
	_ = f.SetCellStr("sheet1", "B1", "编号")
	_ = f.SetCellStr("sheet1", "C1", "供应价")
	_ = f.SetCellStr("sheet1", "D1", "市场价")
	_ = f.SetCellStr("sheet1", "E1", "销售价")
	_ = f.SetCellStr("sheet1", "F1", "销售积分")
	_ = f.SetCellStr("sheet1", "G1", "销售利润率(%)")
	_ = f.SetCellStr("sheet1", "H1", "库存")
	_ = f.SetCellStr("sheet1", "I1", "运费模板")
	_ = f.SetCellStr("sheet1", "J1", "状态")
	_ = f.SetCellStr("sheet1", "K1", "结算类型")
	_ = f.SetCellStr("sheet1", "L1", "限购数量")
	_ = f.SetCellStr("sheet1", "M1", "创建时间")

	if len(data) > 0 {
		var row = 2
		for _, val := range data {
			strRwo := strconv.Itoa(row)

			status := "下架"
			statusInfo, ok := valobj.GoodsStatusMap[valobj.GoodsStatusObj(val.Status)]
			if ok {
				status = statusInfo
			}

			saleProfit := decimal.Zero
			if val.SalePrice.GreaterThan(decimal.Zero) {
				saleProfit = (val.SalePrice.Sub(val.SupplierPrice)).Div(val.SalePrice)
			}
			_ = f.SetCellStr("sheet1", "A"+strRwo, val.GoodsName+"-"+val.GoodsSkuName)
			_ = f.SetCellStr("sheet1", "B"+strRwo, val.SkuNo)
			_ = f.SetCellStr("sheet1", "C"+strRwo, val.SupplierPrice.String())
			_ = f.SetCellStr("sheet1", "D"+strRwo, val.MarketPrice.String())
			_ = f.SetCellStr("sheet1", "E"+strRwo, val.SalePrice.String())
			_ = f.SetCellStr("sheet1", "F"+strRwo, helper.Int64ToString(int64(val.SaleIntegral)))
			_ = f.SetCellStr("sheet1", "G"+strRwo, saleProfit.Mul(decimal.NewFromInt(100)).Round(2).String()+"%")
			_ = f.SetCellStr("sheet1", "H"+strRwo, helper.Int64ToString(int64(val.Stock)))
			_ = f.SetCellStr("sheet1", "I"+strRwo, val.TransportName)
			_ = f.SetCellStr("sheet1", "J"+strRwo, status)
			_ = f.SetCellStr("sheet1", "K"+strRwo, val.SettlementType.String())
			_ = f.SetCellStr("sheet1", "L"+strRwo, helper.Int64ToString(int64(val.NumLimit)))
			_ = f.SetCellStr("sheet1", "M"+strRwo, helper.GetTimeDateInt64(val.CreateTime))
			row++
		}
	}

	f.SetActiveSheet(index)
	var fileName = "poolGoods_" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	ctx.Response().Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Response().Header().Set("Content-Disposition", fmt.Sprintf("attachment;filename=\"%s\"", fileName))
	ctx.Response().Header().Set("Content-Transfer-Encoding", "binary")
	ctx.Response().Header().Set("Cache-Control", "must-revalidate")
	ctx.Response().Header().Set("Cache-Control", "max-age=0")
	ctx.Response().Header().Set("Pragma", "public")
	buff, err := f.WriteToBuffer()
	if err != nil {
		return err
	}
	_, err = buff.WriteTo(ctx.Response())
	if err != nil {
		return err
	}
	return nil
}

// List Deprecated
func (g *GoodsService) List(ctx context.Context, req *adminv1.GoodsListReq) (*adminv1.GoodsListRsp, error) {
	return nil, nil
	var in = &bizBo.GoodsListBo{
		Id:              int(req.Id),
		Name:            req.Name,
		BrandName:       req.BrandName,
		CreateTimeStart: 0,
		CreateTimeEnd:   0,
		StockStart:      int(req.StockStart),
		StockEnd:        int(req.StockEnd),
		BrandIds:        nil,
		ReqPageBo: bizBo.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}
	if req.Status != nil {
		status := int(*req.Status)
		in.Status = &status
	}
	if len(req.CreateTimeStart) > 0 {
		time, err := helper.GetTimestamp(req.CreateTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("createTimeStart时间解析失败")
		}
		in.CreateTimeStart = int(time)
	}
	if len(req.CreateTimeEnd) > 0 {
		time, err := helper.GetTimestamp(req.CreateTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("createTimeEnd时间解析失败")
		}
		in.CreateTimeEnd = int(time)
	}
	count, data, err := g.biz.List(ctx, in)

	list := make([]*adminv1.GoodsListRsp_GoodsListItem, 0, in.GetPageSize())
	var rsp = &adminv1.GoodsListRsp{
		List:  list,
		Page:  int32(in.GetPage()),
		Count: int32(count),
	}
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}

	for _, val := range data {
		item := &adminv1.GoodsListRsp_GoodsListItem{
			Id:        int32(val.Id),
			BrandId:   int32(val.BrandId),
			BrandName: val.BrandName,
			Name:      val.Name,
			//SalePrice:     helper.Float64ToString(val.SalePrice, 2),
			//OriginalPrice: helper.Float64ToString(val.OriginalPrice, 2),
			//Discount:      helper.Float64ToString(val.Discount, 2),
			Image: val.Image,
			//Stock:         int32(val.Stock),
			Status:     int32(val.Status),
			CreateTime: helper.GetTimeDate(val.CreateTime),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

// Add Deprecated
func (g *GoodsService) Add(ctx context.Context, req *adminv1.GoodsInfo) (*adminv1.GoodsAddRsp, error) {
	//salePrice, err := strconv.ParseFloat(req.SalePrice, 64)
	//if err != nil {
	//	return nil, apierr.ErrorParam("参数错误:%s", req.SalePrice)
	//}
	//originalPrice, err := strconv.ParseFloat(req.OriginalPrice, 64)
	//if err != nil {
	//	return nil, apierr.ErrorParam("参数错误:%s", req.OriginalPrice)
	//}
	//
	//channelPrice, err := strconv.ParseFloat(req.ChannelPrice, 64)
	//if err != nil {
	//	return nil, apierr.ErrorParam("参数错误:%s", req.ChannelPrice)
	//}
	//discount, _ := strconv.ParseFloat(req.Discount, 64)
	//discountStr := strconv.FormatFloat(discount, 'f', 2, 64)
	//discount, _ = strconv.ParseFloat(discountStr, 64)
	//
	//var in = &bizBo.GoodsInfoBo{
	//	Type:         valobj.GoodsTypeVir,
	//	BrandId:      int(req.BrandId),
	//	Name:         req.Name,
	//	Image:        req.Image,
	//	DetailImage:  strings.Join(req.DetailImage, ","),
	//	Detail:       req.Detail,
	//	Recommend:    nil,
	//	Status:       nil,
	//	Stock:        nil,
	//	Integral:     nil,
	//	NumLimit:     int(req.GetNumLimit()),
	//	ProductId:    int(req.ProductId),
	//	ChannelPrice: channelPrice,
	//	Sort:         int(req.Sort),
	//	CategoryId:   int(req.GetCategoryId()),
	//}
	//if req.Recommend != nil {
	//	recommend := int(*req.Recommend)
	//	in.Recommend = &recommend
	//}
	//
	//if req.Status != nil {
	//	status := int(*req.Status)
	//	in.Status = &status
	//}
	////if req.Stock != nil {
	////	stock := int(*req.Stock)
	////	in.Stock = &stock
	////}
	////if req.Integral != nil {
	////	integral := int(*req.Integral)
	////	in.Integral = &integral
	////}
	//
	//id, err := g.biz.Add(ctx, in)
	//if err != nil {
	//	return nil, apierr.ErrorSystemPanic("操作失败:", err)
	//}
	//
	//return &adminv1.GoodsAddRsp{ShopId: int32(id)}, nil
	return nil, nil
}

// Update Deprecated
func (g *GoodsService) Update(ctx context.Context, req *adminv1.GoodsInfo) (*adminv1.GoodsUpdateRsp, error) {

	//var in = &bizBo.GoodsInfoBo{
	//	ShopId:            int(req.ShopId),
	//	BrandId:       int(req.BrandId),
	//	Name:          req.Name,
	//	SalePrice:     0,
	//	OriginalPrice: 0,
	//	Discount:      0,
	//	Image:         req.Image,
	//	DetailImage:   strings.Join(req.DetailImage, ","),
	//	Detail:        req.Detail,
	//	Recommend:     nil,
	//	Status:        nil,
	//	Stock:         nil,
	//	Integral:      nil,
	//	NumLimit:      int(req.GetNumLimit()),
	//	ProductId:     int(req.ProductId),
	//	Sort:          int(req.Sort),
	//}
	//if req.Recommend != nil {
	//	recommend := int(*req.Recommend)
	//	in.Recommend = &recommend
	//}
	//
	//if req.Status != nil {
	//	status := int(*req.Status)
	//	in.Status = &status
	//}
	//if req.Stock != nil {
	//	stock := int(*req.Stock)
	//	in.Stock = &stock
	//}
	//if req.Integral != nil {
	//	integral := int(*req.Integral)
	//	in.Integral = &integral
	//}
	//
	//if req.SalePrice != "" {
	//	salePrice, err := strconv.ParseFloat(req.SalePrice, 64)
	//	if err != nil {
	//		return nil, apierr.ErrorParam("参数错误:%s", req.SalePrice)
	//	}
	//	in.SalePrice = salePrice
	//}
	//
	//if req.OriginalPrice != "" {
	//	originalPrice, err := strconv.ParseFloat(req.OriginalPrice, 64)
	//	if err != nil {
	//		return nil, apierr.ErrorParam("参数错误:%s", req.OriginalPrice)
	//	}
	//	in.OriginalPrice = originalPrice
	//}
	//
	//if req.Discount != "" {
	//	discount, err := strconv.ParseFloat(req.Discount, 64)
	//	if err != nil {
	//		return nil, apierr.ErrorParam("参数错误:%s", req.Discount)
	//	}
	//	discountStr := strconv.FormatFloat(discount, 'f', 2, 64)
	//	discount, _ = strconv.ParseFloat(discountStr, 64)
	//	in.Discount = discount
	//}
	//if req.ChannelPrice != "" {
	//	channelPrice, err := strconv.ParseFloat(req.ChannelPrice, 64)
	//	if err != nil {
	//		return nil, apierr.ErrorParam("参数错误:%s", req.ChannelPrice)
	//	}
	//	in.ChannelPrice = channelPrice
	//}
	//row, err := g.biz.Update(ctx, in)
	//if err != nil {
	//	return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	//}
	//
	//return &adminv1.GoodsUpdateRsp{EffectRow: int32(row)}, nil
	return nil, nil
}

// Detail Deprecated
func (g *GoodsService) Detail(ctx context.Context, req *adminv1.GoodsDetailReq) (*adminv1.GoodsDetailRsp, error) {
	//data, err := g.biz.Detail(ctx, int(req.ShopId))
	//if err != nil {
	//	return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	//}
	//if data == nil {
	//	return nil, nil
	//}
	//
	//var rsp = &adminv1.GoodsDetailRsp{
	//	ShopId:            int32(data.ShopId),
	//	BrandId:       int32(data.BrandId),
	//	Name:          data.Name,
	//	SalePrice:     helper.Float64ToString(data.SalePrice, 2),
	//	OriginalPrice: helper.Float64ToString(data.OriginalPrice, 2),
	//	Discount:      helper.Float64ToString(data.Discount, 2),
	//	Image:         data.Image,
	//	DetailImage:   nil,
	//	Detail:        data.Detail,
	//	Recommend:     int32(data.Recommend),
	//	Status:        int32(data.Status),
	//	Stock:         int32(data.Stock),
	//	Integral:      int32(data.Integral),
	//	NumLimit:      int32(data.NumLimit),
	//	ProductId:     int32(data.ProductId),
	//	ChannelPrice:  helper.Float64ToString(data.ChannelPrice, 2),
	//	Sort:          int32(data.Sort),
	//}
	//if data.DetailImage != "" {
	//	rsp.DetailImage = strings.Split(data.DetailImage, ",")
	//}
	//return rsp, nil
	return nil, nil
}

// UpdateStatus Deprecated
func (g *GoodsService) UpdateStatus(ctx context.Context, req *adminv1.GoodsUpdateStatusReq) (*adminv1.GoodsUpdateStatusRsp, error) {
	//id := int(req.ShopId)
	//status := int(*req.Status)
	//row, err := g.biz.UpdateStatus(ctx, id, &status)
	//if err != nil {
	//	return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	//}
	//return &adminv1.GoodsUpdateStatusRsp{EffectRow: int32(row)}, nil
	return nil, nil
}

// Del Deprecated
func (g *GoodsService) Del(ctx context.Context, req *adminv1.GoodsDelReq) (*adminv1.GoodsDelRsp, error) {
	//id := int(req.GetId())
	//if id <= 0 {
	//	return nil, apierr.ErrorParam("参数错误")
	//}
	//row, err := g.biz.Del(ctx, id)
	//if err != nil {
	//	return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	//}
	//return &adminv1.GoodsDelRsp{EffectRow: int32(row)}, nil
	return nil, nil
}

// All 所有商品-SKU维度
func (g *GoodsService) All(ctx context.Context, req *adminv1.GoodsSkuAllReq) (*adminv1.GoodsSkuAllRsp, error) {
	in := &bizBo.GoodsAllBo{Name: req.GetName()}
	res, err := g.biz.All(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	rsp := &adminv1.GoodsSkuAllRsp{
		List: make([]*adminv1.GoodsSkuAllRsp_GoodsSkuItem, 0),
	}
	for _, v := range res {
		rsp.List = append(rsp.List, &adminv1.GoodsSkuAllRsp_GoodsSkuItem{
			Id:    int32(v.Id),
			Name:  fmt.Sprintf("%s/%s", v.Name, v.SkuName),
			SkuId: int32(v.SkuId),
			SkuNo: v.SkuNo,
		})
	}
	return rsp, nil
}

func (g *GoodsService) SkuSort(ctx context.Context, req *adminv1.GoodsSkuSortReq) (*adminv1.GoodsSkuSortRsp, error) {
	skuNo := req.GetSkuNo()
	if skuNo == "" {
		return nil, apierr.ErrorParam("参数错误")
	}
	sort := int(req.GetSort())
	if sort < 0 {
		return nil, apierr.ErrorParam("排序值不能小于0")
	}
	row, err := g.biz.SkuSort(ctx, skuNo, sort)
	if err != nil {
		return nil, err
	}
	return &adminv1.GoodsSkuSortRsp{EffectRow: int32(row)}, nil
}
