package admin

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/do"
	"cardMall/internal/pkg/helper"
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"time"

	"cardMall/api/adminv1"
	"cardMall/api/common"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/service/admin/build"
)

type ActivityService struct {
	activityBiz *adminbiz.ActivityBiz
	adminv1.UnimplementedActivityServer
}

func NewActivityService(
	activityBiz *adminbiz.ActivityBiz,
) *ActivityService {
	return &ActivityService{
		activityBiz: activityBiz,
	}
}

func (q *ActivityService) Upsert(ctx context.Context, req *adminv1.ActivityUpsertReq) (*common.RespEmpty, error) {
	params := &bo.ActivityAddReq{
		Id:           int(req.GetId()),
		Name:         req.<PERSON>ame(),
		Banner:       req.GetBanner(),
		Bg:           req.GetBackground(),
		Status:       valobj.ActivityStatusDisable,
		ActivityType: valobj.ActivityTypeObj(req.GetActivityType()),
	}
	if req.GetStartTime() != "" {
		startTime, err := time.ParseInLocation(time.DateTime, req.GetStartTime(), helper.GetLocalTimeZone())
		if err != nil {
			return nil, err
		}
		params.StartTime = startTime
	}
	if req.GetEndTime() != "" {
		endTime, err := time.ParseInLocation(time.DateTime, req.GetEndTime(), helper.GetLocalTimeZone())
		if err != nil {
			return nil, err
		}
		params.EndTime = endTime
	}
	var err error
	if err = params.Validate(); err != nil {
		return nil, err
	}

	switch params.ActivityType {
	case valobj.ActivityTypeNormal:
		err = q.UpsertNormal(ctx, params, req)
	case valobj.ActivityTypeFlashSale:
		err = q.UpsertFlashSale(ctx, params, req)
	default:
		return nil, apierr.ErrorParam("未知的活动类型")
	}
	return nil, err
}

func (q *ActivityService) UpsertNormal(ctx context.Context, in *bo.ActivityAddReq, req *adminv1.ActivityUpsertReq) error {
	in.GoodsGoodsSkuNos = req.GetGoodsSkuNos()
	if err := in.ValidateCommon(); err != nil {
		return err
	}
	return q.activityBiz.UpsertNormal(ctx, in)
}

func (q *ActivityService) UpsertFlashSale(ctx context.Context, in *bo.ActivityAddReq, req *adminv1.ActivityUpsertReq) error {
	var err error
	for _, sku := range req.GetFlashSale().GetSku() {
		in.FlashSaleGoods = append(in.FlashSaleGoods, &bo.FlashSaleGoodsUpsertBo{
			SkuNo:    sku.GetSkuNo(),
			Price:    sku.GetPrice(),
			LimitNum: int(sku.GetLimitNum()),
			Stock:    int(sku.GetStock()),
			Sort:     int(sku.GetSort()),
		})
	}
	dates := in.GetFlashSaleDate()
	for _, date := range dates {
		for _, saleTime := range req.GetFlashSale().GetTime() {
			item := &bo.FlashSaleTimeUpsertBo{
				Date: int(date.Unix()),
			}
			item.StartTime, err = in.GetFlashSaleTime(date, saleTime.GetStartTime())
			if err != nil {
				return err
			}
			item.EndTime, err = in.GetFlashSaleTime(date, saleTime.GetEndTime())
			in.FlashSaleTime = append(in.FlashSaleTime, item)
		}
	}
	if err = in.ValidateFlashSale(); err != nil {
		return err
	}
	return q.activityBiz.UpsertFlashSale(ctx, in)
}

func (q *ActivityService) UpdateStatus(ctx context.Context, req *adminv1.ActivityUpdateStatusReq) (*common.RespEmpty, error) {
	params := &bo.ActivityUpdateStatusReq{
		Id:     int(req.GetId()),
		Status: valobj.ActivityStatusObj(req.GetStatus()),
	}
	if err := params.Validate(); err != nil {
		return nil, err
	}
	if err := q.activityBiz.UpdateStatus(ctx, params); err != nil {
		return nil, err
	}
	return nil, nil
}

func (q *ActivityService) List(ctx context.Context, req *adminv1.ActivityListReq) (*adminv1.ActivityListRsp, error) {
	activityTimes, err := helper.ToTimeRange(req.GetActivityTime())
	if err != nil {
		return nil, err
	}
	params := &bo.ActivityListReq{
		Page:          build.ToPageReq(req.GetPage()),
		Name:          req.GetName(),
		Status:        valobj.ActivityProgressStatusObj(req.GetStatus()),
		ActivityTimes: activityTimes,
		ActivityType:  valobj.ActivityTypeObj(req.GetActivityType()),
	}
	items, pageInfo, err := q.activityBiz.List(ctx, params)
	if err != nil {
		return nil, err
	}
	rsp := &adminv1.ActivityListRsp{
		Page: build.ToPageRsp(pageInfo),
	}
	if len(items) == 0 {
		return rsp, nil
	}

	var orderCounts []*do.PayOrderActivityTotalDo
	orderCounts, err = q.activityBiz.OrderCount(ctx, slice.Map(items, func(_ int, item *do.ActivityDo) int { return item.ID }))
	if err != nil {
		return nil, err
	}

	var goodsCounts []*do.ActivityGoodsCountDo
	goodsCounts, err = q.activityBiz.GoodsCount(ctx, items)
	if err != nil {
		return nil, err
	}

	orderCountsMap := slice.KeyBy(orderCounts, func(item *do.PayOrderActivityTotalDo) int { return item.ActivityID })
	goodsCountsMap := slice.KeyBy(goodsCounts, func(item *do.ActivityGoodsCountDo) int { return item.ActivityID })
	for _, item := range items {
		rspItem := &adminv1.ActivityItem{
			Id:             int32(item.ID),
			Name:           item.Name,
			Banner:         item.Banner,
			Background:     item.Bg,
			ProgressStatus: int32(item.GetStatus()),
			StartTime:      item.StartTime.Format(time.DateTime),
			EndTime:        item.EndTime.Format(time.DateTime),
			ActivityStatus: int32(item.GetShopAdminActivityStatus()),
			ActivityType:   int32(item.ActivityType),
			GoodsNum:       0,
			OrderNum:       0,
		}

		orderCount := orderCountsMap[item.ID]
		if orderCount != nil {
			rspItem.OrderNum = int32(orderCount.Total)
		}
		goodsCount := goodsCountsMap[item.ID]
		if goodsCount != nil {
			rspItem.GoodsNum = int32(goodsCount.Count)
		}
		rsp.All = append(rsp.All, rspItem)
	}
	return rsp, nil
}

func (q *ActivityService) Detail(ctx context.Context, req *adminv1.ActivityDetailReq) (*adminv1.ActivityItem, error) {
	activity := q.activityBiz.FindOne(ctx, int(req.GetId()))
	if activity == nil {
		return nil, apierr.ErrorDbNotFound("活动不存在")
	}
	switch activity.ActivityType {
	case valobj.ActivityTypeNormal:
		return q.detailNormal(ctx, activity.ID)
	case valobj.ActivityTypeFlashSale:
		return q.detailFlashSale(ctx, activity.ID)
	default:
		return nil, apierr.ErrorParam("未知的活动类型")
	}
}

func (q *ActivityService) detailNormal(ctx context.Context, id int) (*adminv1.ActivityItem, error) {
	activity, err := q.activityBiz.Detail(ctx, id)
	if err != nil {
		return nil, err
	}
	rsp := &adminv1.ActivityItem{
		Id:             int32(activity.ID),
		Name:           activity.Name,
		Banner:         activity.Banner,
		Background:     activity.Bg,
		ProgressStatus: int32(activity.GetStatus()),
		StartTime:      activity.StartTime.Format(time.DateTime),
		EndTime:        activity.EndTime.Format(time.DateTime),
		ActivityStatus: int32(activity.ActivityStatus),
		ActivityType:   int32(activity.ActivityType),
	}
	rsp.ActivityCommon = &adminv1.ActivityCommon{
		GoodsList: make([]*adminv1.ActivityCommon_GoodsSkuItem, 0, len(activity.GoodsList)),
		GoodsIds:  helper.IntToInt32(activity.GoodsIds),
	}
	for _, skuDo := range activity.GoodsList {
		rsp.ActivityCommon.GoodsList = append(rsp.ActivityCommon.GoodsList, &adminv1.ActivityCommon_GoodsSkuItem{
			Id:          int32(skuDo.Id),
			SkuNo:       skuDo.SkuNo,
			Name:        skuDo.GetSkuFullName(),
			Image:       skuDo.Image,
			SalePrice:   skuDo.SalePrice,
			Stock:       int32(skuDo.Stock),
			Status:      int32(skuDo.Status),
			MarketPrice: skuDo.MarketPrice,
		})
	}
	return rsp, nil
}

func (q *ActivityService) detailFlashSale(ctx context.Context, id int) (*adminv1.ActivityItem, error) {
	activity, err := q.activityBiz.DetailFlashSale(ctx, id)
	if err != nil {
		return nil, err
	}
	rsp := &adminv1.ActivityItem{
		Id:             int32(activity.ID),
		Name:           activity.Name,
		Banner:         activity.Banner,
		Background:     activity.Bg,
		ProgressStatus: int32(activity.GetStatus()),
		StartTime:      activity.StartTime.Format(time.DateTime),
		EndTime:        activity.EndTime.Format(time.DateTime),
		ActivityStatus: int32(activity.ActivityStatus),
		ActivityType:   int32(activity.ActivityType),
	}
	rsp.FlashSale = &adminv1.ActivityFlashSale{
		Time: make([]*adminv1.ActivityFlashSale_Time, 0),
		Sku:  make([]*adminv1.ActivityFlashSale_Sku, 0, len(activity.FlashSaleGoods)),
	}
	for _, sku := range activity.FlashSaleGoods {
		rsp.FlashSale.Sku = append(rsp.FlashSale.Sku, &adminv1.ActivityFlashSale_Sku{
			SkuNo:    sku.SkuNo,
			Stock:    int32(sku.Stock),
			Price:    sku.Price,
			LimitNum: int32(sku.LimitNum),
			Sort:     int32(sku.Sort),
			Extra: &adminv1.ActivityFlashSale_Sku_Extra{
				Name:          sku.GoodsSku.GetSkuFullName(),
				Image:         sku.GoodsSku.Image,
				SupplierPrice: sku.GoodsSku.SupplierPrice,
				SalePrice:     sku.GoodsSku.SalePrice,
				MarketPrice:   sku.GoodsSku.MarketPrice,
				Stock:         int32(sku.GoodsSku.Stock),
			},
		})
	}

	for _, t := range activity.GetFlashSaleTimeFrame() {
		rsp.FlashSale.Time = append(rsp.FlashSale.Time, &adminv1.ActivityFlashSale_Time{
			StartTime: t[1],
			EndTime:   t[2],
		})
	}
	return rsp, nil
}

func (q *ActivityService) Del(ctx context.Context, req *adminv1.ActivityDelReq) (*common.RespEmpty, error) {
	if err := q.activityBiz.Del(ctx, int(req.GetId())); err != nil {
		return nil, err
	}
	return nil, nil
}
