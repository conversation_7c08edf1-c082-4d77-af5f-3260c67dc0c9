package admin

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"
	"unicode/utf8"

	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	adminDo "cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/appbiz"
	bo3 "cardMall/internal/module/appbiz/bo"
	appDO "cardMall/internal/module/appbiz/do"
	"cardMall/internal/pkg/build"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/service/admin/convertor"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/shopspring/decimal"

	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/xuri/excelize/v2"
)

type OrderService struct {
	base
	adminv1.UnimplementedOrderServer
	biz                *adminbiz.OrderBiz
	payBiz             *appbiz.PayBiz
	userBiz            *adminbiz.UserBiz
	sysTaskBiz         *adminbiz.SysTaskBiz
	integralConfigBiz  *adminbiz.IntegralConfigBiz
	afterSaleConvertor *convertor.AfterSaleConvertor
	giftCardDs         *ds.GiftCardDs
}

func NewOrderService(biz *adminbiz.OrderBiz,
	integralConfigBiz *adminbiz.IntegralConfigBiz,
	payBiz *appbiz.PayBiz,
	sysTaskBiz *adminbiz.SysTaskBiz,
	userBiz *adminbiz.UserBiz,
	giftCardDs *ds.GiftCardDs,
	afterSaleConvertor *convertor.AfterSaleConvertor) *OrderService {
	return &OrderService{biz: biz, sysTaskBiz: sysTaskBiz, integralConfigBiz: integralConfigBiz, payBiz: payBiz, userBiz: userBiz, afterSaleConvertor: afterSaleConvertor, giftCardDs: giftCardDs}
}
func (o *OrderService) List(ctx context.Context, req *adminv1.OrderListReq) (*adminv1.OrderListRsp, error) {
	var in = &bo.OrderListBo{
		OrderNumber:      req.OrderNumber,
		GoodsName:        req.GoodsName,
		CreateTimeStart:  0,
		CreateTimeEnd:    0,
		FinishTimeStart:  0,
		FinishTimeEnd:    0,
		BrandName:        req.GetBrandName(),
		OrderType:        valobj.OrderTypeObj(req.GetOrderType()),
		UserId:           int(req.GetUserId()),
		Account:          req.GetAccount(),
		CardCouponNumber: req.CardCouponNumber,
		SettlementType:   req.SettlementType,
		CardGiftNumber:   req.CardGiftNumber,
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		AdminLoginInfoBo: o.GetLoginInfoX(ctx).ToLoginInfo(),
	}
	if req.CreateTimeStart != "" {
		t, err := helper.GetTimestamp(req.CreateTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.CreateTimeStart)
		}
		in.CreateTimeStart = int(t)
	}
	if req.CreateTimeEnd != "" {
		t, err := helper.GetTimestamp(req.CreateTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.CreateTimeEnd)
		}
		in.CreateTimeEnd = int(t)
	}

	if req.FinishTimeStart != "" {
		t, err := helper.GetTimestamp(req.FinishTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.FinishTimeStart)
		}
		in.FinishTimeStart = int(t)
	}
	if req.FinishTimeEnd != "" {
		t, err := helper.GetTimestamp(req.FinishTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.FinishTimeEnd)
		}
		in.FinishTimeEnd = int(t)
	}
	if req.Status != nil {
		status := valobj.OrderStatusObj(req.GetStatus())
		in.Status = &status
	}
	if req.SiteId != nil {
		siteId := int(req.GetSiteId())
		in.SiteId = &siteId
	}

	list := make([]*adminv1.OrderListRsp_OrderListItem, 0, in.GetPageSize())
	var rsp = &adminv1.OrderListRsp{
		Page:  int32(in.GetPage()),
		Count: 0,
		List:  list,
	}

	count, data, err := o.biz.List(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}

	rsp.Count = int32(count)
	for _, val := range data {
		cardGiftNumbers, err := o.biz.GetCardGift(ctx, val.PayOrderNumber)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
		}
		item := &adminv1.OrderListRsp_OrderListItem{
			Id:                     int32(val.Id),
			OrderNumber:            val.OrderNumber,
			Status:                 int32(val.Status),
			CreateTime:             helper.GetTimeDate(val.CreateTime),
			UserId:                 int32(val.UserId),
			PayTime:                "",
			GoodsName:              build.GenGoodsNameWithSKU(val.GoodsName, val.GoodsSkuName),
			GoodsImage:             val.GoodsImage,
			BrandName:              val.BrandName,
			PayAmount:              helper.Float64ToString(val.PayAmount, 2),
			SalePrice:              helper.Float64ToString(val.SalePrice, 2),
			Account:                val.Account,
			FinishTime:             "",
			PayIntegral:            int32(val.PayIntegral),
			OrderType:              int32(val.OrderType),
			SiteName:               val.SiteName,
			CardCouponNumber:       val.CardCouponNumber,
			SettlementType:         int32(val.SettlementType),
			SaleIntegral:           int32(val.SaleIntegral),
			ActualDiscountIntegral: int32(val.ActualDiscountIntegral),
			CardGiftNumber:         strings.Join(cardGiftNumbers, ","),
			PayChannelType:         int32(val.PayChannel),
		}
		if val.FinishTime > 0 {
			item.FinishTime = helper.GetTimeDate(val.FinishTime)
		}
		if val.PayTime > 0 {
			item.PayTime = helper.GetTimeDate(val.PayTime)
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (o *OrderService) ThirdList(ctx context.Context, req *adminv1.ThirdOrderListReq) (*adminv1.ThirdOrderListRsp, error) {
	var in = &bo.OrderListBo{
		OrderNumber:     req.OrderNumber,
		GoodsName:       req.GoodsName,
		CreateTimeStart: 0,
		CreateTimeEnd:   0,
		FinishTimeStart: 0,
		FinishTimeEnd:   0,
		BrandName:       req.GetBrandName(),
		OrderType:       valobj.OrderTypeObj(req.GetOrderType()),
		UserId:          int(req.GetUserId()),
		CardGiftNumber:  req.CardGiftNumber,
		SettlementType:  req.GetSettlementType(),
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}
	if req.CreateTimeStart != "" {
		t, err := helper.GetTimestamp(req.CreateTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.CreateTimeStart)
		}
		in.CreateTimeStart = int(t)
	}
	if req.CreateTimeEnd != "" {
		t, err := helper.GetTimestamp(req.CreateTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.CreateTimeEnd)
		}
		in.CreateTimeEnd = int(t)
	}

	if req.FinishTimeStart != "" {
		t, err := helper.GetTimestamp(req.FinishTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.FinishTimeStart)
		}
		in.FinishTimeStart = int(t)
	}
	if req.FinishTimeEnd != "" {
		t, err := helper.GetTimestamp(req.FinishTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.FinishTimeEnd)
		}
		in.FinishTimeEnd = int(t)
	}
	if req.Status != nil {
		status := valobj.OrderStatusObj(req.GetStatus())
		in.Status = &status
	}
	if req.SiteId != nil {
		siteId := int(req.GetSiteId())
		in.SiteId = &siteId
	}

	list := make([]*adminv1.ThirdOrderListRsp_OrderListItem, 0, in.GetPageSize())
	var rsp = &adminv1.ThirdOrderListRsp{
		Page:  int32(in.GetPage()),
		Count: 0,
		List:  list,
	}

	count, data, err := o.biz.ThirdList(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}

	rsp.Count = int32(count)
	for _, val := range data {
		cardGiftNumbers, err := o.biz.GetCardGift(ctx, val.PayOrderNumber)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
		}
		item := &adminv1.ThirdOrderListRsp_OrderListItem{
			Id:             int32(val.Id),
			OrderNumber:    val.OrderNumber,
			Status:         int32(val.Status),
			CreateTime:     helper.GetTimeDate(val.CreateTime),
			UserId:         int32(val.UserId),
			PayTime:        "",
			GoodsName:      val.GoodsName,
			GoodsImage:     val.GoodsImage,
			BrandName:      val.BrandName,
			PayAmount:      helper.Float64ToString(val.PayAmount, 2),
			SalePrice:      helper.Float64ToString(val.SalePrice, 2),
			Account:        val.Account,
			FinishTime:     "",
			PayIntegral:    int32(val.PayIntegral),
			OrderType:      int32(val.OrderType),
			SiteName:       val.SiteName,
			SettlementType: int32(val.SettlementType),
			CardGiftNumber: strings.Join(cardGiftNumbers, ","),
			TotalAmount:    helper.Float64ToString(val.TotalAmount, 2),
			PayChannelType: int32(val.PayChannel),
		}
		if val.FinishTime > 0 {
			item.FinishTime = helper.GetTimeDate(val.FinishTime)
		}
		if val.PayTime > 0 {
			item.PayTime = helper.GetTimeDate(val.PayTime)
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (o *OrderService) Detail(ctx context.Context, req *adminv1.OrderDetailReq) (*adminv1.OrderDetailRsp, error) {
	data, err := o.biz.Detail(ctx, int(req.Id))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	var rsp = &adminv1.OrderDetailRsp{
		Id:                   int32(data.Id),
		OrderNumber:          data.OrderNumber,
		Status:               int32(data.Status),
		CreateTime:           helper.GetTimeDate(data.CreateTime),
		UserId:               int32(data.UserId),
		PayTime:              "",
		GoodsName:            build.GenGoodsNameWithSKU(data.GoodsSkuName, data.GoodsSkuName),
		GoodsImage:           data.GoodsImage,
		BrandName:            data.BrandName,
		PayAmount:            helper.Float64ToString(data.PayAmount, 2),
		SalePrice:            helper.Float64ToString(data.SalePrice, 2),
		Account:              data.Account,
		FinishTime:           "",
		CouponId:             int32(data.CouponId),
		CouponTitle:          data.CouponTitle,
		CouponCodeId:         int32(data.CouponCodeId),
		CouponDiscountAmount: helper.Float64ToString(data.CouponDiscountAmount, 2),
		CouponCode:           data.CouponCode,
		PayIntegral:          int32(data.PayIntegral),
		ChannelPrice:         helper.Float64ToString(data.ChannelPrice, 4),
		OrderType:            int32(data.OrderType),
		PayOrderNumber:       data.PayOrderNumber,
		PayType:              int32(data.PayType),
		SiteName:             data.SiteName,
		CardCouponNumber:     data.CardCouponNumber,
		SettlementType:       data.SettlementType,
		SaleIntegral:         int32(data.SaleIntegral),
		CardGiftNumber:       data.GetCardGiftNos(),
		CardGiftAmount:       data.GetCardGiftAmount().InexactFloat64(),
		TotalAmount:          helper.Float64ToString(data.TotalAmount, 2),
		PayChannelType:       int32(data.PayChannel),
	}
	if data.GoodsName != data.GoodsSkuName {
		rsp.GoodsName = data.GoodsName + "_" + data.GoodsSkuName
	}
	orderTypeObj := valobj.OrderTypeObj(data.OrderType)
	if orderTypeObj == valobj.OrderTypeMeiTuan || orderTypeObj == valobj.OrderTypeQianZhuCinema || orderTypeObj == valobj.OrderTypeQianZhuKFC {
		rsp.GoodsName = data.GoodsName
	}
	if data.PayTime > 0 {
		rsp.PayTime = helper.GetTimeDate(data.PayTime)
	}
	if data.FinishTime > 0 {
		rsp.FinishTime = helper.GetTimeDate(data.FinishTime)
	}
	if data.OrderAfterSale != nil {
		rsp.AfterSale = &adminv1.OrderDetailRsp_AfterSale{
			Id:          int32(data.OrderAfterSale.Id),
			OrderId:     int32(data.OrderAfterSale.OrderId),
			OrderNumber: data.OrderAfterSale.OrderNumber,
			Reason:      data.OrderAfterSale.Reason,
			Images:      nil,
		}
		rsp.AfterSale.Images = make([]string, 0)
		_ = json.Unmarshal([]byte(data.OrderAfterSale.Images), &rsp.AfterSale.Images)
	}
	return rsp, nil
}

func (o *OrderService) Refund(ctx context.Context, req *adminv1.OrderRefundReq) (*adminv1.OrderRefundRsp, error) {
	if req.GetId() <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	adminInfo, err := o.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotLogin("获取登录信息失败:%s", err.Error())
	}
	err = o.biz.Refund(ctx, &bo.RefundBo{
		OrderId:      int(req.GetId()),
		RefundAmount: 0,
		RefundReason: "管理员手动退款",
	}, adminInfo)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.OrderRefundRsp{EffectRow: 1}, nil
}

func (o *OrderService) AfterSaleOver(ctx context.Context, req *adminv1.OrderAfterSaleOverReq) (*adminv1.OrderAfterSaleOverRsp, error) {
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	row, err := o.biz.AfterSaleOver(ctx, id)
	if err != nil {
		return nil, err
	}
	return &adminv1.OrderAfterSaleOverRsp{EffectRow: int32(row)}, nil
}

func (o *OrderService) Cancel(ctx context.Context, req *adminv1.OrderCancelReq) (*adminv1.OrderCancelRsp, error) {
	if req.GetId() <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	row, err := o.biz.Cancel(ctx, int(req.GetId()))
	if err != nil {
		return nil, err
	}
	return &adminv1.OrderCancelRsp{EffectRow: int32(row)}, nil
}

func (o *OrderService) Export(ctx context.Context, req *adminv1.OrderExportReq) (*adminv1.OrderExportRsp, error) {
	var (
		err error
	)
	adminDo, err := o.base.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	var in = &bo.OrderListBo{
		OrderNumber:      req.OrderNumber,
		GoodsName:        req.GoodsName,
		CreateTimeStart:  0,
		CreateTimeEnd:    0,
		FinishTimeStart:  0,
		FinishTimeEnd:    0,
		BrandName:        req.GetBrandName(),
		OrderType:        valobj.OrderTypeObj(req.GetOrderType()),
		UserId:           int(req.GetUserId()),
		Account:          req.GetAccount(),
		CardCouponNumber: req.CardCouponNumber,
		CardGiftNumber:   req.CardGiftNumber,
		SettlementType:   req.SettlementType,
	}
	if req.CreateTimeStart != "" {
		t, err := helper.GetTimestamp(req.CreateTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.CreateTimeStart)
		}
		in.CreateTimeStart = int(t)
	}
	if req.CreateTimeEnd != "" {
		t, err := helper.GetTimestamp(req.CreateTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.CreateTimeEnd)
		}
		in.CreateTimeEnd = int(t)
	}

	if req.FinishTimeStart != "" {
		t, err := helper.GetTimestamp(req.FinishTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.FinishTimeStart)
		}
		in.FinishTimeStart = int(t)
	}
	if req.FinishTimeEnd != "" {
		t, err := helper.GetTimestamp(req.FinishTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("参数错误:%s", req.FinishTimeEnd)
		}
		in.FinishTimeEnd = int(t)
	}
	if req.Status != nil {
		status := valobj.OrderStatusObj(req.GetStatus())
		in.Status = &status
	}
	if req.SiteId != nil {
		siteId := int(req.GetSiteId())
		in.SiteId = &siteId
	}

	if err = in.ExportValidate(); err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}

	sysTask, err := o.sysTaskBiz.GenerateTask(ctx, &bo2.SysTaskExecuteBo{
		SysTaskName:   "虚拟订单导出",
		SysTaskType:   valobj.SysTaskTypeExport,
		SysTaskSource: valobj.SysTaskSourceVirtualOrderExport,
		Parameter:     in.String(),
		Remark:        "虚拟订单导出",
		UserID:        adminDo.Id,
		UserName:      adminDo.Name,
		RelatedNo:     "",
	})
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.OrderExportRsp{
		SysTaskNumber: sysTask.SysTaskNumber,
	}, nil
}

func (o *OrderService) ThirdExport(ctx http.Context, ctx1 context.Context) error {
	params := ctx.Request().URL.Query()
	var in = &bo.OrderListBo{
		OrderNumber:     params.Get("orderNumber"),
		GoodsName:       params.Get("goodsName"),
		CreateTimeStart: 0,
		CreateTimeEnd:   0,
		BrandName:       params.Get("brandName"),
		CardGiftNumber:  params.Get("cardGiftNumber"),
	}
	if createStart := params.Get("createTimeStart"); createStart != "" {
		timeStr, err := helper.GetTimestamp(createStart)
		if err != nil {
			return apierr.ErrorParam("参数错误:%s", createStart)
		}
		in.CreateTimeStart = int(timeStr)
	}
	if createTimeEnd := params.Get("createTimeEnd"); createTimeEnd != "" {
		timeStr, err := helper.GetTimestamp(createTimeEnd)
		if err != nil {
			return apierr.ErrorParam("参数错误:%s", createTimeEnd)
		}
		in.CreateTimeEnd = int(timeStr)
	}
	if finishTimeStart := params.Get("finishTimeStart"); finishTimeStart != "" {
		timeStr, err := helper.GetTimestamp(finishTimeStart)
		if err != nil {
			return apierr.ErrorParam("参数错误:%s", finishTimeStart)
		}
		in.FinishTimeStart = int(timeStr)
	}
	if finishTimeEnd := params.Get("finishTimeEnd"); finishTimeEnd != "" {
		timeStr, err := helper.GetTimestamp(finishTimeEnd)
		if err != nil {
			return apierr.ErrorParam("参数错误:%s", finishTimeEnd)
		}
		in.FinishTimeEnd = int(timeStr)
	}
	if len(params.Get("status")) != 0 {
		statusInt, err := strconv.Atoi(params.Get("status"))
		if err != nil {
			return fmt.Errorf("status参数解析失败:%s", err)
		}
		status := valobj.OrderStatusObj(statusInt)
		in.Status = &status
	}
	if params.Get("orderType") != "" {
		orderTypeInt, err := strconv.Atoi(params.Get("orderType"))
		if err != nil {
			return fmt.Errorf("payType参数解析失败:%s", err)
		}
		orderType := valobj.OrderTypeObj(orderTypeInt)
		in.OrderType = orderType
	}
	if params.Get("siteId") != "" {
		siteId, err := strconv.Atoi(params.Get("siteId"))
		if err != nil {
			return fmt.Errorf("siteId参数解析失败:%s", err)
		}
		in.SiteId = &siteId
	}
	if params.Get("userId") != "" {
		userId, err := strconv.Atoi(params.Get("userId"))
		if err != nil {
			return fmt.Errorf("userId参数解析失败:%s", err)
		}
		in.UserId = userId
	}
	if params.Get("settlementType") != "" {
		settlementType, err := strconv.Atoi(params.Get("settlementType"))
		if err != nil {
			return fmt.Errorf("settlementType参数解析失败:%s", err)
		}
		in.SettlementType = int32(settlementType)
	}

	if err := in.ExportValidate(); err != nil {
		return err
	}

	data, err := o.biz.ThirdExport(ctx1, in)
	if err != nil {
		return err
	}
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return err
	}
	_ = f.SetCellStr("sheet1", "A1", "订单号")
	_ = f.SetCellStr("sheet1", "B1", "品牌")
	_ = f.SetCellStr("sheet1", "C1", "商品名称")
	_ = f.SetCellStr("sheet1", "D1", "售价")
	_ = f.SetCellStr("sheet1", "E1", "实付金额")
	_ = f.SetCellStr("sheet1", "F1", "状态")
	_ = f.SetCellStr("sheet1", "G1", "下单时间")
	_ = f.SetCellStr("sheet1", "H1", "支付时间")
	_ = f.SetCellStr("sheet1", "I1", "用户ID")
	_ = f.SetCellStr("sheet1", "J1", "充值账号")
	_ = f.SetCellStr("sheet1", "K1", "完成时间")
	_ = f.SetCellStr("sheet1", "L1", "订单类型")
	_ = f.SetCellStr("sheet1", "M1", "订单来源")
	_ = f.SetCellStr("sheet1", "N1", "结算类型")
	_ = f.SetCellStr("sheet1", "O1", "礼品卡号兑换码")
	_ = f.SetCellStr("sheet1", "P1", "礼品卡抵扣")

	if len(data) > 0 {
		var row = 2
		for _, v := range data {
			strRwo := strconv.Itoa(row)

			createTime := ""
			if v.CreateTime > 0 {
				createTime = helper.GetTimeDate(v.CreateTime)
			}

			payTime := ""
			if v.PayTime > 0 {
				payTime = helper.GetTimeDate(v.PayTime)
			}

			finishTime := ""
			if v.FinishTime > 0 {
				finishTime = helper.GetTimeDate(v.FinishTime)
			}
			_ = f.SetCellStr("sheet1", "A"+strRwo, v.OrderNumber)
			_ = f.SetCellStr("sheet1", "B"+strRwo, v.BrandName)
			_ = f.SetCellStr("sheet1", "C"+strRwo, v.GoodsName)
			_ = f.SetCellStr("sheet1", "D"+strRwo, helper.Float64ToString(v.SalePrice, 2))
			_ = f.SetCellStr("sheet1", "E"+strRwo, helper.Float64ToString(v.PayAmount, 2))
			_ = f.SetCellStr("sheet1", "F"+strRwo, v.Status.GetName())
			_ = f.SetCellStr("sheet1", "G"+strRwo, createTime)
			_ = f.SetCellStr("sheet1", "H"+strRwo, payTime)
			_ = f.SetCellStr("sheet1", "I"+strRwo, strconv.Itoa(v.UserId))
			_ = f.SetCellStr("sheet1", "J"+strRwo, v.Account)
			_ = f.SetCellStr("sheet1", "K"+strRwo, finishTime)
			_ = f.SetCellStr("sheet1", "L"+strRwo, v.OrderType.GetName())
			_ = f.SetCellStr("sheet1", "M"+strRwo, v.SiteName)
			_ = f.SetCellStr("sheet1", "N"+strRwo, v.SettlementType.String())
			_ = f.SetCellStr("sheet1", "O"+strRwo, v.GetCardGiftNos())
			_ = f.SetCellStr("sheet1", "P"+strRwo, v.GetCardGiftAmount().String())
			row++
		}
	}

	f.SetActiveSheet(index)
	var fileName = "card_mall_order" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	ctx.Response().Header().Set("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Response().Header().Set("Content-Disposition", fmt.Sprintf("attachment;filename=\"%s\"", fileName))
	ctx.Response().Header().Set("Content-Transfer-Encoding", "binary")
	ctx.Response().Header().Set("Cache-Control", "must-revalidate")
	ctx.Response().Header().Set("Cache-Control", "max-age=0")
	ctx.Response().Header().Set("Pragma", "public")
	buff, err := f.WriteToBuffer()
	if err != nil {
		return err
	}
	_, err = buff.WriteTo(ctx.Response())
	if err != nil {
		return err
	}
	return nil
}

func (o *OrderService) Del(ctx context.Context, req *adminv1.OrderDelReq) (*adminv1.OrderDelRsp, error) {
	payOrderId := int(req.GetPayOrderId())
	if payOrderId <= 0 {
		return nil, apierr.ErrorParam("参数错误")
	}
	row, err := o.biz.Del(ctx, payOrderId)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.OrderDelRsp{EffectRow: int32(row)}, nil
}
func (o *OrderService) RealOrderList(ctx context.Context, req *adminv1.RealOrderListReq) (*adminv1.RealOrderListResp, error) {
	var (
		createTimeStart int64
		createTimeEnd   int64
		err             error
	)
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		createTimeStart, err = helper.GetTimestamp(req.CreateTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
		createTimeEnd, err = helper.GetTimestamp(req.CreateTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
	}
	var in = &bo.RealOrderListBo{
		Status:           req.Status,
		OrderNumber:      req.OrderNumber,
		CreateTimeStart:  createTimeStart,
		CreateTimeEnd:    createTimeEnd,
		GoodsName:        req.GoodsName,
		GoodsId:          req.GoodsId,
		SupplierId:       req.SupplierId,
		SettlementType:   req.SettlementType,
		SkuNo:            req.SkuNo,
		SiteId:           req.SiteId,
		CardCouponNumber: req.CardCouponNumber,
		CardGiftNumber:   req.CardGiftNumber,
		OnlyTimeout:      req.OnlyTimeout,
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}

	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	count, data, err := o.biz.RealOrderList(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	yearOrderNum, monthOrderNum, todayOrderNum, err := o.biz.OrderNum(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	list := make([]*adminv1.RealOrderListResp_RealOrderListItem, 0, in.GetPageSize())
	var rsp = &adminv1.RealOrderListResp{
		YearOrderNum:  int32(yearOrderNum),
		MonthOrderNum: int32(monthOrderNum),
		TodayOrderNum: int32(todayOrderNum),
		Page:          int32(in.GetPage()),
		Count:         int32(count),
		List:          list,
	}
	if count == 0 {
		return rsp, nil
	}
	realOrderSubListDoMap, realOrderMainListDoMap, realOrderGoodsListDoMap, err := o.getRealOrderDataList(ctx, data)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	for _, orderMainListDo := range realOrderMainListDoMap {
		cardGiftNumbers, err := o.biz.GetCardGift(ctx, orderMainListDo.PayOrderNumber)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
		}
		item := &adminv1.RealOrderListResp_RealOrderListItem{
			Id:               int32(orderMainListDo.ID),
			OrderNumber:      orderMainListDo.OrderNumber,
			CreateTime:       helper.GetTimeDateInt64(orderMainListDo.CreateTime),
			UserId:           int32(orderMainListDo.UserID),
			SubOrderList:     []*adminv1.RealOrderListResp_RealOrderListItem{},
			SiteId:           int32(orderMainListDo.SiteID),
			SiteName:         orderMainListDo.SiteName,
			CardCouponNumber: orderMainListDo.CardCouponNumber,
			PayIntegral:      int32(orderMainListDo.PayIntegral),
			CardGiftNumber:   strings.Join(cardGiftNumbers, ","),
		}
		realOrderSubListArr, ok := realOrderSubListDoMap[orderMainListDo.PayOrderNumber]
		if ok {
			subOrderItems := make([]*adminv1.RealOrderListResp_RealOrderListItem, 0, len(realOrderSubListArr))
			for _, val2 := range realOrderSubListArr {
				subOrderItems = append(subOrderItems, &adminv1.RealOrderListResp_RealOrderListItem{
					Id:                int32(val2.ID),
					OrderNumber:       val2.OrderNumber,
					CreateTime:        helper.GetTimeDateInt64(val2.CreateTime),
					UserId:            int32(val2.UserID),
					SettlementType:    int32(val2.SettlementType),
					TotalAmount:       val2.TotalAmount.String(),
					Status:            int32(val2.Status),
					AfterSaleStatus:   int32(val2.AfterSaleStatus),
					UserAddressName:   val2.UserAddressName,
					PhoneNumber:       val2.PhoneNumber,
					Area:              val2.Area,
					Detail:            val2.Detail,
					RealGoods:         o.convertRealOrderGoods(realOrderGoodsListDoMap[val2.OrderNumber]),
					SubOrderList:      []*adminv1.RealOrderListResp_RealOrderListItem{},
					PayIntegral:       int32(val2.PayIntegral),
					PayAmount:         val2.PayAmount.String(),
					IsTimeout:         val2.GetIsTimeout(),
					DeliverExpireTime: helper.GetTimeDate(val2.DeliverExpireTime),
					PayChannelType:    int32(valobj.PayOrderPayTypeObj(val2.PayType).GetPayChannel()),
				})
			}
			sort.Slice(subOrderItems, func(i, j int) bool {
				return subOrderItems[i].CreateTime > subOrderItems[j].CreateTime
			})

			item.SubOrderList = subOrderItems
		}
		rsp.List = append(rsp.List, item)
	}
	sort.Slice(rsp.List, func(i, j int) bool {
		return rsp.List[i].CreateTime > rsp.List[j].CreateTime
	})
	return rsp, nil
}

func (o *OrderService) convertRealOrderGoods(realOrderGoodsList []*adminDo.RealOrderGoodsListDo) []*adminv1.RealOrderGoods {
	realOrderGoodsItem := make([]*adminv1.RealOrderGoods, 0, len(realOrderGoodsList))
	if realOrderGoodsList != nil {
		for _, realOrderGoods := range realOrderGoodsList {
			realOrderGoodsItem = append(realOrderGoodsItem, &adminv1.RealOrderGoods{
				SkuNo:                  realOrderGoods.SkuNo,
				GoodsSkuId:             int32(realOrderGoods.GoodsSkuID),
				GoodsId:                int32(realOrderGoods.GoodsID),
				GoodsName:              build.GenGoodsNameWithSKU(realOrderGoods.GoodsName, realOrderGoods.GoodsSkuName),
				GoodsSkuName:           realOrderGoods.GoodsSkuName,
				Quantity:               int32(realOrderGoods.Quantity),
				GoodsImage:             realOrderGoods.GoodsImage,
				SalePrice:              realOrderGoods.SalePrice.String(),
				OriginPrice:            realOrderGoods.OriginPrice.String(),
				ChannelPrice:           realOrderGoods.ChannelPrice.String(),
				SupplierPrice:          realOrderGoods.SupplierPrice.String(),
				CouponDiscountAmount:   realOrderGoods.CouponDiscountAmount.String(),
				PayAmount:              realOrderGoods.PayAmount.String(),
				PayIntegral:            int32(realOrderGoods.PayIntegral),
				IntegralDiscountAmount: realOrderGoods.IntegralDiscountAmount.String(),
				FreightFee:             realOrderGoods.FreightFee.String(),
				SaleIntegral:           int32(realOrderGoods.SaleIntegral),
				ActualDiscountIntegral: int32(realOrderGoods.ActualDiscountIntegral),
			})
		}
	}
	return realOrderGoodsItem
}
func (o *OrderService) convertRealOrderOperatorLog(orderOperatorLogDos []*bizdo.OrderOperatorLogDo) []*adminv1.RealOrderOperatorLog {
	realOrderOperatorLog := make([]*adminv1.RealOrderOperatorLog, 0, len(orderOperatorLogDos))
	if orderOperatorLogDos != nil {
		for _, orderOperatorLog := range orderOperatorLogDos {
			realOrderOperatorLog = append(realOrderOperatorLog, &adminv1.RealOrderOperatorLog{
				CreateTime:       helper.GetTimeDateInt64(orderOperatorLog.CreateTime),
				Content:          orderOperatorLog.Content,
				OperatorUserType: int32(orderOperatorLog.OperatorUserType),
				OperatorUserId:   int32(orderOperatorLog.OperatorUserID),
				OperatorUserName: orderOperatorLog.OperatorUserName,
				OrderNumber:      orderOperatorLog.OrderNumber,
			})
		}
	}
	return realOrderOperatorLog
}
func (o *OrderService) getRealOrderDataList(ctx context.Context, realOrderListDos []*adminDo.RealOrderListDo) (map[string][]*adminDo.RealOrderListDo, map[string]*adminDo.RealOrderMainListDo, map[string][]*adminDo.RealOrderGoodsListDo, error) {

	var (
		payOrderNumbers         = make([]string, 0, len(realOrderListDos))
		orderNumbers            = make([]string, 0, len(realOrderListDos))
		realOrderSubListDoMap   = make(map[string][]*adminDo.RealOrderListDo)
		realOrderMainListDoMap  = make(map[string]*adminDo.RealOrderMainListDo)
		realOrderGoodsListDoMap = make(map[string][]*adminDo.RealOrderGoodsListDo)
	)
	// 子订单
	for _, val := range realOrderListDos {
		realOrderSubListDoMap[val.PayOrderNumber] = append(realOrderSubListDoMap[val.PayOrderNumber], val)
		payOrderNumbers = append(payOrderNumbers, val.PayOrderNumber)
		orderNumbers = append(orderNumbers, val.OrderNumber)
	}
	// 主订单
	orderMainList, err := o.biz.RealOrderMainList(ctx, &bo.RealOrderMainListBo{
		PayOrderNumbers: payOrderNumbers,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	for _, val := range orderMainList {
		realOrderMainListDoMap[val.PayOrderNumber] = val
	}
	// 订单商品
	realOrderGoodsList, err := o.biz.RealOrderGoodsList(ctx, &bo.RealOrderGoodsListBo{
		OrderNumbers: orderNumbers,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	for _, val := range realOrderGoodsList {
		realOrderGoodsListDoMap[val.OrderNumber] = append(realOrderGoodsListDoMap[val.OrderNumber], val)
	}
	return realOrderSubListDoMap, realOrderMainListDoMap, realOrderGoodsListDoMap, nil
}

// RealOrderDetail 主订单详情
func (o *OrderService) RealOrderDetail(ctx context.Context, req *adminv1.RealOrderDetailReq) (*adminv1.RealOrderDetailRsp, error) {
	if utf8.RuneCountInString(req.GetOrderNumber()) < 1 {
		return nil, apierr.ErrorParam("操作失败:订单号不能为空")
	}
	// 查询主订单
	mainRealOrderDetailDo, err := o.biz.MainRealOrderByPayOrderNumber(ctx, req.OrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 查询子订单
	realOrderDetailDos, err := o.biz.SubRealOrderByPayOrderNumber(ctx, req.OrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	var (
		orderNumbers = slice.Map(realOrderDetailDos, func(_ int, item *adminDo.SubRealOrderDetailDo) string {
			return item.OrderNumber
		})
		orderOperatorLogDos     = make([]*bizdo.OrderOperatorLogDo, 0)
		realOrderDeliverDos     = make([]*bizdo.RealOrderDeliverDo, 0)
		realOrderGoodsList      = make([]*adminDo.RealOrderGoodsListDo, 0)
		realOrderGoodsListDoMap = make(map[string][]*adminDo.RealOrderGoodsListDo)
		realOrderDeliverDoMap   = make(map[string][]*bizdo.RealOrderDeliverDo)
		items                   = make([]*adminv1.RealOrderDetailRsp_RealOrderDetailItem, 0, len(realOrderDetailDos))
		amountInformation       = make([]*adminv1.AmountInformation, 0)
	)
	//查询订单商品
	realOrderGoodsList, err = o.biz.RealOrderGoodsList(ctx, &bo.RealOrderGoodsListBo{
		OrderNumbers: orderNumbers,
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 查询订单操作日志
	orderOperatorLogDos, err = o.biz.RealOrderOperatorLog(ctx, orderNumbers)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 查询订单物流信息
	realOrderDeliverDos, err = o.biz.RealOrderDeliver(ctx, orderNumbers)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 组装费用信息
	amountInformation, err = o.getAmountInformation(ctx, mainRealOrderDetailDo)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	for _, val := range realOrderGoodsList {
		realOrderGoodsListDoMap[val.OrderNumber] = append(realOrderGoodsListDoMap[val.OrderNumber], val)
	}
	for _, val := range realOrderDeliverDos {
		realOrderDeliverDoMap[val.OrderNumber] = append(realOrderDeliverDoMap[val.OrderNumber], val)
	}

	orderCardGift := o.giftCardDs.GetOrderCardGift(ctx, mainRealOrderDetailDo.PayOrderNumber)
	totalAmount := decimal.Zero
	totalRefundAmount := decimal.Zero
	freightFeeTotalAmount := decimal.Zero
	freightFeeTotalRefundAmount := decimal.Zero

	for _, val := range realOrderDetailDos {
		totalAmount = totalAmount.Add(orderCardGift.GetTotalAmount(val.OrderNumber))
		totalRefundAmount = totalRefundAmount.Add(orderCardGift.GetTotalRefundAmount(val.OrderNumber))
		freightFeeTotalAmount = freightFeeTotalAmount.Add(orderCardGift.GetFreightFeeTotalAmount(val.OrderNumber))
		freightFeeTotalRefundAmount = freightFeeTotalRefundAmount.Add(orderCardGift.GetFreightFeeTotalRefundAmount(val.OrderNumber))
		item := &adminv1.RealOrderDetailRsp_RealOrderDetailItem{
			Id:              int32(val.ID),
			OrderNumber:     val.OrderNumber,
			CreateTime:      helper.GetTimeDateInt64(val.CreateTime),
			UserId:          int32(val.UserID),
			TotalAmount:     val.TotalAmount.String(),
			Status:          int32(val.Status),
			AfterSaleStatus: int32(val.AfterSaleStatus),
			FreightFee:      val.FreightFee.Add(freightFeeTotalAmount).String(),
			RealGoods:       o.convertRealOrderGoods(realOrderGoodsListDoMap[val.OrderNumber]),
			PayIntegral:     int32(val.PayIntegral),
			PayChannelType:  int32(valobj.PayOrderPayTypeObj(mainRealOrderDetailDo.PayType).GetOuterPayChannel()),
		}
		orderDeliverDos, ok := realOrderDeliverDoMap[val.OrderNumber]
		if ok {
			logisticsInfos := make([]*adminv1.LogisticsInfo, 0, len(orderDeliverDos))
			for _, realOrderDeliverDo := range orderDeliverDos {
				logisticsInfos = append(logisticsInfos, &adminv1.LogisticsInfo{
					LogisticsNo: realOrderDeliverDo.LogisticsNo,
					KdCode:      realOrderDeliverDo.KdCode,
					KdName:      realOrderDeliverDo.KdName,
				})
			}
			item.LogisticsInfo = logisticsInfos
		}
		items = append(items, item)
	}
	return &adminv1.RealOrderDetailRsp{
		PayChannelType:                      int32(valobj.PayOrderPayTypeObj(mainRealOrderDetailDo.PayType).GetOuterPayChannel()),
		AmountInformation:                   amountInformation,
		TotalPayAmount:                      mainRealOrderDetailDo.TotalPayAmount.String(),
		OrderNumber:                         mainRealOrderDetailDo.OrderNumber,
		SettlementType:                      int32(mainRealOrderDetailDo.SettlementType),
		UserAddressName:                     mainRealOrderDetailDo.UserAddressName,
		PhoneNumber:                         mainRealOrderDetailDo.PhoneNumber,
		Area:                                mainRealOrderDetailDo.Area,
		Detail:                              mainRealOrderDetailDo.Detail,
		RealOrderOperatorLog:                o.convertRealOrderOperatorLog(orderOperatorLogDos),
		RealOrderItem:                       items,
		PayIntegral:                         int32(mainRealOrderDetailDo.PayIntegral),
		FreightFee:                          mainRealOrderDetailDo.FreightFee.Add(freightFeeTotalAmount).String(),
		CardGiftTotalAmount:                 totalAmount.String(),
		CardGiftTotalRefundAmount:           totalRefundAmount.String(),
		CardGiftFreightFeeTotalAmount:       freightFeeTotalAmount.String(),
		CardGiftFreightFeeTotalRefundAmount: freightFeeTotalRefundAmount.String(),
	}, nil
}

func (o *OrderService) RealOrderSubDetail(ctx context.Context, req *adminv1.RealOrderDetailReq) (*adminv1.RealOrderSubDetailRsp, error) {
	if utf8.RuneCountInString(req.GetOrderNumber()) < 1 {
		return nil, apierr.ErrorParam("操作失败:订单号不能为空")
	}

	subRealOrderDetailDo, err := o.biz.SubRealOrderByOrderNumber(ctx, req.OrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	mainRealOrderDetailDo, err := o.biz.MainRealOrderByPayOrderNumber(ctx, subRealOrderDetailDo.PayOrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 订单商品
	realOrderGoodsList, err := o.biz.RealOrderGoodsList(ctx, &bo.RealOrderGoodsListBo{
		OrderNumbers: []string{subRealOrderDetailDo.OrderNumber},
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 订单日志
	orderOperatorLogDos, err := o.biz.RealOrderOperatorLog(ctx, []string{subRealOrderDetailDo.OrderNumber})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 订单物流
	realOrderDeliverDos, err := o.biz.RealOrderDeliverOne(ctx, subRealOrderDetailDo.OrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 用户信息
	userDo, err := o.userBiz.GetOneUserById(ctx, subRealOrderDetailDo.UserID)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}

	payStatus := mainRealOrderDetailDo.PayStatus
	switch subRealOrderDetailDo.Status {
	case valobj.OrderStatusRefundPart:
		payStatus = 5
	case valobj.OrderStatusRefunded:
		payStatus = 4
	default:
	}

	result := &adminv1.RealOrderSubDetailRsp{
		Id:                   int32(subRealOrderDetailDo.ID),
		OrderNumber:          subRealOrderDetailDo.OrderNumber,
		PayOrderNumber:       subRealOrderDetailDo.PayOrderNumber,
		PlaceOrderType:       0,
		CreateTime:           helper.GetTimeDateInt64(subRealOrderDetailDo.CreateTime),
		Status:               int32(subRealOrderDetailDo.Status),
		AfterSaleStatus:      int32(subRealOrderDetailDo.AfterSaleStatus),
		TotalAmount:          subRealOrderDetailDo.TotalAmount.String(),
		UserId:               int32(subRealOrderDetailDo.UserID),
		UserAccount:          userDo.PhoneNumber,
		UserPhoneNumber:      userDo.PhoneNumber,
		UserNickName:         userDo.NickName,
		UserClientType:       int32(userDo.ClientType),
		UserCreateTime:       helper.GetTimeDate(userDo.CreateTime),
		UserAddressName:      mainRealOrderDetailDo.UserAddressName,
		PhoneNumber:          mainRealOrderDetailDo.PhoneNumber,
		Area:                 mainRealOrderDetailDo.Area,
		Detail:               mainRealOrderDetailDo.Detail,
		PayType:              int32(mainRealOrderDetailDo.PayType),
		PayStatus:            int32(payStatus),
		RealGoods:            o.convertRealOrderGoods(realOrderGoodsList),
		RealOrderOperatorLog: o.convertRealOrderOperatorLog(orderOperatorLogDos),
		LogisticsInfo:        make([]*adminv1.LogisticsInfo, 0),
		FreightFee:           subRealOrderDetailDo.FreightFee.String(),
		PayIntegral:          int32(subRealOrderDetailDo.PayIntegral),
		PayChannelType:       int32(valobj.PayOrderPayTypeObj(mainRealOrderDetailDo.PayType).GetOuterPayChannel()),
	}
	if len(realOrderDeliverDos) > 0 {
		logisticsInfos := make([]*adminv1.LogisticsInfo, 0, len(realOrderDeliverDos))
		for _, realOrderDeliverDo := range realOrderDeliverDos {
			logisticsInfos = append(logisticsInfos, &adminv1.LogisticsInfo{
				LogisticsNo: realOrderDeliverDo.LogisticsNo,
				KdCode:      realOrderDeliverDo.KdCode,
				KdName:      realOrderDeliverDo.KdName,
			})
		}
		result.LogisticsInfo = logisticsInfos
	}
	return result, nil
}

func (o *OrderService) RealOrderLogistics(ctx context.Context, req *adminv1.RealOrderLogisticsReq) (*adminv1.RealOrderLogisticsRsp, error) {
	in := &bo3.OrderLogisticsFindLastBo{
		OrderNumber: req.GetOrderNumber(),
		LogisticsNo: req.GetLogisticsNo(),
	}
	if err := in.Validate(); err != nil {
		return nil, apierr.ErrorParam("操作失败:参数错误")
	}

	list, err := o.biz.RealOrderLogistics(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic(err.Error())
	}
	l := make([]*adminv1.RealOrderLogisticsRsp_OrderLogisticsListItem, 0, len(list))
	for _, v := range list {
		item := &adminv1.RealOrderLogisticsRsp_OrderLogisticsListItem{
			LogisticsNo: v.LogisticsNo,
			OpMessage:   v.OpMessage,
			KdCode:      v.KdCode,
			OrderNumber: v.OrderNumber,
			OpTime:      helper.GetTimeDate(v.OpTime),
			OpDesc:      v.OpDesc,
			AddressText: v.AddressText,
		}
		l = append(l, item)
	}
	return &adminv1.RealOrderLogisticsRsp{
		List: l,
	}, nil
}
func (o *OrderService) getAmountInformation(ctx context.Context, order *adminDo.MainRealOrderDetailDo) ([]*adminv1.AmountInformation, error) {
	amountInformation := make([]*adminv1.AmountInformation, 0)
	orderCardGift := o.giftCardDs.GetOrderCardGift(ctx, order.PayOrderNumber)
	if order.TotalAmount.GreaterThan(decimal.Zero) {
		cardGiftFreightFeeTotalAmount := orderCardGift.GetFreightFeeTotalAmount()
		totalAmount := &adminv1.AmountInformation{
			Type:   "商品总额",
			Desc:   "暂无",
			Amount: "+" + order.TotalAmount.Sub(order.FreightFee).Sub(cardGiftFreightFeeTotalAmount).Round(2).String(),
		}
		amountInformation = append(amountInformation, totalAmount)
	}
	if order.CouponDiscountAmount.GreaterThan(decimal.Zero) {
		couponInfo, err := o.biz.GetOrderCouponInfo(ctx, order.CouponID)
		if err != nil {
			return nil, errors.New("获取优惠券失败")
		}
		if couponInfo != nil {
			couponDiscountAmount := &adminv1.AmountInformation{
				Type:   "优惠券",
				Desc:   couponInfo.Title,
				Amount: "-" + order.CouponDiscountAmount.Round(2).String(),
			}
			amountInformation = append(amountInformation, couponDiscountAmount)
		}
	}
	if order.IntegralDiscountAmount.GreaterThan(decimal.Zero) {
		shopId := isolationcustomer.GetShopIdZero(ctx)
		if shopId == 0 {
			return nil, apierr.ErrorNotAllow("获取商城信息失败")
		}
		integralConf, _ := o.integralConfigBiz.Query(ctx, shopId)
		totalPayIntegralAmount := &adminv1.AmountInformation{
			Type:   fmt.Sprintf("%s抵扣", integralConf.Name),
			Desc:   fmt.Sprintf("%d%s=1元", integralConf.ExchangeRate, integralConf.Name),
			Amount: "-" + order.IntegralDiscountAmount.Round(2).String(),
		}
		amountInformation = append(amountInformation, totalPayIntegralAmount)
	}
	cardGiftFreightFeeTotalAmount := orderCardGift.GetFreightFeeTotalAmount()
	if order.FreightFee.Add(cardGiftFreightFeeTotalAmount).GreaterThan(decimal.Zero) {
		freightFeeAmount := &adminv1.AmountInformation{
			Type:   "运费",
			Desc:   "",
			Amount: "+" + order.FreightFee.Add(cardGiftFreightFeeTotalAmount).Round(2).String(),
		}
		amountInformation = append(amountInformation, freightFeeAmount)
	}
	cardGiftTotalAmount := orderCardGift.GetTotalAmount()
	if cardGiftTotalAmount.GreaterThan(decimal.Zero) {
		cardGiftAmountInformation := &adminv1.AmountInformation{
			Type:   "礼品卡",
			Desc:   "",
			Amount: "-" + cardGiftTotalAmount.Round(2).String(),
		}
		amountInformation = append(amountInformation, cardGiftAmountInformation)
	}
	return amountInformation, nil
}
func (o *OrderService) RealOrderAfterSaleList(ctx context.Context, req *adminv1.AfterSaleListReq) (*adminv1.AfterSaleListRsp, error) {
	if utf8.RuneCountInString(req.GetOrderNumber()) < 1 {
		return nil, apierr.ErrorParam("操作失败:订单号不能为空")
	}
	// 售后详情
	orderAfterSaleDos, err := o.biz.AfterSaleList(ctx, req.GetOrderNumber())
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	l := make([]*adminv1.AfterSaleListRsp_AfterSaleListItem, 0, len(orderAfterSaleDos))
	for _, itemDo := range orderAfterSaleDos {
		item := &adminv1.AfterSaleListRsp_AfterSaleListItem{
			Id:                int32(itemDo.ID),
			OrderNumber:       itemDo.OrderNumber,
			AfterSaleType:     int32(itemDo.Type),
			AfterSaleTypeText: itemDo.Type.String(),
			ApplyTime:         helper.GetTimeDate(itemDo.CreateTime),
			ExchangeOrderNo:   itemDo.ExchangeOrderNo,
			AfterSaleNo:       itemDo.AfterSaleNo,
		}
		l = append(l, item)
	}
	return &adminv1.AfterSaleListRsp{
		AfterSaleList: l,
	}, nil
}
func (o *OrderService) RealOrderAfterSaleDetail(ctx context.Context, req *adminv1.AfterSaleDetailReq) (*adminv1.AfterSaleDetailRsp, error) {

	if req.GetAfterSaleId() == 0 {
		return nil, apierr.ErrorParam("操作失败:售后单id不能为空")
	}
	// 售后详情
	itemDo, err := o.biz.AfterSaleDetail(ctx, int(req.AfterSaleId))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	subRealOrderDetailDo, err := o.biz.SubRealOrderByOrderNumber(ctx, itemDo.OrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	mainRealOrderDetailDo, err := o.biz.MainRealOrderByPayOrderNumber(ctx, subRealOrderDetailDo.PayOrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	// 订单商品
	realOrderGoodsList, err := o.biz.RealOrderGoodsList(ctx, &bo.RealOrderGoodsListBo{
		OrderNumbers: []string{subRealOrderDetailDo.OrderNumber},
	})
	// 用户信息
	userDo, err := o.userBiz.GetOneUserById(ctx, subRealOrderDetailDo.UserID)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	afterSaleGoods, err := o.TransferReturnGoodsItems(ctx, itemDo, realOrderGoodsList)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	rsp := &adminv1.AfterSaleDetailRsp{
		Id:                       int32(itemDo.ID),
		OrderNumber:              itemDo.OrderNumber,
		AfterSaleType:            int32(itemDo.Type),
		AfterSaleTypeText:        itemDo.Type.String(),
		ApplyTime:                helper.GetTimeDate(itemDo.CreateTime),
		AfterSaleStatus:          int32(itemDo.Status),
		AfterSaleStatusText:      itemDo.Status.String(),
		AfterSaleReason:          itemDo.Reason,
		AfterSaleImages:          itemDo.GetImages(),
		Log:                      o.afterSaleConvertor.TransferLogItems(itemDo),
		BuyerDeliver:             o.afterSaleConvertor.TransferDeliverInfo(mainRealOrderDetailDo, userDo),
		ExchangeOrderNo:          itemDo.ExchangeOrderNo,
		Goods:                    afterSaleGoods,
		RefundAmount:             decimal.NewFromFloat(itemDo.RefundAmount).String(),
		AfterSaleNo:              itemDo.AfterSaleNo,
		ApplyRemark:              itemDo.Remark,
		ReceiveStatus:            int32(itemDo.ReceiveStatus),
		ReceiveStatusText:        itemDo.ReceiveStatus.String(),
		OrderAfterSaleStatus:     int32(subRealOrderDetailDo.AfterSaleStatus),
		RefundFreightFee:         decimal.NewFromFloat(itemDo.RefundFreightFee).String(),
		RefundCardGiftAmount:     decimal.NewFromFloat(itemDo.RefundCardGiftAmount).String(),
		RefundCardGiftFreightFee: decimal.NewFromFloat(itemDo.RefundCardGiftFreightFee).String(),
	}

	return rsp, nil
}
func (o *OrderService) TransferReturnGoodsItems(ctx context.Context, itemDo *bizdo.OrderAfterSaleDo, realOrderGoodsList []*adminDo.RealOrderGoodsListDo) (goodsRsp []*adminv1.AfterSaleGoods, err error) {
	var (
		realOrderGoodsListDoMap = make(map[string]*adminDo.RealOrderGoodsListDo)
		orderLogistics          *appDO.OrderLogisticsDo
	)
	for _, val := range realOrderGoodsList {
		realOrderGoodsListDoMap[val.OrderNumber+val.SkuNo] = val
	}
	saleGoodsDos := itemDo.GetSaleGoodsByType(valobj.AfterSaleGoodsTypeApply)
	if len(saleGoodsDos) == 0 {
		return nil, errors.New("售后商品不存在")
	}
	for _, saleGoodsDo := range saleGoodsDos {
		var (
			supplier *bizdo.SupplierDo
		)
		realOrderGoodsListDo, ok := realOrderGoodsListDoMap[itemDo.OrderNumber+saleGoodsDo.SkuNo]
		if !ok {
			return nil, errors.New("售后商品不存在")
		}
		if itemDo.SupplierID < isolationcustomer.OfficialSupplierIdMax {
			supplier = &bizdo.SupplierDo{ID: 1, Name: "官方供应商"}
		} else {

			supplier, err = o.biz.GetSupplier(isolationcustomer.WithDisableShopCtx(ctx), itemDo.SupplierID)
			if err != nil {
				return nil, errors.New("供应商获取失败")
			}
			if supplier.ShopID == 0 {
				supplier = &bizdo.SupplierDo{ID: 2, Name: "企业供应商"}
			}
		}
		if supplier == nil {
			return nil, errors.New("供应商获取失败")
		}
		elems := &adminv1.AfterSaleGoods{
			SkuNo:          saleGoodsDo.SkuNo,
			GoodsName:      saleGoodsDo.GoodsName,
			Quantity:       int32(realOrderGoodsListDo.Quantity),
			GoodsNum:       int32(saleGoodsDo.GoodsNum),
			SalePrice:      realOrderGoodsListDo.SalePrice.String(),
			DiscountAmount: realOrderGoodsListDo.CouponDiscountAmount.Add(realOrderGoodsListDo.IntegralDiscountAmount).String(),
			OrderType:      6,
			SupplierId:     int32(supplier.ID),
			SupplierName:   supplier.Name,
			UpdateTime:     helper.GetTimeDate(itemDo.UpdateTime),
			SaleIntegral:   int32(saleGoodsDo.SaleIntegral),
		}
		if itemDo.OrderAfterSaleDeliver != nil {

			orderLogistics, err = o.biz.FindLastOrderLogistics(ctx, &bo3.OrderLogisticsFindLastBo{
				OrderNumber: itemDo.OrderNumber,
				LogisticsNo: itemDo.OrderAfterSaleDeliver.ExpressNo,
			})
			if err != nil {
				return nil, errors.New("售后单物流信息获取失败")
			}
			if orderLogistics != nil {
				elems.OpDesc = orderLogistics.OpDesc
			}
		}
		goodsRsp = append(goodsRsp, elems)
	}
	return
}

func (o *OrderService) RealOrderExport(ctx context.Context, req *adminv1.RealOrderExportReq) (*adminv1.RealOrderExportRsp, error) {
	var (
		createTimeStart int64
		createTimeEnd   int64
		err             error
	)
	adminDo, err := o.base.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	if req.CreateTimeStart != "" && req.CreateTimeEnd != "" {
		createTimeStart, err = helper.GetTimestamp(req.CreateTimeStart)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
		createTimeEnd, err = helper.GetTimestamp(req.CreateTimeEnd)
		if err != nil {
			return nil, apierr.ErrorParam("操作失败:%s", err)
		}
	}

	var in = &bo.ExportRealOrderListBo{
		Status:           req.Status,
		OrderNumber:      req.OrderNumber,
		CreateTimeStart:  createTimeStart,
		CreateTimeEnd:    createTimeEnd,
		GoodsName:        req.GoodsName,
		GoodsId:          req.GoodsId,
		SupplierId:       req.SupplierId,
		SettlementType:   valobj.PayOrderSettlementTypeObj(req.SettlementType),
		SkuNo:            req.SkuNo,
		SiteId:           req.SiteId,
		CardCouponNumber: req.CardCouponNumber,
		CardGiftNumber:   req.CardGiftNumber,
	}

	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}

	sysTask, err := o.sysTaskBiz.GenerateTask(ctx, &bo2.SysTaskExecuteBo{
		SysTaskName:   "实物订单导出",
		SysTaskType:   valobj.SysTaskTypeExport,
		SysTaskSource: valobj.SysTaskSourceRealOrderExport,
		Parameter:     in.String(),
		Remark:        "实物订单导出",
		UserID:        adminDo.Id,
		UserName:      adminDo.Name,
		RelatedNo:     "",
	})
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &adminv1.RealOrderExportRsp{
		SysTaskNumber: sysTask.SysTaskNumber,
	}, nil
}
