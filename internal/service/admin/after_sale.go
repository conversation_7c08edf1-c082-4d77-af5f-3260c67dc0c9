package admin

import (
	"cardMall/api/adminv1"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/data/ent/order"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/service"
	"cardMall/internal/service/admin/convertor"
	commonconvertor "cardMall/internal/service/common/convertor"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

type AfterSaleService struct {
	service.AdminBase
	adminv1.UnimplementedAfterSaleSrvServer

	log                *log.Helper
	afterSaleBiz       *supplierbiz.AfterSaleBiz
	afterSaleConvertor *convertor.AfterSaleConvertor
	pageConvertor      *commonconvertor.PageConvertor
}

func NewAfterSaleService(log *log.Helper, afterSaleBiz *supplierbiz.AfterSaleBiz, afterSaleConvertor *convertor.AfterSaleConvertor, pageConvertor *commonconvertor.PageConvertor) *AfterSaleService {
	return &AfterSaleService{log: log, afterSaleBiz: afterSaleBiz, afterSaleConvertor: afterSaleConvertor, pageConvertor: pageConvertor}
}

func (a *AfterSaleService) List(ctx context.Context, req *adminv1.AfterSaleListV2Req) (*adminv1.AfterSaleListV2Resp, error) {
	var reqBo *commonbo.OrderAfterSaleSearchBo
	_ = mapstructure.Decode(req, &reqBo)
	if len(req.Date) == 2 {
		start, err := helper.DateStringToTime(req.Date[0])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateStart = int(start.Unix())
		end, err := helper.DateStringToTime(req.Date[1])
		if err != nil {
			return nil, err
		}
		reqBo.OrderDateEnd = int(end.Unix())
	}

	// reqBo.SupplierId = a.GetSupplierIdX(ctx)
	reqBo.SortData = []*commonbo.CommonSortBo{
		{Asc: false, Field: order.FieldID},
	}
	reqBo.Edges = &commonbo.OrderAfterSaleEdgesBo{
		WithOrderAfterSaleGoods: true,
		WithSupplier:            true,
		WithOrderAfterSaleLog:   true,
	}

	dos, pageInfo := a.afterSaleBiz.SearchList(ctx, reqBo)
	list := make([]*adminv1.AfterSaleData, 0, len(dos))
	var tmpData *adminv1.AfterSaleData
	for _, itemDo := range dos {
		tmpData = &adminv1.AfterSaleData{
			Id:                  int32(itemDo.ID),
			OrderNumber:         itemDo.OrderNumber,
			AfterSaleType:       int32(itemDo.Type),
			AfterSaleTypeText:   itemDo.Type.String(),
			ApplyTime:           helper.GetTimeDate(itemDo.CreateTime, helper.DateFormat),
			AfterSaleStatus:     int32(itemDo.Status),
			AfterSaleStatusText: itemDo.GetStatusText(),
			AfterSaleReason:     itemDo.Reason,
			AfterSaleImages:     itemDo.GetImages(),
			RefundAmount:        itemDo.RefundAmount,
			AfterSaleNo:         itemDo.AfterSaleNo,
			Refund: &adminv1.AfterSaleData_RefundDetail{
				GoodsAmount: itemDo.RefundGoodsAmount,
				FreightFee:  itemDo.RefundFreightFee,
			},
			AfterSalePlatformStatus:     int32(itemDo.PlatformStatus),
			AfterSalePlatformStatusText: itemDo.PlatformStatus.GetPlatformText(),
			SupplierId:                  int32(itemDo.SupplierID),
			SupplierName:                itemDo.GetSupplierInfo().Name,
			RefuseReason:                itemDo.GetSupplierRefuseReason(),
			RefundCardGiftAmount:        itemDo.RefundCardGiftAmount,
			RefundCardGiftFreightFee:    itemDo.RefundCardGiftFreightFee,
			//Goods:               nil,
			//ExchangeGoods:       nil,
			//RefundGoodsAmount:   0,
			//ExchangeGoodsAmount: 0,
		}
		tmpData.Goods, tmpData.RefundGoodsAmount = a.afterSaleConvertor.TransferReturnGoodsItems(itemDo)
		tmpData.ExchangeGoods, tmpData.ExchangeGoodsAmount = a.afterSaleConvertor.TransferExchangeGoodsItems(itemDo)
		list = append(list, tmpData)
	}

	return &adminv1.AfterSaleListV2Resp{
		List: list,
		Page: a.pageConvertor.ToRespPage(pageInfo),
	}, nil
}

func (a *AfterSaleService) Detail(ctx context.Context, req *adminv1.AfterSaleDetailV2Req) (*adminv1.AfterSaleDetailV2Resp, error) {
	itemDo, err := a.afterSaleBiz.GetWithEdges(ctx, int(req.Id), &commonbo.OrderAfterSaleEdgesBo{
		WithOrderAfterSaleDeliver: true,
		WithOrderAfterSaleGoods:   true,
		WithOrderAfterSaleLog:     true,
		WithSupplier:              true,
	})
	if err != nil {
		return nil, err
	}

	goodsRsp := make([]*adminv1.AfterSaleGoods, 0, len(itemDo.OrderAfterSaleGoods))
	for _, goodsItem := range itemDo.OrderAfterSaleGoods {
		goodsRsp = append(goodsRsp, &adminv1.AfterSaleGoods{
			SkuNo:     goodsItem.SkuNo,
			GoodsName: goodsItem.GoodsName,
			GoodsNum:  int32(goodsItem.GoodsNum),
		})
	}

	rsp := &adminv1.AfterSaleDetailV2Resp{
		Id:                  int32(itemDo.ID),
		OrderNumber:         itemDo.OrderNumber,
		AfterSaleType:       int32(itemDo.Type),
		AfterSaleTypeText:   itemDo.Type.String(),
		ApplyTime:           helper.GetTimeDate(itemDo.CreateTime, helper.DateFormat),
		AfterSaleStatus:     int32(itemDo.Status),
		AfterSaleStatusText: itemDo.GetStatusText(),
		AfterSaleReason:     itemDo.Reason,
		AfterSaleImages:     itemDo.GetImages(),
		Log:                 a.afterSaleConvertor.TransferLogItemsV2(itemDo),
		BuyerDeliver:        a.afterSaleConvertor.TransferDeliverInfoV2(itemDo.GetBuyerDeliver()),
		ExchangeOrderNo:     itemDo.ExchangeOrderNo,
		RefundAmount:        itemDo.RefundAmount,
		AfterSaleNo:         itemDo.AfterSaleNo,
		AfterSaleRemark:     itemDo.Remark,
		ReceiveStatus:       int32(itemDo.ReceiveStatus),
		ReceiveStatusText:   itemDo.ReceiveStatus.String(),
		Refund: &adminv1.AfterSaleDetailV2Resp_RefundDetail{
			GoodsAmount: itemDo.RefundGoodsAmount,
			FreightFee:  itemDo.RefundFreightFee,
		},
		AfterSalePlatformStatus:     int32(itemDo.PlatformStatus),
		AfterSalePlatformStatusText: itemDo.PlatformStatus.GetPlatformText(),
		SupplierId:                  int32(itemDo.SupplierID),
		SupplierName:                itemDo.GetSupplierInfo().Name,
		RefuseReason:                itemDo.GetSupplierRefuseReason(),
		RefundCardGiftAmount:        itemDo.RefundCardGiftAmount,
		//Goods:               nil,
		//ExchangeGoods:       nil,
		//RefundGoodsAmount:   0,
		//ExchangeGoodsAmount: 0,
	}
	rsp.Goods, rsp.RefundGoodsAmount = a.afterSaleConvertor.TransferReturnGoodsItems(itemDo)
	rsp.ExchangeGoods, rsp.ExchangeGoodsAmount = a.afterSaleConvertor.TransferExchangeGoodsItems(itemDo)

	return rsp, nil
}

func (a *AfterSaleService) getAfterSaleOperaBo(ctx context.Context, id int) *bo.AfterSaleOperaBo {
	return &bo.AfterSaleOperaBo{
		Id:               id,
		AdminLoginInfoBo: a.GetLoginInfoX(ctx).ToLoginInfo(),
	}
}

func (a *AfterSaleService) Approve(ctx context.Context, req *adminv1.AfterSaleApproveReq) (*adminv1.AfterSaleApproveResp, error) {
	var reqBo *bo.AfterSaleAuditCommonBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	if err := reqBo.Validate(false); err != nil {
		return nil, err
	}
	err := a.afterSaleBiz.ApproveByPlatformToUser(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &adminv1.AfterSaleApproveResp{}, nil
}

func (a *AfterSaleService) Refuse(ctx context.Context, req *adminv1.AfterSaleRefuseReq) (*adminv1.AfterSaleRefuseResp, error) {
	var reqBo *bo.AfterSaleAuditCommonBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))

	if err := reqBo.Validate(true); err != nil {
		return nil, err
	}
	err := a.afterSaleBiz.RefuseByPlatformToUser(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &adminv1.AfterSaleRefuseResp{}, nil
}

func (a *AfterSaleService) ApproveToSupplier(ctx context.Context, req *adminv1.AfterSaleApproveReq) (*adminv1.AfterSaleApproveResp, error) {
	var reqBo *bo.AfterSaleAuditCommonBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))
	if err := reqBo.Validate(true); err != nil {
		return nil, err
	}
	err := a.afterSaleBiz.ApproveByPlatformToSupplier(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &adminv1.AfterSaleApproveResp{}, nil
}

func (a *AfterSaleService) RefuseToSupplier(ctx context.Context, req *adminv1.AfterSaleRefuseReq) (*adminv1.AfterSaleRefuseResp, error) {
	var reqBo *bo.AfterSaleAuditCommonBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.AfterSaleOperaBo = a.getAfterSaleOperaBo(ctx, int(req.Id))

	if err := reqBo.Validate(true); err != nil {
		return nil, err
	}
	err := a.afterSaleBiz.RefuseByPlatformToSupplier(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &adminv1.AfterSaleRefuseResp{}, nil
}
