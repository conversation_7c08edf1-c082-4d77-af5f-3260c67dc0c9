package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
)

type PayConfigService struct {
	adminv1.UnimplementedPayConfigServer
	biz *adminbiz.PayConfigBiz
}

func NewPayConfigService(biz *adminbiz.PayConfigBiz) *PayConfigService {
	return &PayConfigService{
		biz: biz,
	}
}

func (p *PayConfigService) Add(ctx context.Context, req *adminv1.PayConfigAddReq) (*adminv1.PayConfigAddRsp, error) {
	in := &bo.PayConfigAddBo{
		Alipay:       valobj.PayMerchantPayStatusObj(req.GetAlipay()),
		WxPay:        valobj.PayMerchantPayStatusObj(req.GetWxPay()),
		AlipayApplet: valobj.PayMerchantPayStatusDisable,
		WxApplet:     valobj.PayMerchantPayStatusDisable,
		Channel:      make([]*bo.PayConfigChannelBo, 0),
	}
	for _, channel := range req.GetChannels() {
		item := &bo.PayConfigChannelBo{
			ChannelType: valobj.PayConfigChannelTypeObj(channel.GetChannelType()),
			AppId:       channel.GetAppId(),
			PublicKey:   channel.GetPublicKey(),
			PrivateKey:  channel.GetPrivateKey(),
			Status:      valobj.PayConfigStatusObj(channel.GetStatus()),
			MchId:       channel.GetMchId(),
			SerialNo:    channel.GetSerialNo(),
			AppSecret:   channel.GetAppSecret(),
		}
		if item.ChannelType.IsAlipayApplet() {
			item.AlipaySignType = valobj.PayConfigAlipaySignTypeCertificate
			item.AlipayCertificate = &bo.PayConfigChannelAlipayCertificateBo{
				AlipayCertPublicKey: channel.GetAlipayCertificate().GetAlipayCertPublicKey(),
				AlipayRootCert:      channel.GetAlipayCertificate().GetAlipayRootCert(),
				AppCertPublicKey:    channel.GetAlipayCertificate().GetAppCertPublicKey(),
			}
			item.PublicKey = item.AlipayCertificate.AlipayCertPublicKey
		}
		if item.ChannelType.IsApplet() {
			if item.ChannelType.IsWx() {
				in.WxApplet = valobj.PayMerchantPayStatusEnable
			} else if item.ChannelType.IsAlipay() {
				in.AlipayApplet = valobj.PayMerchantPayStatusEnable
			}
		}
		if err := item.Validate(); err != nil {
			return nil, err
		}
		in.Channel = append(in.Channel, item)
	}

	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	if err := in.Validate(customerId == isolationcustomer.SaasPlatformCustomer); err != nil {
		return nil, err
	}

	id, err := p.biz.Add(ctx, in)
	if err != nil {
		return nil, err
	}
	return &adminv1.PayConfigAddRsp{
		EffectRow: int32(id),
	}, nil
}

func (p *PayConfigService) All(ctx context.Context, req *adminv1.PayConfigAllReq) (*adminv1.PayConfigAllRsp, error) {
	payMerchant, payConfig, err := p.biz.All(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("查询失败:%s", err)
	}
	if len(payMerchant) == 0 {
		return &adminv1.PayConfigAllRsp{}, nil
	}
	rsp := &adminv1.PayConfigAllRsp{
		List: nil,
	}

	for _, val := range payMerchant {
		if val.PayType.IsH5() {
			rsp.Alipay = int32(val.Alipay)
			rsp.WxPay = int32(val.WxPay)
		} else if val.PayType.IsApplet() {
			rsp.AlipayApplet = int32(val.Alipay)
			rsp.WxApplet = int32(val.WxPay)
		}
	}
	list := make([]*adminv1.PayConfigAllRsp_PayConfigItem, 0, len(payConfig))
	for _, val := range payConfig {
		val.DataHidden()
		item := &adminv1.PayConfigAllRsp_PayConfigItem{
			Id:          int32(val.Id),
			ChannelType: int32(val.ChannelType),
			AppId:       val.AppId,
			PublicKey:   val.PublicKey,
			PrivateKey:  val.PrivateKey,
			Status:      int32(val.Status),
			MchId:       val.GetMchId(),
			SerialNo:    val.GetSerialNo(),
			AppSecret:   val.AppSecret,
			AlipayCertificate: &adminv1.AlipayCertificate{
				AlipayCertPublicKey: val.GetAlipayCertPublicKey(),
				AlipayRootCert:      val.GetAlipayRootCert(),
				AppCertPublicKey:    val.GetAppCertPublicKey(),
			},
		}
		list = append(list, item)
	}
	rsp.List = list
	return rsp, nil
}

func (p *PayConfigService) UpdateStatus(ctx context.Context, req *adminv1.PayConfigUpdateStatusReq) (*adminv1.PayConfigUpdateStatusRsp, error) {
	in := &bo.PayConfigUpdateBo{
		Id:     int(req.GetId()),
		Status: valobj.PayConfigStatusObj(req.GetStatus()),
	}
	row, err := p.biz.Update(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("更新失败:%s", err)
	}
	return &adminv1.PayConfigUpdateStatusRsp{
		EffectRow: int32(row),
	}, nil
}
