package admin

import (
	"context"

	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/pkg/helper"
)

type BannerService struct {
	adminv1.UnimplementedBannerServer
	biz *adminbiz.BannerBiz
}

func NewBannerService(biz *adminbiz.BannerBiz) *BannerService {
	return &BannerService{biz: biz}
}

func (b *BannerService) List(ctx context.Context, req *adminv1.BannerListReq) (*adminv1.BannerListRsp, error) {
	var in = &bo.BannerListBo{
		bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}
	count, data, err := b.biz.List(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}

	var rsp = &adminv1.BannerListRsp{
		List:  nil,
		Page:  int32(in.GetPage()),
		Count: int32(count),
	}
	if count == 0 {
		return rsp, nil
	}

	rsp.List = make([]*adminv1.BannerListRsp_BannerListItem, 0, len(data))
	for _, val := range data {
		item := &adminv1.BannerListRsp_BannerListItem{
			Id:            int32(val.Id),
			Name:          val.Name,
			Image:         val.Image,
			Sort:          int32(val.Sort),
			Status:        int32(val.Status),
			Url:           val.Url,
			CreateTime:    helper.GetTimeDate(val.CreateTime),
			UpdateTime:    helper.GetTimeDate(val.UpdateTime),
			RelationType:  int32(val.RelationType),
			RelationValue: val.RelationValue,
		}
		rsp.List = append(rsp.List, item)
	}

	return rsp, nil
}

func (b *BannerService) Add(ctx context.Context, req *adminv1.BannerAddReq) (*adminv1.BannerAddRsp, error) {
	var in = &bo.BannerAddBo{
		Name:             req.Name,
		Image:            req.Image,
		Sort:             int(req.Sort),
		Status:           nil,
		RelationType:     valobj.BannerRelationTypeObj(req.GetRelationType()),
		RelationValue:    req.GetRelationValue(),
		RelationValueIds: helper.Int32ToInt(req.GetRelationValueIds()),
	}
	if req.Status != nil {
		status := int(*req.Status)
		in.Status = &status
	} else {
		status := valobj.BannerStatusDisable
		in.Status = &status
	}
	if err := in.Validate(); err != nil {
		return nil, err
	}
	id, err := b.biz.Add(ctx, in)
	if err != nil {
		return nil, err
	}

	return &adminv1.BannerAddRsp{Id: int32(id)}, nil
}

func (b *BannerService) Update(ctx context.Context, req *adminv1.BannerUpdateReq) (*adminv1.BannerUpdateRsp, error) {
	var in = &bo.BannerUpdateBo{
		Id:               int(req.Id),
		Name:             req.Name,
		Image:            req.Image,
		Sort:             int(req.Sort),
		Status:           nil,
		RelationType:     valobj.BannerRelationTypeObj(req.GetRelationType()),
		RelationValue:    req.GetRelationValue(),
		RelationValueIds: helper.Int32ToInt(req.GetRelationValueIds()),
	}
	if req.Status != nil {
		status := int(*req.Status)
		in.Status = &status
	} else {
		status := valobj.BannerStatusDisable
		in.Status = &status
	}
	if err := in.Validate(); err != nil {
		return nil, err
	}
	row, err := b.biz.Update(ctx, in)
	if err != nil {
		return nil, err
	}
	return &adminv1.BannerUpdateRsp{EffectRow: int32(row)}, nil
}

func (b *BannerService) UpdateStatus(ctx context.Context, req *adminv1.BannerUpdateStatusReq) (*adminv1.BannerUpdateStatusRsp, error) {
	var in = &bo.BannerUpdateStatusBo{
		Id:     int(req.Id),
		Status: nil,
	}
	status := int(*req.Status)
	in.Status = &status

	row, err := b.biz.UpdateStatus(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.BannerUpdateStatusRsp{EffectRow: int32(row)}, nil
}

func (b *BannerService) Del(ctx context.Context, req *adminv1.BannerDelReq) (*adminv1.BannerDelRsp, error) {
	row, err := b.biz.Del(ctx, int(req.Id))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.BannerDelRsp{EffectRow: int32(row)}, nil
}

func (b *BannerService) Detail(ctx context.Context, req *adminv1.BannerDetailReq) (*adminv1.BannerDetailRsp, error) {
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorParam("参数错误")
	}
	data, err := b.biz.Detail(ctx, id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.BannerDetailRsp{
		Id:            int32(data.Id),
		Name:          data.Name,
		Image:         data.Image,
		Sort:          int32(data.Sort),
		Status:        int32(data.Status),
		Url:           data.Url,
		CreateTime:    helper.GetTimeDate(data.CreateTime),
		UpdateTime:    helper.GetTimeDate(data.UpdateTime),
		RelationType:  int32(data.RelationType),
		RelationValue: data.RelationValue,
	}, nil
}
