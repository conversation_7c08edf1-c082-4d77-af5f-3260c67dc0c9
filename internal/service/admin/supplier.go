package admin

import (
	"context"

	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bizBo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/pkg/jwt"
	"cardMall/internal/server/middleware/authtools"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/jinzhu/copier"
	"github.com/pkg/errors"
)

type SupplierService struct {
	base
	adminv1.UnimplementedSupplierServer
	biz      *adminbiz.SupplierBiz
	adminBiz *adminbiz.AdminBiz
	conf     *conf.Bootstrap
	jwt      *jwt.JWT
}

func NewSupplierService(biz *adminbiz.SupplierBiz, adminBiz *adminbiz.AdminBiz, conf *conf.Bootstrap, jwt *jwt.JWT) *SupplierService {
	return &SupplierService{biz: biz, adminBiz: adminBiz, conf: conf, jwt: jwt}
}

func (s *SupplierService) Add(ctx context.Context, req *adminv1.SupplierAddReq) (*adminv1.SupplierAddRsp, error) {
	if req.GetName() == "" {
		return nil, apierr.ErrorParam("缺少参数")
	}
	in := &bizBo.SupplierAddBo{
		Name:   req.GetName(),
		Status: valobj.SupplierStatusEnable,
		Pid:    int(req.GetPid()),
		ShopId: authtools.GetLoginInfo(ctx).GetShopId(),
	}
	if err := in.Validate(); err != nil {
		return nil, apierr.ErrorParam(err.Error())
	}
	id, err := s.biz.Add(ctx, in)
	if err != nil {
		return nil, err
	}
	return &adminv1.SupplierAddRsp{
		Id: int32(id),
	}, nil
}

func (s *SupplierService) All(ctx context.Context, req *adminv1.SupplierAllReq) (*adminv1.SupplierAllRsp, error) {
	in := &bizBo.SupplierQueryBo{
		Name:   req.GetName(),
		Status: valobj.SupplierStatusObj(req.GetStatus()),
		Pid:    int(req.GetPid()),
	}

	res, err := s.biz.All(ctx, in)
	if err != nil {
		return nil, err
	}
	rsp := &adminv1.SupplierAllRsp{
		List: nil,
	}
	list := make([]*adminv1.SupplierAllRsp_SupplierItem, 0, len(res))
	err = copier.Copy(&list, &res)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	rsp.List = list
	return rsp, nil
}

func (s *SupplierService) List(ctx context.Context, req *adminv1.SupplierAllReq) (*adminv1.SupplierAllRsp, error) {
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	saasCustomerDo, err := s.biz.GetCustomer(ctx, customerId)
	if err != nil {
		return nil, err
	}
	supplierAllRsp, err := s.All(isolationcustomer.WithCustomerAndDisableShopCtx(ctx, isolationcustomer.SaasPlatformCustomer), req)
	if err != nil {
		return nil, err
	}
	supplierAllRsp.List = slice.Map(supplierAllRsp.List, func(index int, item *adminv1.SupplierAllRsp_SupplierItem) *adminv1.SupplierAllRsp_SupplierItem {
		item.SourceType = 1
		return item
	})
	supplierPlatformAllRsp, err := s.All(ctx, req)
	if err != nil {
		return nil, err
	}
	supplierPlatformAllRsp.List = slice.Map(supplierPlatformAllRsp.List, func(index int, item *adminv1.SupplierAllRsp_SupplierItem) *adminv1.SupplierAllRsp_SupplierItem {
		item.SourceType = 2
		return item
	})
	supplierAllRsp.List = append(supplierAllRsp.List, supplierPlatformAllRsp.List...)
	if saasCustomerDo.CompanySupplierEnable {
		supplierAllRsp.CompanySupplierIds = saasCustomerDo.CompanySupplierIds
	}
	if saasCustomerDo.ShoppingSupplierEnable {
		supplierAllRsp.ShoppingSupplierIds = saasCustomerDo.ShoppingSupplierIds
	}
	return supplierAllRsp, nil
}

func (s *SupplierService) Update(ctx context.Context, req *adminv1.SupplierUpdateReq) (*adminv1.SupplierUpdateRsp, error) {
	in := &bizBo.SupplierUpdateBo{
		Id:   int(req.GetId()),
		Name: req.GetName(),
	}
	if err := in.Validate(); err != nil {
		return nil, apierr.ErrorParam(err.Error())
	}
	row, err := s.biz.Update(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("更新失败")
	}

	return &adminv1.SupplierUpdateRsp{EffectRow: int32(row)}, nil
}

func (s *SupplierService) Del(ctx context.Context, req *adminv1.SupplierDelReq) (*adminv1.SupplierDelRsp, error) {
	row, err := s.biz.Del(ctx, int(req.GetId()))
	return &adminv1.SupplierDelRsp{EffectRow: int32(row)}, err
}

func (s *SupplierService) Login(ctx context.Context, req *adminv1.SupplierLoginReq) (*adminv1.SupplierLoginRsp, error) {
	supplierId := int(req.GetSupplierId())
	if supplierId <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	loginInfo, err := s.adminInfo(ctx)
	if err != nil {
		return nil, err
	}

	adminInfo, err := s.adminBiz.AccountInfo(ctx, loginInfo.Id)
	if err != nil {
		return nil, err
	}
	if !adminInfo.Type.IsShopAdmin() {
		return nil, apierr.ErrorNotAllow("账号类型错误")
	}

	supplierInfo, err := s.biz.FindById(ctx, supplierId)
	if err != nil {
		return nil, err
	}
	if supplierInfo == nil {
		return nil, apierr.ErrorParam("供应商不存在")
	}
	if supplierInfo.CustomerID != adminInfo.CustomerId {
		return nil, apierr.ErrorParam("供应商不属于当前商城")
	}

	adminInfo.Type = valobj.AdminTypeShopToSupplier
	adminInfo.SupplierID = supplierId
	token, err := s.jwt.GetAdminToken(adminInfo)
	if err != nil {
		return nil, errors.WithMessage(err, "登录时生成token异常")
	}
	if err = s.adminBiz.LoginSuccessLog(ctx, adminInfo, valobj.AdminLoginTypeAccount); err != nil {
		return nil, errors.WithMessage(err, "登录时记录日志失败")
	}

	return &adminv1.SupplierLoginRsp{
		Token:     token,
		UrlSuffix: adminInfo.GetUrlSuffix(),
		BaseUrl:   s.conf.GetSite().GetCustomerAdmin(),
	}, nil
}
