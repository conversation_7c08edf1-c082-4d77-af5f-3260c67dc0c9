package admin

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/module/adminbiz"
	"cardMall/internal/pkg/helper"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

type GoodsBrandService struct {
	base
	adminv1.UnimplementedGoodsBrandServer
	biz *adminbiz.GoodsBrandBiz
}

func NewGoodsBrandService(biz *adminbiz.GoodsBrandBiz) *GoodsBrandService {
	return &GoodsBrandService{biz: biz}
}

func (g *GoodsBrandService) SyncBrand(ctx context.Context, req *adminv1.SyncBrandReq) (*adminv1.SyncBrandRsp, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂停使用")
	if req.SourceType == 0 {
		return nil, apierr.ErrorParam("操作失败:品牌类型不能为空")
	}

	goodsBrandDos, err := g.biz.SyncBrand(ctx, int(req.SourceType))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	var rsp = &adminv1.SyncBrandRsp{}
	for _, item := range goodsBrandDos {
		rspItem := &adminv1.SyncBrandRsp_GoodsBrandListItem{
			Id:         int32(item.Id),
			Name:       item.Name,
			Sort:       int32(item.Sort),
			Status:     int32(item.Status),
			Label:      item.Label,
			Logo:       item.Logo,
			Recommend:  int32(item.Recommend),
			CreateTime: helper.GetTimeDate(item.CreateTime),
			UpdateTime: helper.GetTimeDate(item.UpdateTime),
			Remark:     item.Remark,
		}
		rsp.List = append(rsp.List, rspItem)
	}
	return rsp, nil
}
func (g *GoodsBrandService) Sync(ctx context.Context, req *adminv1.GoodsBrandSyncReq) (*adminv1.GoodsBrandSyncRsp, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂停使用")
	if len(req.Ids) == 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	if req.SourceType == 0 {
		return nil, apierr.ErrorParam("操作失败:品牌类型不能为空")
	}

	err := g.biz.Sync(ctx, helper.Int32ToInt(req.Ids), int(req.SourceType))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.GoodsBrandSyncRsp{EffectRows: int32(len(req.Ids))}, nil
}

func (g *GoodsBrandService) Add(ctx context.Context, req *adminv1.GoodsBrandAddReq) (*adminv1.GoodsBrandAddRsp, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂停使用")
	var in = &bo2.GoodsBrandAddBo{}
	_ = mapstructure.Decode(req, &in)
	if err := in.Validate(); err != nil {
		return nil, err
	}

	res, err := g.biz.Add(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}

	var rsp = &adminv1.GoodsBrandAddRsp{Id: int32(res.Id)}
	return rsp, nil
}

func (g *GoodsBrandService) All(ctx context.Context, req *adminv1.GoodsBrandAllReq) (*adminv1.GoodsBrandAllRsp, error) {
	var in = &bo2.GoodsBrandQueryBo{GoodsCategoryId: int(req.CategoryId)}
	res, err := g.biz.Query(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}

	var rsp = &adminv1.GoodsBrandAllRsp{}
	for _, item := range res {
		rspItem := &adminv1.GoodsBrandAllRsp_GoodsBrandListItem{
			Id:         int32(item.Id),
			Name:       item.Name,
			Sort:       int32(item.Sort),
			Status:     int32(item.Status),
			Label:      item.Label,
			Logo:       item.Logo,
			Recommend:  int32(item.Recommend),
			CreateTime: helper.GetTimeDate(item.CreateTime),
			UpdateTime: helper.GetTimeDate(item.UpdateTime),
			Remark:     item.Remark,
		}
		rsp.List = append(rsp.List, rspItem)
	}

	return rsp, nil
}

func (g *GoodsBrandService) Update(ctx context.Context, req *adminv1.GoodsBrandUpdateReq) (*adminv1.GoodsBrandUpdateRsp, error) {
	var in = &bo2.GoodsBrandUpdateBo{}
	_ = mapstructure.Decode(req, &in)
	if err := in.Validate(); err != nil {
		return nil, err
	}

	row, err := g.biz.Update(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.GoodsBrandUpdateRsp{EffectRows: int32(row)}, nil
}

func (g *GoodsBrandService) List(ctx context.Context, req *adminv1.GoodsBrandListReq) (*adminv1.GoodsBrandListRsp, error) {
	var in = &bo2.GoodsBrandQueryBo{
		GoodsCategoryId: 0,
		Name:            req.GetName(),
		ReqPageBo:       bo2.ReqPageBo{PageSize: int(req.GetPageSize()), Page: int(req.GetPage())},
	}
	if req.Status != nil {
		status := int(*req.Status)
		in.Status = &status
	}
	if req.Recommend != nil {
		recommend := int(*req.Recommend)
		in.Recommend = &recommend
	}
	list := make([]*adminv1.GoodsBrandListRsp_GoodsBrandListItem, 0, in.GetPageSize())
	var rsp = &adminv1.GoodsBrandListRsp{
		List:  list,
		Page:  int32(in.ReqPageBo.GetPage()),
		Count: 0,
	}

	count, data, goodsSkuCount, err := g.biz.List(ctx, in)
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}
	rsp.Count = int32(count)

	if count > 0 {
		goodsSkuCountMap := slice.KeyBy(goodsSkuCount, func(item *do.GoodsSkuGroupByBrandDo) int {
			return item.BrandId
		})
		for _, item := range data {
			listItem := &adminv1.GoodsBrandListRsp_GoodsBrandListItem{
				Id:         int32(item.Id),
				Name:       item.Name,
				Sort:       int32(item.Sort),
				Status:     int32(item.Status),
				Label:      item.Label,
				Logo:       item.Logo,
				Recommend:  int32(item.Recommend),
				CreateTime: helper.GetTimeDate(item.CreateTime),
				UpdateTime: helper.GetTimeDate(item.UpdateTime),
				Remark:     item.Remark,
			}
			if goodsSkuCountMap[item.Id] != nil {
				listItem.GoodsNum = int32(goodsSkuCountMap[item.Id].Num)
			}
			rsp.List = append(rsp.List, listItem)
		}
	}
	return rsp, nil
}

func (g *GoodsBrandService) Del(ctx context.Context, req *adminv1.GoodsBrandDelReq) (*adminv1.GoodsBrandDelRsp, error) {
	return nil, apierr.ErrorNotAllow("操作失败:功能暂停使用")
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorSystemPanic("参数错误")
	}
	row, err := g.biz.Del(ctx, id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &adminv1.GoodsBrandDelRsp{EffectRows: int32(row)}, nil
}
