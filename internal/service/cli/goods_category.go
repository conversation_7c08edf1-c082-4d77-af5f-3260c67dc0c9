package cli

import (
	"cardMall/internal/conf"
	"cardMall/internal/module/clibiz"
	"cardMall/internal/pkg/isolationcustomer"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/spf13/cobra"
)

type GoodsCategoryMigrateService struct {
	biz  *clibiz.GoodsCategoryBiz
	conf *conf.Bootstrap
	log  *log.Helper
}

func NewGoodsCategoryMigrateService(biz *clibiz.GoodsCategoryBiz, conf *conf.Bootstrap, log *log.Helper) *GoodsCategoryMigrateService {
	return &GoodsCategoryMigrateService{biz: biz, conf: conf, log: log}
}

func (g *GoodsCategoryMigrateService) Run(cmd *cobra.Command, args []string) {
	var err error
	if len(args) == 0 {
		g.log.Errorf("程序执行失败：缺少参数")
		return
	}
	if args[0] == "common" {
		ctx := isolationcustomer.WithCustomerIdCtx(cmd.Context(), isolationcustomer.SaasPlatformCustomer)
		err = g.biz.CategoryAndBrandMigrate(ctx)
		if err != nil {
			g.log.Errorf("程序执行失败：%v", err)
		}
		g.log.Info("程序执行成功！")
	} else if args[0] == "extra" {
		for _, db := range g.conf.Sharding {
			if db.GetCustomerId().GetMin() == isolationcustomer.SaasPlatformCustomer {
				continue
			}
			ctx := isolationcustomer.WithCustomerIdCtx(cmd.Context(), int(db.GetCustomerId().GetMin()))
			err = g.biz.CategoryAndBrandExtraMigrate(ctx)
			if err != nil {
				g.log.Errorf("程序执行失败：%v", err)
			}
			g.log.Info("程序执行成功！")
		}
	} else if args[0] == "goods" {
		for _, db := range g.conf.Sharding {
			if db.GetCustomerId().GetMin() == isolationcustomer.SaasPlatformCustomer {
				continue
			}
			ctx := isolationcustomer.WithCustomerIdCtx(cmd.Context(), int(db.GetCustomerId().GetMin()))
			g.biz.GoodsCategoryBrandMigrate(ctx)
			g.log.Info("程序执行成功！")
		}
	} else {
		g.log.Errorf("程序执行失败：参数错误")
	}
	return
}

func (g *GoodsCategoryMigrateService) GetCommend() string {
	return "goodsCategoryBrand"
}

func (g *GoodsCategoryMigrateService) GetShort() string {
	return "虚拟商品类目数据迁移"
}

func (g *GoodsCategoryMigrateService) GetLong() string {
	return "虚拟商品类目数据从一级改成三级类目"
}
