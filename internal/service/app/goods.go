package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/pkg/helper"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"unicode/utf8"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
)

type GoodsService struct {
	applet.UnimplementedGoodsServer
	biz            *appbiz.GoodsBiz
	categoryBiz    *appbiz.GoodsCategoryBiz
	integralConfig *appbiz.IntegralConfigBiz
}

func NewGoodsService(biz *appbiz.GoodsBiz, categoryBiz *appbiz.GoodsCategoryBiz, integralConfig *appbiz.IntegralConfigBiz) *GoodsService {
	return &GoodsService{biz: biz, categoryBiz: categoryBiz, integralConfig: integralConfig}
}

func (g *GoodsService) Recommend(ctx context.Context, req *applet.GoodsRecommendReq) (*applet.GoodsRecommendRsp, error) {
	recommend := valobj.GoodsRecommendYes
	status := valobj.GoodsStatusEnable
	categoryIds := make([]int, 0)
	if req.GetCategoryId() > 0 {
		categoryIds, _ = g.categoryBiz.GetLeafNodeId(ctx, int(req.GetCategoryId()))
	}
	in := &bo.GoodsQueryBo{
		Recommend:      &recommend,
		Status:         &status,
		OrderSort:      true,
		CategoryIds:    categoryIds,
		Size:           int(req.GetSize()),
		PayIntegralMin: int(req.GetPayIntegralMin()),
		PayIntegralMax: int(req.GetPayIntegralMax()),
		IsIntegralShop: req.GetIsIntegral(),
		GoodsShowType:  valobj.SysConfigGoodsShowTypeObj(req.GetGoodsShowType()),
		OrderBy:        valobj.GoodsClientSearchOrderObj(req.GetOrderBy()),
	}
	if req.GetPage() > 0 {
		in.Page = &bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		}
	}

	integralConfig, err := g.integralConfig.Find(ctx)
	if err != nil {
		return nil, err
	}
	if in.IsIntegralShop {
		if !integralConfig.IsIntegralShopEnable() {
			return nil, apierr.ErrorParam("积分商城未开启")
		}
		in.IntegralShopEnable = true
		in.IntegralCash = integralConfig.IntegralCash
		in.IntegralExchange = integralConfig.IntegralExchange
	} else {
		if integralConfig.IsEnable() {
			in.IntegralShopEnable = integralConfig.IsIntegralShopEnable()
		}
	}

	count, data, err := g.biz.Recommend(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}

	var rsp = &applet.GoodsRecommendRsp{List: make([]*applet.GoodsRecommendRsp_GoodsRecommendItem, 0, len(data)), Count: int32(count), Page: req.GetPage()}
	if data == nil {
		return rsp, nil
	}

	for _, val := range data {
		item := &applet.GoodsRecommendRsp_GoodsRecommendItem{
			Id:           int32(val.Id),
			SkuNo:        val.SkuNo,
			Name:         helper.GetSkuName(val.Name, val.SkuName),
			Image:        val.Image,
			SalePrice:    helper.Float64ToString(val.SalePrice, 2),
			SaleVolume:   int32(val.SalesVolume),
			BrandId:      int32(val.BrandId),
			CategoryId:   int32(val.CategoryId),
			SaleIntegral: int32(val.SaleIntegral),
			MarketPrice:  helper.Float64ToString(val.MarketPrice, 2),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *GoodsService) List(ctx context.Context, req *applet.GoodsListReq) (*applet.GoodsListRsp, error) {
	status := valobj.GoodsStatusEnable
	categoryIds := helper.Int32ToInt(req.GetCategoryIds())
	if req.GetCategoryId() > 0 {
		categoryIds = append(categoryIds, int(req.GetCategoryId()))
	}
	if len(categoryIds) > 0 {
		categoryIds, _ = g.categoryBiz.GetLeafNodeIds(ctx, categoryIds)
	}
	brandIds := make([]int, 0)
	if len(req.GetBrandIds()) > 0 {
		brandIds = helper.Int32ToInt(req.GetBrandIds())
	}
	if brandId := int(req.GetBrandId()); brandId > 0 {
		brandIds = append(brandIds, brandId)
	}
	in := &bo.GoodsQueryBo{
		BrandIds:      brandIds,
		CategoryIds:   categoryIds,
		Status:        &status,
		OrderSort:     true,
		GoodsShowType: valobj.SysConfigGoodsShowTypeObj(req.GetGoodsShowType()),
		Page: &bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		IsIntegralShop: req.GetIsIntegral(),
		OrderBy:        valobj.GoodsClientSearchOrderObj(req.GetOrderBy()),
	}

	integralConfig, err := g.integralConfig.Find(ctx)
	if err != nil {
		return nil, err
	}
	if in.IsIntegralShop {
		if !integralConfig.IsIntegralShopEnable() {
			return nil, apierr.ErrorParam("积分商城未开启")
		}
		in.IntegralShopEnable = true
		in.PayIntegralMin = int(req.GetPayIntegralMin())
		in.PayIntegralMax = int(req.GetPayIntegralMax())
		in.IntegralCash = integralConfig.IntegralCash
		in.IntegralExchange = integralConfig.IntegralExchange
	} else {
		if integralConfig.IsEnable() {
			in.IntegralShopEnable = integralConfig.IsIntegralShopEnable()
		}
	}

	count, data, err := g.biz.List(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}
	rsp := &applet.GoodsListRsp{Count: int32(count), Page: int32(in.Page.GetPage()), List: make([]*applet.GoodsListRsp_GoodsListItem, 0, len(data))}
	if len(data) == 0 || count == 0 {
		return rsp, nil
	}

	goodsIds := slice.Map(data, func(_ int, item *do.GoodsListDo) int {
		if item.SkuName == "默认规格" {
			return item.Id
		}
		return 0
	})

	var goodsSkuCount []*do.GoodsSkuCountDo
	var goodsSkuCountMap map[int]*do.GoodsSkuCountDo
	goodsIds = slice.Unique(goodsIds)
	if len(goodsIds) > 1 {
		goodsSkuCount, err = g.biz.SkuGroupCount(ctx, goodsIds...)
		if err != nil {
			return nil, err
		}
		goodsSkuCountMap = slice.KeyBy(goodsSkuCount, func(item *do.GoodsSkuCountDo) int {
			return item.GoodsId
		})
	}

	for _, val := range data {
		name := val.Name
		skuCount := goodsSkuCountMap[val.Id]
		if skuCount != nil && skuCount.Count > 1 {
			name = helper.GetSkuName(val.Name, val.SkuName)
		}
		item := &applet.GoodsListRsp_GoodsListItem{
			Id:           int32(val.Id),
			SkuNo:        val.SkuNo,
			Name:         name,
			Image:        val.Image,
			SalePrice:    helper.Float64ToString(val.SalePrice, 2),
			SaleVolume:   int32(val.SalesVolume),
			BrandId:      int32(val.BrandId),
			CategoryId:   int32(val.CategoryId),
			SaleIntegral: int32(val.SaleIntegral),
			MarketPrice:  helper.Float64ToString(val.MarketPrice, 2),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *GoodsService) GoodsFreightFee(ctx context.Context, req *applet.GoodsFreightFeeReq) (*applet.GoodsFreightFeeResp, error) {
	var reqBo *bo.GoodsFreightFeeBo
	_ = mapstructure.Decode(req, &reqBo)
	if err := reqBo.Validate(); err != nil {
		return nil, err
	}

	res, err := g.biz.GoodsFreightFee(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &applet.GoodsFreightFeeResp{
		FreightFee: res.FreightFee.InexactFloat64(),
		IsFree:     res.IsFree,
	}, nil
}

func (g *GoodsService) Detail(ctx context.Context, req *applet.GoodsDetailReq) (*applet.GoodsDetailRsp, error) {
	if req.GetId() <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	data, err := g.biz.Detail(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}

	rsp := &applet.GoodsDetailRsp{
		Id:         int32(data.Id),
		Name:       data.Name,
		Type:       int32(data.Type),
		Images:     data.GetImages(),
		Detail:     data.Detail,
		SaleVolume: int32(data.SalesVolume),
		Sku:        make([]*applet.GoodsDetailRsp_GoodsSkuItem, 0, len(data.Sku)),
		Spec:       data.Spec,
	}
	if len(rsp.Images) == 0 {
		rsp.Images = append(rsp.Images, data.Image)
	}

	for _, v := range data.Sku {
		fullName := helper.GetSkuName(data.Name, v.Name)
		if v.Name == "默认规格" && len(data.Sku) == 1 {
			fullName = data.Name
		}
		item := &applet.GoodsDetailRsp_GoodsSkuItem{
			Id:            int32(v.ID),
			Name:          v.Name,
			FullName:      fullName,
			Image:         v.Image,
			SalePrice:     helper.Float64ToString(v.SalePrice, 2),
			Stock:         int32(v.Stock),
			BaseGoodsType: int32(v.ProductType),
			SkuNo:         v.SkuNo,
			SaleIntegral:  int32(v.SaleIntegral),
			MarketPrice:   helper.Float64ToString(v.MarketPrice, 2),
		}
		rsp.Sku = append(rsp.Sku, item)
	}
	return rsp, nil
}

func (g *GoodsService) Search(ctx context.Context, req *applet.GoodsSearchReq) (*applet.GoodsSearchRsp, error) {
	name := req.Name
	if name == "" {
		return nil, apierr.ErrorParam("请输入要搜索的商品")
	}
	if l := utf8.RuneCountInString(name); l < 2 || l > 20 {
		return nil, apierr.ErrorParam("商品名称长度在2-20之间")
	}
	in := &bo.GoodsQueryBo{
		Name:          name,
		OrderSort:     true,
		GoodsShowType: valobj.SysConfigGoodsShowTypeObj(req.GetGoodsShowType()),
		Page: &bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		OrderBy: valobj.GoodsClientSearchOrderObj(req.GetOrderBy()),
	}

	integralConfig, err := g.integralConfig.Find(ctx)
	if err != nil {
		return nil, err
	}
	if integralConfig.IsIntegralShopEnable() {
		in.IntegralShopEnable = true
		in.IntegralExchange = integralConfig.IntegralExchange
		in.IntegralCash = integralConfig.IntegralCash
	} else {
		if integralConfig.IsEnable() {
			in.IntegralShopEnable = integralConfig.IsIntegralShopEnable()
		}
	}

	count, data, _ := g.biz.Search(ctx, in)
	var rsp = &applet.GoodsSearchRsp{
		List:  make([]*applet.GoodsSearchRsp_GoodsSearchItem, 0),
		Count: int32(count),
		Page:  int32(in.Page.GetPage()),
	}

	for _, val := range data {
		item := &applet.GoodsSearchRsp_GoodsSearchItem{
			Id:           int32(val.Id),
			SkuNo:        val.SkuNo,
			Name:         helper.GetSkuName(val.Name, val.SkuName),
			Image:        val.Image,
			SalePrice:    helper.Float64ToString(val.SalePrice, 2),
			SaleVolume:   int32(val.SalesVolume),
			BrandId:      int32(val.BrandId),
			CategoryId:   int32(val.CategoryId),
			SaleIntegral: int32(val.SaleIntegral),
			MarketPrice:  helper.Float64ToString(val.MarketPrice, 2),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *GoodsService) DetailParams(ctx context.Context, req *applet.GoodsDetailParamsReq) (*applet.GoodsDetailParamsRsp, error) {
	if req.GetGoodsId() <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	res, err := g.biz.DetailParams(ctx, int(req.GoodsId))
	if err != nil {
		return nil, err
	}

	return &applet.GoodsDetailParamsRsp{
		GoodsId:         int32(res.GoodsId),
		BrandId:         int32(res.BrandId),
		GoodsCategoryId: int32(res.CategoryId),
	}, nil
}

func (g *GoodsService) SkuInfo(ctx context.Context, req *applet.GoodsSkuInfoReq) (*applet.GoodsSkuInfoRsp, error) {
	var reqBo *bo.GoodsSkuCreateOrderBo
	_ = mapstructure.Decode(req, &reqBo)
	if err := reqBo.Validate(); err != nil {
		return nil, apierr.ErrorParam("%s", err)
	}

	res, _ := g.biz.SkuInfo(ctx, reqBo)

	rsp := &applet.GoodsSkuInfoRsp{
		List: make([]*applet.GoodsSkuInfoRsp_GoodsSkuInfoItem, 0, len(res)),
	}
	for _, val := range res {
		item := &applet.GoodsSkuInfoRsp_GoodsSkuInfoItem{
			Id:            int32(val.ID),
			GoodsName:     val.GoodsName,
			Image:         val.Image,
			SalePrice:     helper.Float64ToString(val.SalePrice, 2),
			Stock:         int32(val.Stock),
			Status:        int32(val.Status),
			GoodsId:       int32(val.GoodsID),
			NumLimit:      int32(val.NumLimit),
			BaseGoodsType: int32(val.ProductType),
			SkuName:       val.Name,
			GoodsType:     int32(val.GoodsType),
			Quantity:      int32(val.Quantity),
			SkuNo:         val.SkuNo,
			SaleIntegral:  int32(val.SaleIntegral),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *GoodsService) Discount(ctx context.Context, req *applet.GoodsDiscountReq) (*applet.GoodsDiscountRsp, error) {
	size := int(req.GetSize())
	if size <= 0 {
		return nil, apierr.ErrorParam("参数错误")
	}
	in := &bo.GoodsDiscountQueryBo{
		Size: size,
	}

	integralConfig, err := g.integralConfig.Find(ctx)
	if err != nil {
		return nil, err
	}
	if req.GetIsIntegral() {
		if !integralConfig.IsIntegralShopEnable() {
			return nil, apierr.ErrorParam("积分商城未开启")
		}
		in.IsIntegralShop = true
	} else {
		in.IsIntegralShop = false
		if integralConfig.IsEnable() {
			in.IntegralShopEnable = integralConfig.IsIntegralShopEnable()
		}
	}
	res, err := g.biz.GetDiscount(ctx, in)
	if err != nil {
		return nil, err
	}
	rsp := &applet.GoodsDiscountRsp{
		List: make([]*applet.GoodsDiscountRsp_GoodsDiscountItem, 0, len(res)),
	}

	for _, val := range res {
		item := &applet.GoodsDiscountRsp_GoodsDiscountItem{
			Id:          int32(val.GoodsID),
			SkuId:       int32(val.ID),
			Name:        fmt.Sprintf("%s/%s", val.GoodsName, val.Name),
			Image:       val.Image,
			Discount:    helper.Float64ToString(val.Discount, 2),
			CategoryId:  int32(val.CategoryId),
			MarketPrice: helper.Float64ToString(val.MarketPrice, 2),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *GoodsService) GetGoodsSkuListByActivityId(ctx context.Context, req *applet.GetGoodsSkuListByActivityIdReq) (*applet.GetGoodsSkuListByActivityIdRsp, error) {
	params := &bo.GoodsSkuListByActivityIdQueryBo{
		ActivityId: int(req.GetActivityId()),
		Page: &bo2.ReqPageBo{
			Page:     int(req.GetPage()),
			PageSize: int(req.GetPageSize()),
		},
	}
	goodsSkuList, pageRsp, err := g.biz.GetGoodsSkuListByActivityId(ctx, params)
	if err != nil {
		return nil, err
	}
	rsp := &applet.GetGoodsSkuListByActivityIdRsp{
		List:     make([]*applet.GetGoodsSkuListByActivityIdRsp_GoodsSkuInfoItem, 0, len(goodsSkuList)),
		Page:     int32(pageRsp.Page),
		PageSize: int32(pageRsp.PageSize),
		Total:    int32(pageRsp.Total),
	}
	for _, val := range goodsSkuList {
		item := &applet.GetGoodsSkuListByActivityIdRsp_GoodsSkuInfoItem{
			Id:            int32(val.ID),
			GoodsName:     val.GetGoodsName(),
			Image:         val.Image,
			SalePrice:     helper.Float64ToString(val.SalePrice, 2),
			SaleVolume:    int32(val.SalesVolume),
			Stock:         int32(val.Stock),
			Status:        int32(val.Status),
			GoodsId:       int32(val.GoodsID),
			NumLimit:      int32(val.NumLimit),
			BaseGoodsType: int32(val.ProductType),
			SkuName:       val.Name,
			GoodsType:     int32(val.GoodsType),
			Quantity:      int32(val.Quantity),
			SkuNo:         val.SkuNo,
			SaleIntegral:  int32(val.SaleIntegral),
			CategoryId:    int32(val.CategoryId),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *GoodsService) FlashSaleSkuList(ctx context.Context, req *applet.FlashSaleSkuListReq) (*applet.FlashSaleSkuListRsp, error) {
	params := &bo.FlashSaleListBo{
		ActivityId: int(req.GetActivityId()),
		Page: &bo2.ReqPageBo{
			Page:     int(req.GetPage()),
			PageSize: int(req.GetPageSize()),
		},
	}
	if err := params.Validate(); err != nil {
		return nil, err
	}

	count, res, err := g.biz.FlashSaleSkuList(ctx, params)
	if err != nil {
		return nil, err
	}

	rsp := &applet.FlashSaleSkuListRsp{Page: int32(params.Page.GetPage()), Count: int32(count)}
	if count == 0 {
		return rsp, nil
	}

	for _, item := range res {
		rsp.List = append(rsp.List, &applet.FlashSaleSkuListRsp_FlashSaleSkuListItem{
			SkuNo:         item.SkuNo,
			Name:          item.Name,
			Image:         item.Image,
			SalePrice:     helper.Float64ToString(item.SalePrice, 2),
			DiscountPrice: helper.Float64ToString(item.DiscountPrice, 2),
			Stock:         int32(item.ActivityCurrentStock),
			TotalStock:    int32(item.ActivityTotalStock),
			LimitNum:      int32(item.NumLimit),
		})
	}
	return rsp, nil
}

func (g *GoodsService) FlashSaleSkuDetail(ctx context.Context, req *applet.FlashSaleSkuDetailReq) (*applet.FlashSaleSkuDetailRsp, error) {
	res, err := g.biz.FlashSaleSkuDetail(ctx, int(req.GetActivityId()), req.GetSkuNo())
	if err != nil {
		return nil, err
	}
	rsp := &applet.FlashSaleSkuDetailRsp{
		Id:         int32(res.Id),
		Name:       res.Name,
		Type:       int32(res.Type),
		Images:     res.GetImages(),
		Detail:     res.Detail,
		SaleVolume: int32(res.SalesVolume),
		Sku:        make([]*applet.FlashSaleSkuDetailRsp_GoodsSkuItem, 0, len(res.Sku)),
	}
	if len(rsp.Images) == 0 {
		rsp.Images = append(rsp.Images, res.Image)
	}

	for _, v := range res.Sku {
		item := &applet.FlashSaleSkuDetailRsp_GoodsSkuItem{
			Id:            int32(v.ID),
			Name:          v.Name,
			FullName:      helper.GetSkuName(res.Name, v.Name),
			Image:         v.Image,
			SalePrice:     helper.Float64ToString(v.SalePrice, 2),
			Stock:         int32(v.Stock),
			BaseGoodsType: int32(v.ProductType),
			SkuNo:         v.SkuNo,
			SaleIntegral:  int32(v.SaleIntegral),
			MarketPrice:   helper.Float64ToString(v.MarketPrice, 2),
			NumLimit:      int32(v.NumLimit),
		}
		rsp.Sku = append(rsp.Sku, item)
	}
	return rsp, nil
}
