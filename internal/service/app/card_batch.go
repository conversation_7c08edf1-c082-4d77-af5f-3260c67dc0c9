package app

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	pb "cardMall/api/applet"
	"cardMall/internal/biz/cache"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/quick_area"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"encoding/json"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/shopspring/decimal"
	"io"
	"sort"
	"time"
	"unicode/utf8"
)

type CardBatchService struct {
	base
	adminv1.UnimplementedGoodsServer

	biz                 *appbiz.CardBatchBiz
	orderCardBatchCache cache.OrderCardBatchCache
	giftCardDs          *ds.GiftCardDs
	conf                *conf.Bootstrap
	reseller            *ds.ResellerDs
}

func NewCardBatchService(biz *appbiz.CardBatchBiz, orderCardBatchCache cache.OrderCardBatchCache, giftCardDs *ds.GiftCardDs, conf *conf.Bootstrap, reseller *ds.ResellerDs) *CardBatchService {
	return &CardBatchService{biz: biz, orderCardBatchCache: orderCardBatchCache, giftCardDs: giftCardDs, conf: conf, reseller: reseller}
}

// My 我的卡券列表
func (s *CardBatchService) My(ctx context.Context, req *pb.MyCardBatchCouponReq) (*pb.MyCardBatchCouponRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	batchCouponList, err := s.biz.GetCardBatchCouponList(ctx, userInfo.Id)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	enableList := make([]*pb.CardBatchCouponInfo, 0)
	disableList := make([]*pb.CardBatchCouponInfo, 0)
	for _, batchCoupon := range batchCouponList {

		item := &pb.CardBatchCouponInfo{
			Id:                  int32(batchCoupon.ID),
			CardBatchNumber:     batchCoupon.CardBatchNumber,
			CardCouponNumber:    batchCoupon.CardCouponNumber,
			CardBatchName:       batchCoupon.CardBatch.CardBatchName,
			UseRule:             batchCoupon.CardBatch.UseRule,
			CouponStatus:        int32(batchCoupon.Status),
			Remark:              batchCoupon.CardBatch.Remark,
			UseExpireStart:      helper.GetTimeDate(batchCoupon.UseExpireStart),
			UseExpireEnd:        helper.GetTimeDate(batchCoupon.UseExpireEnd),
			CreateTime:          helper.GetTimeDate(batchCoupon.CreateTime),
			ExchangeGoodsNum:    int32(batchCoupon.ExchangeGoodsNum),
			SurplusNum:          int32(batchCoupon.ExchangeGoodsNum - batchCoupon.CardCouponUseNum),
			CardCouponUseNum:    int32(batchCoupon.CardCouponUseNum),
			UseSkuNos:           batchCoupon.GetUsedSkuNos(),
			ExchangeGoodsRepeat: int32(batchCoupon.ExchangeGoodsRepeat),
		}
		if batchCoupon.Status == valobj.CardBatchCouponStatusUnUsed {
			if batchCoupon.UseExpireEnd < int(time.Now().Unix()) {
				item.SurplusNum = 0
				disableList = append(disableList, item)
			} else {
				enableList = append(enableList, item)
			}
		}
		if batchCoupon.Status == valobj.CardBatchCouponStatusUsed {

			if batchCoupon.UseExpireEnd < int(time.Now().Unix()) {
				item.SurplusNum = 0
				disableList = append(disableList, item)
			} else {
				if batchCoupon.CardCouponUseNum < batchCoupon.ExchangeGoodsNum {
					enableList = append(enableList, item)
				} else {
					item.SurplusNum = 0
					disableList = append(disableList, item)
				}
			}

		}
		if batchCoupon.Status == valobj.CardBatchCouponStatusCancel {
			item.SurplusNum = 0
			disableList = append(disableList, item)
		}
	}
	return &pb.MyCardBatchCouponRsp{
		EnableList:  enableList,
		DisableList: disableList,
	}, nil
}

// Record 兑换记录
func (s *CardBatchService) Record(ctx context.Context, req *pb.CardBatchCouponRecordReq) (*pb.CardBatchCouponRecordRsp, error) {

	batchCoupon, orderDos, err := s.biz.GetCardBatchCouponOrderList(ctx, int(req.CardBatchCouponId))
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	recordList := make([]*pb.CardBatchCouponRecordRsp_RecordInfo, 0)
	for _, orderDo := range orderDos {

		goodsList := make([]*pb.CardBatchCouponRecordRsp_OrderGoodsItem, 0)
		for _, orderGoods := range orderDo.OrderGoods {
			orderGoodsItem := &pb.CardBatchCouponRecordRsp_OrderGoodsItem{
				GoodsId:      int32(orderGoods.GoodsID),
				CategoryId:   int32(orderGoods.CategoryID),
				GoodsName:    orderGoods.GoodsName,
				GoodsImage:   orderGoods.GoodsImage,
				GoodsSkuName: orderGoods.GoodsSkuName,
				SalePrice:    helper.Float64ToString(orderGoods.SalePrice, 2),
				Quantity:     int32(orderGoods.Quantity),
				SkuNo:        orderGoods.SkuNo,
			}
			goodsList = append(goodsList, orderGoodsItem)
		}

		item := &pb.CardBatchCouponRecordRsp_RecordInfo{
			OrderNumber: orderDo.OrderNumber,
			CreateTime:  helper.GetTimeDate(orderDo.CreateTime),
			UseNum:      int32(orderDo.Num),
			GoodsList:   goodsList,
		}
		recordList = append(recordList, item)
	}
	rsp := &pb.CardBatchCouponRecordRsp{
		CardBatchCoupon: &pb.CardBatchCouponInfo{
			Id:                  int32(batchCoupon.ID),
			CardBatchNumber:     batchCoupon.CardBatchNumber,
			CardCouponNumber:    batchCoupon.CardCouponNumber,
			CardBatchName:       batchCoupon.CardBatch.CardBatchName,
			UseRule:             batchCoupon.CardBatch.UseRule,
			CouponStatus:        int32(batchCoupon.Status),
			Remark:              batchCoupon.CardBatch.Remark,
			UseExpireStart:      helper.GetTimeDate(batchCoupon.UseExpireStart),
			UseExpireEnd:        helper.GetTimeDate(batchCoupon.UseExpireEnd),
			ExchangeGoodsNum:    int32(batchCoupon.ExchangeGoodsNum),
			SurplusNum:          int32(batchCoupon.ExchangeGoodsNum - batchCoupon.CardCouponUseNum),
			CardCouponUseNum:    int32(batchCoupon.CardCouponUseNum),
			UseSkuNos:           batchCoupon.GetUsedSkuNos(),
			ExchangeGoodsRepeat: int32(batchCoupon.ExchangeGoodsRepeat),
		},
		RecordList: recordList,
	}
	return rsp, nil
}
func (s *CardBatchService) ExchangeCheck(ctx context.Context, req *pb.CardBatchCouponExchangeReq) (*pb.CardBatchCouponExchangeCheckRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if l := utf8.RuneCountInString(req.CardCouponNumber); l <= 0 || l > 50 {
		return nil, apierr.ErrorSystemPanic("操作失败:券码不能为空/格式错误")
	}
	batchCoupon, err := s.biz.ExchangeCheck(ctx, userInfo.Id, req.CardCouponNumber)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	rsp := &pb.CardBatchCouponExchangeCheckRsp{
		CardBatchCoupon: &pb.CardBatchCouponInfo{
			Id:                  int32(batchCoupon.ID),
			CardBatchNumber:     batchCoupon.CardBatchNumber,
			CardCouponNumber:    batchCoupon.CardCouponNumber,
			CardBatchName:       batchCoupon.CardBatch.CardBatchName,
			UseRule:             batchCoupon.CardBatch.UseRule,
			CouponStatus:        int32(batchCoupon.Status),
			Remark:              batchCoupon.CardBatch.Remark,
			UseExpireStart:      helper.GetTimeDate(batchCoupon.UseExpireStart),
			UseExpireEnd:        helper.GetTimeDate(batchCoupon.UseExpireEnd),
			ExchangeGoodsNum:    int32(batchCoupon.ExchangeGoodsNum),
			SurplusNum:          int32(batchCoupon.ExchangeGoodsNum - batchCoupon.CardCouponUseNum),
			CardCouponUseNum:    int32(batchCoupon.CardCouponUseNum),
			UseSkuNos:           batchCoupon.GetUsedSkuNos(),
			ExchangeGoodsRepeat: int32(batchCoupon.ExchangeGoodsRepeat),
			BindStatus:          int32(batchCoupon.BindStatus),
		},
	}
	return rsp, nil
}
func (s *CardBatchService) Exchange(ctx context.Context, req *pb.CardBatchCouponExchangeReq) (*pb.CardBatchCouponExchangeRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if l := utf8.RuneCountInString(req.CardCouponNumber); l <= 0 || l > 50 {
		return nil, apierr.ErrorSystemPanic("操作失败:券码不能为空/格式错误")
	}
	batchCoupon, err := s.biz.Exchange(ctx, userInfo.Id, req.CardCouponNumber)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	rsp := &pb.CardBatchCouponExchangeRsp{
		CardBatchCoupon: &pb.CardBatchCouponInfo{
			Id:                  int32(batchCoupon.ID),
			CardBatchNumber:     batchCoupon.CardBatchNumber,
			CardCouponNumber:    batchCoupon.CardCouponNumber,
			CardBatchName:       batchCoupon.CardBatch.CardBatchName,
			UseRule:             batchCoupon.CardBatch.UseRule,
			CouponStatus:        int32(batchCoupon.Status),
			Remark:              batchCoupon.CardBatch.Remark,
			UseExpireStart:      helper.GetTimeDate(batchCoupon.UseExpireStart),
			UseExpireEnd:        helper.GetTimeDate(batchCoupon.UseExpireEnd),
			ExchangeGoodsNum:    int32(batchCoupon.ExchangeGoodsNum),
			SurplusNum:          int32(batchCoupon.ExchangeGoodsNum - batchCoupon.CardCouponUseNum),
			CardCouponUseNum:    int32(batchCoupon.CardCouponUseNum),
			UseSkuNos:           batchCoupon.GetUsedSkuNos(),
			ExchangeGoodsRepeat: int32(batchCoupon.ExchangeGoodsRepeat),
			BindStatus:          int32(batchCoupon.BindStatus),
		},
	}
	return rsp, nil
}
func (s *CardBatchService) Goods(ctx context.Context, req *pb.CardBatchCouponGoodsReq) (*pb.CardBatchCouponGoodsRsp, error) {
	if l := utf8.RuneCountInString(req.CardBatchNumber); l <= 0 || l > 50 {
		return nil, apierr.ErrorSystemPanic("操作失败:卡券批次号不能为空/格式错误")
	}
	goodsSkuList, baseGoodsMap, err := s.biz.GetCardBatchCouponGoodsList(ctx, req.CardBatchNumber)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	recordList := make([]*pb.CardBatchCouponGoodsRsp_CardBatchCouponGoodsListItem, 0)
	for _, goodsSku := range goodsSkuList {
		item := &pb.CardBatchCouponGoodsRsp_CardBatchCouponGoodsListItem{
			GoodsName:     goodsSku.Goods.Name,
			Type:          int32(goodsSku.Goods.Type),
			SkuNo:         goodsSku.SkuNo,
			SkuId:         int32(goodsSku.Id),
			Name:          goodsSku.Name,
			Image:         goodsSku.Image,
			SalePrice:     helper.Float64ToString(goodsSku.SalePrice, 2),
			SupplierPrice: helper.Float64ToString(goodsSku.SupplierPrice, 2),
			SaleVolume:    int32(goodsSku.Goods.SalesVolume),
			BrandId:       int32(goodsSku.Goods.BrandID),
			CategoryId:    int32(goodsSku.Goods.CategoryID),
		}
		baseGoods, ok := baseGoodsMap[goodsSku.SkuNo]
		if ok {
			item.AccountType = helper.SliceConvertSlice[int32, int](baseGoods.ToAccountTypeInt())
			item.BaseGoodsType = int32(baseGoods.Type)
		}
		recordList = append(recordList, item)
	}
	return &pb.CardBatchCouponGoodsRsp{
		List: recordList,
	}, nil
}

func (s *CardBatchService) CreateOrder(ctx context.Context, req *pb.CreateOrderReq) (*pb.CreateOrderRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	var reqBo *bo.CreateCardBatchOrderBo
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.UserId = userInfo.Id
	reqBo.UserName = userInfo.NickName

	if err := s.orderCardBatchCache.LockOrder(ctx, reqBo.UserId, reqBo.CouponId); err != nil {
		return nil, apierr.ErrorNotAllow("操作过于频繁，请稍后再试")
	}
	defer func() {
		_ = s.orderCardBatchCache.UnlockOrder(ctx, reqBo.UserId, reqBo.CouponId)
	}()

	res, err := s.biz.CreateOrder(ctx, reqBo)
	if err != nil {
		return nil, err
	}

	return &pb.CreateOrderRsp{
		OrderNumber: res.OrderNumber,
		CreateTime:  helper.GetTimeDate(res.CreateTime),
		PayOrderId:  int32(res.PayOrderId),
	}, nil
}

func (s *CardBatchService) GiftCardUseAble(ctx context.Context, req *pb.GiftCardUseAbleReq) (*pb.GiftCardUseAbleRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if !userInfo.Invalid() {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	cardBatchList, cardBatchGiftList, err := s.biz.GiftCardUseAble(ctx, userInfo.Id)
	if err != nil {
		return nil, err
	}
	cardBatchListMap := slice.KeyBy(cardBatchList, func(item *bizdo.CardBatchDo) int {
		return item.ID
	})

	rsp := &pb.GiftCardUseAbleRsp{List: make([]*pb.GiftCardUseAbleRsp_GiftCard, 0)}
	for _, card := range cardBatchGiftList {
		rsp.List = append(rsp.List, &pb.GiftCardUseAbleRsp_GiftCard{
			Id:             int32(card.ID),
			BatchName:      cardBatchListMap[card.CardBatchID].CardBatchName,
			UseExpireStart: helper.GetTimeDate(card.UseExpireStart),
			UseExpireEnd:   helper.GetTimeDate(card.UseExpireEnd),
			ExchangeAmount: helper.Float64ToString(card.ExchangeAmount, 2),
			Balance:        helper.Float64ToString(card.CardGiftBalance, 2),
			CardGiftNumber: card.CardGiftNumber,
		})
	}

	return rsp, nil
}
func (s *CardBatchService) MyGiftBalance(ctx context.Context, req *pb.MyGiftReq) (*pb.MyGiftBalanceRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	batchGiftList, err := s.biz.GetCardBatchGiftList(ctx, userInfo.Id)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}

	cardGiftBalance := decimal.Zero
	for _, batchGift := range batchGiftList {
		if batchGift.Status == valobj.CardBatchGiftStatusUnUsed {
			if batchGift.UseExpireEnd > int(time.Now().Unix()) {
				cardGiftBalance = cardGiftBalance.Add(decimal.NewFromFloat(batchGift.CardGiftBalance))
			}
		}
		if batchGift.Status == valobj.CardBatchGiftStatusUsed {
			if batchGift.UseExpireEnd > int(time.Now().Unix()) {
				cardGiftBalance = cardGiftBalance.Add(decimal.NewFromFloat(batchGift.CardGiftBalance))
			}
		}
	}
	return &pb.MyGiftBalanceRsp{CardGiftBalance: cardGiftBalance.String()}, nil
}

// MyGift 我的礼品卡券列表
func (s *CardBatchService) MyGift(ctx context.Context, req *pb.MyGiftReq) (*pb.MyGiftRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	batchGiftList, err := s.biz.GetCardBatchGiftList(ctx, userInfo.Id)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	enableList := make([]*pb.CardBatchGiftInfo, 0)
	disableList := make([]*pb.CardBatchGiftInfo, 0)
	for _, batchGift := range batchGiftList {

		item := &pb.CardBatchGiftInfo{
			Id:                int32(batchGift.ID),
			CardBatchNumber:   batchGift.CardBatchNumber,
			CardGiftNumber:    batchGift.CardGiftNumber,
			CardBatchName:     batchGift.CardBatch.CardBatchName,
			UseRule:           batchGift.CardBatch.UseRule,
			GiftStatus:        int32(batchGift.Status),
			Remark:            batchGift.CardBatch.Remark,
			UseExpireStart:    helper.GetTimeDate(batchGift.UseExpireStart),
			UseExpireEnd:      helper.GetTimeDate(batchGift.UseExpireEnd),
			CreateTime:        helper.GetTimeDate(batchGift.CreateTime),
			ExchangeAmount:    helper.Float64ToString(batchGift.ExchangeAmount, 2),
			SurplusAmount:     helper.Float64ToString(batchGift.CardGiftBalance, 2),
			CardGiftUseAmount: helper.Float64ToString(batchGift.ExchangeAmount-batchGift.CardGiftBalance, 2),
		}
		if batchGift.Status == valobj.CardBatchGiftStatusUnUsed {
			if batchGift.UseExpireEnd < int(time.Now().Unix()) {
				item.SurplusAmount = "0"
				disableList = append(disableList, item)
			} else {
				if batchGift.CardGiftBalance > 0 {
					enableList = append(enableList, item)
				} else {
					item.SurplusAmount = "0"
					disableList = append(disableList, item)
				}
			}
		}
		if batchGift.Status == valobj.CardBatchGiftStatusUsed {

			if batchGift.UseExpireEnd < int(time.Now().Unix()) {
				item.SurplusAmount = "0"
				disableList = append(disableList, item)
			} else {
				if batchGift.CardGiftBalance > 0 {
					enableList = append(enableList, item)
				} else {
					item.SurplusAmount = "0"
					disableList = append(disableList, item)
				}
			}

		}
		if batchGift.Status == valobj.CardBatchGiftStatusCancel {
			item.SurplusAmount = "0"
			disableList = append(disableList, item)
		}
	}
	sort.Slice(enableList, func(i, j int) bool {
		return enableList[i].Id > enableList[j].Id
	})
	sort.Slice(disableList, func(i, j int) bool {
		return disableList[i].Id > disableList[j].Id
	})

	return &pb.MyGiftRsp{
		EnableList:  enableList,
		DisableList: disableList,
	}, nil
}

// GiftRecord 兑换记录
func (s *CardBatchService) GiftRecord(ctx context.Context, req *pb.GiftRecordReq) (*pb.GiftRecordRsp, error) {

	batchGift, orderDos, err := s.biz.GetCardBatchGiftOrderList(ctx, int(req.CardBatchGiftId))
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}

	recordList := make([]*pb.GiftRecordRsp_RecordInfo, 0)
	for _, orderDo := range orderDos {
		orderTransactionDos, _ := s.biz.FindOrderTransaction(ctx, batchGift.ID, orderDo.OrderNumber)

		for _, orderTransactionDo := range orderTransactionDos {
			item := &pb.GiftRecordRsp_RecordInfo{
				OrderNumber: orderDo.OrderNumber,
				CreateTime:  helper.GetTimeDate(orderTransactionDo.CreateTime),
				AmountType:  int32(orderTransactionDo.Type),
				TotalAmount: helper.Float64ToString(orderTransactionDo.Amount, 2),
			}
			recordList = append(recordList, item)
		}
	}
	sort.Slice(recordList, func(i, j int) bool {
		return recordList[i].CreateTime > recordList[j].CreateTime
	})

	rsp := &pb.GiftRecordRsp{
		CardGiftCoupon: &pb.CardBatchGiftInfo{
			Id:                int32(batchGift.ID),
			CardBatchNumber:   batchGift.CardBatchNumber,
			CardGiftNumber:    batchGift.CardGiftNumber,
			CardBatchName:     batchGift.CardBatch.CardBatchName,
			UseRule:           batchGift.CardBatch.UseRule,
			GiftStatus:        int32(batchGift.Status),
			Remark:            batchGift.CardBatch.Remark,
			UseExpireStart:    helper.GetTimeDate(batchGift.UseExpireStart),
			UseExpireEnd:      helper.GetTimeDate(batchGift.UseExpireEnd),
			ExchangeAmount:    helper.Float64ToString(batchGift.ExchangeAmount, 2),
			SurplusAmount:     helper.Float64ToString(batchGift.CardGiftBalance, 2),
			CardGiftUseAmount: helper.Float64ToString(batchGift.ExchangeAmount-batchGift.CardGiftBalance, 2),
		},
		RecordList: recordList,
	}
	return rsp, nil

}
func (s *CardBatchService) ExchangeGiftCheck(ctx context.Context, req *pb.GiftExchangeReq) (*pb.ExchangeGiftCheckRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if l := utf8.RuneCountInString(req.CardGiftNumber); l <= 0 || l > 50 {
		return nil, apierr.ErrorSystemPanic("操作失败:券码不能为空/格式错误")
	}
	batchGift, err := s.biz.ExchangeGiftCheck(ctx, userInfo.Id, req.CardGiftNumber)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	rsp := &pb.ExchangeGiftCheckRsp{
		CardBatchGift: &pb.CardBatchGiftInfo{
			Id:                int32(batchGift.ID),
			CardBatchNumber:   batchGift.CardBatchNumber,
			CardGiftNumber:    batchGift.CardGiftNumber,
			CardBatchName:     batchGift.CardBatch.CardBatchName,
			UseRule:           batchGift.CardBatch.UseRule,
			GiftStatus:        int32(batchGift.Status),
			Remark:            batchGift.CardBatch.Remark,
			UseExpireStart:    helper.GetTimeDate(batchGift.UseExpireStart),
			UseExpireEnd:      helper.GetTimeDate(batchGift.UseExpireEnd),
			ExchangeAmount:    helper.Float64ToString(batchGift.ExchangeAmount, 2),
			SurplusAmount:     helper.Float64ToString(batchGift.CardGiftBalance, 2),
			CardGiftUseAmount: helper.Float64ToString(batchGift.ExchangeAmount-batchGift.CardGiftBalance, 2),
			BindStatus:        int32(batchGift.BindStatus),
		},
	}
	return rsp, nil
}
func (s *CardBatchService) GiftExchange(ctx context.Context, req *pb.GiftExchangeReq) (*pb.GiftExchangeRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}
	if l := utf8.RuneCountInString(req.CardGiftNumber); l <= 0 || l > 50 {
		return nil, apierr.ErrorSystemPanic("操作失败:券码不能为空/格式错误")
	}
	batchGift, err := s.biz.ExchangeGift(ctx, userInfo.Id, userInfo.NickName, req.CardGiftNumber)
	if err != nil {
		return nil, apierr.ErrorParam("%s", err)
	}
	rsp := &pb.GiftExchangeRsp{
		CardBatchGift: &pb.CardBatchGiftInfo{
			Id:                int32(batchGift.ID),
			CardBatchNumber:   batchGift.CardBatchNumber,
			CardGiftNumber:    batchGift.CardGiftNumber,
			CardBatchName:     batchGift.CardBatch.CardBatchName,
			UseRule:           batchGift.CardBatch.UseRule,
			GiftStatus:        int32(batchGift.Status),
			Remark:            batchGift.CardBatch.Remark,
			UseExpireStart:    helper.GetTimeDate(batchGift.UseExpireStart),
			UseExpireEnd:      helper.GetTimeDate(batchGift.UseExpireEnd),
			ExchangeAmount:    helper.Float64ToString(batchGift.ExchangeAmount, 2),
			SurplusAmount:     helper.Float64ToString(batchGift.CardGiftBalance, 2),
			CardGiftUseAmount: helper.Float64ToString(batchGift.ExchangeAmount-batchGift.CardGiftBalance, 2),
			BindStatus:        int32(batchGift.BindStatus),
		},
	}
	return rsp, nil
}

func (s *CardBatchService) CardGiftDiscount(ctx http.Context) error {
	rsp := `{"discount":false, "success":false}`
	body := ctx.Request().Body
	defer body.Close()

	content, err := io.ReadAll(body)
	if err != nil {
		_, _ = ctx.Response().Write([]byte(rsp))
		return nil
	}

	params := &bo.GiftCardDiscountBo{}
	err = json.Unmarshal(content, &params)
	if err != nil {
		_, _ = ctx.Response().Write([]byte(rsp))
		return nil
	}

	reseller, err := s.reseller.GetPlatformReseller(ctx)
	if err != nil {
		_, _ = ctx.Response().Write([]byte(rsp))
		return nil
	}

	if !params.SignCheck(reseller.SecretKey) {
		_, _ = ctx.Response().Write([]byte(rsp))
		return nil
	}
	if params.Uid == "" {
		return apierr.ErrorParam("获取参数失败")
	}

	cpCtx, userId, err := quick_area.OpenUserIdDecode(ctx, params.Uid)
	if err != nil {
		_, _ = ctx.Response().Write([]byte(rsp))
		return nil
	}

	discount, err := s.biz.FindUseAbleOne(cpCtx, userId)
	if err != nil {
		_, _ = ctx.Response().Write([]byte(rsp))
		return nil
	}
	_, _ = ctx.Response().Write([]byte(fmt.Sprintf(`{"discount":%v, "success":true}`, discount)))
	return nil
}
