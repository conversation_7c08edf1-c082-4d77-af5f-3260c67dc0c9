package app

import (
	"cardMall/api/applet"
	"cardMall/internal/module/appbiz"
	"context"
)

type KDService struct {
	biz *appbiz.KDBiz
}

func NewKDService(biz *appbiz.KDBiz) *KDService {
	return &KDService{
		biz: biz,
	}
}

func (K KDService) All(ctx context.Context, req *applet.KDAllReq) (*applet.KDAllRsp, error) {
	d, err := K.biz.All(ctx, req.GetName())
	if err != nil {
		return nil, err
	}
	rsp := &applet.KDAllRsp{}
	for _, kd := range d {
		rsp.List = append(rsp.List, &applet.KDAllRsp_KDItem{
			Id:   int32(kd.Id),
			Name: kd.Name,
		})
	}
	return rsp, nil
}
