package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/transport/http"
)

type ShopService struct {
	configBiz         *appbiz.SysConfigBiz
	shopBiz           *appbiz.ShopBiz
	saasShopAppletBiz *appbiz.SaasShopAppletBiz
	applet.UnimplementedShopServer
}

func NewShopService(configBiz *appbiz.SysConfigBiz, shopBiz *appbiz.ShopBiz, saasShopAppletBiz *appbiz.SaasShopAppletBiz) *ShopService {
	return &ShopService{configBiz: configBiz, shopBiz: shopBiz, saasShopAppletBiz: saasShopAppletBiz}
}

func (s *ShopService) Config(ctx context.Context, req *applet.ShopConfigReq) (*applet.ShopConfigRsp, error) {
	shop, err := s.shopBiz.Get(ctx)
	if err != nil {
		return nil, err
	}
	if shop == nil {
		return nil, apierr.ErrorNotAllow("获取商城信息失败")
	}
	rsp := &applet.ShopConfigRsp{ConfigItem: make([]*applet.ShopConfigRsp_ConfigItem, 0)}
	rsp.Name = shop.Name
	rsp.Status = int32(shop.Status)
	configs, err := s.configBiz.ShopConfig(ctx, []string{
		valobj.SysConfigShopLogo,
		valobj.SysConfigLoginType,
		valobj.SysConfigLoginMode,
		valobj.SysConfigShopColor,
	}...)
	if err != nil {
		return nil, err
	}

	for _, config := range configs {
		rsp.ConfigItem = append(rsp.ConfigItem, &applet.ShopConfigRsp_ConfigItem{
			ConfigKey:   config.ConfigKey,
			ConfigValue: config.ConfigValue,
		})
	}
	return rsp, nil
}

func (s *ShopService) Info(ctx http.Context) error {
	appId := ctx.Query().Get("appId")
	if appId == "" {
		return apierr.ErrorParam("参数错误")
	}
	shopApplet, err := s.saasShopAppletBiz.GetByAppId(ctx, appId)
	if err != nil {
		return apierr.ErrorDbNotFound("获取商城信息失败")
	}
	if shopApplet == nil {
		return apierr.ErrorDbNotFound("获取商城信息失败")
	}
	codeC := isolationcustomer.EncodeC(shopApplet.CustomerID, shopApplet.ShopID)
	ctx.Response().Header().Set("Content-Type", "application/json")
	_, _ = ctx.Response().Write([]byte(fmt.Sprintf(`{"code":200,"msg":"","data":{"code":"%s"}}`, codeC)))
	return nil
}
