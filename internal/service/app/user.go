package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/pkg/helper"
	"context"
)

type UserService struct {
	base
	applet.UnimplementedUserServer
	biz           *appbiz.UserBiz
	couponCodeBiz *appbiz.CouponCodeBiz
	loginV2Biz    *appbiz.LoginV2Biz
	authBiz       *appbiz.AuthBiz
}

func NewUserService(biz *appbiz.UserBiz, couponCodeBiz *appbiz.CouponCodeBiz, loginV2Biz *appbiz.LoginV2Biz, authBiz *appbiz.AuthBiz) *UserService {
	return &UserService{biz: biz, couponCodeBiz: couponCodeBiz, loginV2Biz: loginV2Biz, authBiz: authBiz}
}

func (u *UserService) Update(ctx context.Context, req *applet.UserUpdateReq) (*applet.UserUpdateRsp, error) {
	user, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	var in = &bo.UserUpdateBo{
		Id:          user.Id,
		WxNickName:  req.GetWxNickName(),
		WxAvatarUrl: req.GetWxAvatarUrl(),
		PhoneNumber: req.GetPhoneNumber(),
	}
	if err = in.Validate(); err != nil {
		return nil, err
	}

	row, err := u.biz.UpdateUserInfo(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("用户信息同步失败:%s", err)
	}
	return &applet.UserUpdateRsp{EffectRow: int32(row)}, nil
}

func (u *UserService) Info(ctx context.Context, _ *applet.UserInfoReq) (*applet.UserInfoRsp, error) {
	user, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	user, err = u.biz.Info(ctx, &bo.UserQueryBo{Id: user.Id})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	user.CouponNum, err = u.couponCodeBiz.UserCouponTotal(ctx, user.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	return &applet.UserInfoRsp{
		UserId:           int32(user.Id),
		WxNickName:       user.NickName,
		WxAvatarUrl:      user.AvatarUrl,
		Integral:         int32(user.Integral),
		FreezeIntegral:   int32(user.FreezeIntegral),
		CouponNum:        int32(user.CouponNum),
		PhoneNumber:      user.PhoneNumber,
		WxOfficialOpenId: user.WxOfficialOpenId,
	}, nil
}

func (u *UserService) IntegralLog(ctx context.Context, req *applet.UserIntegralLogReq) (*applet.UserIntegralLogRsp, error) {
	user, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	var in = &bo.UserIntegralLogListBo{
		UserId: user.Id,
		Type:   valobj.UserIntegralLogTypeObj(req.Type),
		ReqPageBo: bo2.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}
	count, data, err := u.biz.IntegralLog(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("积分记录获取失败:%s", err)
	}

	var rsp = &applet.UserIntegralLogRsp{
		List:  nil,
		Page:  int32(in.GetPage()),
		Count: int32(count),
	}
	if count == 0 {
		return rsp, nil
	}

	list := make([]*applet.UserIntegralLogRsp_UserIntegralLogItem, 0, len(data))
	rsp.List = list
	for _, val := range data {
		item := &applet.UserIntegralLogRsp_UserIntegralLogItem{
			Id:         int32(val.Id),
			UserId:     int32(val.UserId),
			Remark:     val.Remark,
			Type:       int32(val.Type),
			Integral:   int32(val.Integral),
			Status:     int32(val.Integral),
			CreateTime: helper.GetTimeDate(val.CreateTime),
		}
		rsp.List = append(rsp.List, item)
	}

	user, err = u.biz.Info(ctx, &bo.UserQueryBo{Id: user.Id})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	rsp.IntegralTotal = int32(user.Integral)

	return rsp, nil
}

func (u *UserService) SaveOpenId(ctx context.Context, req *applet.UserOpenIdReq) (*applet.UserOpenIdRsp, error) {
	user, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	in := &bo.UserUpdateV2Bo{
		Id: user.Id,
	}

	code := req.GetCode()
	if code == "" {
		return nil, apierr.ErrorSystemPanic("授权码不能为空")
	}
	clientType := valobj.UserClientTypeObj(req.GetClientType())
	switch clientType {
	case valobj.UserClientTypeWxApplet:
		res, err := u.authBiz.GetOpenId(ctx, code)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("授权失败:%s", err)
		}
		in.WxOpenId = res.OpenId
	case valobj.UserClientTypeWxOfficial:
		openId, err := u.loginV2Biz.GetWxOfficialOpenId(ctx, code)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("授权失败:%s", err)
		}
		in.WxOfficialOpenId = openId
	case valobj.UserClientTypeAlipayMP:
		authRes, err := u.authBiz.GetAlipayAuthInfo(ctx, code)
		if err != nil || authRes.OpenId == "" {
			return nil, apierr.ErrorSystemPanic("授权失败:%s", err)
		}
		in.AlipayOpenId = authRes.OpenId
	default:
		return nil, apierr.ErrorParam("无效的客户端类型:%d", req.GetClientType())
	}

	_, err = u.biz.UpdateUserInfoV2(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("更新用户信息失败:%s", err)
	}
	return nil, nil
}
