package app

import (
	"cardMall/api/apierr"
	pb "cardMall/api/applet"
	"cardMall/internal/biz/ds"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/pkg/helper"
	"context"
)

type CartService struct {
	base
	pb.UnimplementedCartServer
	goodsCategoryDs *ds.GoodsCategoryDs
	biz             *appbiz.CartItemBiz
}

func NewCartService(biz *appbiz.CartItemBiz,
	goodsCategoryDs *ds.GoodsCategoryDs) *CartService {
	return &CartService{biz: biz, goodsCategoryDs: goodsCategoryDs}
}

func (s *CartService) GetCart(ctx context.Context, req *pb.GetCartReq) (*pb.GetCartRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	in := &bo.CartItemListBo{
		UserId:     userInfo.Id,
		CartItemId: int(req.CartItemId),
		Size:       int(req.Size),
	}
	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	total, hasMore, cartItemDos, err := s.biz.GetCartList(ctx, in)

	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	rsp := &pb.GetCartRsp{
		CartItems: []*pb.CartItemInfo{},
		HasMore:   hasMore,
		Total:     int64(total),
	}
	if len(cartItemDos) == 0 {
		return rsp, nil
	}

	goodsInfoSkuDoMap, err := s.getCartGoodsSkuInfo(ctx, cartItemDos)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	for _, val := range cartItemDos {
		var (
			categoryName = ""
		)
		cartGoodsInfo := &pb.CartGoodsInfo{}
		goodsInfo, ok := goodsInfoSkuDoMap[val.GoodsSkuNo]
		if ok {

			categoryName, err = s.goodsCategoryDs.GetTreeLineJoin(ctx, goodsInfo.CategoryId, "/")
			if err != nil {
				return nil, apierr.ErrorNotAllow("操作失败:%s", err)
			}
			cartGoodsInfo = &pb.CartGoodsInfo{
				Type:           int32(goodsInfo.Type),
				BaseGoodsType:  int32(goodsInfo.ProductType),
				GoodsName:      goodsInfo.GoodsName,
				GoodsId:        int64(goodsInfo.GoodsID),
				MarketPrice:    goodsInfo.MarketPrice.String(),
				SalePrice:      goodsInfo.SalePrice.String(),
				GoodsSkuStatus: int32(goodsInfo.GoodsSkuStatus),
				Status:         int32(goodsInfo.Status),
				GoodsSkuId:     int64(goodsInfo.GoodsSkuID),
				GoodsSkuName:   goodsInfo.GoodsSkuName,
				CategoryId:     int64(goodsInfo.CategoryId),
				CategoryName:   categoryName,
				BrandId:        int64(goodsInfo.BrandId),
				BrandName:      goodsInfo.BrandName,
				Image:          goodsInfo.Image,
				GoodsSkuImage:  goodsInfo.GoodsSkuImage,
				Stock:          int32(goodsInfo.Stock),
				NumLimit:       int32(goodsInfo.NumLimit),
				GoodsSkuNo:     goodsInfo.GoodsSkuNo,
				SaleIntegral:   int32(goodsInfo.SaleIntegral),
			}
		}

		item := &pb.CartItemInfo{
			CartItemId:    int64(val.ID),
			GoodsId:       int64(val.GoodsID),
			GoodsSkuId:    int64(val.GoodsSkuID),
			Quantity:      int32(val.Quantity),
			Price:         val.Price.String(),
			CreateTime:    helper.GetTimeDateInt64(val.CreateTime),
			UpdateTime:    helper.GetTimeDateInt64(val.UpdateTime),
			GoodsSkuNo:    val.GoodsSkuNo,
			CartGoodsInfo: cartGoodsInfo,
		}
		rsp.CartItems = append(rsp.CartItems, item)
	}

	return rsp, nil
}

func (s *CartService) getCartGoodsSkuInfo(ctx context.Context, cartItemDos []*do.CartItemDo) (map[string]*do.CartGoodsInfoDo, error) {
	goodsSkuNos := make([]string, 0, len(cartItemDos))
	for _, val := range cartItemDos {
		goodsSkuNos = append(goodsSkuNos, val.GoodsSkuNo)
	}
	goodsSkuDos, err := s.biz.GetCartGoodsBySkuNo(ctx, goodsSkuNos)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	goodsSkuDoMap := make(map[string]*do.CartGoodsInfoDo)
	for _, val := range goodsSkuDos {
		goodsSkuDoMap[val.GoodsSkuNo] = val
	}
	return goodsSkuDoMap, nil
}

func (s *CartService) DeleteCart(ctx context.Context, req *pb.DeleteCartReq) (*pb.DeleteCartRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotAllow("获取用户信息失败:%s", err)
	}
	effectRow, err := s.biz.DeleteCartItem(ctx, &bo.CartItemDeleteBo{
		UserId: userInfo.Id,
		//CartItemId: req.CartItemIds,
	})
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &pb.DeleteCartRsp{EffectRow: effectRow}, nil
}
func (s *CartService) AddCartItem(ctx context.Context, req *pb.AddCartItemReq) (*pb.AddCartItemRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotAllow("获取用户信息失败:%s", err)
	}
	in := &bo.CartItemAddBo{
		UserId:     userInfo.Id,
		GoodsId:    int(req.GoodsId),
		GoodsSkuNo: req.GoodsSkuNo,
		Quantity:   int(req.Quantity),
	}
	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := s.biz.AddCartItem(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &pb.AddCartItemRsp{EffectRow: effectRow}, nil
}
func (s *CartService) UpdateCartItem(ctx context.Context, req *pb.UpdateCartItemReq) (*pb.UpdateCartItemRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotAllow("获取用户信息失败:%s", err)
	}
	in := &bo.CartItemUpdateBo{
		UserId:     userInfo.Id,
		CartItemId: int(req.CartItemId),
		GoodsId:    int(req.GoodsId),
		GoodsSkuNo: req.GoodsSkuNo,
		Quantity:   int(req.Quantity),
	}
	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	effectRow, err := s.biz.UpdateCartItem(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &pb.UpdateCartItemRsp{EffectRow: effectRow}, nil
}
func (s *CartService) DeleteCartItem(ctx context.Context, req *pb.DeleteCartItemReq) (*pb.DeleteCartItemRsp, error) {
	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotAllow("获取用户信息失败:%s", err)
	}
	in := &bo.CartItemDeleteBo{
		UserId:     userInfo.Id,
		CartItemId: helper.Int64ToInt(req.CartItemIds),
	}
	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := s.biz.DeleteCartItem(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &pb.DeleteCartItemRsp{EffectRow: effectRow}, nil
}
func (s *CartService) UpdateCartQuantity(ctx context.Context, req *pb.UpdateCartQuantityReq) (*pb.UpdateCartQuantityRsp, error) {

	userInfo, err := s.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorNotAllow("获取用户信息失败:%s", err)
	}
	in := &bo.CartQuantityUpdateBo{
		UserId:     userInfo.Id,
		CartItemId: int(req.CartItemId),
		Quantity:   int(req.Quantity),
	}
	err = in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	effectRow, err := s.biz.UpdateCartQuantity(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}

	return &pb.UpdateCartQuantityRsp{EffectRow: effectRow}, nil

}
