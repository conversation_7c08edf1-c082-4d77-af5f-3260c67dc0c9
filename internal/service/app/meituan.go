package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/conf"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/shopspring/decimal"
	"io"
)

type MeiTuanService struct {
	applet.UnimplementedMeiTuanServer
	base
	biz     *appbiz.MeiTuanBiz
	useBiz  *appbiz.UserBiz
	authBiz *appbiz.AuthBiz
	c       *conf.Bootstrap
	log     *log.Helper
}

func NewMeiTuanService(biz *appbiz.MeiTuanBiz, useBiz *appbiz.UserBiz, authBiz *appbiz.AuthBiz, c *conf.Bootstrap, log *log.Helper) *MeiTuanService {
	return &MeiTuanService{biz: biz, useBiz: useBiz, authBiz: authBiz, c: c, log: log}
}

type MeiTuanCreateOrderRequest struct {
	UpstreamTradeNo   string `json:"upstreamTradeNo"`
	StaffNum          string `json:"staffNum"`
	TradeAmount       int    `json:"tradeAmount"`
	ReturnUrl         string `json:"returnUrl"`
	GoodsName         string `json:"goodsName"`
	TradeExpiringTime int    `json:"tradeExpiringTime"`
}

type MeiTuanRefundRequest struct {
	TradeRefundNo   string `json:"tradeRefundNo"`
	UpstreamTradeNo string `json:"upstreamTradeNo"`
	RefundAmount    int    `json:"refundAmount"`
	StaffNum        string `json:"staffNum"`
}

type MeiTuanOrderCloseRequest struct {
	UpstreamTradeNo string `json:"upstreamTradeNo"`
	StaffNum        string `json:"staffNum"`
}

func (m *MeiTuanService) GetLink(ctx context.Context, _ *applet.MeiTuanLinkReq) (*applet.MeiTuanLinkRsp, error) {
	userInfo, err := m.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo == nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	//if m.c.GetServer().GetEnv() != conf.Server_PROD {
	//	userInfo.ShopId = 1
	//}

	u, err := m.biz.GetLink(ctx, userInfo.Id)
	if err != nil {
		m.log.Errorf("获取美团链接失败:%s", err)
		return nil, err
	}
	return &applet.MeiTuanLinkRsp{Url: u}, nil
}

func (m *MeiTuanService) CreateOrder(ctx http.Context) error {
	var errResponse = `{"thirdTradeNo":"","thirdPayUrl":""}`

	body := ctx.Request().Body
	defer body.Close()

	content, err := io.ReadAll(body)
	if err != nil {
		m.log.Errorf("美团下单参数解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}
	m.log.Info("美团下单参数:%s", string(content))

	params := &MeiTuanCreateOrderRequest{}
	err = json.Unmarshal(content, &params)
	if err != nil {
		m.log.Errorf("美团下单参数json解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	cpCtx, userId, err := m.biz.GetUserId(ctx, params.StaffNum)
	if err != nil {
		m.log.Errorf("美团下单参数解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	userInfo, err := m.useBiz.Info(cpCtx, &bo.UserQueryBo{Id: userId})
	if err != nil {
		m.log.Errorf("美团下单失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}
	if userInfo == nil {
		m.log.Errorf("美团下单失败:未找到用户信息")
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	LoginDo, err := m.authBiz.GetLoginToken(userInfo)
	if err != nil {
		m.log.Errorf("美团下单失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	payAmountDecimal := decimal.NewFromInt(int64(params.TradeAmount)).Div(decimal.NewFromInt(100))
	payAmount, _ := payAmountDecimal.Float64()
	in := &bo.MeiTuanOrderCreateBo{
		ClientType:         userInfo.ClientType,
		MeiTuanOrderNumber: params.UpstreamTradeNo,
		UserId:             userId,
		PayAmount:          payAmount,
		ReturnUrl:          params.ReturnUrl,
		GoodsName:          params.GoodsName,
		ExpireTime:         params.TradeExpiringTime,
		UserToken:          LoginDo.Token,
	}

	res, err := m.biz.Create(isolationcustomer.WithCustomerAndShopCtx(ctx, userInfo.CustomerId, userInfo.ShopId), in)
	if err != nil {
		m.log.Errorf("美团创建订单失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	_, err = ctx.Response().Write([]byte(fmt.Sprintf(`{"thirdTradeNo":"%s","thirdPayUrl":"%s"}`, res.OrderNumber, res.PayUrl)))
	if err != nil {
		m.log.Errorf("美团创建订单响应失败:%s", err)
	}
	return nil
}

func (m *MeiTuanService) Refund(ctx http.Context) error {
	var errResponse = `{"thirdRefundNo":""}`

	body := ctx.Request().Body
	defer body.Close()

	content, err := io.ReadAll(body)
	if err != nil {
		m.log.Errorf("美团退款参数解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}
	m.log.Info("美团退款参数:%s", string(content))

	params := &MeiTuanRefundRequest{}
	err = json.Unmarshal(content, &params)
	if err != nil {
		m.log.Errorf("美团退款参数json解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	//将退款金额单位转换
	refundAmountDecimal := decimal.NewFromInt(int64(params.RefundAmount)).Div(decimal.NewFromInt(100))
	refundAmount, _ := refundAmountDecimal.Float64()
	in := &bo.MeiTuanRefundBo{
		MeiTuanOrderNumber: params.UpstreamTradeNo,
		MeiTuanRefundNo:    params.TradeRefundNo,
		RefundAmount:       refundAmount,
	}

	cpCtx, _, err := m.biz.GetUserId(ctx, params.StaffNum)
	if err != nil {
		m.log.Errorf("美团下单参数解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	refundNo, err := m.biz.Refund(cpCtx, in)
	if err != nil {
		m.log.Errorf("美团退款失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	_, err = ctx.Response().Write([]byte(fmt.Sprintf(`{"thirdRefundNo":"%s"}`, refundNo)))
	if err != nil {
		m.log.Errorf("美团退款响应失败:%s", err)
	}
	return nil
}

func (m *MeiTuanService) GetOrderLink(ctx context.Context, req *applet.MeiTuanOrderLinkReq) (*applet.MeiTuanOrderLinkRsp, error) {
	userInfo, err := m.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo == nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	//if m.c.GetServer().GetEnv() != conf.Server_PROD {
	//	userInfo.ShopId = 1
	//}

	u, err := m.biz.GetOrderLink(ctx, userInfo.Id)
	if err != nil {
		m.log.Errorf("获取美团链接失败:%s", err)
		return nil, err
	}
	return &applet.MeiTuanOrderLinkRsp{Url: u}, nil
}

func (m *MeiTuanService) OrderClose(ctx http.Context) error {
	var errResponse = `{"thirdTradeNo":""}`
	body := ctx.Request().Body
	defer body.Close()

	content, err := io.ReadAll(body)
	if err != nil {
		m.log.Errorf("美团关闭订单参数解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}
	m.log.Infof("美团关闭订单参数获取:%s", string(content))

	params := &MeiTuanOrderCloseRequest{}
	err = json.Unmarshal(content, &params)
	if err != nil {
		m.log.Errorf("美团关闭订单参数json解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	cpCtx, _, err := m.biz.GetUserId(ctx, params.StaffNum)
	if err != nil {
		m.log.Errorf("美团下单参数解析失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}

	orderNumber, err := m.biz.OrderClose(cpCtx, params.UpstreamTradeNo)
	if err != nil {
		m.log.Errorf("美团关闭订单失败:%s", err)
		_, _ = ctx.Response().Write([]byte(errResponse))
		return nil
	}
	_, err = ctx.Response().Write([]byte(fmt.Sprintf(`{"thirdTradeNo":"%s"}`, orderNumber)))
	return nil
}

func (m *MeiTuanService) GetOrderDetailLink(ctx context.Context, req *applet.MeiTuanOrderDetailLinkReq) (*applet.MeiTuanOrderDetailLinkRsp, error) {
	userInfo, err := m.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo == nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	//if mm.c.GetServer().GetEnv() != conf.Server_PROD {
	//	userInfo.ShopId = 1
	//}
	if req.GetOrderNumber() == "" {
		return nil, apierr.ErrorSystemPanic("订单号不能为空")
	}

	u, err := m.biz.GetOrderDetailLink(ctx, userInfo.Id, req.GetOrderNumber())
	if err != nil {
		m.log.Errorf("获取美团链接失败:%s", err)
		return nil, err
	}
	return &applet.MeiTuanOrderDetailLinkRsp{Url: u}, nil
}

func (m *MeiTuanService) UpdateOrderSite(ctx context.Context, req *applet.MeiTuanUpdateOrderSiteReq) (*applet.MeiTuanUpdateOrderSiteRsp, error) {
	userInfo, err := m.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo == nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	if req.GetOrderNumber() == "" {
		return nil, apierr.ErrorSystemPanic("订单号不能为空")
	}

	in := &bo.UpdateOrderSiteBo{
		OrderNumber:   req.GetOrderNumber(),
		SiteUniqueStr: req.GetSiteUniqueStr(),
		UserId:        userInfo.Id,
	}
	_, err = m.biz.UpdateOrderSite(ctx, in)
	if err != nil {
		m.log.Errorf("更新订单来源失败:%s", err)
		return nil, err
	}
	return &applet.MeiTuanUpdateOrderSiteRsp{}, nil
}
