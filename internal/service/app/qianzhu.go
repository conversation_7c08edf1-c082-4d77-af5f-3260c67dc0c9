package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/rpc/calvalobj"
	"cardMall/internal/conf"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/pkg/quick_area"
	"context"
	"errors"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/transport/http"
	"io"
	"strconv"
	"strings"
)

type QianZhuService struct {
	applet.UnimplementedQianZhuServer
	base
	biz    *appbiz.QianZhuBiz
	useBiz *appbiz.UserBiz
	conf   *conf.Bootstrap
	log    *log.Helper
}

func NewQianZhuService(biz *appbiz.QianZhuBiz, useBiz *appbiz.UserBiz, conf *conf.Bootstrap, log *log.Helper) *QianZhuService {
	return &QianZhuService{biz: biz, useBiz: useBiz, conf: conf, log: log}
}

func (q *QianZhuService) GetCinemaLink(ctx context.Context, _ *applet.QianZhuCinemaLinkReq) (*applet.QianZhuCinemaLinkRsp, error) {
	userInfo, err := q.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("请先登录")
	}

	user, err := q.useBiz.Info(ctx, &bo.UserQueryBo{Id: userInfo.Id})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if user.PhoneNumber == "" {
		return nil, apierr.ErrorDbNotFundPhone("请先完善个人信息-手机号")
	}
	if user.NickName == "" {
		return nil, apierr.ErrorDbNotFundNickName("请先完善个人信息-昵称")
	}

	in := &bo.QianZhuGetLinkBo{
		UniqueId:    quick_area.OpenUserIdEncode(ctx, userInfo.Id),
		NickName:    user.NickName,
		PhoneNumber: user.PhoneNumber,
	}
	u, err := q.biz.GetCinemaLink(ctx, in)
	if err != nil {
		q.log.Errorf("千猪获取联登链接失败:%s", err)
		return nil, apierr.ErrorSystemPanic("系统繁忙，请稍后再试")
	}
	return &applet.QianZhuCinemaLinkRsp{
		Url: u,
	}, nil
}

func (q *QianZhuService) GetKFCLink(ctx context.Context, _ *applet.QianZhuKFCLinkReq) (*applet.QianZhuKFCLinkRsp, error) {
	userInfo, err := q.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("请先登录")
	}

	user, err := q.useBiz.Info(ctx, &bo.UserQueryBo{Id: userInfo.Id})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if user.PhoneNumber == "" {
		return nil, apierr.ErrorDbNotFundPhone("请先完善个人信息-手机号")
	}
	if user.NickName == "" {
		return nil, apierr.ErrorDbNotFundNickName("请先完善个人信息-昵称")
	}
	in := &bo.QianZhuGetLinkBo{
		UniqueId:    quick_area.OpenUserIdEncode(ctx, userInfo.Id),
		NickName:    user.NickName,
		PhoneNumber: user.PhoneNumber,
	}
	u, err := q.biz.GetKFCLink(ctx, in)
	if err != nil {
		q.log.Errorf("千猪获取联登链接失败:%s", err)
		return nil, apierr.ErrorSystemPanic("系统繁忙，请稍后再试")
	}
	return &applet.QianZhuKFCLinkRsp{
		Url: u,
	}, nil
}

func (q *QianZhuService) CreateOrder(ctx context.Context, req *applet.QianZhuCreateOrderReq) (*applet.QianZhuCreateOrderRsp, error) {
	userInfo, err := q.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}

	in := &bo.QianZhuCreateOrderBo{
		UserId:        userInfo.Id,
		ThirdTradeNo:  req.ThirdTradeNo,
		SiteUniqueStr: req.GetSiteUniqueStr(),
	}
	source, err := strconv.Atoi(req.Source)
	if err != nil {
		sourceStr := strings.ToLower(req.Source)
		switch sourceStr {
		case "cinema":
			in.Source = calvalobj.QianZhuOrderSourceCinema
		case "kfc":
			in.Source = calvalobj.QianZhuOrderSourceKFC
		case "starbucks":
			in.Source = calvalobj.QianZhuOrderSourceStarBucks
		default:
			return nil, apierr.ErrorSystemPanic("未知的订单来源:%s", req.Source)
		}
	} else {
		in.Source = calvalobj.QianZhuOrderSourceObj(source)
	}
	res, err := q.biz.CreateOrder(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("创建订单失败:%s", err)
	}

	return &applet.QianZhuCreateOrderRsp{OrderNumber: res.OrderNumber, OrderStatus: int32(res.OrderStatus)}, nil
}

func (q *QianZhuService) NotifyHandle(ctx http.Context) error {
	body := ctx.Request().Body
	defer body.Close()

	notify := &acldo.QianZhuNotifyRsp{}
	content, err := io.ReadAll(body)
	if err != nil {
		q.log.Errorf("千猪回调通知读取请求体失败:%s", err)
		_, _ = ctx.Response().Write([]byte(notify.FailResponse()))
	}
	q.log.Infof("千猪回调通知内容:%s", string(content))

	notify, err = q.biz.NotifyHandle(ctx, string(content))
	if err != nil {
		q.log.Errorf("千猪回调通知处理失败:%s", err)
		_, _ = ctx.Response().Write([]byte(notify.FailResponse()))
		return nil
	}

	cpCtx, _, err := quick_area.OpenUserIdDecode(ctx, notify.Uid)
	if err != nil {
		q.log.Errorf("千猪回调通知处理失败:%s", err)
		_, _ = ctx.Response().Write([]byte(notify.FailResponse()))
		return nil
	}

	switch notify.EventType {
	case calvalobj.QianZhuNotifyTypePended:
		err = q.biz.OrderPended(cpCtx, notify.ThirdTradeNo)
		break
	case calvalobj.QianZhuNotifyTypeFinished:
		err = q.biz.OrderFinished(cpCtx, notify.ThirdTradeNo)
		break
	case calvalobj.QianZhuNotifyTypeRefund:
		in := &bo.QianZhuRefundBo{
			ThirdTradeNo:  notify.ThirdTradeNo,
			RefundAmount:  notify.Data.RefundAmountFormat(),
			RefundReason:  fmt.Sprintf("退款原因:%s", notify.Data.RefundReason),
			ThirdRefundNo: notify.Data.RefundTradeNo,
		}
		err = q.biz.OrderCancel(cpCtx, in)
		break
	case calvalobj.QianZhuNotifyTypePendedPart:
		in := &bo.QianZhuRefundBo{
			ThirdTradeNo:  notify.ThirdTradeNo,
			RefundAmount:  notify.Data.RefundAmountFormat(),
			RefundReason:  fmt.Sprintf("退款原因:%s", notify.Data.RefundReason),
			ThirdRefundNo: notify.Data.RefundTradeNo,
		}
		err = q.biz.OrderPendedPart(cpCtx, in)
		break
	case calvalobj.QianZhuNotifyTypeDeliverChange:
		err = q.biz.DeliverStatusChange(cpCtx, notify.ThirdTradeNo)
	case calvalobj.QianZhuNotifyTypeUnknown:
		err = errors.New("未知订单状态")
	default:
		q.log.Errorf("千猪回调通知处理失败,未知的订单状态")
		_, _ = ctx.Response().Write([]byte(notify.FailResponse()))
		return nil
	}
	if err != nil {
		q.log.Errorf("千猪回调通知处理失败:%s", err)
		_, _ = ctx.Response().Write([]byte(notify.FailResponse()))
		return nil
	}

	_, _ = ctx.Response().Write([]byte(notify.SuccessResponse()))
	return nil
}

func (q *QianZhuService) OrderDetailLink(ctx context.Context, req *applet.QianZhuOrderDetailLinkReq) (*applet.QianZhuOrderDetailLinkRsp, error) {
	userInfo, err := q.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("请先登录")
	}

	user, err := q.useBiz.Info(ctx, &bo.UserQueryBo{Id: userInfo.Id})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if user.PhoneNumber == "" {
		return nil, apierr.ErrorDbNotFundPhone("请先完善个人信息-手机号")
	}
	if user.NickName == "" {
		return nil, apierr.ErrorDbNotFundNickName("请先完善个人信息-昵称")
	}
	if req.OrderNumber == "" {
		return nil, apierr.ErrorSystemPanic("订单号不能为空")
	}
	in := &bo.QianZhuGetLinkBo{
		UniqueId:    quick_area.OpenUserIdEncode(ctx, userInfo.Id),
		NickName:    user.NickName,
		PhoneNumber: user.PhoneNumber,
	}
	u, err := q.biz.OrderDetailLink(ctx, in, req.OrderNumber)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取链接失败:%s", err)
	}
	return &applet.QianZhuOrderDetailLinkRsp{
		Url: u,
	}, nil
}

func (q *QianZhuService) GetStarBucksLink(ctx context.Context, req *applet.QianZhuStarBucksLinkReq) (*applet.QianZhuStarBucksLinkRsp, error) {
	userInfo, err := q.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("请先登录")
	}

	user, err := q.useBiz.Info(ctx, &bo.UserQueryBo{Id: userInfo.Id})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败:%s", err)
	}
	if user.PhoneNumber == "" {
		return nil, apierr.ErrorDbNotFundPhone("请先完善个人信息-手机号")
	}
	if user.NickName == "" {
		return nil, apierr.ErrorDbNotFundNickName("请先完善个人信息-昵称")
	}

	in := &bo.QianZhuGetLinkBo{
		UniqueId:    quick_area.OpenUserIdEncode(ctx, userInfo.Id),
		NickName:    user.NickName,
		PhoneNumber: user.PhoneNumber,
	}
	u, err := q.biz.GetStarBucksLink(ctx, in)
	if err != nil {
		q.log.Errorf("千猪获取联登链接失败:%s", err)
		return nil, apierr.ErrorSystemPanic("系统繁忙，请稍后再试")
	}
	return &applet.QianZhuStarBucksLinkRsp{
		Url: u,
	}, nil
}
