package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz"
	"context"
	"github.com/duke-git/lancet/v2/maputil"
	"strconv"
)

type SysConfigService struct {
	base
	applet.UnimplementedSysConfigServer
	sysConfigBiz *appbiz.SysConfigBiz
}

func NewSysConfigService(sysConfigBiz *appbiz.SysConfigBiz) *SysConfigService {
	return &SysConfigService{sysConfigBiz: sysConfigBiz}
}

func (s *SysConfigService) Kv(ctx context.Context, req *applet.SysConfigDetailReq) (*applet.SysConfigDetailRsp, error) {
	if len(req.ConfigKeys) == 0 {
		return nil, apierr.ErrorParam("请输入配置项")
	}
	// allow keys
	allowKeys := map[string]bool{
		valobj.SysConfigShopColor:            true,
		valobj.SysConfigShopLogo:             true,
		valobj.SysConfigService:              true,
		valobj.SysConfigQA:                   true,
		valobj.SysConfigPartnerPhone:         true,
		valobj.SysConfigAfterSalePhone:       true,
		valobj.SysConfigCustomerSupportPhone: true,
		valobj.SysConfigGoodsShowType:        true,
		valobj.SysConfigLoginType:            true,
		valobj.SysConfigLoginMode:            true,
		valobj.SysConfigShopScene:            true,
	}
	for _, v := range req.ConfigKeys {
		if !allowKeys[v] {
			return nil, apierr.ErrorNotAllow("不允许获取%s的配置项", v)
		}
	}

	configs, err := s.sysConfigBiz.ShopConfig(ctx, req.ConfigKeys...)
	if err != nil {
		return nil, err
	}
	res := &applet.SysConfigDetailRsp{
		ConfigMap: make(map[string]string, len(configs)),
	}
	for _, v := range configs {
		res.ConfigMap[v.ConfigKey] = v.ConfigValue
		if v.ConfigKey == valobj.SysConfigGoodsShowType {
			res.ConfigMap[v.ConfigKey] = strconv.Itoa(s.sysConfigBiz.GoodsShowTypeTrans(ctx, v.GetGoodsShowType()).GetValue())
		}
		if v.ConfigKey == valobj.SysConfigShopScene {
			scene := v.GetShopScene()
			res.ConfigMap = maputil.Merge(res.ConfigMap, scene.ToOpenMap())
		}
	}
	return res, nil
}
