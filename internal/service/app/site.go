package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/pkg/helper"
	"context"
)

type SiteService struct {
	biz *appbiz.SiteBiz
}

func NewSiteService(biz *appbiz.SiteBiz) *SiteService {
	return &SiteService{biz: biz}
}

func (s SiteService) Info(ctx context.Context, req *applet.SiteInfoReq) (*applet.SiteInfoRsp, error) {
	str := req.GetUniqueStr()
	if str == "" {
		return nil, apierr.ErrorParam("参数错误")
	}
	site, err := s.biz.Info(ctx, str)
	if err != nil {
		return nil, err
	}
	return &applet.SiteInfoRsp{
		Id:         int32(site.Id),
		Name:       site.Name,
		Domain:     site.Domain,
		CreateTime: helper.GetTimeDate(site.CreateTime),
		UpdateTime: helper.GetTimeDate(site.UpdateTime),
		Status:     int32(site.Status),
		UniqueStr:  site.UniqueStr,
	}, nil
}
