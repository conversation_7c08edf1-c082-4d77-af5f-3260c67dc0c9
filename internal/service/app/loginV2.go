package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/api/common"
	"cardMall/api/h5"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/module/appbiz"
	appbo "cardMall/internal/module/appbiz/bo"
	"cardMall/internal/pkg/helper"
	"context"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"time"
)

type LoginV2Service struct {
	base
	h5.UnimplementedLoginServer
	biz     *appbiz.LoginV2Biz
	authBiz *appbiz.AuthBiz
	conf    *conf.Bootstrap
	data    *data.Data
	log     *log.Helper
}

func NewLoginV2Service(biz *appbiz.LoginV2Biz,
	authBiz *appbiz.AuthBiz,
	data *data.Data,
	log *log.Helper,
	conf *conf.Bootstrap) *LoginV2Service {
	return &LoginV2Service{biz: biz, log: log, data: data, authBiz: authBiz, conf: conf}
}

// LoginV2 登录v2
func (l *LoginV2Service) LoginV2(ctx context.Context, req *applet.LoginV2Req) (*applet.LoginV2Rsp, error) {
	in := &appbo.LoginV2Bo{
		Username:    req.Username,
		Password:    req.Password,
		PhoneNumber: req.PhoneNumber,
		VerifyCode:  req.VerifyCode,
		VerifyId:    req.VerifyId,
		ClientType:  valobj.UserClientTypeObj(req.GetClientType()),
		AesKey:      l.conf.AesKey.GetKey(),
	}
	res, err := l.biz.LoginV2(ctx, in)
	if err != nil {
		return nil, apierr.ErrorParam("登录失败:%s", err)
	}
	return &applet.LoginV2Rsp{
		Token:            res.Token,
		UserId:           int32(res.UserId),
		PhoneNumber:      res.PhoneNumber,
		AvatarUrl:        res.AvatarUrl,
		NickName:         res.NickName,
		WxOfficialOpenId: res.WxOfficialOpenId,
	}, nil
}

// OpenId 获取openid
func (l *LoginV2Service) OpenId(ctx context.Context, req *applet.OpenIdV2Req) (*applet.OpenIdV2Rsp, error) {
	code := req.GetCode()
	if code == "" {
		return nil, apierr.ErrorParam("授权失败:缺少参数")
	}
	clientType := valobj.UserClientTypeObj(req.GetClientType())
	if !clientType.IsValid() {
		return nil, apierr.ErrorParam("授权失败:客户端类型错误")
	}
	var (
		openId = ""
		err    error
	)
	switch clientType {
	case valobj.UserClientTypeWxApplet:
		res, err := l.authBiz.GetOpenId(ctx, code)
		if err != nil {
			return nil, apierr.ErrorNotAllow("授权失败:%s", err)
		}
		openId = res.OpenId
	case valobj.UserClientTypeWxOfficial:
		openId, err = l.biz.GetWxOfficialOpenId(ctx, code)
		if err != nil {
			return nil, apierr.ErrorNotAllow("授权失败:%s", err)
		}
	case valobj.UserClientTypeAlipayMP:
		authRes, err := l.authBiz.GetAlipayAuthInfo(ctx, code)
		if err != nil || authRes.OpenId == "" {
			return nil, apierr.ErrorNotAllow("授权失败:%s", err)
		}
		openId = authRes.OpenId
	default:
		return nil, apierr.ErrorSystemPanic("授权失败:类型无法解析")
	}
	return &applet.OpenIdV2Rsp{
		OpenId:     openId,
		ClientType: req.ClientType,
	}, nil
}

// OpenIdLogin OpenId登录
func (l *LoginV2Service) OpenIdLogin(ctx context.Context, req *applet.LoginWxOpenIdV2Req) (*applet.LoginWxOpenIdV2Rsp, error) {

	clientType := valobj.UserClientTypeObj(req.GetClientType())
	if !clientType.IsValid() {
		return nil, apierr.ErrorParam("登录失败:客户端类型错误")
	}
	if !helper.IsAccountPhone(req.PhoneNumber) {
		return nil, apierr.ErrorParam("登录失败:手机号错误")
	}

	in := &appbo.OpenIdLoginBo{
		PhoneNumber: req.PhoneNumber,
		OpenId:      req.OpenId,
		VerifyCode:  req.VerifyCode,
		VerifyId:    req.VerifyId,
		ClientType:  valobj.UserClientTypeObj(req.GetClientType()),
	}
	res, err := l.biz.OpenIdLogin(ctx, in)
	if err != nil {
		return nil, apierr.ErrorParam("登录失败:%s", err)
	}

	return &applet.LoginWxOpenIdV2Rsp{
		Token:            res.Token,
		UserId:           int32(res.UserId),
		PhoneNumber:      res.PhoneNumber,
		AvatarUrl:        res.AvatarUrl,
		NickName:         res.NickName,
		WxOfficialOpenId: res.WxOfficialOpenId,
	}, nil
}
func (l *LoginV2Service) Register(ctx context.Context, req *applet.RegisterReq) (*applet.RegisterRsp, error) {
	in := &appbo.RegisterBo{
		Username:        req.Username,
		PhoneNumber:     req.PhoneNumber,
		Password:        req.Password,
		ConfirmPassword: req.ConfirmPassword,
		VerifyCode:      req.VerifyCode,
		VerifyId:        req.VerifyId,
		ClientType:      valobj.UserClientTypeObj(req.GetClientType()),
		AesKey:          l.conf.AesKey.GetKey(),
	}
	err := l.biz.Register(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("注册失败:%s", err)
	}
	return &applet.RegisterRsp{
		EffectRow: 1,
	}, nil
}
func (l *LoginV2Service) BindPassword(ctx context.Context, req *applet.BindPasswordReq) (*applet.BindPasswordRsp, error) {
	in := &appbo.BindPasswordBo{
		PhoneNumber:     req.PhoneNumber,
		Password:        req.Password,
		ConfirmPassword: req.ConfirmPassword,
		VerifyCode:      req.VerifyCode,
		VerifyId:        req.VerifyId,
		ClientType:      valobj.UserClientTypeObj(req.GetClientType()),
		AesKey:          l.conf.AesKey.GetKey(),
	}
	userInfo, err := l.biz.BindPassword(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("绑定失败:%s", err)
	}
	return &applet.BindPasswordRsp{
		UserId: int32(userInfo.Id),
	}, nil
}

func (l *LoginV2Service) Limiter(ctx context.Context, ip string) (limiter bool, err error) {
	now := time.Now().UnixNano()
	userCntKey := constants.LimiterPrefixTpl.GetKey(ctx, ip)

	//限流
	var limit int64 = 20
	dura := time.Hour * 1
	l.data.Rdb.
		ZRemRangeByScore(ctx, userCntKey,
			"0",
			fmt.Sprint(now-(dura.Nanoseconds())))

	reqs, _ := l.data.Rdb.ZCard(ctx, userCntKey).Result()
	if reqs >= limit {
		limiter = true
		return
	}

	l.data.Rdb.ZAdd(ctx, userCntKey, redis.Z{
		Score:  float64(now),
		Member: helper.Int64ToString(now),
	})
	l.data.Rdb.Expire(ctx, userCntKey, dura)
	return
}
func (l *LoginV2Service) LoginConfig(ctx context.Context, req *applet.LoginConfigReq) (*applet.LoginConfigRsp, error) {
	sysConfigDos, err := l.biz.LoginConfig(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("绑定失败:%s", err)
	}
	configItem := make([]*applet.LoginConfigRsp_ConfigItem, 0, len(sysConfigDos))
	for _, do := range sysConfigDos {
		item := &applet.LoginConfigRsp_ConfigItem{
			ConfigKey:   do.ConfigKey,
			ConfigValue: do.ConfigValue,
		}
		configItem = append(configItem, item)
	}
	return &applet.LoginConfigRsp{
		ConfigItem: configItem,
	}, nil
}
func (l *LoginV2Service) GetAppletUrl(ctx context.Context, req *applet.GetAppletReq) (*applet.GetAppletRsp, error) {
	payUrl, err := l.biz.GetAppletUrl(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("绑定失败:%s", err)
	}
	return &applet.GetAppletRsp{
		PayUrl: payUrl,
	}, nil
}

func (l *LoginV2Service) QuickLogin(ctx context.Context, req *applet.QuickLoginReq) (*applet.QuickLoginRsp, error) {
	openIdCode := req.GetOpenIdCode()
	if openIdCode == "" {
		return nil, apierr.ErrorParam("缺少参数")
	}

	res, err := l.authBiz.GetOpenId(ctx, openIdCode)
	if err != nil {
		return nil, apierr.ErrorNotAllow("授权失败:%s", err)
	}
	openId := res.OpenId

	phoneCode := req.GetPhoneCode()
	phoneNumber := req.GetPhoneNumber()
	if phoneCode == "" && phoneNumber == "" {
		return nil, apierr.ErrorParam("缺少参数")
	}
	if phoneCode != "" {
		phoneNumber, err = l.authBiz.GetAppletPhoneNumber(ctx, phoneCode)
		if err != nil {
			return nil, apierr.ErrorNotAllow("授权失败:%s", err)
		}
	}

	loginInfo, err := l.biz.QuickLogin(ctx, &appbo.QuickLoginBo{
		PhoneNumber: phoneNumber,
		OpenId:      openId,
		ClientType:  valobj.UserClientTypeWxApplet,
	})
	if err != nil {
		return nil, err
	}
	return &applet.QuickLoginRsp{
		Token:       loginInfo.Token,
		PhoneNumber: loginInfo.PhoneNumber,
	}, nil
}

func (l *LoginV2Service) LoginStatus(ctx context.Context, req *common.ReqEmpty) (*applet.LoginStatusRsp, error) {
	user, err := l.UserInfo(ctx)
	if err != nil {
		return nil, err
	}

	IsLogin := true
	if user == nil || user.Id == 0 {
		IsLogin = false
	}
	return &applet.LoginStatusRsp{
		IsLogin: IsLogin,
	}, nil
}
