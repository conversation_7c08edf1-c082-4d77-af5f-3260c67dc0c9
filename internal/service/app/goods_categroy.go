package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
	"github.com/jinzhu/copier"
	"sort"
)

type GoodsCategoryService struct {
	applet.UnimplementedGoodsCategoryServer
	biz *appbiz.GoodsCategoryBiz
}

func NewGoodsCategoryService(biz *appbiz.GoodsCategoryBiz) *GoodsCategoryService {
	return &GoodsCategoryService{biz: biz}
}

func (g *GoodsCategoryService) All(ctx context.Context, req *applet.GoodsCategoryAllReq) (*applet.GoodsCategoryAllRsp, error) {
	data, err := g.biz.All(ctx, &bo.GoodsCategoryQueryBo{
		Type:      valobj.GoodsCategoryTypeObj(req.GetType()),
		Pid:       int(req.GetPid()),
		Level:     int(req.GetLevel()),
		IndexShow: valobj.GoodsCategoryIndexShowObj(req.GetIndexShow()),
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}

	if data == nil {
		return nil, nil
	}

	list := make([]*applet.GoodsCategoryAllRsp_GoodsCategoryAllItem, 0, len(data))
	var rsp = &applet.GoodsCategoryAllRsp{List: list}

	for _, val := range data {
		item := &applet.GoodsCategoryAllRsp_GoodsCategoryAllItem{
			Id:    int32(val.Id),
			Name:  val.Name,
			Sort:  int32(val.Sort),
			Image: val.Image,
			Type:  int32(val.Type),
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}
func (g *GoodsCategoryService) CategoryBrand(ctx context.Context, req *applet.GoodsCategoryBrandReq) (*applet.GoodsCategoryBrandRsp, error) {
	err := req.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	data, err := g.biz.GoodsCategoryBrand(ctx, &bo.GoodsCategoryBrandBo{
		Id: req.GetIds(),
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}

	if data == nil {
		return nil, nil
	}
	list, err := g.convertGoodsCategoryBrand(ctx, data)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}
	return &applet.GoodsCategoryBrandRsp{List: list}, nil
}

func (g *GoodsCategoryService) convertGoodsCategoryBrand(ctx context.Context, goodsCategoryBrandDos []*do.GoodsCategoryBrandDo) ([]*applet.GoodsCategoryBrandRsp_GoodsCategoryAllItem, error) {

	var (
		goodsCategoryAllItem  = make([]*applet.GoodsCategoryBrandRsp_GoodsCategoryAllItem, 0)
		goodsBrandListItemMap = make(map[int32][]*applet.GoodsCategoryBrandRsp_GoodsBrandListItem)
	)
	for _, val := range goodsCategoryBrandDos {

		categoryId := int32(val.CategoryId)
		if _, ok := goodsBrandListItemMap[categoryId]; !ok {
			goodsCategoryAllItem = append(goodsCategoryAllItem, &applet.GoodsCategoryBrandRsp_GoodsCategoryAllItem{
				Id:   categoryId,
				Name: val.CategoryName,
				Sort: int32(val.CategorySort),
				//BrandList: nil,
			})
		}

		goodsBrandListItemMap[categoryId] = append(goodsBrandListItemMap[categoryId], &applet.GoodsCategoryBrandRsp_GoodsBrandListItem{
			Id:    int32(val.BrandId),
			Name:  val.BrandName,
			Logo:  val.BrandLogo,
			Sort:  int32(val.BrandSort),
			Label: val.BrandLabel,
		})
	}
	sort.Slice(goodsCategoryAllItem, func(i, j int) bool {
		return goodsCategoryAllItem[i].Sort < goodsCategoryAllItem[j].Sort
	})
	for _, val := range goodsCategoryAllItem {
		if goodsBrandListItem, ok := goodsBrandListItemMap[val.Id]; ok {
			sort.Slice(goodsBrandListItem, func(i, j int) bool {
				return goodsBrandListItem[i].Sort < goodsBrandListItem[j].Sort
			})
			val.BrandList = goodsBrandListItem
		}
	}

	return goodsCategoryAllItem, nil
}

func (g *GoodsCategoryService) Recommend(ctx context.Context, req *applet.GoodsCategoryRecommendReq) (*applet.GoodsCategoryRecommendRsp, error) {
	data, err := g.biz.Recommend(ctx, &bo.GoodsCategoryRecommendBo{
		Type: valobj.GoodsCategoryTypeObj(req.GetType()),
		Size: int(req.GetSize()),
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("%s", err)
	}
	if data == nil {
		return nil, nil
	}
	list := make([]*applet.GoodsCategoryRecommendRsp_GoodsCategoryRecommendItem, 0, len(data))
	for _, val := range data {
		item := &applet.GoodsCategoryRecommendRsp_GoodsCategoryRecommendItem{
			Id:    int32(val.Id),
			Sort:  int32(val.Sort),
			Name:  val.Name,
			Image: val.Image,
			Type:  int32(val.Type),
		}
		list = append(list, item)
	}
	return &applet.GoodsCategoryRecommendRsp{List: list}, nil
}

func (g *GoodsCategoryService) Tree(ctx context.Context, req *applet.GoodsCategoryTreeReq) (*applet.GoodsCategoryTreeRsp, error) {
	data := g.biz.Tree(ctx)
	var rsp = &applet.GoodsCategoryTreeRsp{}
	list := make([]*applet.GoodsCategoryTreeRsp_GoodsCategoryListItem, 0, len(data))
	err := copier.Copy(&list, &data)
	rsp.List = list
	return rsp, err
}
