package app

import (
	"cardMall/api/apierr"
	"cardMall/api/applet"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

type UserAddressService struct {
	applet.UnimplementedUserAddressServer
	base

	biz      *appbiz.UserAddressBiz
	orderBiz *appbiz.OrderBiz
}

func NewUserAddressService(biz *appbiz.UserAddressBiz, orderBiz *appbiz.OrderBiz) *UserAddressService {
	return &UserAddressService{biz: biz, orderBiz: orderBiz}
}

func (u *UserAddressService) Add(ctx context.Context, req *applet.UserAddressAddReq) (*applet.UserAddressAddRsp, error) {
	userInfo, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	if userInfo == nil || userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	row, err := u.biz.Add(ctx, &bo.UserAddressAddBo{
		ProvinceId: int(req.ProvinceId),
		CityId:     int(req.CityId),
		Name:       req.Name,
		Area:       req.Area,
		Phone:      req.Phone,
		RegionId:   int(req.RegionId),
		Detail:     req.Detail,
		IsDefault:  valobj.UserAddressDefaultObj(req.IsDefault),
		UserId:     userInfo.Id,
	})
	if err != nil {
		return nil, err
	}
	return &applet.UserAddressAddRsp{
		EffectRow: int32(row),
	}, nil
}

func (u *UserAddressService) All(ctx context.Context, req *applet.UserAddressAllReq) (*applet.UserAddressAllRsp, error) {
	var in *bo.UserAddressQueryBo
	_ = mapstructure.Decode(req, &in)
	in.UserId = u.ApiBase.GetLoginInfoX(ctx).GetUserId()

	res, err := u.biz.All(ctx, in)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户地址失败")
	}
	if res == nil {
		return nil, nil
	}

	var areaIds []int
	if in.OrderNo != "" {
		areaIds = slice.Map(res, func(index int, item *do.UserAddressDo) int { return item.ProvinceId })
		areaIds, _, err = u.orderBiz.GetOrderCanChangeAreaIds(ctx, in.OrderNo, areaIds)
		if err != nil {
			return nil, err
		}
	}

	all := make([]*applet.UserAddressAllRsp_UserAddressItem, 0, len(res))
	for _, v := range res {
		item := &applet.UserAddressAllRsp_UserAddressItem{
			Id:         int32(v.Id),
			Name:       v.Name,
			Phone:      v.Phone,
			ProvinceId: int32(v.ProvinceId),
			CityId:     int32(v.CityId),
			RegionId:   int32(v.RegionId),
			Area:       v.Area,
			Detail:     v.Detail,
			IsDefault:  int32(v.IsDefault),
			UserId:     int32(v.UserId),
			Province:   v.Province,
			City:       v.City,
			Region:     v.Region,
			CanChange:  slice.Contain(areaIds, v.ProvinceId),
		}
		all = append(all, item)
	}
	return &applet.UserAddressAllRsp{List: all}, nil
}

func (u *UserAddressService) Update(ctx context.Context, req *applet.UserAddressUpdateReq) (*applet.UserAddressUpdateRsp, error) {
	userInfo, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	if userInfo == nil || userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	row, err := u.biz.Update(ctx, &bo.UserAddressUpdateBo{
		Id:         int(req.Id),
		ProvinceId: int(req.ProvinceId),
		CityId:     int(req.CityId),
		Name:       req.Name,
		Area:       req.Area,
		Phone:      req.Phone,
		RegionId:   int(req.RegionId),
		Detail:     req.Detail,
		IsDefault:  valobj.UserAddressDefaultObj(req.IsDefault),
		UserId:     userInfo.Id,
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic(err.Error())
	}
	return &applet.UserAddressUpdateRsp{
		EffectRow: int32(row),
	}, nil
}

func (u *UserAddressService) Del(ctx context.Context, req *applet.UserAddressDelReq) (*applet.UserAddressDelRsp, error) {
	userInfo, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	if userInfo == nil || userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	in := &bo.UserAddressDelBo{
		Id:     int(req.GetId()),
		UserId: userInfo.Id,
	}
	if err = in.Validate(); err != nil {
		return nil, apierr.ErrorSystemPanic(err.Error())
	}

	row, _ := u.biz.Del(ctx, in)
	return &applet.UserAddressDelRsp{
		EffectRow: int32(row),
	}, nil
}

func (u *UserAddressService) SetDefault(ctx context.Context, req *applet.UserAddressSetDefaultReq) (*applet.UserAddressSetDefaultRsp, error) {
	userInfo, err := u.UserInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	if userInfo == nil || userInfo.Id == 0 {
		return nil, apierr.ErrorSystemPanic("获取用户信息失败")
	}
	in := &bo.UserAddressSetDefaultBo{
		Id:     int(req.GetId()),
		UserId: userInfo.Id,
	}
	if req.IsDefault != nil {
		isDefault := valobj.UserAddressDefaultObj(*req.IsDefault)
		in.IsDefault = &isDefault
	}
	if err = in.Validate(); err != nil {
		return nil, apierr.ErrorSystemPanic(err.Error())
	}

	row, _ := u.biz.SetDefault(ctx, in)
	return &applet.UserAddressSetDefaultRsp{
		EffectRow: int32(row),
	}, nil
}
