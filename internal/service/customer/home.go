package customer

import (
	"cardMall/api/apierr"
	"cardMall/api/customerv1"
	"cardMall/internal/biz/ds"
	"cardMall/internal/module/customerbiz"
	"cardMall/internal/pkg/helper"
	"context"
)

type HomeService struct {
	base
	customerv1.UnimplementedOrderServer
	biz        *customerbiz.HomeBiz
	resellerDs *ds.ResellerDs
}

func NewHomeService(biz *customerbiz.HomeBiz, resellerDs *ds.ResellerDs) *HomeService {
	return &HomeService{
		biz:        biz,
		resellerDs: resellerDs,
	}
}
func (o *HomeService) HomeToday(ctx context.Context, req *customerv1.HomeTodayReq) (*customerv1.HomeTodayRsp, error) {
	homeDayResult, err := o.biz.HomeToday(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	return &customerv1.HomeTodayRsp{
		OrderDay:                homeDayResult.OrderDay,
		SaleAmount:              homeDayResult.SaleAmount.Round(2).String(),
		DailyAverageSalesAmount: homeDayResult.DailyAverageSalesAmount.Round(2).String(),
		RefundAmount:            homeDayResult.RefundAmount.Round(2).String(),
		RefundNum:               int32(homeDayResult.RefundNum),
		PayNum:                  int32(homeDayResult.PayNum),
		DailyAveragePayNum:      int32(homeDayResult.DailyAveragePayNum),
		ConsumerTodayVisitNum:   int32(homeDayResult.ConsumerTodayVisitNum),
		DailyAverageVisitNum:    int32(homeDayResult.DailyAverageVisitNum),
	}, nil
}

func (o *HomeService) HomeSalesTrend(ctx context.Context, req *customerv1.SalesTrendReq) (*customerv1.SalesTrendRsp, error) {
	salesTrendResults, err := o.biz.HomeSalesTrend(ctx, int(req.SalesTrendType))
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	list := make([]*customerv1.SalesTrendRsp_SalesTrendItem, 0, len(salesTrendResults))
	var rsp = &customerv1.SalesTrendRsp{
		List: list,
	}
	if len(salesTrendResults) == 0 {
		return rsp, nil
	}
	for _, res := range salesTrendResults {
		item := &customerv1.SalesTrendRsp_SalesTrendItem{
			SalesTrendDate: res.SalesTrendDate,
			SaleAmount:     res.SaleAmount.Round(2).String(),
		}
		if req.SalesTrendType == 1 {
			item.SalesTrendDate = item.SalesTrendDate + ":00:00"
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (o *HomeService) HomeSalesProportion(ctx context.Context, req *customerv1.HomeSalesProportionReq) (*customerv1.HomeSalesProportionRsp, error) {
	salesProportionResults, err := o.biz.HomeSalesProportion(ctx, int(req.SalesTrendType))
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	list := make([]*customerv1.HomeSalesProportionRsp_SalesProportionItem, 0, len(salesProportionResults))
	var rsp = &customerv1.HomeSalesProportionRsp{
		List: list,
	}
	if len(salesProportionResults) == 0 {
		return rsp, nil
	}
	for _, res := range salesProportionResults {
		item := &customerv1.HomeSalesProportionRsp_SalesProportionItem{
			ShopId:                int32(res.ShopId),
			ShopName:              res.ShopName,
			SaleAmount:            res.SaleAmount.Round(2).String(),
			SaleAmount_Proportion: 0,
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil

}

func (o *HomeService) ResellerBalance(ctx context.Context, req *customerv1.HomeBalanceReq) (*customerv1.HomeBalanceRsp, error) {
	rsp := &customerv1.HomeBalanceRsp{}
	balance, _ := o.resellerDs.GetResellerBalance(ctx)
	rsp.IsShow = true
	rsp.ResellerBalance = helper.Float64ToString(balance, 4)
	return rsp, nil
}
