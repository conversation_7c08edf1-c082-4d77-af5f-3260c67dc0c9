package customer

import (
	"cardMall/internal/manager"
	"context"

	"cardMall/api/apierr"
	"cardMall/api/customerv1"
	commonbo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data/ent"
	customerbiz2 "cardMall/internal/module/customerbiz"
	"cardMall/internal/module/supplierbiz"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/service"
	errors2 "github.com/go-kratos/kratos/v2/errors"
)

type SupplierGoodsDraftService struct {
	service.AdminBase
	customerv1.UnimplementedSupplierGoodsDraftServer

	biz                       *customerbiz2.SupplierGoodsDraftBiz
	supplierBiz               *customerbiz2.SupplierBiz
	goodsBiz                  *supplierbiz.GoodsBiz
	goodsPriceAutoSyncManager *manager.GoodsPriceAutoSyncManager

	goodsCategoryDs *ds.GoodsCategoryDs
	areaRepoDs      *ds.AreaRepoDs
}

func NewSupplierDraftGoodsService(
	biz *customerbiz2.SupplierGoodsDraftBiz,
	goodsBiz *supplierbiz.GoodsBiz,
	supplierBiz *customerbiz2.SupplierBiz,
	goodsPriceAutoSyncManager *manager.GoodsPriceAutoSyncManager,
	goodsCategoryDs *ds.GoodsCategoryDs,
	areaRepoDs *ds.AreaRepoDs) *SupplierGoodsDraftService {
	return &SupplierGoodsDraftService{
		biz:                       biz,
		supplierBiz:               supplierBiz,
		goodsBiz:                  goodsBiz,
		goodsCategoryDs:           goodsCategoryDs,
		areaRepoDs:                areaRepoDs,
		goodsPriceAutoSyncManager: goodsPriceAutoSyncManager,
	}
}

func (g *SupplierGoodsDraftService) DraftList(ctx context.Context, req *customerv1.SupplierGoodsDraftListReq) (*customerv1.SupplierGoodsDraftListRsp, error) {

	//err := req.OrderPreValidate()
	//if err != nil {
	//	return nil, apierr.ErrorParam("操作失败:%s", err)
	//}
	var in = &commonbo.SupplierGoodsDraftListBo{
		SupplierId: req.SupplierId,
		GoodsName:  req.GoodsName,
		GoodsId:    req.GoodsId,
		OpType:     req.OpType,
		CategoryId: g.goodsCategoryDs.FindLeafNodeId(ctx, int(req.CategoryId)),
		Barcode:    req.Barcode,
		BrandName:  req.BrandName,
		Type:       valobj.SupplierGoodsTypeObj(req.Type),
		ReqPageBo: commonbo.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
	}
	err := in.Validate()
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	count, data, err := g.biz.DraftList(ctx, in)

	list := make([]*customerv1.SupplierGoodsDraftListRsp_SupplierGoodsListItem, 0, in.GetPageSize())
	var rsp = &customerv1.SupplierGoodsDraftListRsp{
		List:  list,
		Page:  int32(in.GetPage()),
		Count: int32(count),
	}
	if err != nil {
		return rsp, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	if count == 0 {
		return rsp, nil
	}

	for _, val := range data {
		item := &customerv1.SupplierGoodsDraftListRsp_SupplierGoodsListItem{
			Type:         int32(val.Type),
			GoodsName:    val.GoodsName,
			GoodsId:      int64(val.GoodsID),
			SupplierName: val.SupplierName,
			OpType:       int32(val.OpType),
			UpdateTime:   helper.GetTimeDate(val.UpdateTime),
			DraftId:      int32(val.DraftId),
			Status:       val.Status,
			MainImage:    val.MainImage,
		}
		if val.GoodsCategoryAllNodeDo != nil {
			item.CategoryName = val.GetCategoryAllNodeName()
			item.CategoryId = int32(val.GoodsCategoryAllNodeDo.ID)
		}
		if val.GoodsBrandDo != nil {
			item.BrandName = val.GoodsBrandDo.Name
			item.BrandId = int32(val.GoodsBrandDo.Id)
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, nil
}

func (g *SupplierGoodsDraftService) DraftDetail(ctx context.Context, req *customerv1.SupplierGoodsDraftDetailReq) (*customerv1.SupplierGoodsDraftDetailRsp, error) {

	if req.GetDraftId() <= 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	supplierGoodsDraftDo, err := g.biz.Detail(ctx, req.DraftId)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", errors2.FromError(err).GetMessage())
	}
	draftDetail, err := supplierGoodsDraftDo.GetDraftDetail()
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", errors2.FromError(err).GetMessage())
	}
	var (
		goodsSku               = make([]*customerv1.SupplierGoodsSkuInfo, 0, len(draftDetail.GoodsSku))
		goodsSkuSpecNameArr    []string
		oldGoodsSku            = make([]*customerv1.SupplierGoodsSkuInfo, 0, len(draftDetail.OldGoodsSku))
		oldGoodsSkuSpecNameArr []string
		goods                  = &customerv1.SupplierGoodsInfo{}
		oldGoods               = &customerv1.SupplierGoodsInfo{}
	)
	if draftDetail.GoodsSku != nil {
		for _, val := range draftDetail.GoodsSku {
			if val.Status == valobj.SupplierGoodsStatusDraft {
				continue
			}
			var (
				skuDo *customerv1.SupplierGoodsSkuInfo
			)
			goodsSkuSpecNameArr, skuDo, err = g.convertSupplierGoodsSkuDo(ctx, val)
			if err != nil {
				return nil, apierr.ErrorSystemPanic("操作失败:%s", errors2.FromError(err).GetMessage())
			}
			goodsSku = append(goodsSku, skuDo)
		}
	}
	if draftDetail.OldGoodsSku != nil {
		for _, val := range draftDetail.OldGoodsSku {
			if val.Status == valobj.SupplierGoodsStatusDraft {
				continue
			}
			var (
				skuDo *customerv1.SupplierGoodsSkuInfo
			)
			oldGoodsSkuSpecNameArr, skuDo, err = g.convertSupplierGoodsSkuDo(ctx, val)
			if err != nil {
				return nil, apierr.ErrorSystemPanic("操作失败:%s", errors2.FromError(err).GetMessage())
			}
			oldGoodsSku = append(oldGoodsSku, skuDo)
		}
	}
	if draftDetail.Goods != nil {
		goods, err = g.convertSupplierGoodsInfo(ctx, draftDetail.Goods)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("操作失败:%s", errors2.FromError(err).GetMessage())
		}
	}
	if draftDetail.OldGoods != nil {
		oldGoods, err = g.convertSupplierGoodsInfo(ctx, draftDetail.OldGoods)
		if err != nil {
			return nil, apierr.ErrorSystemPanic("操作失败:%s", errors2.FromError(err).GetMessage())
		}
	}
	detailRsp := &customerv1.SupplierGoodsDraftDetailRsp{
		DraftId: req.DraftId,
		Status:  int32(supplierGoodsDraftDo.Status),
		Detail: &customerv1.DraftDetail{
			Goods:               goods,
			GoodsSku:            goodsSku,
			GoodsSkuSpecName:    goodsSkuSpecNameArr,
			OldGoods:            oldGoods,
			OldGoodsSku:         oldGoodsSku,
			OldGoodsSkuSpecName: oldGoodsSkuSpecNameArr,
			Draft:               draftDetail.Draft,
		},
	}
	return detailRsp, nil
}

func (g *SupplierGoodsDraftService) convertSupplierGoodsInfo(ctx context.Context, supplierGoodsDo *bizdo.SupplierGoodsDo) (*customerv1.SupplierGoodsInfo, error) {
	var (
		categoryName      = ""
		brandName         = ""
		supplierName      = ""
		saleAreaInfo      []*customerv1.AreaInfo
		saleAreaInfoDB    []*bizdo.AreaDo
		notSaleAreaInfo   []*customerv1.AreaInfo
		notSaleAreaInfoDB []*bizdo.AreaDo
	)
	brand, err := g.biz.GetBrandName(ctx, supplierGoodsDo.BrandID)
	if err != nil {
		return nil, err
	}
	if brand != nil {
		brandName = brand.Name
	}

	supplier, err := g.biz.GetSupplierName(ctx, supplierGoodsDo.SupplierID)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, apierr.ErrorDbNotFound("供应商不存在")
		}
		return nil, err
	}
	if supplier != nil {
		supplierName = supplier.Name
	}

	images, err := supplierGoodsDo.GetImages()
	if err != nil {
		return nil, apierr.ErrorSystemPanic("图片解析失败:%s", err)
	}
	categoryName, err = g.goodsCategoryDs.GetTreeLineJoin(ctx, supplierGoodsDo.CategoryID, "/")
	if err != nil {
		return nil, apierr.ErrorSystemPanic("获取分类失败:%s", err)
	}
	if len(supplierGoodsDo.GetSaleArea()) > 0 {
		saleAreaInfoDB, err = g.areaRepoDs.GetAreaByIds(ctx, supplierGoodsDo.GetSaleArea())
		if err != nil {
			return nil, apierr.ErrorSystemPanic("获取销售区域失败:%s", err)
		}

		for _, val := range saleAreaInfoDB {
			saleAreaInfo = append(saleAreaInfo, &customerv1.AreaInfo{
				Id:        int32(val.ID),
				Pid:       int32(val.Pid),
				Name:      val.Name,
				Level:     int32(val.Level),
				MergeName: val.Mergename,
			})
		}
	}
	if len(supplierGoodsDo.GetNotSaleArea()) > 0 {
		notSaleAreaInfoDB, err = g.areaRepoDs.GetAreaByIds(ctx, supplierGoodsDo.GetNotSaleArea())
		if err != nil {
			return nil, apierr.ErrorSystemPanic("获取禁销区域失败:%s", err)
		}
		for _, val := range notSaleAreaInfoDB {
			notSaleAreaInfo = append(notSaleAreaInfo, &customerv1.AreaInfo{
				Id:        int32(val.ID),
				Pid:       int32(val.Pid),
				Name:      val.Name,
				Level:     int32(val.Level),
				MergeName: val.Mergename,
			})
		}
	}
	supplierTransport := &customerv1.SupplierTransport{}
	if supplierGoodsDo.SupplierTransport != nil {
		transport := supplierGoodsDo.SupplierTransport
		supplierTransportItems := make([]*customerv1.SupplierTransportItem, 0)

		for _, supplierTransportItem := range transport.SupplierTransportItem {

			supplierTransportCitys := make([]*customerv1.SupplierTransportCity, 0)
			for _, supplierTransportCity := range supplierTransportItem.SupplierTransportCity {
				supplierTransportCitys = append(supplierTransportCitys, &customerv1.SupplierTransportCity{
					Id:          int32(supplierTransportCity.ID),
					ItemId:      int32(supplierTransportCity.ItemID),
					TransportId: int32(supplierTransportCity.TransportID),
					CityId:      int32(supplierTransportCity.CityID),
					CityName:    supplierTransportCity.CityName,
				})
			}

			supplierTransportItems = append(supplierTransportItems, &customerv1.SupplierTransportItem{
				Id:            int32(supplierTransportItem.ID),
				TransportId:   int32(supplierTransportItem.TransportID),
				DefaultNum:    supplierTransportItem.DefaultNum,
				DefaultPrice:  supplierTransportItem.DefaultPrice,
				AddNum:        supplierTransportItem.AddNum,
				AddPrice:      supplierTransportItem.AddPrice,
				TransportCity: supplierTransportCitys,
			})
		}

		supplierTransport = &customerv1.SupplierTransport{
			Id:            int32(transport.ID),
			Name:          transport.Name,
			Description:   transport.Description,
			KdId:          int32(transport.KdID),
			KdName:        transport.KdName,
			KdCode:        transport.KdCode,
			PricingMode:   int32(transport.PricingMode),
			SupplierId:    int32(transport.SupplierID),
			DefaultNum:    transport.DefaultNum,
			DefaultPrice:  transport.DefaultPrice,
			AddNum:        transport.AddNum,
			AddPrice:      transport.AddPrice,
			TransportItem: supplierTransportItems,
		}
	}
	return &customerv1.SupplierGoodsInfo{
		GoodsId:             int32(supplierGoodsDo.ID),
		GoodsName:           supplierGoodsDo.Name,
		CategoryId:          int32(supplierGoodsDo.CategoryID),
		CategoryName:        categoryName,
		BrandId:             int32(supplierGoodsDo.BrandID),
		BrandName:           brandName,
		SupplierId:          int32(supplierGoodsDo.SupplierID),
		SupplierName:        supplierName,
		InputPoint:          supplierGoodsDo.TaxRate,
		OutputPoint:         supplierGoodsDo.TaxRate,
		MainImage:           supplierGoodsDo.MainImage,
		Images:              images,
		SaleArea:            saleAreaInfo,
		NotSaleArea:         notSaleAreaInfo,
		GoodsType:           int32(supplierGoodsDo.Type),
		LogisticsType:       "",
		Postage:             0,
		Detail:              supplierGoodsDo.Detail,
		SupplierTransport:   supplierTransport,
		DeliverTimeline:     int32(supplierGoodsDo.DeliverTimeline),
		DeliverTimelineText: supplierGoodsDo.DeliverTimeline.String(),
	}, nil
}
func (g *SupplierGoodsDraftService) convertSupplierGoodsSkuDo(ctx context.Context, supplierGoodsSkuDo *bizdo.SupplierGoodsSkuDo) ([]string, *customerv1.SupplierGoodsSkuInfo, error) {
	supplierGoodsSpecDo, err := g.biz.GetSupplierGoodsSpecItem(ctx, supplierGoodsSkuDo.GetSpecItemIds())
	if err != nil {
		return []string{}, nil, err
	}
	specItemValue := map[string]*customerv1.SupplierGoodsSpecInfo{}

	specNameArr := make([]string, 0, len(supplierGoodsSpecDo))
	for _, val := range supplierGoodsSpecDo {
		specNameArr = append(specNameArr, val.SpecName)
		specItemValue[val.SpecName] = &customerv1.SupplierGoodsSpecInfo{
			Name:  val.SpecName,
			Value: val.Name,
		}
	}
	return specNameArr, &customerv1.SupplierGoodsSkuInfo{
		SpecItemIds:     helper.IntToInt32(supplierGoodsSkuDo.GetSpecItemIds()),
		MarketPrice:     supplierGoodsSkuDo.MarketPrice,
		SalePrice:       supplierGoodsSkuDo.SalePrice,
		SupplierPrice:   supplierGoodsSkuDo.SupplierPrice,
		Barcode:         supplierGoodsSkuDo.Barcode,
		SupplierBarcode: supplierGoodsSkuDo.SupplierBarcode,
		Stock:           int32(supplierGoodsSkuDo.Stock),
		SkuNo:           supplierGoodsSkuDo.SkuNo,
		SpecItemValue:   specItemValue,
		ProductId:       supplierGoodsSkuDo.ProductID,
		ProductName:     supplierGoodsSkuDo.ProductName,
		ProductType:     supplierGoodsSkuDo.ProductType.GetName(),
	}, nil
}
func (g *SupplierGoodsDraftService) DraftReview(ctx context.Context, req *customerv1.SupplierGoodsDraftReviewReq) (*customerv1.SupplierGoodsDraftReviewRsp, error) {
	if req.GetDraftId() <= 0 {
		return nil, apierr.ErrorParam("操作失败:id不能为空")
	}
	adminDo, err := g.AdminBase.GetLoginInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}

	bo := &commonbo.GoodsReviewBo{
		DraftId:        int(req.DraftId),
		Status:         valobj.SupplierGoodsAuditStatusObj(req.Status),
		ReviewUserId:   adminDo.GetUserId(),
		ReviewUserName: adminDo.GetUserName(),
		Remark:         req.Remark,
		IsSystem:       false,
	}
	err = bo.Validate()
	if err != nil {
		return nil, err
	}
	err = g.goodsPriceAutoSyncManager.OnCustomerReviewerGoodsPass(ctx, bo)
	if err != nil {
		return nil, err
	}
	return &customerv1.SupplierGoodsDraftReviewRsp{EffectRow: 1}, err
}
