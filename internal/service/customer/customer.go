package customer

import (
	"cardMall/api/apierr"
	"cardMall/api/common"
	"cardMall/api/customerv1"
	"cardMall/internal/biz/bo"
	"cardMall/internal/module/customerbiz"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/service"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
)

func NewCustomerService(customerBiz *customerbiz.CustomerBiz) *CustomerService {
	return &CustomerService{customerBiz: customerBiz}
}

type CustomerService struct {
	customerv1.UnimplementedCustomerServer
	service.AdminBase

	customerBiz *customerbiz.CustomerBiz
}

func (c CustomerService) LoginPageSetting(ctx context.Context, req *customerv1.LoginPageSettingReq) (*customerv1.LoginPageSettingResp, error) {
	loginInfo := c.AdminBase.GetLoginInfoX(ctx)
	if loginInfo == nil {
		return nil, apierr.ErrorNotAllow("请先登录")
	}

	settingDo, err := c.customerBiz.GetSetting(ctx, loginInfo.GetCustomerId())
	if err != nil {
		return nil, err
	}
	return &customerv1.LoginPageSettingResp{
		Icon:      settingDo.Icon,
		Logo:      settingDo.Logo,
		BgImage:   settingDo.BgImage,
		Name:      settingDo.Name,
		Prompt:    settingDo.Prompt,
		NameColor: settingDo.NameColor,
	}, nil

}

func (c CustomerService) SetLoginPageSetting(ctx context.Context, req *customerv1.SetLoginPageSettingReq) (*common.RespEmpty, error) {
	loginInfo := c.AdminBase.GetLoginInfoX(ctx)
	if loginInfo == nil {
		return nil, apierr.ErrorNotAllow("请先登录")
	}

	reqBo := new(bo.SaveCustomerSettingBo)
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.CustomerId = loginInfo.GetCustomerId()
	if err := reqBo.Validate(); err != nil {
		return nil, err
	}
	err := c.customerBiz.SaveSetting(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, nil
}

func (c CustomerService) ShopLoginPageSetting(ctx context.Context, req *customerv1.ShopLoginPageSettingReq) (*customerv1.ShopLoginPageSettingResp, error) {
	loginInfo := c.AdminBase.GetLoginInfoX(ctx)
	if loginInfo == nil {
		return nil, apierr.ErrorNotAllow("请先登录")
	}

	settingDo, err := c.customerBiz.GetShopSetting(isolationcustomer.WithCustomerIdCtx(ctx, loginInfo.GetCustomerId()), loginInfo.GetCustomerId(), int(req.ShopId))
	if err != nil {
		return nil, err
	}
	return &customerv1.ShopLoginPageSettingResp{
		Icon:      settingDo.Icon,
		Logo:      settingDo.Logo,
		BgImage:   settingDo.BgImage,
		Name:      settingDo.Name,
		Prompt:    settingDo.Prompt,
		NameColor: settingDo.NameColor,
		ShopId:    req.ShopId,
	}, nil

}

func (c CustomerService) SetShopLoginPageSetting(ctx context.Context, req *customerv1.SetShopLoginPageSettingReq) (*common.RespEmpty, error) {
	loginInfo := c.AdminBase.GetLoginInfoX(ctx)
	if loginInfo == nil {
		return nil, apierr.ErrorNotAllow("请先登录")
	}

	reqBo := new(bo.SaveShopSettingBo)
	_ = mapstructure.Decode(req, &reqBo)
	reqBo.CustomerId = loginInfo.GetCustomerId()
	if err := reqBo.Validate(); err != nil {
		return nil, err
	}
	err := c.customerBiz.SaveShopSetting(isolationcustomer.WithCustomerIdCtx(ctx, loginInfo.CustomerId), reqBo)
	if err != nil {
		return nil, err
	}
	return &common.RespEmpty{}, nil
}
