package customer

import (
	"cardMall/api/apierr"
	"cardMall/api/customerv1"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	adminbizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	customerbiz2 "cardMall/internal/module/customerbiz"
	"cardMall/internal/pkg/helper"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

type AccountService struct {
	customerv1.UnimplementedAccountServer
	biz        *customerbiz2.AdminBiz
	sysRoleBiz *customerbiz2.SysRoleBiz
	sysMenuBiz *customerbiz2.SysMenuBiz
	base
}

func NewAccountService(biz *customerbiz2.AdminBiz, sysRoleBiz *customerbiz2.SysRoleBiz, sysMenuBiz *customerbiz2.SysMenuBiz) *AccountService {
	return &AccountService{biz: biz, sysRoleBiz: sysRoleBiz, sysMenuBiz: sysMenuBiz}
}
func (l *AccountService) BackendUserInfo(ctx context.Context, req *customerv1.BackendUserInfoReq) (*customerv1.BackendUserInfoRsp, error) {
	loginInfo := l.GetLoginInfoX(ctx)
	if err := l.CheckCustomerInfo(ctx, loginInfo.CustomerId, loginInfo.ShopId); err != nil {
		return nil, apierr.ErrorNotAllow("登录不成功.")
	}
	var (
		sysMenuDos []*do.SysMenuDo
		err        error
	)
	if loginInfo.IsRoot.IsYes() {
		sysMenuDos, err = l.sysMenuBiz.GetSysMenuList(ctx)
	} else {
		sysMenuDos, err = l.sysMenuBiz.GetSysMenuListByRoleId(ctx, loginInfo.GetRoleId())
	}

	if err != nil {
		return nil, err
	}
	list := make([]*customerv1.BackendUserInfoRsp_MenuTree, 0)
	for _, sysMenuDo := range sysMenuDos {
		item := &customerv1.BackendUserInfoRsp_MenuTree{
			Id:                int32(sysMenuDo.Id),
			MenuName:          sysMenuDo.MenuName,
			ParentId:          int32(sysMenuDo.ParentId),
			MenuPath:          sysMenuDo.MenuPath,
			MenuIcon:          sysMenuDo.MenuIcon,
			MenuSort:          int32(sysMenuDo.MenuSort),
			MenuType:          int32(sysMenuDo.MenuType),
			ApiPath:           sysMenuDo.ApiPath,
			ApiMethod:         int32(sysMenuDo.ApiMethod),
			FrontComponent:    sysMenuDo.FrontComponent,
			FrontUrl:          sysMenuDo.FrontUrl,
			ProcessType:       int32(sysMenuDo.ProcessType),
			ProcessTypeValues: sysMenuDo.ProcessTypeMPartToValues(sysMenuDo.ParseProcessType()),
		}
		list = append(list, item)
	}
	return &customerv1.BackendUserInfoRsp{
		Name:         loginInfo.GetUserName(),
		ListMenuTree: list,
		Id:           int32(loginInfo.GetUserId()),
		Username:     loginInfo.GetUserName(),
		ShopId:       int32(loginInfo.ShopId),
		CustomerId:   int32(loginInfo.CustomerId),
		IsRoot:       int32(loginInfo.IsRoot),
	}, nil
}
func (a *AccountService) Add(ctx context.Context, req *customerv1.AccountAddReq) (*customerv1.AccountAddRsp, error) {
	_, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	//if admin.IsSupperAdmin() {
	//	return nil, apierr.ErrorNotAllow("账号无此操作权限")
	//}
	in := &bo.AdminAddBo{
		Name:     req.GetName(),
		Account:  req.GetAccount(),
		Status:   valobj.AdminStatusObj(req.GetStatus()),
		Password: req.GetPassword(),
		RoleId:   int(req.RoleId),
		Mobile:   req.Mobile,
		Type:     valobj.AdminTypeCustomer,
	}
	if err = in.Validate(); err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	id, err := a.biz.AddAdmin(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}

	return &customerv1.AccountAddRsp{EffectRow: int32(id)}, nil
}

func (a *AccountService) List(ctx context.Context, req *customerv1.AccountListReq) (*customerv1.AccountListRsp, error) {
	in := &bo.AdminListBo{
		Name:    req.GetName(),
		Account: req.GetAccount(),
		RoleId:  int(req.GetRoleId()),
		Status:  valobj.AdminStatusObj(req.GetStatus()),
		Mobile:  req.Mobile,
		ReqPageBo: bo.ReqPageBo{
			PageSize: int(req.GetPageSize()),
			Page:     int(req.GetPage()),
		},
		Type: valobj.AdminTypeCustomer,
	}
	rsp := &customerv1.AccountListRsp{
		Page: int32(in.GetPage()),
	}
	total, list, err := a.biz.ListAdmin(ctx, in)
	rsp.Count = int32(total)
	if total == 0 {
		return rsp, apierr.ErrorNotAllow("操作失败:%s", err)
	}

	accountRole, err := a.getAccountRole(ctx, list)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	rsp.List = make([]*customerv1.AccountListRsp_AccountListItem, 0, len(list))
	for _, v := range list {
		item := &customerv1.AccountListRsp_AccountListItem{
			Id:         int32(v.Id),
			Name:       v.Name,
			Account:    v.Account,
			Status:     int32(v.Status),
			CreateTime: helper.GetTimeDate(v.CreateTime),
			UpdateTime: helper.GetTimeDate(v.UpdateTime),
			Mobile:     v.Mobile,
			IsRoot:     int32(v.IsRoot),
		}
		roleInfo, ok := accountRole[v.RoleID]
		if ok {
			item.RoleId = int32(roleInfo.Id)
			item.RoleName = roleInfo.RoleName
		}
		rsp.List = append(rsp.List, item)
	}
	return rsp, err
}
func (s *AccountService) getAccountRole(ctx context.Context, adminDos []*do.AdminDo) (map[int]*adminbizdo.SysRoleDo, error) {

	roleIds := slice.Map(adminDos, func(_ int, item *do.AdminDo) int {
		return item.RoleID
	})
	sysRoleDos, err := s.sysRoleBiz.FindByRoleIds(ctx, roleIds)
	if err != nil {
		return nil, err
	}
	roleMap := slice.KeyBy(sysRoleDos, func(item *adminbizdo.SysRoleDo) int {
		return item.Id
	})
	return roleMap, nil
}
func (a *AccountService) Update(ctx context.Context, req *customerv1.AccountUpdateReq) (*customerv1.AccountUpdateRsp, error) {
	admin, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	adminDo, err := a.biz.AccountInfo(ctx, admin.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if adminDo.Id == int(req.Id) {
		if !valobj.AdminStatusObj(req.GetStatus()).IsValid() {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己状态")
		}
		if req.GetRoleId() != 0 {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己角色")
		}
	}
	updateAdminDo, err := a.biz.AccountInfo(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	in := new(bo.AdminUpdateBo)
	in.Id = int(req.GetId())
	if req.GetPassword() != "" {
		in.Password = req.GetPassword()
	}
	if req.GetAccount() != "" {
		in.Account = req.GetAccount()
	}
	if req.GetName() != "" {
		in.Name = req.GetName()
	}
	if !valobj.AdminStatusObj(req.GetStatus()).IsValid() {
		if updateAdminDo.IsRoot.IsYes() {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管状态")
		}
		in.Status = valobj.AdminStatusObj(req.GetStatus())
	}
	if req.GetRoleId() != 0 {
		if updateAdminDo.IsRoot.IsYes() {
			return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管角色")
		}
		in.RoleId = int(req.RoleId)
	}
	if req.Mobile != "" {
		in.Mobile = req.Mobile
	}
	if err = in.Validate(); err != nil {
		return nil, err
	}
	row, err := a.biz.Update(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}

	err = a.sysMenuBiz.RefreshUserRole(ctx, in.Id)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &customerv1.AccountUpdateRsp{EffectRow: int32(row)}, nil
}

func (a *AccountService) Del(ctx context.Context, req *customerv1.AccountDelReq) (*customerv1.AccountDelRsp, error) {
	id := int(req.GetId())
	if id <= 0 {
		return nil, apierr.ErrorParam("缺少参数")
	}
	admin, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	adminDo, err := a.biz.AccountInfo(ctx, admin.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if adminDo.Id == int(req.Id) {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己")
	}
	updateAdminDo, err := a.biz.AccountInfo(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if updateAdminDo.IsRoot == valobj.AdminIsRootYes {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管")
	}
	row, err := a.biz.Del(ctx, id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &customerv1.AccountDelRsp{EffectRow: int32(row)}, nil
}

func (a *AccountService) UpdateStatus(ctx context.Context, req *customerv1.AccountUpdateStatusReq) (*customerv1.AccountUpdateStatusRsp, error) {

	admin, err := a.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	adminDo, err := a.biz.AccountInfo(ctx, admin.Id)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if adminDo.Id == int(req.Id) {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改自己")
	}
	updateAdminDo, err := a.biz.AccountInfo(ctx, int(req.GetId()))
	if err != nil {
		return nil, apierr.ErrorSystemPanic("系统错误:%s", err.Error())
	}
	if updateAdminDo.IsRoot == valobj.AdminIsRootYes {
		return nil, apierr.ErrorSystemPanic("系统错误:不允许修改超管")
	}
	in := &bo.AdminUpdateStatusBo{
		Id:     int(req.GetId()),
		Status: valobj.AdminStatusObj(req.GetStatus()),
	}

	if err = in.Validate(); err != nil {
		return nil, apierr.ErrorParam("%s", err.Error())
	}

	row, err := a.biz.UpdateStatus(ctx, in)
	if err != nil {
		return nil, apierr.ErrorNotAllow("操作失败:%s", err)
	}
	return &customerv1.AccountUpdateStatusRsp{EffectRow: int32(row)}, nil
}
func (a *AccountService) SendSMS(ctx context.Context, req *customerv1.AccountSendSMSReq) (*customerv1.AccountSendSMSRsp, error) {
	adminDo, err := a.base.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	res, err := a.biz.SendSMS(ctx, adminDo)
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &customerv1.AccountSendSMSRsp{
		VerifyId:   res.VerifyId,
		ExpireTime: int32(res.ExpireTime),
	}, nil
}
func (a *AccountService) SMSCheck(ctx context.Context, req *customerv1.AccountSMSCheckReq) (*customerv1.AccountSMSCheckRsp, error) {
	adminDo, err := a.base.adminInfo(ctx)
	if err != nil {
		return nil, apierr.ErrorParam("操作失败:%s", err)
	}
	err = a.biz.AdminCheckSMS(ctx, adminDo, &bo.AdminCheckSmsBo{
		VerifyCode: req.VerifyCode,
		VerifyId:   req.VerifyId,
	})
	if err != nil {
		return nil, apierr.ErrorSystemPanic("操作失败:%s", err)
	}
	return &customerv1.AccountSMSCheckRsp{
		Result: true,
	}, nil
}
