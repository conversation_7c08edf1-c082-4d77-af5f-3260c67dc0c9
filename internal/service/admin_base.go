package service

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/server/middleware/authtools"
	"context"
)

type AdminBase struct {
	*Base
}

// GetLoginInfoX 获取登录用户id
func (b *AdminBase) GetLoginInfoX(ctx context.Context) *authtools.LoginContextOpts {
	loginOpts, err := b.GetLoginInfo(ctx)
	if err != nil {
		panic(err)
	}
	return loginOpts
}

// GetLoginInfo 获取登录用户id
func (b *AdminBase) GetLoginInfo(ctx context.Context) (*authtools.LoginContextOpts, error) {
	val := authtools.GetLoginInfo(ctx)
	if val.GetUserId() <= 0 {
		return nil, apierr.ErrorDbNotFound("获取登录信息失败")
	}
	return val, nil
}

// GetSupplierId 获取供应商id
func (b *AdminBase) GetSupplierId(ctx context.Context) (int, error) {
	loginOpts, err := b.GetLoginInfo(ctx)
	if err != nil {
		return 0, err
	}
	supplierId := 0
	switch loginOpts.AdminType {
	case valobj.AdminTypeShopAdmin, valobj.AdminTypeCustomer, valobj.AdminTypeSaasAdmin:
		supplierId = loginOpts.SupplierId
	case valobj.AdminTypeShopSupplier, valobj.AdminTypeCustomerSupplier, valobj.AdminTypeSaasSupplier:
		supplierId = loginOpts.SupplierId
		if supplierId <= 0 {
			return 0, apierr.ErrorDbNotFound("获取供应商信息失败")
		}
	case valobj.AdminTypeCustomerToSupplier, valobj.AdminTypeShopToSupplier:
		supplierId = loginOpts.SupplierId
	default:
		return 0, apierr.ErrorDbNotFound("登录信息异常")
	}
	return supplierId, nil
}

// GetSupplierIdX 获取供应商id
func (b *AdminBase) GetSupplierIdX(ctx context.Context) int {
	val, err := b.GetSupplierId(ctx)
	if err != nil {
		panic(err)
	}
	return val
}

// CheckShopInfo 检查店铺信息
func (b *AdminBase) CheckShopInfo(ctx context.Context, shopId int, customerIds ...int) error {
	if isoShopId, ok := isolationcustomer.GetShopId(ctx); ok && isoShopId != nil && *isoShopId > 0 && *isoShopId != shopId {
		return apierr.ErrorNotAllow("店铺信息异常")
	}
	if len(customerIds) > 0 {
		if isoCustomerId := isolationcustomer.GetCustomerIdZero(ctx); customerIds[0] > 0 && isoCustomerId != customerIds[0] {
			return apierr.ErrorNotAllow("客户信息异常")
		}
	}
	return nil
}

// CheckCustomerInfo 检查店铺信息
func (b *AdminBase) CheckCustomerInfo(ctx context.Context, customerId int, shopIds ...int) error {
	if isoCustomerId := isolationcustomer.GetCustomerIdZero(ctx); isoCustomerId > 0 && isoCustomerId != customerId {
		return apierr.ErrorNotAllow("客户信息异常")
	}
	if len(shopIds) > 0 {
		if isoCustomerId := isolationcustomer.GetShopIdZero(ctx); shopIds[0] > 0 && isoCustomerId != shopIds[0] {
			return apierr.ErrorNotAllow("客户信息异常")
		}
	}

	return nil
}
