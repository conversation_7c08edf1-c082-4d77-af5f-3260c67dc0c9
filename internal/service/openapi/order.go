package openapi

import (
	"cardMall/api/apierr"
	"cardMall/api/openapi"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/manager"
	"cardMall/internal/module/openapibiz"
	"cardMall/internal/pkg/helper"
	"context"
	"github.com/duke-git/lancet/v2/pointer"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/golang-module/carbon/v2"
	"github.com/pkg/errors"
	"github.com/shopspring/decimal"
	"strings"
	"time"
)

type OrderService struct {
	openapi.UnimplementedOrderServiceServer

	resellerDs *ds.ResellerDs

	notifyPaidManager *manager.NotifyPaidManager

	orderBiz *openapibiz.OrderBiz
	userBiz  *openapibiz.UserBiz
}

func NewOrderService(resellerDs *ds.ResellerDs, notifyPaidManager *manager.NotifyPaidManager, orderBiz *openapibiz.OrderBiz, userBiz *openapibiz.UserBiz) *OrderService {
	return &OrderService{resellerDs: resellerDs, notifyPaidManager: notifyPaidManager, orderBiz: orderBiz, userBiz: userBiz}
}

// Pay 支付
func (o *OrderService) Pay(ctx context.Context, req *openapi.OrderPayReq) (*openapi.OrderPayResp, error) {
	err := o.notifyPaidManager.PayByOrderNo(ctx, req.PayOrderNo)
	if err != nil {
		return nil, err
	}
	return &openapi.OrderPayResp{IsSuccess: true}, nil
}

// PayOrderDetail 支付单详情
func (o *OrderService) PayOrderDetail(ctx context.Context, req *openapi.OrderDetailReq) (*openapi.OrderDetailResp, error) {
	// 查询订单
	payOrder, err := o.orderBiz.GetPayOrder(ctx, req.PayOrderNo, &bo.PayOrderEdgeReqBo{WithUser: true})
	if err != nil {
		return nil, err
	}
	if payOrder == nil {
		return nil, apierr.ErrorOrderNotFound("支付订单不存在")
	}
	mainOrder, subOrders, err := o.orderBiz.GetOrderDetailByPayOrder(ctx, req.PayOrderNo, &bo.OrderWithEdgeBo{WithGoods: true})
	if err != nil {
		return nil, err
	}

	res := &openapi.OrderDetailResp{
		PayOrderNo:  payOrder.OrderNumber,
		UnionUserId: pointer.UnwarpOrDefault(payOrder.User.UnionUserId),
		Status:      int32(payOrder.Status),
		StatusDesc:  payOrder.Status.GetName(),
		CreateTime:  carbon.CreateFromTimestamp(int64(payOrder.CreateTime)).Format("Y-m-d H:i:s"),
		PayAmount:   float32(mainOrder.PayAmount),
	}
	res.Orders, err = o.buildOrders(ctx, subOrders)
	if err != nil {
		return nil, err
	}

	if payOrder.PayTime > 0 {
		res.PayTime = carbon.CreateFromTimestamp(int64(payOrder.PayTime)).Format("Y-m-d H:i:s")
	}

	return res, nil
}

// buildOrders 构建订单
func (o *OrderService) buildOrders(ctx context.Context, orders []*do.OrderDo) ([]*openapi.Order, error) {
	resOrders := make([]*openapi.Order, 0, len(orders))
	if len(orders) == 0 {
		return resOrders, nil
	}

	subOrderNos := slice.Map(orders, func(_ int, item *do.OrderDo) string {
		return item.OrderNumber
	})
	// 取退款单数据
	refundOrders, err := o.orderBiz.GetSaleRefundOrders(ctx, subOrderNos...)
	if err != nil {
		return nil, errors.WithMessage(err, "openapi订单详情接口时异常，获取退款单异常")
	}
	refundOrdersMap := make(map[string][]*do.OrderAfterSaleDo)
	for _, refund := range refundOrders {
		refundOrdersMap[refund.OrderNumber] = append(refundOrdersMap[refund.OrderNumber], refund)
	}

	for _, order := range orders {
		row := &openapi.Order{
			OrderNo:    order.OrderNumber,
			PayOrderNo: order.PayOrderNumber,
			Status:     int32(order.Status),
			StatusDesc: order.Status.GetClientName(),
			FreightFee: float32(order.FreightFee),
			PayAmount:  float32(order.PayAmount),
			Account:    order.Account,
			GoodsList:  make([]*openapi.Order_Goods, 0, len(order.OrderGoods)),
			UpdateTime: time.Unix(int64(order.UpdateTime), 0).Format(time.DateTime),
		}
		// 构建订单商品
		for _, orderGoods := range order.OrderGoods {
			goodsName := orderGoods.GoodsName
			if orderGoods.GoodsSkuName != "" {
				goodsName = strings.Join(strings.Split(orderGoods.GoodsSkuName, ","), " ")
			}
			row.GoodsList = append(row.GoodsList, &openapi.Order_Goods{
				SkuNo:         orderGoods.SkuNo,
				GoodsName:     goodsName,
				OriginPrice:   float32(orderGoods.OriginPrice),
				SalePrice:     float32(orderGoods.SalePrice),
				Quantity:      int32(orderGoods.Quantity),
				SupplierPrice: float32(orderGoods.SupplierPrice),
				GoodsImage:    orderGoods.GoodsImage,
			})
		}
		// 构建退款单
		for _, afterSaleDo := range refundOrdersMap[order.OrderNumber] {
			refundFreightFee := decimal.NewFromFloat(afterSaleDo.RefundFreightFee)
			refundGoodsAmount := decimal.NewFromFloat(afterSaleDo.RefundGoodsAmount)
			row.AfterSaleOrders = append(row.AfterSaleOrders, &openapi.Order_RefundOrders{
				AfterSaleOrderNo: afterSaleDo.AfterSaleNo,
				RefundAmount:     float32(refundFreightFee.Add(refundGoodsAmount).InexactFloat64()),
			})
		}
		resOrders = append(resOrders, row)
	}
	return resOrders, nil
}

// OrderList 已支付的订单列表
func (o *OrderService) OrderList(ctx context.Context, req *openapi.OrderOrderListReq) (*openapi.OrderOrderListResp, error) {
	beginTime, err := time.ParseInLocation(carbon.DateTimeLayout, req.UpdateTimeBegin, time.Local)
	if err != nil {
		return nil, apierr.ErrorParam("无效的起始时间")
	}
	endTime, err := time.ParseInLocation(carbon.DateTimeLayout, req.UpdateTimeEnd, time.Local)
	if err != nil {
		return nil, apierr.ErrorParam("无效的结束时间")
	}
	if endTime.Format(time.DateOnly) != beginTime.Format(time.DateOnly) {
		return nil, apierr.ErrorParam("起始时间和结束时间必须在同一天")
	}
	userId := 0
	if req.Mobile != "" {
		if !helper.IsMobileFormat(req.Mobile) {
			return nil, apierr.ErrorParam("无效的手机号")
		}
		// 查询用户
		var userInfo *do.UserDo
		userInfo, err = o.userBiz.GetInfoByMobile(ctx, req.Mobile)
		if err != nil {
			return nil, err
		}
		if userInfo == nil {
			return &openapi.OrderOrderListResp{}, nil
		}
		userId = userInfo.ID
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// 获取订单
	pageSize := int(req.PageSize)
	orders, err := o.orderBiz.FetchPaidOrders(ctx, &bo.FetchPaidOrdersReqBo{
		UpdateTimeBegin: &beginTime,
		UpdateTimeEnd:   &endTime,
		ScrollId:        int(req.ScrollOffset),
		PageSize:        pageSize,
		Edges:           &bo.OrderWithEdgeBo{WithGoods: true, WithUser: true},
		NotStatus:       []valobj.OrderStatusObj{valobj.OrderStatusUnPaid, valobj.OrderStatusPayCreate, valobj.OrderStatusClose},
		UserId:          userId,
	})
	if err != nil {
		return nil, err
	}

	res := &openapi.OrderOrderListResp{}
	res.Orders, err = o.buildOrders(ctx, orders)
	if err != nil {
		return nil, err
	}

	if len(orders) >= pageSize {
		res.ScrollOffset = int32(orders[len(orders)-1].ID)
	}

	return res, nil
}

// MovieOrderListForUpdate 根据最后更新时间取电影订单列表
func (o *OrderService) MovieOrderListForUpdate(ctx context.Context, req *openapi.MovieOrderListForUpdateReq) (*openapi.MovieOrderListForUpdateResp, error) {
	listRes, err := o.orderBiz.GetMovieOrderListForUpdate(ctx, &bo.QzMovieOrdersByUpdateReq{
		PageIndex:       int(req.PageIndex),
		PageSize:        int(req.PageSize),
		UpdateTimeBegin: req.UpdateTimeBegin,
		UpdateTimeEnd:   req.UpdateTimeEnd,
	})
	if err != nil {
		return nil, err
	}
	res := &openapi.MovieOrderListForUpdateResp{
		Total:     listRes.Total,
		PageSize:  listRes.PageSize,
		PageIndex: listRes.PageIndex,
		PageCount: listRes.PageCount,
	}
	for _, row := range listRes.Data {
		item := &openapi.MovieOrderListForUpdateResp_Order{
			CreateTime:      row.CreateTime,
			OrderNo:         row.OrderNo,
			MarketUnitPrice: row.MarketUnitPrice,
			Status:          row.Status,
			StatusDesc:      row.StatusDesc,
			UnitPrice:       row.UnitPrice,
			TotalPrice:      row.TotalPrice,
			PaymentTime:     row.PaymentTime,
			Amount:          row.Amount,
			SeatsDesc:       row.SeatsDesc,
			SeatsCount:      row.SeatsCount,
			CinemaId:        row.CinemaId,
			CinemaAddr:      row.CinemaAddr,
			CinemaName:      row.CinemaName,
			FilmId:          row.FilmId,
			FilmName:        row.FilmName,
			Pic:             row.Pic,
			UserMobile:      row.UserMobile,
			ShowTime:        row.ShowTime,
			ShowEndTime:     row.ShowEndTime,
			CompleteTime:    row.CompleteTime,
			CancelTime:      row.CancelTime,
			CancelType:      row.CancelType,
			DrawTime:        row.DrawTime,
		}
		res.Data = append(res.Data, item)
	}
	return res, nil
}
