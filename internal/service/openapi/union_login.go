package openapi

import (
	"cardMall/api/apierr"
	"cardMall/api/openapi"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/conf"
	"cardMall/internal/module/appbiz"
	"cardMall/internal/module/appbiz/bo"
	appDo "cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/openapibiz"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/strutil"
	"github.com/duke-git/lancet/v2/validator"
	"github.com/go-kratos/kratos/v2/log"
)

type UnionLoginService struct {
	openapi.UnimplementedUnionLoginServer
	log         *log.Helper
	qianzhuRepo rpc.QianZhuRepo
	resellerDs  *ds.ResellerDs
	conf        *conf.Bootstrap
	userBiz     *openapibiz.UserBiz
	loginV2Biz  *appbiz.LoginV2Biz
}

func NewUnionLoginService(log *log.Helper, qianzhuRepo rpc.QianZhuRepo, resellerDs *ds.ResellerDs, conf *conf.Bootstrap, userBiz *openapibiz.UserBiz, loginV2Biz *appbiz.LoginV2Biz) *UnionLoginService {
	return &UnionLoginService{log: log, qianzhuRepo: qianzhuRepo, resellerDs: resellerDs, conf: conf, userBiz: userBiz, loginV2Biz: loginV2Biz}
}

// 中间登录页面
const unionLoginPath = "subPackageA/order/outLogin"

// Get 获取联合登录链接
func (s *UnionLoginService) Get(ctx context.Context, req *openapi.GetUnionLoginReq) (*openapi.GetUnionLoginResp, error) {
	// check
	if req.NickName != "" && strutil.IsBlank(req.NickName) {
		return nil, apierr.ErrorParam("昵称不能全是格白符")
	}
	if req.Mobile != "" && !helper.IsMobileFormat(req.Mobile) {
		return nil, apierr.ErrorParam("手机号格式不正确")
	}
	if strutil.IsBlank(req.UniqueUserId) {
		return nil, apierr.ErrorParam("用户ID不能全是空白符号")
	}
	if req.Avatar != "" && !validator.IsUrl(req.Avatar) {
		return nil, apierr.ErrorParam("头像链接不合法")
	}

	// 获取token
	user, err := s.userBiz.RegisterByUnion(ctx, &bo2.RegisterUserUnionLoginBo{
		Mobile:      req.Mobile,
		NickName:    req.NickName,
		Avatar:      req.Avatar,
		UnionUserId: req.UniqueUserId,
	})
	if err != nil {
		return nil, err
	}
	token, err := s.loginV2Biz.GetLoginToken(&appDo.UserDo{
		Id:               user.ID,
		WxOpenId:         user.WxOpenID,
		NickName:         user.NickName,
		AvatarUrl:        user.AvatarURL,
		Integral:         user.Integral,
		FreezeIntegral:   user.FreezeIntegral,
		PhoneNumber:      user.PhoneNumber,
		ClientType:       user.ClientType,
		WxOfficialOpenId: user.WxOfficialOpenID,
		AlipayOpenId:     user.AlipayOpenID,
		Username:         user.Username,
		CustomerId:       user.CustomerID,
		ShopId:           user.ShopID,
	})
	if err != nil {
		return nil, err
	}

	// 构建c
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	shopId := isolationcustomer.GetShopIdZero(ctx)
	c := isolationcustomer.EncodeC(customerId, shopId)

	// 获取链接
	loginUrl := ""
	if req.Path == "movie" || req.Path == "kfc" {
		scenceUrl := ""
		scenceUrl, err = s.getSceneLoginUrl(ctx, req, user.ID)
		if err != nil {
			return nil, err
		}
		loginUrl = fmt.Sprintf("%s?c=%s#/%s?token=%s&redirectUrl=%s", s.conf.Site.Domain, c, unionLoginPath, token.Token, scenceUrl)
	} else {
		loginUrl = fmt.Sprintf("%s?c=%s#/%s?token=%s", s.conf.Site.Domain, c, req.Path, token.Token)
	}
	return &openapi.GetUnionLoginResp{
		Url: loginUrl,
	}, nil
}

// getSceneLoginUrl 获取场景登录链接
func (s *UnionLoginService) getSceneLoginUrl(ctx context.Context, req *openapi.GetUnionLoginReq, userId int) (string, error) {
	// 获取分销商ID
	resellerInfo, err := s.resellerDs.GetResellerInfo(ctx)
	if err != nil {
		return "", apierr.ErrorException("获取分销商异常%s", err)
	}
	link := ""
	switch req.Path {
	case "movie":
		// 调用千猪联合登录接口
		link, err = s.qianzhuRepo.GetCinemaLink(ctx, &bo.QianZhuGetLinkBo{
			UniqueId:    fmt.Sprintf("%s-%d", req.UniqueUserId, userId),
			NickName:    req.NickName,
			PhoneNumber: req.Mobile,
		}, &acldo.ResellerDo{
			Id:        resellerInfo.Id,
			SecretKey: resellerInfo.SecretKey,
		})
	case "kfc":
		// 调用千猪联合登录接口
		link, err = s.qianzhuRepo.GetKFCLink(ctx, &bo.QianZhuGetLinkBo{
			UniqueId:    fmt.Sprintf("%s-%d", req.UniqueUserId, userId),
			NickName:    req.NickName,
			PhoneNumber: req.Mobile,
		}, &acldo.ResellerDo{
			Id:        resellerInfo.Id,
			SecretKey: resellerInfo.SecretKey,
		})
	}
	return link, nil
}
