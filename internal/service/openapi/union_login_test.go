package openapi

import (
	"cardMall/internal/pkg/openapi"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"testing"
	"time"
)

var secret = "4C8D372B29AFABE951C609313C13605B"
var mchid = "4dAp"

func TestUnionLoginService_Get(t *testing.T) {
	// 真实的http请求，调试时使用
	return
	url := "http://127.0.0.1:8002/openapi/v1/user/unionLogin"
	body := "{\n  \"path\": \"movie\",\n  \"uniqueUserId\": \"xxx\",\n  \"nickName\": \"test\",\n  \"mobile\": \"18140154016\"\n}"
	payload := strings.NewReader(body)

	req, _ := http.NewRequest("POST", url, payload)
	now := int(time.Now().Unix())
	signature := openapi.GenerateRequestSign("/openapi/v1/user/unionLogin", body, mchid, secret, now)
	req.Header.Add("authorization", fmt.Sprintf("MD5 mchid=%s,timestamp=%d,signature=%s", mchid, now, signature))
	req.Header.Add("content-type", "application/json")

	res, _ := http.DefaultClient.Do(req)

	defer res.Body.Close()
	respBody, _ := io.ReadAll(res.Body)

	type respStruct struct {
		Url string
	}
	var result *respStruct
	err := json.Unmarshal(respBody, &result)
	if err != nil {
		t.Errorf("解析失败: %v", err)
	}
	if result.Url == "" {
		t.Errorf("URL返回结果为空")
	}
	fmt.Printf("%s", respBody)
}
