package excel

import (
	"fmt"
	"io"
	"os"
	"testing"
)

func TestIsDispImg(t *testing.T) {
	t.Run("test", func(t *testing.T) {
		if IsDispImg("=DISPIMG(\"123\",1)") {
			t.Log("ok")
		} else {
			t.<PERSON><PERSON>r("error")
		}
	})

	t.<PERSON>("test", func(t *testing.T) {
		if IsDispImg("=DISPIMG(\"123\",1a)") {
			t.Log("ok")
		} else {
			t.<PERSON>rror("error")
		}
	})

	t.Run("pics", func(t *testing.T) {
		if IsDispImg(`=DISPIMG("ID_3D89411001A343029992CAE2572CA3ED",1)`) {
			t.Log("ok")
		} else {
			t.<PERSON><PERSON>r("error")
		}
	})
}

func TestBytesToBase64(t *testing.T) {
	t.Log(Base64ToBytes(""))
	t.Log(BytesToBase64(nil))

	f, err := os.Open("批量导入实物商品.xlsx")
	if err != nil {
		t.Fatal(err)
		return
	}
	defer f.Close()
	fileAll, err := io.ReadAll(f)
	if err != nil {
		t.Fatal(err)
		return
	}
	_, excelFile, err := ParseExcelWithBytes(fileAll)
	if err != nil {
		t.Fatal(err)
		return
	}

	pics, err := excelFile.GetPictures("Sheet1", "E4")
	if err != nil {
		t.Fatal(err)
		return
	}

	for i, pic := range pics {
		b64 := BytesToBase64(pic.File)
		os.WriteFile("bytes.txt", []byte(b64), 0666)
		fileBytes := Base64ToBytes(b64)
		err = os.WriteFile(fmt.Sprintf("pic-%d%s", i, pic.Extension), fileBytes, 0666)
		if err != nil {
			t.Fatal(err)
			return
		}
	}
}
