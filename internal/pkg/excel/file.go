package excel

import (
	"encoding/base64"
	"regexp"
)

const imageUrlExpr = `=DISPIMG\("([A-Za-z0-9_]+)",(\d+)\)`

// IsDispImg 判断是否是图片链接
//
//	=DISPIMG("ID_3D89411001A343029992CAE2572CA3ED",1)格式
func IsDispImg(str string) bool {
	compile, err := regexp.Compile(imageUrlExpr)
	if err != nil {
		panic(err)
	}
	return compile.MatchString(str)
}

// Base64ToBytes base64转bytes
func Base64ToBytes(base64Str string) []byte {
	if base64Str == "" {
		return nil
	}

	decodeString, err := base64.StdEncoding.DecodeString(base64Str)
	if err != nil {
		return nil
	}
	return decodeString
}

// BytesToBase64 bytes转base64
func BytesToBase64(bytes []byte) string {
	if bytes == nil {
		return ""
	}
	return base64.StdEncoding.EncodeToString(bytes)
}
