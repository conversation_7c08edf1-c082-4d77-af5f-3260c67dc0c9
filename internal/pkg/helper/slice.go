package helper

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strings"
)

// SliceIntUnique 数组去重
func SliceIntUnique(arr []int) []int {
	m := make(map[int]struct{}, len(arr))
	for _, v := range arr {
		m[v] = struct{}{}
	}
	arr = arr[:0]
	for k := range m {
		arr = append(arr, k)
	}
	return arr
}

// SliceSortUnique 数组去重-保留原本顺序
func SliceSortUnique[T any, K comparable](arr []T, key func(T) K) []T {
	m := make(map[K]struct{}, len(arr))
	indexList := make([]T, 0, len(arr))
	for _, v := range arr {
		k := key(v)
		if _, ok := m[k]; ok {
			continue
		}
		m[k] = struct{}{}
		indexList = append(indexList, v)
	}
	return indexList
}

// InSliceInt 判断元素是否在数组中
func InSliceInt(v int, s []int) bool {
	for _, i := range s {
		if i == v {
			return true
		}
	}
	return false
}

func SliceInt32ToInt(s []int32) []int {
	r := make([]int, 0, len(s))
	for _, v := range s {
		r = append(r, int(v))
	}
	return r
}

// InSlice 判断元素是否在数组中
func InSlice[T any](v T, s []T) bool {
	for _, i := range s {
		if reflect.DeepEqual(i, v) {
			return true
		}
	}
	return false
}

// SliceTo 将slice转换为指定类型
func SliceTo[T, R any](s []T, f func(T) (R, bool)) []R {
	r := make([]R, 0, len(s))
	for _, v := range s {
		if v, ok := f(v); ok {
			r = append(r, v)
		}
	}
	return r
}

// SliceEqual 判断两个slice是否相等
func SliceEqual[T comparable](s1, s2 []T) bool {
	if len(s1) != len(s2) {
		return false
	}
	for i := range s1 {
		if s1[i] != s2[i] {
			return false
		}
	}
	return true
}

// SliceToMap 将slice转换为map
func SliceToMap[T, K comparable, U any](s []T, f func(T) (K, U, bool)) map[K]U {
	m := make(map[K]U)
	for _, v := range s {
		k, u, ok := f(v)
		if !ok {
			continue
		}
		m[k] = u
	}
	return m
}

// SliceSplit 将slice分割成多个子数组
func SliceSplit[T any](slice []T, maxSize int) [][]T {
	var chunks [][]T
	for i := 0; i < len(slice); i += maxSize {
		end := i + maxSize
		if end > len(slice) {
			end = len(slice)
		}
		chunks = append(chunks, slice[i:end])
	}
	return chunks
}

// SliceJsonString 将切片转换为JSON字符串
func SliceJsonString[T any](slice []T) string {
	jsonBytes, err := json.Marshal(slice)
	if err != nil {
		return "[]"
	}
	return string(jsonBytes)
}

// SliceJoin 将切片转换为字符串，使用指定的分隔符
func SliceJoin[T any](slice []T, sep string) string {
	var strs []string
	for _, v := range slice {
		strs = append(strs, fmt.Sprintf("%v", v))
	}
	return strings.Join(strs, sep)
}

// SlicesIsExist 判断切片中是否存在指定元素
func SlicesIsExist[T any](slice []T, f func(T) bool) bool {
	for _, v := range slice {
		if f(v) {
			return true
		}
	}
	return false
}

// SlicesCount 统计切片中满足条件的元素个数
func SlicesCount[T any](slice []T, f func(T) bool) int64 {
	count := int64(0)
	for _, v := range slice {
		if f(v) {
			count++
		}
	}
	return count
}
