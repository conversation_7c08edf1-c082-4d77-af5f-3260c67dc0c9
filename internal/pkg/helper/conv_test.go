package helper

import (
	"testing"
)

func TestIsFloat(t *testing.T) {
	tests := []struct {
		name string
		args string
		want bool
	}{
		{"EmptyString", "", false},
		{"WhitespaceString", "   ", false},
		{"ValidInteger", "123", true},
		{"ValidFloat", "123.456", true},
		{"InvalidTrailingDot", "123.", false},
		{"ValidLeadingDot", ".456", true},
		{"InvalidMultipleDots", "123.456.789", false},
		{"InvalidAlphanumeric", "123abc", false},
		{"InvalidFloatWithAlphabetic", "123.abc", false},
		{"InvalidFloatWithAlphabeticSuffix", "123.456abc", false},
		{"InvalidAlphanumericWithDot", "123abc.456", false},
		{"InvalidMultipleDotsWithAlphabetic", "123.456.789abc", false},
		{"ValidFloatWithPercent", "123%", true},
		{"ValidFloatWithPercentSuffix", "123.456%", true},
		{"InvalidMultipleDotsWithPercent", "123.456.789%", false},
		{"InvalidAlphanumericWithPercent", "123abc%", false},
		{"InvalidFloatWithAlphabeticAndPercent", "123.abc%", false},
		{"InvalidFloatWithAlphabeticSuffixAndPercent", "123.456abc%", false},
		{"InvalidAlphanumericWithDotAndPercent", "123abc.456%", false},
		{"InvalidMultipleDotsWithAlphabeticAndPercent", "123.456.789abc%", false},
		{"InvalidMultipleDotsWithoutAlphabetic", "123.456.789", false},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := IsFloat(tt.args); got != tt.want {
				t.Errorf("IsFloat(\"%s\") = %v, want %v", tt.args, got, tt.want)
			}
		})
	}
}

// TestIsInt 测试 IsInt 函数的各种输入情况。
func TestIsInt(t *testing.T) {
	tests := []struct {
		name     string // 测试名称
		input    string
		expected bool
	}{
		{"ValidPositiveInteger", "123", true},
		{"ValidNegativeInteger", "-123", true},
		{"ValidPositiveSignedInteger", "+123", true},
		{"InvalidDecimalNumber", "123.45", false},
		{"InvalidAlphanumericString", "abc123", false},
		{"EmptyString", "", false},
		{"OnlyPlusSign", "+", false},
		{"OnlyMinusSign", "-", false},
		{"Zero", "0", true},
		{"NegativeZero", "-0", true},
		{"PositiveZero", "+0", true},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			result := IsInt(test.input)
			if result != test.expected {
				t.Errorf("IsInt(%q) = %v, want %v", test.input, result, test.expected)
			}
		})
	}
}
