package helper

import (
	"crypto/md5"
	"encoding/base64"
	"encoding/hex"
	"github.com/duke-git/lancet/v2/cryptor"
	"github.com/go-kratos/kratos/v2/log"
	"golang.org/x/crypto/bcrypt"
	"golang.org/x/crypto/sha3"
)

// MD5 MD5加密
func MD5(str string) string {
	h := md5.New()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func Sha384(str string) string {
	h := sha3.New384()
	h.Write([]byte(str))
	return hex.EncodeToString(h.Sum(nil))
}

func AesEcbEncrypt(key, data string) string {
	return base64.StdEncoding.EncodeToString(cryptor.AesEcbEncrypt([]byte(data), []byte(key)))
}

func AesEcbDecrypt(key, data string) string {
	defer func() {
		if err := recover(); err != nil {
			log.Errorf("AesEcbDecrypt error:%v", err)
		}
	}()
	buf, err := base64.StdEncoding.DecodeString(data)
	if err != nil {
		return ""
	}
	if buf == nil {
		return ""
	}
	return string(cryptor.AesEcbDecrypt(buf, []byte(key)))
}

// GetPwdHash 获取密码hash
func GetPwdHash(pwd string) (string, error) {
	if pwd == "" {
		return "", nil
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(pwd), bcrypt.DefaultCost)
	return string(hashedPassword), err
}
