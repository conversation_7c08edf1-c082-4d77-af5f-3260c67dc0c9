package helper

import (
	"fmt"
	"image"
	"io"
	"net/http"
	"time"
)

func GetAspectRatio(url string) (float64, error) {
	// 1. 发起HTTP请求
	client := http.Client{
		Timeout: 3 * time.Second,
	}
	resp, err := client.Get(url)
	if err != nil {
		return 0, fmt.Errorf("网络请求失败: %v", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("HTTP错误状态码: %d", resp.StatusCode)
	}

	// 2. 创建带缓冲的读取器（避免完全加载大文件）
	limitedReader := io.LimitReader(resp.Body, 2<<20) // 限制读取前2MB

	// 3. 解码图片配置（自动检测格式，支持jpeg/png/gif/webp）
	config, format, err := image.DecodeConfig(limitedReader)
	if err != nil {
		return 0, fmt.Erro<PERSON>("图片解码失败: %v (检测到格式: %s)", err, format)
	}

	// 4. 计算高宽比
	return float64(config.Height) / float64(config.Width), nil
}
