package helper

import (
	"context"
	"fmt"
	"github.com/google/cel-go/cel"
	"github.com/google/cel-go/common/types/ref"
	"math/big"
	"math/rand"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"

	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"github.com/go-kratos/kratos/v2"
	"github.com/go-kratos/kratos/v2/transport/http"
)

const PayOrderNumberLen = 7
const OrderNumberLen = 8
const ClientIpDefault = "*************"

func Rand(len int) string {
	if len <= 0 {
		return "0"
	}
	baseStr := "9"
	for i := len - 1; i > 0; i-- {
		baseStr += "0"
	}
	rand.NewSource(time.Now().UnixNano())

	base, _ := strconv.Atoi(baseStr)
	randNumber := rand.Intn(base)
	return fmt.Sprintf("%0*d", len, randNumber)
}

// CreateOrderNumber 生成订单号,时间+ len 位随机数
func CreateOrderNumber(generator *idgenerator.Generator) string {
	return strconv.Itoa(generator.GetOne())
}

// GetOrderNumber 生成订单号
func GetOrderNumber(generator *idgenerator.Generator) string {
	return CreateOrderNumber(generator)
}

// GetPayOrderNumber 生成支付单号
func GetPayOrderNumber(generator *idgenerator.Generator) string {
	return "P" + CreateOrderNumber(generator)
}

// GetRefundNo 生成退款单号
func GetRefundNo(generator *idgenerator.Generator) string {
	return "R" + CreateOrderNumber(generator)
}

func GetAfterAfterSaleNumber(generator *idgenerator.Generator) string {
	return "A" + CreateOrderNumber(generator)
}
func GetPaySerialNumber(generator *idgenerator.Generator) string {
	return "LX" + CreateOrderNumber(generator)
}

// IsAccountPhone 验证是否是手机号
func IsAccountPhone(str string) bool {
	re := regexp.MustCompile(`^1[3456789]\d{9}$`)
	return re.MatchString(str)
}

// IsAccountQQ 验证是否是QQ号
func IsAccountQQ(str string) bool {
	re := regexp.MustCompile(`^[1-9]\d{4,10}$`)
	return re.MatchString(str)
}

// IsAccountEmail 验证是否是邮箱
func IsAccountEmail(str string) bool {
	re := regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	return re.MatchString(str)
}

// IsAccountWechat 验证是否是微信号
func IsAccountWechat(str string) bool {
	re := regexp.MustCompile(`^[a-zA-Z0-9_-]{5,20}$`)
	return re.MatchString(str)
}

// GetClientIp 获取客户端ip
func GetClientIp(ctx context.Context) (clientIp string) {
	r, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return ClientIpDefault
	}
	xForwardedFor := r.Header.Get("X-Forwarded-For")
	ip := strings.TrimSpace(strings.Split(xForwardedFor, ",")[0])
	if ip != "" {
		return ip
	}

	ip = strings.TrimSpace(r.Header.Get("X-Real-Ip"))
	if ip != "" {
		return ip
	}

	if ip, _, err := net.SplitHostPort(strings.TrimSpace(r.RemoteAddr)); err == nil {
		return ip
	}

	return ClientIpDefault
}

func GetUserAgent(ctx context.Context) string {
	r, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return ""
	}
	return r.Header.Get("User-Agent")
}

func GetClientToken(ctx context.Context) (token string) {
	r, ok := http.RequestFromServerContext(ctx)
	if !ok {
		return
	}
	token = r.Header.Get("Authorization")
	token = strings.ReplaceAll(token, "Bearer ", "")
	return
}

// RandStr 随机生成一个大小写字母+数字的len位字符串
func RandStr(l int) string {
	rand.NewSource(time.Now().UnixNano())

	// 可选的字符集合
	charset := "abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ2345689"

	b := make([]byte, l)
	for i := range b {
		b[i] = charset[rand.Intn(len(charset))]
	}

	return string(b)
}

func GetGlobalCacheKey(ctx context.Context, key string) string {
	s, ok := kratos.FromContext(ctx)
	if !ok {
		return key
	}
	return fmt.Sprintf("%s:%s", s.Name(), key)
}
func TernaryString(condition bool, trueVal, falseVal string) string {
	if condition {
		return trueVal
	}
	return falseVal
}

func TernaryAny[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func IgnoreErr[T any](x T, err error) T {
	return x
}

// GetFileExt 获取文件后缀
func GetFileExt(fileName string) string {
	ext := strings.ToLower(fileName[strings.LastIndex(fileName, ".")+1:])
	return ext
}

// GetSkuName 获取SKU的完整商品名称
func GetSkuName(goodsName, skuName string) string {
	return fmt.Sprintf("%s %s", goodsName, strings.ReplaceAll(skuName, ",", " "))
}

func Cel(formula string) (ref.Val, error) {
	env, err := cel.NewEnv()
	if err != nil {
		return nil, err
	}
	ast, iss := env.Compile(formula)
	if iss.Err() != nil {
		return nil, iss.Err()
	}
	prog, err := env.Program(ast)
	if err != nil {
		return nil, err
	}
	out, _, err := prog.Eval(map[string]interface{}{})
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GetTransferToShopAccount 获取越级登录到到商城的登录账号
func GetTransferToShopAccount(code string) string {
	return fmt.Sprintf("%s_%s", "saas超管", code)
}

func EncodeBase62(input string) (string, error) {
	num, ok := new(big.Int).SetString(input, 10)
	if !ok {
		return "", fmt.Errorf("无效的数字: %s", input)
	}

	if num.Cmp(big.NewInt(0)) == 0 {
		return "0", nil
	}

	const charset = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
	var chars []byte
	base := big.NewInt(62)
	rem := new(big.Int)
	zero := big.NewInt(0)

	for num.Cmp(zero) > 0 {
		num.DivMod(num, base, rem)
		chars = append(chars, charset[rem.Int64()])
	}

	// 反转字符顺序
	for i, j := 0, len(chars)-1; i < j; i, j = i+1, j-1 {
		chars[i], chars[j] = chars[j], chars[i]
	}

	return string(chars), nil
}

func SlicesTo[T, R any](slices []T, f func(T) R) []R {
	if len(slices) == 0 {
		return nil
	}
	list := make([]R, 0, len(slices))
	for _, v := range slices {
		list = append(list, f(v))
	}
	return list
}

func ToTimeRange(rangeTimes []string) ([]time.Time, error) {
	if len(rangeTimes) == 0 {
		return nil, nil
	}
	res := make([]time.Time, 0, len(rangeTimes))
	for _, v := range rangeTimes {
		t, err := time.Parse("2006-01-02 15:04:05", v)
		if err != nil {
			return nil, err
		}
		res = append(res, t)
	}
	return res, nil
}
