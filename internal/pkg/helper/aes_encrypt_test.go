package helper

import (
	"fmt"
	"testing"
)

func TestAES256EcbEncode(t *testing.T) {
	type args struct {
		code string
		key  string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "生成成功",
			args: args{
				code: "4C8D372B29AFABE951C609313C13605B",
				key:  "dY2Nv9SlpmTeBjYMxUi14dxGfuVHcqlp",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := AES256EcbEncode(tt.args.code, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("AES256EcbEncode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			fmt.Println(got)
			//if got != tt.want {
			//	t.Errorf("AES256EcbEncode() got = %v, want %v", got, tt.want)
			//}
		})
	}
}
func TestAES256EcbDecode(t *testing.T) {
	type args struct {
		code string
		key  string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		{
			name: "成功解码",
			args: args{
				code: "rGh0vOC99HTyqd/9NQSTSg==",
				key:  "dY2Nv9SlpmTeBjYMxUi14dxGfuVHcqlp",
			},
			want:    "123456",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := AES256EcbDecode(tt.args.code, tt.args.key)
			if (err != nil) != tt.wantErr {
				t.Errorf("AES256EcbDecode() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("AES256EcbDecode() got = %v, want %v", got, tt.want)
			}
		})
	}
}
