package helper

import (
	"fmt"
	"testing"
)

func TestSha1(t *testing.T) {

	testCases := []struct {
		input    string
		expected string
	}{
		{"", "da39a3ee5e6b4b0d3255bfef95601890afd80709"},
		{"hello", "aaf4c61ddcc5e8a2dabede0f3b482cd9aea9434d"},
		{"world", "7c211433f02071597741e6ff5a8ea34789abbf43"},
	}

	for _, tc := range testCases {
		actual := Sha384(tc.input)
		if actual != tc.expected {
			t.Errorf("MD5(%q) = %q, expected %q", tc.input, actual, tc.expected)
		}
	}
}

func TestAesEcbEncrypt(t *testing.T) {
	//ecbEncrypt1 := AesEcbEncrypt("8LcegFCMqJ2GGdUatMsLHiA0I7DJSfyK", "Luo123456")
	//fmt.Println(ecbEncrypt1)

	password := "Ab1234569"
	//fmt.Println(ValidatePassword(password))
	//hashedPassword, _ := GetPwdHash(password)
	//fmt.Println(string(hashedPassword))
	ecbEncrypt2 := AesEcbEncrypt("8LcegFCMqJ2GGdUatMsLHiA0I7DJSfyK", password)
	fmt.Println(ecbEncrypt2)

	aesEcbDecrypt := AesEcbDecrypt("8LcegFCMqJ2GGdUatMsLHiA0I7DJSfyK", ecbEncrypt2)
	fmt.Println(aesEcbDecrypt)
	//err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(aesEcbDecrypt))
	//if err != nil {
	//	fmt.Println("账号或密码错误")
	//}
	//fmt.Println("账号或密码正确")

}
