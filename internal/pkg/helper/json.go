package helper

import (
	"fmt"
	"reflect"
	"strings"
)

func GetJsonTagValues(info interface{}, tags ...string) ([]interface{}, error) {
	if len(tags) == 0 {
		return nil, fmt.<PERSON><PERSON><PERSON>("no tags")
	}
	value := reflect.ValueOf(info)
	if value.Kind() != reflect.Ptr {
		return nil, fmt.<PERSON><PERSON><PERSON>("not a pointer")
	}
	value = value.Elem()
	typ := value.Type()
	fieldMap := make(map[string]interface{})
	for i := 0; i < value.NumField(); i++ {
		field := value.Field(i)
		fieldType := typ.Field(i)
		jsonTag := fieldType.Tag.Get("json")
		if jsonTag == "" || jsonTag == "-" {
			continue
		}
		tagParts := strings.Split(jsonTag, ",")
		jsonKey := tagParts[0]
		fieldMap[jsonKey] = field.Interface()
	}
	if len(fieldMap) == 0 {
		return nil, fmt.<PERSON><PERSON><PERSON>("no tag found")
	}
	res := make([]interface{}, len(tags))
	for k, tag := range tags {
		res[k] = fieldMap[tag]
	}
	return res, nil
}
