package helper

import (
	"bytes"
	"strconv"
	"strings"
	"sync"
	"unicode"
)

var (
	bfPool = sync.Pool{
		New: func() interface{} {
			return bytes.NewBuffer([]byte{})
		},
	}
)

func Utf8StrLength(s string) int {
	return len([]rune(s))
}

func RemoveLastStr(str string) string {
	return SubStr(str, 0, Utf8StrLength(str)-1)
}

func SubStr(str string, start, length int) string {
	rs := []rune(str)
	rl := len(rs)
	end := 0

	if start < 0 {
		start = rl - 1 + start
	}
	end = start + length

	if start > end {
		start, end = end, start
	}

	if start < 0 {
		start = 0
	}
	if end > rl {
		end = rl
	}
	return string(rs[start:end])
}

// JoinInt32s format int32 slice like:n1,n2,n3.
func JoinInt32s(is []int, p string) string {
	if len(is) == 0 {
		return ""
	}
	if len(is) == 1 {
		return strconv.FormatInt(int64(is[0]), 10)
	}
	buf := bfPool.Get().(*bytes.Buffer)
	for _, i := range is {
		buf.WriteString(strconv.FormatInt(int64(i), 10))
		buf.WriteString(p)
	}
	if buf.Len() > 0 {
		buf.Truncate(buf.Len() - 1)
	}
	s := buf.String()
	buf.Reset()
	bfPool.Put(buf)
	return s
}

// JoinInt64s format int64 slice like:n1,n2,n3.
func JoinInt64s(is []int64, p string) string {
	if len(is) == 0 {
		return ""
	}
	if len(is) == 1 {
		return strconv.FormatInt(is[0], 10)
	}
	buf := bfPool.Get().(*bytes.Buffer)
	for _, i := range is {
		buf.WriteString(strconv.FormatInt(i, 10))
		buf.WriteString(p)
	}
	if buf.Len() > 0 {
		buf.Truncate(buf.Len() - 1)
	}
	s := buf.String()
	buf.Reset()
	bfPool.Put(buf)
	return s
}

// 为每个元素添加单引号
func JoinString(is []string) string {
	// 为每个元素添加单引号
	for i, s := range is {
		is[i] = "'" + s + "'"
	}
	// 使用Join函数将修改后的切片元素拼接成一个字符串
	quotedStr := strings.Join(is, ", ")
	return quotedStr
}
func MaskMiddleCharacters(input string) string {
	if len(input) < 8 {
		return "字符串长度必须至少为8个字符"
	}

	start := input[:4]
	end := input[len(input)-4:]
	middle := strings.Repeat("*", len(input)-8)

	return start + middle + end
}
func ValidatePassword(password string) bool {
	if len(password) < 8 || len(password) > 20 {
		return false
	}
	hasUpperCase := false
	hasLowerCase := false
	hasDigit := false

	for i, char := range password {
		if unicode.IsUpper(char) {
			hasUpperCase = true
		} else if unicode.IsLower(char) {
			hasLowerCase = true
		} else if unicode.IsDigit(char) {
			hasDigit = true
		}
		if i > 0 {
			prevChar := rune(password[i-1])
			if char == prevChar {
				return false
			}
		}
	}
	return hasUpperCase && hasLowerCase && hasDigit
}

func HasChineseCharacters(s string) bool {
	for _, r := range s {
		if unicode.Is(unicode.Scripts["Han"], r) {
			return true
		}
	}
	return false
}

// PrivacyPhone 手机号进行隐私处理
func PrivacyPhone(phone string) string {
	if len(phone) < 11 {
		return phone
	}
	return phone[:3] + "****" + phone[7:]
}
