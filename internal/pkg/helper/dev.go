package helper

import (
	"cardMall/internal/conf"
)

const (
	dev   = "dev"
	local = "local"
	test  = "test"
)

// IsProduction 生产环境
func IsProduction(env conf.Server_Env) bool {
	return env == conf.Server_PROD
}

// IsPre 预发环境
func IsPre(env conf.Server_Env) bool {
	return env == conf.Server_PRE
}

// IsDev 开发环境
func IsDev(env conf.Server_Env) bool {
	return IsLocal(env) || IsTest(env)
}

// IsLocal 开发环境
func IsLocal(env conf.Server_Env) bool {
	return env == conf.Server_LOCAL
}

// IsTest 开发环境
func IsTest(env conf.Server_Env) bool {
	return env == conf.Server_TEST
}
