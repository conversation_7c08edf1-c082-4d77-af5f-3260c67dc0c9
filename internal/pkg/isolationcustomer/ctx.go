package isolationcustomer

import (
	"context"
	"net/url"
	"strconv"

	"cardMall/api/apierr"
)

const (
	customerIdCtxKey      = 1
	shopIdCtxKey          = 2
	sassDbCtxKey          = 3
	customerDisableCtxKey = 4
	userIdCtxKey          = 5
)

const (
	OfficialSupplierIdMax = 1000
	SaasPlatformCustomer  = 1
	SaasPlatformSupplier  = 1
)

// WithUserIdCtx 将用户id写入上下文
func WithUserIdCtx(ctx context.Context, userId int) context.Context {
	return context.WithValue(ctx, userIdCtxKey, userId)
}

// WithCustomerIdCtx 将客户id写入上下文
func WithCustomerIdCtx(ctx context.Context, customerId int) context.Context {
	return context.WithValue(ctx, customerIdCtxKey, customerId)
}

// WithDisableCustomerCtx 禁用customer_id 查询
func WithDisableCustomerCtx(ctx context.Context) context.Context {
	return context.WithValue(ctx, customerDisableCtxKey, struct{}{})
}

// WithEnableCustomerCtx 启用customer_id 查询
func WithEnableCustomerCtx(ctx context.Context) context.Context {
	return context.WithValue(ctx, customerDisableCtxKey, nil)
}

// WithCustomerIdAndEnableCtx 将客户id写入上下文并启用条件查询或写入
func WithCustomerIdAndEnableCtx(ctx context.Context, customerId int) context.Context {
	ctx = WithCustomerIdCtx(ctx, customerId)
	return WithEnableCustomerCtx(ctx)
}

func WithShopIdCtx(ctx context.Context, shopId *int) context.Context {
	if shopId == nil {
		return WithDisableShopCtx(ctx)
	}
	return context.WithValue(ctx, shopIdCtxKey, *shopId)
}

// WithDisableShopCtx 将店铺id写入上下文
func WithDisableShopCtx(ctx context.Context) context.Context {
	return context.WithValue(ctx, shopIdCtxKey, -1)
}

// WithSassDbCtx 将指定取sass数据库写入上下文
func WithSassDbCtx(ctx context.Context) context.Context {
	return context.WithValue(ctx, sassDbCtxKey, struct{}{})
}

// GetCustomerId 从上下文中获取客户id
func GetCustomerId(ctx context.Context) (*int, bool) {
	v, ok := ctx.Value(customerIdCtxKey).(int)
	if !ok {
		return nil, false
	}
	return &v, true
}

// GetShopId 从上下文中获取店铺id
func GetShopId(ctx context.Context) (*int, bool) {
	v, ok := ctx.Value(shopIdCtxKey).(int)
	if !ok {
		return nil, false
	}
	if v <= -1 {
		return nil, true
	}
	return &v, true
}

// GetUserId 从上下文中获取用户id
func GetUserId(ctx context.Context) (*int, bool) {
	v, ok := ctx.Value(userIdCtxKey).(int)
	if !ok {
		return nil, false
	}
	if v <= -1 {
		return nil, true
	}
	return &v, true
}

// GetCustomerIdZero 从上下文中获取客户id
func GetCustomerIdZero(ctx context.Context) int {
	id, _ := GetCustomerId(ctx)
	if id == nil {
		return 0
	}
	return *id
}

// GetShopIdZero 从上下文中获取店铺id
func GetShopIdZero(ctx context.Context) int {
	id, _ := GetShopId(ctx)
	if id == nil {
		return 0
	}
	return *id
}

// GetUserIdZero 从上下文中获取用户id
func GetUserIdZero(ctx context.Context) int {
	id, _ := GetUserId(ctx)
	if id == nil {
		return 0
	}
	return *id
}

// IsSassDbCtx 判断是否是sass数据库
func IsSassDbCtx(ctx context.Context) bool {
	_, ok := ctx.Value(sassDbCtxKey).(struct{})
	return ok
}

func WithOfficialSupplierPlatformCtx(ctx context.Context) context.Context {
	return WithCustomerIdCtx(ctx, SaasPlatformCustomer)
}

// IsDisableShopCtx 判断是否不需要设置shop条件
func IsDisableShopCtx(ctx context.Context) bool {
	shopId, _ := GetShopId(ctx)
	return shopId == nil
}

// IsDisableCustomerCtx 判断是否不需要设置customer条件
func IsDisableCustomerCtx(ctx context.Context) bool {
	if _, ok := ctx.Value(customerDisableCtxKey).(struct{}); ok {
		return true
	}
	return false
}

// WithSupplierAndDisableShopCtx 通过供应商ID切库并禁用shop_id查询
func WithSupplierAndDisableShopCtx(ctx context.Context, supplierId int) context.Context {
	if supplierId <= OfficialSupplierIdMax {
		ctx = WithOfficialSupplierPlatformCtx(ctx)
	}
	return WithDisableShopCtx(ctx)
}

// WithCustomerAndDisableShopCtx 通过企业ID切库并禁用shop_id查询
func WithCustomerAndDisableShopCtx(ctx context.Context, customerId int) context.Context {
	ctx = WithCustomerIdAndEnableCtx(ctx, customerId)
	return WithDisableShopCtx(ctx)
}

// WithDisableCtx 禁用企业和商城ID查询
func WithDisableCtx(ctx context.Context) context.Context {
	return WithDisableShopCtx(WithDisableCustomerCtx(ctx))
}

// WithCustomerAndShopCtx 同时设置企业ID和商城ID
func WithCustomerAndShopCtx(ctx context.Context, customerId, shopId int) context.Context {
	return WithCustomerIdAndEnableCtx(WithShopIdCtx(ctx, &shopId), customerId)
}

// GetOpenApiCtx 获取对外接口的企业ID并放到ctx中
func GetOpenApiCtx(ctx context.Context, query url.Values) (context.Context, error) {
	customerId, err := strconv.Atoi(query.Get("customerId"))
	if err != nil {
		return nil, err
	}
	if customerId == 0 {
		return nil, apierr.ErrorParam("customerId参数错误")
	}
	return WithCustomerAndDisableShopCtx(ctx, customerId), nil
}

// GetOpenApiShopCtx 获取对外接口的企业ID并放到ctx中
func GetOpenApiShopCtx(ctx context.Context, query url.Values) (context.Context, error) {
	customerId, err := strconv.Atoi(query.Get("customerId"))
	if err != nil {
		return nil, err
	}
	if customerId == 0 {
		return nil, apierr.ErrorParam("customerId参数错误")
	}
	shopId, err := strconv.Atoi(query.Get("shopId"))
	if err != nil {
		return nil, err
	}
	if shopId == 0 {
		return nil, apierr.ErrorParam("shopId参数错误")
	}
	return WithCustomerAndShopCtx(ctx, customerId, shopId), nil
}
