package jwt

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/conf"
	"flag"
	"fmt"
	"github.com/go-kratos/kratos/v2/config"
	"github.com/go-kratos/kratos/v2/config/file"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"os"
	"testing"
)

func NewTestJWT(t *testing.T) *JWT {
	var flagconf string
	flag.StringVar(&flagconf, "conf", "D:\\lansexiongdi\\cardMall\\src\\cardMall\\configs", "config path, eg: -conf config.yaml")
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", 1,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	)
	c := config.New(
		config.WithSource(
			file.NewSource(flagconf),
		),
	)
	defer c.Close()

	if err := c.Load(); err != nil {
		panic(err)
	}

	var bc conf.Bootstrap
	if err := c.Scan(&bc); err != nil {
		panic(err)
	}
	logHelper := log.NewHelper(logger)
	return NewJWT(logHelper, &bc)
}

func TestJWT_GetAdminToken(t *testing.T) {
	jwt := NewTestJWT(t)
	admin := &do.AdminDo{
		Id:         1,
		Name:       "test",
		CustomerId: 1,
		ShopId:     0,
	}
	token, err := jwt.GetAdminToken(admin)
	if err != nil {
		panic(err)
	}
	fmt.Println("token:", token)
}

func TestJWT_GetAdminInfo(t *testing.T) {
	var token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************.fe1UeAb6vqxGnrmm1gufBDuGhbnBHI7iRBWPwJopKfQ"
	jwt := NewTestJWT(t)
	adminInfo, err := jwt.GetAdminClaims(token)
	if err != nil {
		jwt.hLog.Error(err)
		return
	}
	fmt.Printf("admininfo:%#v", adminInfo)
}
