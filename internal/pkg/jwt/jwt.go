package jwt

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/constants"
	"errors"
	"fmt"
	"strconv"
	"time"

	"cardMall/api/apierr"
	"cardMall/internal/conf"
	appDO "cardMall/internal/module/appbiz/do"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/golang-jwt/jwt/v4"
)

type JWT struct {
	hLog *log.Helper
	conf *conf.Bootstrap
}

func NewJWT(hLog *log.Helper, conf *conf.Bootstrap) *JWT {
	return &JWT{hLog: hLog, conf: conf}
}
func (j *JWT) GetJwtTimeout() time.Duration {
	duration := j.conf.GetJwt().GetTimeout().AsDuration()
	return duration
}

// getToken JWT 加密
func (j *JWT) getToken(claims jwt.Claims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	signToken, err := token.SignedString([]byte(j.conf.GetJwt().GetKey()))
	if err != nil {
		j.hLog.<PERSON>rrorf("生成登录token失败,claims:%v,key:%s, err:", claims, j.conf.GetJwt().GetKey(), err)
		return "", err
	}
	return signToken, nil
}

// parseToken JWT 解密
func (j *JWT) parseToken(signedToken string, claims jwt.Claims) error {
	token, err := jwt.ParseWithClaims(signedToken, claims, func(token *jwt.Token) (interface{}, error) {
		if token.Method != jwt.SigningMethodHS256 {
			return nil, fmt.Errorf("无效的签名方法：%v", token.Header["alg"])
		}
		return []byte(j.conf.GetJwt().GetKey()), nil
	})
	if token == nil || err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return apierr.ErrorNotLogin("登录信息失效，请重新登录")
		}
		return apierr.ErrorNotLogin("登录失效，请重新登录")
	}
	if !token.Valid {
		return apierr.ErrorNotLogin("登录失效，请重新登录")
	}
	return err
}

// GetAdminToken B端登录获取登录token
func (j *JWT) GetAdminToken(adminDo *do.AdminDo) (string, error) {
	var claims = AdminClaims{
		pClaims: pClaims{
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:    constants.AppName,
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.conf.GetJwt().GetTimeout().AsDuration())),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				ID:        strconv.Itoa(adminDo.Id),
			}},
		Name:       adminDo.Account,
		RoleId:     adminDo.RoleID,
		CustomerId: adminDo.CustomerId,
		ShopId:     adminDo.ShopId,
		AdminType:  adminDo.Type,
		SupplierId: adminDo.SupplierID,
	}
	return j.getToken(claims)
}

// GetSaasToShopToken saas账号登录商城获取登录token
func (j *JWT) GetSaasToShopToken(adminDo *do.AdminDo, saasUserId int) (string, error) {
	var claims = AdminClaims{
		pClaims: pClaims{
			RegisteredClaims: jwt.RegisteredClaims{
				Issuer:    constants.AppName,
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(j.conf.GetJwt().GetTimeout().AsDuration())),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				ID:        strconv.Itoa(adminDo.Id),
			}},
		Name:       adminDo.Account,
		RoleId:     adminDo.RoleID,
		CustomerId: adminDo.CustomerId,
		ShopId:     adminDo.ShopId,
		AdminType:  adminDo.Type,
		SupplierId: adminDo.SupplierID,
		SaasUser: &SaasAdminClaims{
			UserId: saasUserId,
			Mobile: adminDo.Mobile,
		},
	}
	return j.getToken(claims)
}

func (j *JWT) GetAdminClaims(token string) (*AdminClaims, error) {
	var claims = &AdminClaims{}
	err := j.parseToken(token, claims)
	if err != nil {
		return nil, err
	}
	return claims, nil
}

// GetAppClaims C端登录获取用户信息
func (j *JWT) GetAppClaims(token string) (*ClientClaims, error) {
	var claims = &ClientClaims{}
	err := j.parseToken(token, claims)
	if err != nil {
		return nil, err
	}
	return claims, nil
}

// GetAppToken C端登录获取登录token
func (j *JWT) GetAppToken(userInfo *appDO.UserDo) (string, error) {
	var claims = ClientClaims{
		pClaims: pClaims{
			RegisteredClaims: jwt.RegisteredClaims{
				ID:        strconv.Itoa(userInfo.Id),
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(10 * 365 * 86400 * time.Second)),
			}},
		UserId:           userInfo.Id,
		NickName:         userInfo.NickName,
		Avatar:           userInfo.AvatarUrl,
		OpenId:           userInfo.WxOpenId,
		PhoneNumber:      userInfo.PhoneNumber,
		ClientType:       userInfo.ClientType,
		WxOfficialOpenId: userInfo.WxOfficialOpenId,
		CustomerId:       userInfo.CustomerId,
		ShopId:           userInfo.ShopId,
	}
	return j.getToken(claims)
}

// GetAppInfo C端登录获取用户信息
func (j *JWT) GetAppInfo(token string) (*appDO.UserDo, error) {
	var claims = &ClientClaims{}
	err := j.parseToken(token, claims)
	if err != nil {
		return nil, err
	}

	var clientInfo = &appDO.UserDo{
		Id:          claims.UserId,
		NickName:    claims.NickName,
		AvatarUrl:   claims.Avatar,
		WxOpenId:    claims.OpenId,
		PhoneNumber: claims.PhoneNumber,
		ClientType:  claims.ClientType,
	}
	return clientInfo, nil
}
