package openapi

import (
	"cardMall/internal/pkg/helper"
	"fmt"
)

// GeneratePushSign 生成推送签名
func GeneratePushSign(mchId, secret, body string) string {
	// md5Str = md5(body+mchId+secret)
	md5Str := fmt.Sprintf("%s%s%s", body, mchId, secret)
	sign := helper.MD5(md5Str)
	return sign
}

// GenerateRequestSign 生成商户请求我们的的签名
func GenerateRequestSign(path, body string, mchId, secret string, timestamp int) string {
	//calcStr = path + body + timestamp + mchId + secret
	calcStr := fmt.Sprintf("%s%s%d%s%s", path, body, timestamp, mchId, secret)
	sign := helper.MD5(calcStr)
	return sign
}
