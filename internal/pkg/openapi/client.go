package openapi

import (
	"bytes"
	"cardMall/api/apierr"
	"cardMall/internal/conf"
	"cardMall/internal/pkg/helper"
	"context"
	"encoding/json"
	"github.com/duke-git/lancet/v2/retry"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"time"
)

type Client struct {
	secret string
	url    string
	c      *conf.Bootstrap
}

func NewClient(url, enSecret string, c *conf.Bootstrap) (*Client, error) {
	// 解密密钥
	secret, err := helper.AES256EcbDecode(enSecret, c.OpenApi.ShopSecretDataSecret)
	if err != nil {
		return nil, apierr.ErrorException("解密店铺密钥失败")
	}
	return &Client{secret: secret, url: url, c: c}, nil
}

// Push 推送
func (c *Client) Push(ctx context.Context, url, body, mchId string) (string, error) {
	// 加签
	sign := GeneratePushSign(mchId, c.secret, body)
	// 创建 HTTP 请求的客户端
	request, err := http.NewRequest("POST", url, bytes.NewBuffer([]byte(body)))
	if err != nil {
		return "", errors.WithMessage(err, "创建Request失败")
	}
	request.Header.Set("Content-Type", "application/json")
	request.Header.Set("Lsxd-Signature", sign)

	// 发起请求
	var resp *http.Response
	var respBody []byte
	_ = retry.Retry(func() error {
		httpClient := &http.Client{
			Timeout: 10 * time.Second,
		}
		resp, err = httpClient.Do(request)
		if err != nil {
			return err
		}
		respBody, err = io.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		if resp.StatusCode == http.StatusOK {
			respMap := make(map[string]string)
			err = json.Unmarshal(respBody, &respMap)
			if err != nil {
				return errors.WithMessage(err, "解析响应body失败")
			}
			if respMap["code"] == "SUCCESS" {
				return nil
			}
			return errors.Errorf("响应body不符合法，code只能是SUCCESS才表示成功")
		}
		// 标识为失败
		err = errors.Errorf("响应状态码：%d", resp.StatusCode)
		return err
	}, retry.RetryTimes(10), retry.RetryDuration(time.Second*10))

	return string(respBody), err
}
