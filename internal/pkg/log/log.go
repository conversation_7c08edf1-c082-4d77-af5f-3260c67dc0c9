package log

import (
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"os"
)

type BusinessLogger struct {
	*log.Helper
}

type MeiTuanLogger struct {
	*log.Helper
}

type NotifyLogger struct {
	*log.Helper
}

type PayLogger struct {
	*log.Helper
}

type QianZhuLogger struct {
	*log.Helper
}

type RechargeLogger struct {
	*log.Helper
}

type FileLogger struct {
	PayLogHelper      *PayLogger
	NotifyLogHelper   *NotifyLogger
	BusinessLogHelper *BusinessLogger
	RechargeLogHelper *RechargeLogger
	MeiTuanLogger     *MeiTuanLogger
	QianZhuLogger     *QianZhuLogger
}

func NewFileLogger(id, name, version string) *FileLogger {
	return &FileLogger{
		PayLogHelper:      &PayLogger{Helper: GetHelperLogger("pay", id, name, version)},
		NotifyLogHelper:   &NotifyLogger{Helper: GetHelperLogger("notify", id, name, version)},
		BusinessLogHelper: &BusinessLogger{Helper: GetHelperLogger("business", id, name, version)},
		RechargeLogHelper: &RechargeLogger{Helper: GetHelperLogger("recharge", id, name, version)},
		MeiTuanLogger:     &MeiTuanLogger{Helper: GetHelperLogger("meituan", id, name, version)},
		QianZhuLogger:     &QianZhuLogger{Helper: GetHelperLogger("qianzhu", id, name, version)},
	}
}

func GetHelperLogger(fileName, id, name, version string) *log.Helper {
	logFile := fileName + ".log"
	f, err := os.OpenFile("./"+logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
	if err != nil {
		panic("打开" + logFile + "失败:" + err.Error())
	}
	return log.NewHelper(log.With(
		log.NewStdLogger(f),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", id,
		"service.name", name,
		"service.version", version,
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
	))
}
