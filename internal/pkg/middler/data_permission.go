package middler

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/go-kratos/kratos/v2/middleware"
	"github.com/go-kratos/kratos/v2/transport"
)

type includeUsersContextKey struct{}

// DataPermission 数据权限绑定中间件
func DataPermission() middleware.Middleware {
	return func(handler middleware.Handler) middleware.Handler {
		return func(ctx context.Context, req interface{}) (interface{}, error) {
			if tp, ok := transport.FromServerContext(ctx); ok {
				// 获取Include-Users请求头数据
				includeUsersStr := tp.RequestHeader().Get("Include-Users")
				includeUsersStr = fmt.Sprintf("[%s]", includeUsersStr)
				var includeUsers []int
				if err := json.Unmarshal([]byte(includeUsersStr), &includeUsers); err != nil {
					// 没有此数据时候， 默认返回当前登录用户ID
					includeUsers = []int{int(GetUserId(ctx))}
				}
				ctx = context.WithValue(ctx, includeUsersContextKey{}, includeUsers)
			}
			return handler(ctx, req)
		}
	}
}

// GetIncludeUsers 获取权限用户ID列表
func GetIncludeUsers(ctx context.Context) []int {
	if v, ok := ctx.Value(includeUsersContextKey{}).([]int); ok {
		return v
	}
	// 没有此数据时候， 默认返回当前登录用户ID
	return []int{}
}
