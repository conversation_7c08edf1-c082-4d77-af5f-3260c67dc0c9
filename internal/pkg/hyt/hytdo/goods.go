package hytdo

import (
	"cardMall/internal/pkg/hyt/hytvo"
	"fmt"
	"sort"
	"strings"
)

type GoodsListReq struct {
	GoodsNums       []string             `json:"goods_nums,omitempty"`
	Status          hytvo.GoodsStatusObj `json:"status,omitempty"`
	Title           string               `json:"title,omitempty"`
	IsHot           hytvo.GoodsIsHotObj  `json:"is_hot,omitempty"`
	GoodsCategoryId int                  `json:"goods_category_id,omitempty"`
	Brand           string               `json:"brand,omitempty"`
}

type GoodsListReqNext struct {
	*GoodsListReq
	NextId int `json:"next_id,omitempty"`
	Limit  int `json:"limit,omitempty"`
}

type GoodsListResp struct {
	List   []*GoodsListItem `json:"list"`
	NextId int              `json:"next_id"`
}

type GoodsListChild struct {
	Brand             string  `json:"brand"`
	ExternalPrice     float64 `json:"external_price"`
	ExternalUrl       string  `json:"external_url"`
	GoodsAttributes   string  `json:"goods_attributes"`
	GoodsBarCode      string  `json:"goods_bar_code"`
	GoodsCode         string  `json:"goods_code"`
	GoodsIllustration string  `json:"goods_illustration"`
	GoodsNum          string  `json:"goods_num"`
	Introduction      string  `json:"introduction"`
	Price             float64 `json:"price"`
	SellByDate        int     `json:"sell_by_date"`
	SellByDateUnit    string  `json:"sell_by_date_unit"`
	Title             string  `json:"title"`
	Unit              string  `json:"unit"`
}

type GoodsListMedia struct {
	Sort int                     `json:"sort"`
	Type hytvo.GoodsMediaTypeObj `json:"type"`
	Url  string                  `json:"url"`
}

type GoodsCategoryItem struct {
	Id   int    `json:"id"`
	Name string `json:"name"`
}

type GoodsListItem struct {
	Brand             string               `json:"brand"`
	Child             []*GoodsListChild    `json:"child"`
	Discount          float64              `json:"discount"`
	ExternalPrice     float64              `json:"external_price"` // 电商平台销售价
	ExternalUrl       string               `json:"external_url"`
	GoodsAttributes   string               `json:"goods_attributes"`
	GoodsBarCode      string               `json:"goods_bar_code"`
	GoodsCode         string               `json:"goods_code"`
	GoodsIllustration string               `json:"goods_illustration"`
	GoodsNum          string               `json:"goods_num"`
	Introduction      string               `json:"introduction"`
	IsComposeGoods    int                  `json:"is_compose_goods"`
	IsHot             hytvo.GoodsIsHotObj  `json:"is_hot"`
	Media             []*GoodsListMedia    `json:"media"`
	Price             float64              `json:"price"`        // 授权价
	SalesPrice        float64              `json:"sales_price"`  // 建议销售价
	MarketPrice       float64              `json:"market_price"` // 市场价
	SellByDate        int                  `json:"sell_by_date"`
	SellByDateUnit    string               `json:"sell_by_date_unit"`
	Status            int                  `json:"status"`
	Title             string               `json:"title"`
	Unit              string               `json:"unit"`
	TaxRate           float64              `json:"tax_rate"`
	SupplierId        int                  `json:"supplier_id"`
	SupplierName      string               `json:"supplier_name"`
	GoodsCategoryList []*GoodsCategoryItem `json:"goods_category_list"`
}

type GoodsListItemAsync struct {
	Goods []*GoodsListItem
	Err   error
}

func (g *GoodsListItem) GetSuggestPrice() float64 {
	return g.SalesPrice
}

func (g *GoodsListItem) GetSupplierPrice() float64 {
	return g.Price
}

func (g *GoodsListItem) GetMarketPrice() float64 {
	return g.MarketPrice
}

func (g *GoodsListItem) Equal(goods *GoodsListItem) bool {
	if g.GetBrand() != goods.GetBrand() {
		return false
	}
	if g.GoodsNum != goods.GoodsNum {
		return false
	}
	if len(g.GoodsCategoryList) != len(goods.GoodsCategoryList) {
		return false
	} else {
		if g.GoodsCategoryList[0].Id != goods.GoodsCategoryList[0].Id {
			return false
		}
	}
	//if g.ExternalPrice != goods.ExternalPrice {
	//	return false
	//}
	if g.GoodsBarCode != goods.GoodsBarCode {
		return false
	}
	if g.GoodsCode != goods.GoodsCode {
		return false
	}
	if g.Title != goods.Title {
		return false
	}
	if g.GetMarketPrice() != goods.GetMarketPrice() {
		return false
	}
	if g.GetSuggestPrice() != goods.GetSuggestPrice() {
		return false
	}
	if g.GetSupplierPrice() != goods.GetSupplierPrice() {
		return false
	}
	if g.GetTaxRate() != goods.GetTaxRate() {
		return false
	}
	if g.GetDetail() != goods.GetDetail() {
		return false
	}
	if len(g.GetMainImage()) != len(goods.GetMainImage()) {
		return false
	} else {
		img1 := g.GetMainImages()
		img2 := goods.GetMainImages()
		sort.Strings(img1)
		sort.Strings(img2)
		for i := 0; i < len(img1); i++ {
			if img1[i] != img2[i] {
				return false
			}
		}
	}
	if g.SupplierId != goods.SupplierId {
		return false
	}
	if g.SupplierName != goods.SupplierName {
		return false
	}
	return true
}

func (g *GoodsListItem) GetBrand() string {
	brand := g.Brand
	brand = strings.TrimLeft(brand, " ")
	brand = strings.TrimRight(brand, " ")
	return brand
}

func (g *GoodsListItem) BrandIsEmpty() bool {
	if g.GetBrand() == "" {
		return true
	}
	if g.GetBrand() == "-" {
		return true
	}
	return false
}

func (g *GoodsListItem) GetSpecStr() string {
	return "默认规格"
	specStr := g.GoodsAttributes
	specStr = strings.ReplaceAll(specStr, "&nbsp; ", "")
	specStr = strings.ReplaceAll(specStr, "&nbsp;", "")
	specStr = strings.ReplaceAll(specStr, "<p>", "")
	specStr = strings.ReplaceAll(specStr, "</p>", "")
	specStr = strings.ReplaceAll(specStr, "<br>", "")
	return specStr
}

func (g *GoodsListItem) GetTaxRate() float64 {
	return g.TaxRate
}

func (g *GoodsListItem) GetLastCategoryId() int {
	// 获取最后一级分类id
	id := 0
	for _, category := range g.GoodsCategoryList {
		id = category.Id
	}
	return id
}

func (g *GoodsListItem) GetGoodsNum() string {
	return g.GoodsNum
}

func (g *GoodsListItem) GetMainImage() string {
	for _, media := range g.Media {
		if media.Type == hytvo.GoodsMediaTypeMobile {
			return media.Url
		}
	}
	return ""
}

func (g *GoodsListItem) GetMainImages() []string {
	images := make([]string, 0)
	for _, media := range g.Media {
		if media.Type == hytvo.GoodsMediaTypeMobile || media.Type == hytvo.GoodsMediaTypePC {
			images = append(images, media.Url)
		}
	}
	return images
}

func (g *GoodsListItem) GetDetail() string {
	detail := g.Introduction
	if g.GoodsIllustration != "" {
		detail = detail + "<br />" + g.GoodsIllustration
	}
	if g.GoodsAttributes != "" {
		detail = detail + "<br />" + g.GoodsAttributes
	}

	for _, media := range g.Media {
		if media.Type == hytvo.GoodsMediaTypeDetail {
			detail = detail + fmt.Sprintf("<br /><p><img src=\"%s\"></p>", media.Url)
		}
	}
	return detail
}

type GoodsDetailReq struct {
	GoodsNum string `json:"goods_num,omitempty"`
}

type GoodsStocksReq struct {
	GoodsNums []string `json:"goods_nums,omitempty"`
}

type GoodsStocksResp struct {
	List []*GoodsStocksItem `json:"list"`
}

type GoodsStocksItem struct {
	GoodsNum      string `json:"goods_num"`
	StockIsEnough bool   `json:"stock_is_enough"`
}
