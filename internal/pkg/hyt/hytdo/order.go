package hytdo

import "cardMall/internal/pkg/hyt/hytvo"

type OrderInfoRequest struct {
	OrderNum         string `json:"order_num,omitempty" validate:"required_without=CustomerOrderNum"`
	CustomerOrderNum string `json:"customer_order_num,omitempty" validate:"required_without=OrderNum"`
	SubOrderNum      string `json:"sub_order_num,omitempty"`
}

// 订单基础信息
type OrderBasic struct {
	ID                    int                    `json:"id"`
	OrderNum              string                 `json:"order_num"`
	SubOrderNum           string                 `json:"sub_order_num"`
	Consignee             string                 `json:"consignee"`
	ConsigneeAddress      string                 `json:"consignee_address"`
	ConsigneeProvinceCode string                 `json:"consignee_province_code"`
	ConsigneeCityCode     string                 `json:"consignee_city_code"`
	ConsigneeAreaCode     string                 `json:"consignee_area_code"`
	ConsigneeCountyCode   string                 `json:"consignee_county_code,omitempty"` // 街道编码（可选）
	ConsigneeMobile       string                 `json:"consignee_mobile"`
	CustomerID            int                    `json:"customer_id"`
	CustomerName          string                 `json:"customer_name"`
	CustomerOrderNum      string                 `json:"customer_order_num"`
	Status                hytvo.OrderStatusObj   `json:"status"`
	InRemark              string                 `json:"in_remark,omitempty"`
	CreatedAt             string                 `json:"created_at"`
	LogisticsFee          float64                `json:"logistics_fee,omitempty"`
	SendAt                string                 `json:"send_at,omitempty"`
	TotalAmount           float64                `json:"total_amount"`
	OrderAfterType        hytvo.AfterSaleTypeObj `json:"order_after_type,omitempty"`
}

// 订单商品信息
type OrderGoods struct {
	ID                int                `json:"id"`
	GoodsID           int                `json:"goods_id"`
	GoodsNum          string             `json:"goods_num"`
	GoodsName         string             `json:"goods_name"`
	Number            int                `json:"number"`
	OrderID           int                `json:"order_id"`
	SalePrice         float64            `json:"sale_price"`
	IsCompose         hytvo.IsComposeObj `json:"is_compose"`
	ChildrenGoodsList []*ComposeGoods    `json:"children_goods_list,omitempty"`
}

// 组合商品
type ComposeGoods struct {
	ComposeGoodsID   int    `json:"compose_goods_id"`
	ComposeGoodsName string `json:"compose_goods_name"`
}

// {"orderInfo":{"goodsList":[{"childrenGoodsList":[{"childrenGoodsList":[],"compose_goods_id":25,"compose_goods_name":"牛肉干","goods_id":24,"goods_name":"旺仔牛奶","goods_num":"123456ss","goods_specs":"","goods_unit":"","id":461,"is_compose":0,"number":1,"order_id":335,"sale_price":3}],"compose_goods_id":0,"compose_goods_name":"","goods_id":25,"goods_name":"牛肉干","goods_num":"code12345","goods_specs":"","goods_unit":"元","id":462,"is_compose":1,"number":1,"order_id":335,"sale_price":34}],"orderBasic":{"consignee":"qing7260","consignee_address":"北京北京市东城区北京,北京市,东城区","consignee_area_code":"110101000000","consignee_city_code":"110100000000","consignee_county_code":"0","consignee_mobile":"15730407260","consignee_province_code":"110000000000","created_at":"2025-3-11 17:26:38","customer_id":35,"customer_name":"蓝熊卡券","customer_order_num":"lx745689344","id":335,"in_remark":"","logistics_fee":0,"order_after_type":0,"order_num":"O25031117263738894","send_at":"","status":1,"total_amount":34}}}
// 订单详情响应
type OrderInfoResponse struct {
	OrderInfo []*OrderInfo `json:"list"`
}

func (o *OrderInfoResponse) CanCancel() bool {
	if o == nil || o.OrderInfo == nil || len(o.OrderInfo) == 0 {
		return false
	}
	canCancel := true
	for _, order := range o.OrderInfo {
		if !order.OrderBasic.Status.IsWaitingForReview() && !order.OrderBasic.Status.IsWaitingForDelivery() {
			canCancel = false
		}
	}
	return canCancel
}

func (o *OrderInfoResponse) GetOrderNum() string {
	if o == nil || o.OrderInfo == nil || len(o.OrderInfo) == 0 {
		return ""
	}
	return o.OrderInfo[0].OrderBasic.OrderNum
}

func (o *OrderInfoResponse) GetCustomerOrderNum() string {
	if o == nil || o.OrderInfo == nil || len(o.OrderInfo) == 0 {
		return ""
	}
	return o.OrderInfo[0].OrderBasic.CustomerOrderNum
}

func (o *OrderInfoResponse) IsCancel() bool {
	if o == nil || o.OrderInfo == nil || len(o.OrderInfo) == 0 {
		return false
	}
	isCancel := true
	for _, order := range o.OrderInfo {
		if !order.OrderBasic.Status.IsCanceled() {
			isCancel = false
		}
	}
	return isCancel
}

func (o *OrderInfoResponse) OrderAfterTypeIsNone() bool {
	if o == nil || o.OrderInfo == nil || len(o.OrderInfo) == 0 {
		return false
	}
	return o.OrderInfo[0].OrderBasic.OrderAfterType.IsNone()
}

type OrderInfo struct {
	OrderBasic *OrderBasic   `json:"orderBasic"`
	GoodsList  []*OrderGoods `json:"goodsList"`
}

type SubmitOrderRequest struct {
	OrderBasic *SubmitOrderBasic   `json:"orderBasic"`
	GoodsList  []*SubmitOrderGoods `json:"goodsList"`
}

type SubmitOrderBasic struct {
	CustomerOrderNum      string `json:"customer_order_num"`
	Consignee             string `json:"consignee"`
	ConsigneeMobile       string `json:"consignee_mobile"`
	ConsigneeProvinceCode string `json:"consignee_province_code"`
	ConsigneeCityCode     string `json:"consignee_city_code"`
	ConsigneeAreaCode     string `json:"consignee_area_code"`
	ConsigneeCountyCode   string `json:"consignee_county_code,omitempty"` // 街道编码（可选）
	ConsigneeAddress      string `json:"consignee_address"`
	InRemark              string `json:"in_remark,omitempty"`
}

type SubmitOrderGoods struct {
	GoodsNum string `json:"goods_num"`
	Number   int    `json:"number"`
}

type SubmitOrderResponse struct {
	OrderNum     string   `json:"order_num"`
	SubOrderNums []string `json:"sub_order_nums"`
}

func (s *SubmitOrderResponse) SubmitSuccess() bool {
	if s == nil || s.SubOrderNums == nil {
		return false
	}
	if s.OrderNum == "" || len(s.SubOrderNums) == 0 {
		return false
	}
	return len(s.SubOrderNums) > 0
}

// ---------------------- 批量订单列表 ----------------------
// 请求参数
type OrderListRequest struct {
	Page              int      `json:"page" validate:"required"`
	Limit             int      `json:"limit" validate:"required"`
	OrderNums         []string `json:"order_nums,omitempty"`
	CustomerOrderNums []string `json:"customer_order_nums,omitempty"`
	Status            []int    `json:"status,omitempty"`
	CreatedAt         []string `json:"created_at,omitempty"`
}

// 响应结构
type OrderListResponse struct {
	List  []*OrderInfoResponse `json:"list"`
	Total int                  `json:"total"`
}

// ---------------------- 关闭订单 ----------------------
type CloseOrderRequest struct {
	OrderNum         string `json:"order_num,omitempty" validate:"required_without=CustomerOrderNum"`
	CustomerOrderNum string `json:"customer_order_num,omitempty" validate:"required_without=OrderNum"`
	Message          string `json:"message"`
}

type CloseOrderResponse struct {
	OrderNum string               `json:"order_num"`
	Status   hytvo.OrderStatusObj `json:"status"`
}

// CancelSuccess
func (c *CloseOrderResponse) CancelSuccess() bool {
	if c == nil || c.OrderNum == "" {
		return false
	}
	return true
}

// ---------------------- 物流信息 ----------------------
type LogisticsRequest struct {
	OrderNum         string `json:"order_num,omitempty" validate:"required_without=CustomerOrderNum"`
	CustomerOrderNum string `json:"customer_order_num,omitempty" validate:"required_without=OrderNum"`
}

type LogisticsInfo struct {
	ID              int                      `json:"id"`
	LogisticsName   string                   `json:"logistics_name"`
	LogisticsOddNum string                   `json:"logistics_odd_num"`
	LogisticsFee    float64                  `json:"logistics_fee"`
	OrderNum        string                   `json:"order_num"`
	CreatedAt       string                   `json:"created_at"`
	LogisticsJSON   string                   `json:"logistics_json"`
	Status          hytvo.LogisticsStatusObj `json:"status"` // 快递状态枚举
}

type LogisticsResponse struct {
	OrderLogistics []*LogisticsInfo `json:"orderLogistics"`
}

type FinishOrderRequest struct {
	OrderNum         string `json:"order_num,omitempty" validate:"required_without=CustomerOrderNum"`
	CustomerOrderNum string `json:"customer_order_num,omitempty" validate:"required_without=OrderNum"`
	SubOrderNum      string `json:"sub_order_num,omitempty"`
}

type FinishOrderResponse struct {
	OrderNum         string `json:"order_num,omitempty" validate:"required_without=CustomerOrderNum"`
	CustomerOrderNum string `json:"customer_order_num,omitempty" validate:"required_without=OrderNum"`
}

// ---------------------- 售后管理 ----------------------
type AfterSaleApplyRequest struct {
	OrderAfterBasic *OrderAfterSaleBasic `json:"orderAfterBasic"`
	GoodsList       []*AfterSaleGoods    `json:"goodsList" validate:"required"`
}

type OrderAfterSaleBasic struct {
	Type          hytvo.AfterSaleTypeObj `json:"type" validate:"required"`
	Reason        string                 `json:"reason" validate:"required"`
	Image         string                 `json:"image,omitempty"`
	Remark        string                 `json:"remark,omitempty"`
	OldOrderNum   string                 `json:"old_order_num" validate:"required"`
	ThirdAfterNum string                 `json:"third_after_num,omitempty"`
}

type AfterSaleGoods struct {
	GoodsNum    string `json:"goods_num" validate:"required"`
	Number      int    `json:"number" validate:"required"`
	NewGoodsNum string `json:"new_goods_num,omitempty"`
}

// {"afterList":[{"goodsList":[{"goods_num":"code12345","number":1}],"order_after_num":"A25031117314318715"}]}
type AfterSaleApplyResponse struct {
	AfterList []*AfterSaleList `json:"afterList"`
}
type AfterSaleList struct {
	GoodsList     []*AfterSaleGoodsResponseItems `json:"goodsList"`
	OrderAfterNum string                         `json:"order_after_num"`
}
type AfterSaleGoodsResponseItems struct {
	GoodsNum string `json:"goods_num"`
	Number   int    `json:"number"`
}

// 售后退回请求
type AfterSaleReturnRequest struct {
	OrderAfterNum         string `json:"order_after_num" validate:"required"`
	ReturnLogistics       string `json:"return_logistics" validate:"required"`
	ReturnLogisticsOddNum string `json:"return_logistics_odd_num" validate:"required"`
}

type AfterSaleReturnResponse struct {
	OrderAfterNum string `json:"order_after_num"`
}

// ---------------------- 财务管理 ----------------------
type FinanceBillRequest struct {
	Page      int      `json:"page" validate:"required"`
	Limit     int      `json:"limit" validate:"required"`
	GoodsName string   `json:"goods_name,omitempty"`
	CreatedAt []string `json:"created_at,omitempty"`
}

type BillItem struct {
	CustomerName        string  `json:"customer_name"`
	GoodsName           string  `json:"goods_name"`
	Number              int     `json:"number"`
	PayType             int     `json:"pay_type"`
	ReceiptType         int     `json:"receipt_type"`
	SettlementUnitPrice float64 `json:"settlement_unit_price"`
	TotalAmount         float64 `json:"total_amount"`
	IsConfirm           int     `json:"is_confirm"`
	IsInvoicing         int     `json:"is_invoicing"`
	CreatedAt           string  `json:"created_at"`
}

type FinanceBillResponse struct {
	List  []*BillItem `json:"list"`
	Total int         `json:"total"`
}

type CloseAfterSaleRequest struct {
	OrderAfterNum string `json:"order_after_num"`
	Remark        string `json:"remark"`
}

type CloseAfterSaleResponse struct {
	OrderAfterNum string `json:"order_after_num"`
}

func (c *CloseAfterSaleResponse) CloseSuccess() bool {
	return false
}

type AfterSaleInfoRequest struct {
	OrderAfterNum string `json:"order_after_num"`
}

type AfterSaleInfoResponse struct {
	Info *AfterSaleInfo `json:"info"`
}

type AfterSaleInfo struct {
	GoodsList []*AfterSaleGoodsItem `json:"goodsList"`
	OrderInfo *AfterSaleOrderInfo   `json:"orderInfo"`
}

type AfterSaleOrderInfo struct {
	CreatedAt             string                   `json:"created_at"`
	Id                    int                      `json:"id"`
	Image                 string                   `json:"image"`
	NewOrderNum           string                   `json:"new_order_num"`
	OldOrderNum           string                   `json:"old_order_num"`
	OrderAfterNum         string                   `json:"order_after_num"`
	Reason                string                   `json:"reason"`
	ReturnLogistics       string                   `json:"return_logistics"`
	ReturnLogisticsOddNum string                   `json:"return_logistics_odd_num"`
	Status                hytvo.AfterSaleStatusObj `json:"status"`
	Type                  hytvo.AfterSaleTypeObj   `json:"type"`
}

type AfterSaleGoodsItem struct {
	GoodsId      int    `json:"goods_id"`
	GoodsName    string `json:"goods_name"`
	GoodsNum     string `json:"goods_num"`
	Id           int    `json:"id"`
	Number       int    `json:"number"`
	OrderAfterId int    `json:"order_after_id"`
	SalePrice    int    `json:"sale_price"`
	TotalNumber  int    `json:"total_number"`
}

type UpdateAddressRequest struct {
	OrderNum              string `json:"order_num,omitempty"`
	CustomerOrderNum      string `json:"customer_order_num,omitempty"`
	Consignee             string `json:"consignee"`
	ConsigneeMobile       string `json:"consignee_mobile"`
	ConsigneeProvinceCode string `json:"consignee_province_code"`
	ConsigneeCityCode     string `json:"consignee_city_code"`
	ConsigneeAreaCode     string `json:"consignee_area_code"`
	ConsigneeCountyCode   string `json:"consignee_county_code,omitempty"`
	ConsigneeAddress      string `json:"consignee_address"`
}

type UpdateAddressResponse struct {
	OrderNum         string   `json:"order_num,omitempty"`
	CustomerOrderNum string   `json:"customer_order_num,omitempty"`
	SubOrderNums     []string `json:"sub_order_nums,omitempty"`
}
