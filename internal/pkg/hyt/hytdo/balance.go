package hytdo

type BalanceHistoryReq struct {
	CreatedAt []string `json:"created_at,omitempty"`      // 时间范围（格式：["开始时间", "结束时间"]）
	FromType  int      `json:"from_type,omitempty"`       // 来源类型 1-充值 2-订单下单 3-订单退订
	OrderNum  string   `json:"order_num,omitempty"`       // 关联订单号
	Page      int      `json:"page" validate:"required"`  // 页码（必填）
	Limit     int      `json:"limit" validate:"required"` // 每页条数（必填）
}

type BalanceHistoryItem struct {
	Amount         float64 `json:"amount"`          // 变动金额（单位元）
	CreatedAt      string  `json:"created_at"`      // 产生时间（格式：yyyy-MM-dd HH:mm:ss）
	CurrentBalance float64 `json:"current_balance"` // 变动后余额（单位元）
	FromType       float64 `json:"from_type"`       //
	OrderNum       float64 `json:"order_num"`       //
	Remark         float64 `json:"remark"`          //
}

type BalanceHistoryResp struct {
	List  []BalanceHistoryItem `json:"list"` // 流水记录列表
	Total int                  `json:"total"`
}

type BalanceResp struct {
	Balance     float64 `json:"balance"`      // 当前余额（单位元）
	CreditGrant float64 `json:"credit_grant"` // 客户授信金额（单位元）
	FullName    string  `json:"full_name"`    // 客户全称
	Name        string  `json:"name"`         // 客户名称
}
