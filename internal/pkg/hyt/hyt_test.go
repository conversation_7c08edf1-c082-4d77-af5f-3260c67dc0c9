package hyt

import (
	"cardMall/internal/initialize"
	"cardMall/internal/pkg/hyt/hytdo"
	"cardMall/internal/pkg/hyt/hytvo"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/tracing"
	"os"
	"testing"
)

// Test
func TestGoodsList(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)

	res, err := client.GoodsList(&hytdo.GoodsListReq{Status: hytvo.GoodsStatusOffline, GoodsNums: nil})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestGoodsStocks(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)

	res, err := client.GoodsStocks(&hytdo.GoodsStocksReq{GoodsNums: []string{
		"code12345",
	}})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestOrderDetail(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	res, err := client.OrderInfo(&hytdo.OrderInfoRequest{CustomerOrderNum: "P2142929291077537792"})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestGoodsDetail(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	res, err := client.GoodsDetail(&hytdo.GoodsDetailReq{GoodsNum: "TEST001"})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestCloseOrderRequest(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	res, err := client.CloseOrder(&hytdo.CloseOrderRequest{CustomerOrderNum: "lx745689322"})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestSubmitOrder(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	in := &hytdo.SubmitOrderRequest{
		OrderBasic: &hytdo.SubmitOrderBasic{
			CustomerOrderNum:      "c216251661541287666",
			Consignee:             "qing7260",
			ConsigneeMobile:       "15730407260",
			ConsigneeProvinceCode: "110000000000",
			ConsigneeCityCode:     "110100000000",
			ConsigneeAreaCode:     "110101000000",
			ConsigneeCountyCode:   "",
			ConsigneeAddress:      "北京,北京市,东城区",
			InRemark:              "",
		},
		GoodsList: make([]*hytdo.SubmitOrderGoods, 0),
	}
	// {"orderBasic":{"customer_order_num":"2162834388819349505","consignee":"李先生","consignee_mobile":"18383398524","consignee_province_code":"110000000000","consignee_city_code":"110100000000","consignee_area_code":"110105000000","consignee_address":"认为热望亲热委屈认为去","in_remark":"蓝熊saas-41-175"},"goodsList":[{"goods_num":"HW004","number":1},{"goods_num":"Lzw_1002","number":1}]}>
	in.GoodsList = append(in.GoodsList, &hytdo.SubmitOrderGoods{
		GoodsNum: "HW004",
		Number:   2,
	})
	in.GoodsList = append(in.GoodsList, &hytdo.SubmitOrderGoods{
		GoodsNum: "Lzw_1002",
		Number:   2,
	})
	res, err := client.SubmitOrder(in)
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestAfterSaleApply(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	orderInfo, err := client.OrderInfo(&hytdo.OrderInfoRequest{
		CustomerOrderNum: "2163683060352737282",
		// OrderNum: "MA25031809085028826",
	})
	println(orderInfo, err)

	//res, err := client.AfterSaleApply(&hytdo.AfterSaleApplyRequest{
	//	OrderAfterBasic: &hytdo.OrderAfterSaleBasic{
	//		Type:          hytvo.AfterSaleTypeReissue,
	//		Reason:        "不想要了，申请退回",
	//		Image:         "",
	//		Remark:        "不想要了，申请退回",
	//		OldOrderNum:   "O25031519361637637",
	//		ThirdAfterNum: "lx745689355",
	//	},
	//	GoodsList: []*hytdo.AfterSaleGoods{
	//		{
	//			GoodsNum:    "HW004",
	//			Number:      1,
	//			NewGoodsNum: "HW004",
	//		},
	//	},
	//})
	//if err != nil {
	//	t.Error(err)
	//}
	//t.Log(res)
}
func TestAfterSaleInfo(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)

	res, err := client.AfterSaleInfo(&hytdo.AfterSaleInfoRequest{
		OrderAfterNum: "A25031516442661207",
	})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestFinishOrder(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)

	res, err := client.FinishOrder(&hytdo.FinishOrderRequest{
		CustomerOrderNum: "2163945660168781826",
		SubOrderNum:      "O25031815295086218",
	})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}
func TestAddress(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)

	res, err := client.AddressList(&hytdo.AddressListRequest{
		Pcode: "",
	})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}

func TestCategoryList(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	res, err := client.CategoryList(&hytdo.CategoryListRequest{Limit: 100, Page: 1, Search: &hytdo.CategorySearch{Name: "", ParentId: 0}})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}
func TestBrandList(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	res, err := client.BrandList(&hytdo.BrandListRequest{Limit: 100, Page: 1, Search: &hytdo.BrandSearch{Name: "", Id: 0}})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}
func TestAfterSaleReturn(t *testing.T) {
	bc := initialize.LoadConfig()
	logger := log.With(log.NewStdLogger(os.Stdout),
		"ts", log.DefaultTimestamp,
		"caller", log.DefaultCaller,
		"service.id", "id",
		"service.name", "Name",
		"service.version", "Version",
		"trace.id", tracing.TraceID(),
		"span.id", tracing.SpanID(),
		"request.id", "RequestId",
	)
	logHelper := log.NewHelper(logger)
	client := NewClient(bc, logHelper)
	res, err := client.AfterSaleReturn(&hytdo.AfterSaleReturnRequest{
		OrderAfterNum:         "A25031619274316679",
		ReturnLogistics:       "EMS",
		ReturnLogisticsOddNum: "12312312123",
	})
	if err != nil {
		t.Error(err)
	}
	t.Log(res)
}
