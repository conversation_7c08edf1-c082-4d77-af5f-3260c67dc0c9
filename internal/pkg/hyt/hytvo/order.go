package hytvo

// 订单状态（1：待审核，2：待发货，3：已发货，4：已完成，5：已取消，6：审核不通过）
//-status

type OrderStatusObj int

const (
	OrderStatusWaitingForReview   OrderStatusObj = 1
	OrderStatusWaitingForDelivery OrderStatusObj = 2
	OrderStatusDelivered          OrderStatusObj = 3
	OrderStatusCompleted          OrderStatusObj = 4
	OrderStatusCancelled          OrderStatusObj = 5
	OrderStatusReviewNotPassed    OrderStatusObj = 6
)

func (o OrderStatusObj) IsWaitingForReview() bool {
	return o == OrderStatusWaitingForReview
}
func (o OrderStatusObj) IsWaitingForDelivery() bool {
	return o == OrderStatusWaitingForDelivery
}
func (o OrderStatusObj) IsNotPassed() bool {
	return o == OrderStatusReviewNotPassed
}

func (o OrderStatusObj) IsCompleted() bool {
	return o == OrderStatusCompleted
}

func (o OrderStatusObj) IsCanceled() bool {
	return o == OrderStatusCancelled
}

// 是否组合商品 0 不是， 1 是
// -is_compose
type IsComposeObj int

const (
	IsComposeNo  IsComposeObj = 0
	IsComposeYes IsComposeObj = 1
)

// 快递状态 1 暂无记录 2 在途中 3 派送中 4 已签收 (完结状态) 5 用户拒签 6 疑难件 7 无效单 (完结状态) 8 超时单 9 签收失败 10 退回
type LogisticsStatusObj int

const (
	LogisticsStatusNoRecord    LogisticsStatusObj = 1
	LogisticsStatusInTransit   LogisticsStatusObj = 2
	LogisticsStatusDispatching LogisticsStatusObj = 3
	LogisticsStatusSigned      LogisticsStatusObj = 4
	LogisticsStatusRefused     LogisticsStatusObj = 5
	LogisticsStatusDoubt       LogisticsStatusObj = 6
	LogisticsStatusInvalid     LogisticsStatusObj = 7
	LogisticsStatusTimeout     LogisticsStatusObj = 8
	LogisticsStatusFailed      LogisticsStatusObj = 9
	LogisticsStatusReturn      LogisticsStatusObj = 10
)

// 售后类型（1：退货退款，2：退款不退货，3：换货-同产品换货，4：换货-不同产品换货，5：补发）
type AfterSaleTypeObj int

const (
	AfterSaleTypeNone            AfterSaleTypeObj = 0
	AfterSaleTypeReturnRefund    AfterSaleTypeObj = 1
	AfterSaleTypeRefundNoReturn  AfterSaleTypeObj = 2
	AfterSaleTypeExchangeSamePro AfterSaleTypeObj = 3
	AfterSaleTypeExchangeDiffPro AfterSaleTypeObj = 4
	AfterSaleTypeReissue         AfterSaleTypeObj = 5
)

func (o AfterSaleTypeObj) IsNone() bool {
	return o == AfterSaleTypeNone
}

// 售后订单状态（1：待审核，2：审核不通过，3：待退回，4：待确认，5：待发货，6：待完成，7：售后完成）
type AfterSaleStatusObj int

const (
	AfterSaleStatusWaitingForReview   AfterSaleStatusObj = 1
	AfterSaleStatusReviewNotPassed    AfterSaleStatusObj = 2
	AfterSaleStatusWaitingForReturn   AfterSaleStatusObj = 3
	AfterSaleStatusWaitingForConfirm  AfterSaleStatusObj = 4
	AfterSaleStatusWaitingForDelivery AfterSaleStatusObj = 5
	AfterSaleStatusWaitingForComplete AfterSaleStatusObj = 6 // 审核通过
	AfterSaleStatusComplete           AfterSaleStatusObj = 7
	AfterSaleStatusRefuse             AfterSaleStatusObj = 8 // 售后拒绝
)

func (o AfterSaleStatusObj) IsNotPassed() bool {
	return o == AfterSaleStatusReviewNotPassed
}

func (o AfterSaleStatusObj) IsComplete() bool {
	return o == AfterSaleStatusComplete
}

func (o AfterSaleStatusObj) GetInt() int {
	return int(o)
}
