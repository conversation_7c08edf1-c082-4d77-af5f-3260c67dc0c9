package appbiz

import (
	"cardMall/api/apierr"
	bizBo "cardMall/internal/biz/bo"
	bizDo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/aclbo"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/data/ent"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/hyt"
	"cardMall/internal/pkg/hyt/hytdo"
	"cardMall/internal/pkg/isolationcustomer"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/coroutine"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"context"
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	xss "github.com/feiin/go-xss"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"strings"
	"time"
)

type OrderAfterSaleBiz struct {
	goodsRepo                 bizRepo.GoodsRepo
	orderAfterSaleRepo        bizRepo.OrderAfterSaleRepo
	orderAfterSaleGoodsRepo   bizRepo.OrderAfterSaleGoodsRepo
	orderOperatorLogRepo      bizRepo.OrderOperatorLogRepo
	orderAfterSaleLogRepo     bizRepo.OrderAfterSaleLogRepo
	orderAfterSaleDeliverRepo bizRepo.OrderAfterSaleDeliverRepo
	orderAfterSaleDealRepo    bizRepo.OrderAfterSaleDealRepo
	supplierGoodsSkuRepo      bizRepo.SupplierGoodsSkuRepo
	trans                     bizRepo.TransactionRepo
	bizOrderRepo              bizRepo.OrderRepo
	goodsSkuRepo              bizRepo.GoodsSkuRepo
	bizOrderGoodsRepo         bizRepo.OrderGoodsRepo
	giftCardDs                *ds.GiftCardDs
	orderGoodsRepo            repository.OrderGoodsRepo
	orderRepo                 repository.OrderRepo
	kdRepo                    repository.KdRepo
	orderLogisticsRepo        repository.OrderLogisticsRepo
	orderAfterSaleDs          *ds.OrderAfterSaleBaseDs
	logistics                 rpc.LogisticsRpc
	log                       *log.Helper
	idGenerator               *idgenerator.Generator
	hytClient                 *hyt.Client
	OrderAfterSaleRelRepo     bizRepo.OrderAfterSaleRelRepo
	conf                      *conf.Bootstrap
}

func NewOrderAfterSaleBiz(goodsRepo bizRepo.GoodsRepo, orderAfterSaleRepo bizRepo.OrderAfterSaleRepo, orderAfterSaleGoodsRepo bizRepo.OrderAfterSaleGoodsRepo, orderOperatorLogRepo bizRepo.OrderOperatorLogRepo, orderAfterSaleLogRepo bizRepo.OrderAfterSaleLogRepo, orderAfterSaleDeliverRepo bizRepo.OrderAfterSaleDeliverRepo, orderAfterSaleDealRepo bizRepo.OrderAfterSaleDealRepo, supplierGoodsSkuRepo bizRepo.SupplierGoodsSkuRepo, trans bizRepo.TransactionRepo, bizOrderRepo bizRepo.OrderRepo, goodsSkuRepo bizRepo.GoodsSkuRepo, bizOrderGoodsRepo bizRepo.OrderGoodsRepo, giftCardDs *ds.GiftCardDs, orderGoodsRepo repository.OrderGoodsRepo, orderRepo repository.OrderRepo, kdRepo repository.KdRepo, orderLogisticsRepo repository.OrderLogisticsRepo, orderAfterSaleDs *ds.OrderAfterSaleBaseDs, logistics rpc.LogisticsRpc, log *log.Helper, idGenerator *idgenerator.Generator, hytClient *hyt.Client, orderAfterSaleRelRepo bizRepo.OrderAfterSaleRelRepo, conf *conf.Bootstrap) *OrderAfterSaleBiz {
	return &OrderAfterSaleBiz{goodsRepo: goodsRepo, orderAfterSaleRepo: orderAfterSaleRepo, orderAfterSaleGoodsRepo: orderAfterSaleGoodsRepo, orderOperatorLogRepo: orderOperatorLogRepo, orderAfterSaleLogRepo: orderAfterSaleLogRepo, orderAfterSaleDeliverRepo: orderAfterSaleDeliverRepo, orderAfterSaleDealRepo: orderAfterSaleDealRepo, supplierGoodsSkuRepo: supplierGoodsSkuRepo, trans: trans, bizOrderRepo: bizOrderRepo, goodsSkuRepo: goodsSkuRepo, bizOrderGoodsRepo: bizOrderGoodsRepo, giftCardDs: giftCardDs, orderGoodsRepo: orderGoodsRepo, orderRepo: orderRepo, kdRepo: kdRepo, orderLogisticsRepo: orderLogisticsRepo, orderAfterSaleDs: orderAfterSaleDs, logistics: logistics, log: log, idGenerator: idGenerator, hytClient: hytClient, OrderAfterSaleRelRepo: orderAfterSaleRelRepo, conf: conf}
}

// CreateAfterSale 实物商品售后
func (o *OrderAfterSaleBiz) CreateAfterSale(ctx context.Context, reqBo *bo.OrderAfterSaleCreateBo) (id int, err error) {
	if err = reqBo.Validate(); err != nil {
		return
	}
	reqBo.MergeSkus()
	orderDo, _ := o.bizOrderRepo.GetWithEdges(ctx, reqBo.OrderNo, &bizBo.OrderWithEdgeBo{WithGoods: true})

	if orderDo.SupplierID == int(o.conf.Hyt.SupplierId) {
		if orderDo.IsExchangeOrder() {
			return 0, apierr.ErrorException("暂不支持多次换货，请联系客服")
		} else {
			for _, sku := range reqBo.Skus {
				if orderDo.OrderNumber != sku.SourceOrderNo {
					return 0, apierr.ErrorException("换货订单不能继续申请售后，请联系客服")
				}
			}
		}
	}

	if err = o.CheckCreateAfterSale(reqBo, orderDo); err != nil {
		return 0, err
	}
	if err = o.CheckOriginalApplyStatus(ctx, orderDo.GetOriginalOrderNo()); err != nil {
		return 0, err
	}
	goodsDos, err := o.orderAfterSaleDs.GetOrderApplyInfo(ctx, orderDo)
	if err != nil {
		return 0, err
	}
	var isAll bool
	if isAll, err = o.CheckOriginalOrderApplyInfo(reqBo, goodsDos); err != nil {
		return 0, err
	}

	if reqBo.ApplyTypeIsRefund() {
		res, err := o.CalculateApplyGoodsMaxRefundData(ctx, reqBo, orderDo, orderDo.OrderGoods, isAll)
		if err != nil {
			return 0, err
		}
		// 如果订单有礼品卡抵扣，则退款金额需要减去礼品卡金额 由于用不能修改运费和商品推荐金额，所以直接取测算的金额
		// 没有礼品卡抵扣，走正常逻辑
		if res.IsOrderCardGiftDiscount {
			reqBo.RefundGoodsAmount = res.RefundGoodsAmount
			reqBo.FreightFee = res.FreightFee
		} else {
			if reqBo.RefundGoodsAmount.GreaterThan(res.RefundGoodsAmount) {
				return 0, apierr.ErrorException("退款金额超过最大可退商品金额")
			}
			if reqBo.FreightFee.GreaterThan(res.FreightFee) {
				return 0, apierr.ErrorException("退款金额超过最大可退运费金额")
			}
		}
		// 获取计算出来的退款礼品卡金额
		reqBo.RefundCardGiftAmount = res.RefundCardGiftAmount
		reqBo.RefundCardGiftFreightFee = res.RefundCardGiftFreightFee
	}

	var parentDealMap map[string]*bizDo.OrderAfterSaleDealDo
	if orderDo.IsExchangeOrder() {
		var parentDealDos []*bizDo.OrderAfterSaleDealDo
		parentDealDos, err = o.orderAfterSaleDealRepo.FindExchangeInfoByOrderNos(ctx, reqBo.GetSourceOrderNos()...)
		if err != nil && !ent.IsNotFound(err) {
			return 0, err
		}
		parentDealMap = slice.KeyBy(parentDealDos, func(item *bizDo.OrderAfterSaleDealDo) string {
			return item.GetRefundApplyUniqueNo()
		})
	}

	refundAmount := reqBo.GetRefundAmount().InexactFloat64()
	refundGoodsAmount := reqBo.RefundGoodsAmount.InexactFloat64()
	freightFee := reqBo.FreightFee.InexactFloat64()
	exchangeSkuNoMap := make(map[string]*bizDo.GoodsSkuDo)
	if reqBo.Type.IsAfterSaleExchange() {
		freightFee = 0
		refundGoodsAmount = 0
		refundAmount = 0
		var skuDos []*bizDo.GoodsSkuDo
		skuDos, err = o.goodsSkuRepo.FindWithGoodsBySkuNos(ctx, reqBo.GetExchangeSkuNos())
		if err != nil {
			return 0, err
		}
		exchangeSkuNoMap = slice.KeyBy(skuDos, func(item *bizDo.GoodsSkuDo) string {
			return item.SkuNo
		})
		if err = o.CheckSkuNoOnSale(reqBo, exchangeSkuNoMap); err != nil {
			return 0, err
		}
	}
	originalOrderNo := orderDo.GetOriginalOrderNo()

	relatedOrderNos, err := o.orderAfterSaleDealRepo.GetRelatedOrderNos(ctx, orderDo.GetOriginalOrderNo())
	if err != nil {
		return 0, err
	}
	relatedOrderGoods, err := o.bizOrderGoodsRepo.FindByOrderNumber(ctx, relatedOrderNos...)
	originalSkuNoMap := slice.KeyBy(relatedOrderGoods, func(item *bizDo.OrderGoodsDo) string { return item.SkuNo })
	for k, v := range orderDo.GetOriginalSkuNoMap() {
		originalSkuNoMap[k] = v
	}
	//// 创建售后单
	//isAllOrderGoods := true
	//if orderDo.SupplierID == int(o.conf.Hyt.SupplierId) && reqBo.Type.IsRefund() {
	//	reqSkuMap := slice.KeyBy(reqBo.Skus, func(item *bo.OrderAfterSaleEntitySku) string { return item.SkuNo })
	//	for _, tmpDo := range orderDo.OrderGoods {
	//		if val, ok := reqSkuMap[tmpDo.SkuNo]; !ok || tmpDo.Quantity != val.Num {
	//			isAllOrderGoods = false
	//		}
	//	}
	//}
	noShopAudit := false
	if orderDo.SupplierID == int(o.conf.Hyt.SupplierId) {
		noShopAudit = true
	}

	err = o.trans.Exec(ctx, func(ctx context.Context) error {
		platformStatus := valobj.AfterSalePlatformStatusWaitAudit
		if noShopAudit {
			platformStatus = valobj.AfterSalePlatformStatusApproveToUser
		}

		//创建售后单
		var newAfterSale *bizDo.OrderAfterSaleDo
		newAfterSale, err = o.orderAfterSaleRepo.Create(ctx, &bizDo.OrderAfterSaleDo{
			UserID:                   reqBo.UserId,
			SupplierID:               orderDo.SupplierID,
			OrderID:                  orderDo.ID,
			OrderNumber:              orderDo.OrderNumber,
			Reason:                   reqBo.Reason,
			Images:                   reqBo.ImagesJson(),
			Remark:                   reqBo.Remark,
			RefundAmount:             refundAmount,
			RefundFreightFee:         freightFee,
			RefundGoodsAmount:        refundGoodsAmount,
			ReceiveStatus:            reqBo.ReceiveStatus,
			Status:                   valobj.AfterSaleStatusWaitAudit,
			PlatformStatus:           platformStatus,
			Type:                     reqBo.Type,
			CreateTime:               helper.GetNow(),
			UpdateTime:               helper.GetNow(),
			AfterSaleNo:              helper.GetAfterAfterSaleNumber(o.idGenerator),
			OriginalOrderNo:          originalOrderNo,
			RefundCardGiftAmount:     reqBo.RefundCardGiftAmount.InexactFloat64(),
			RefundCardGiftFreightFee: reqBo.RefundCardGiftFreightFee.InexactFloat64(),
		})
		if err != nil {
			return err
		}
		id = newAfterSale.ID
		//记录售后商品
		for _, applySkuInfo := range reqBo.Skus {
			applyOrderGoods, ok := originalSkuNoMap[applySkuInfo.SkuNo]
			if !ok {
				return errors.New("商品信息异常")
			}
			var (
				orderAfterSaleGoodsDo *bizDo.OrderAfterSaleGoodsDo
				parentDealDo          *bizDo.OrderAfterSaleDealDo
			)
			if _, ok = parentDealMap[applySkuInfo.GetApplySkuUniqueNo()]; ok {
				parentDealDo = parentDealMap[applySkuInfo.GetApplySkuUniqueNo()]
			}
			orderAfterSaleGoodsDo, err = o.orderAfterSaleGoodsRepo.Create(ctx, &bizDo.OrderAfterSaleGoodsDo{
				OrderID:       orderDo.ID,
				OrderNumber:   orderDo.OrderNumber,
				Pid:           newAfterSale.ID,
				CreateTime:    helper.GetNow(),
				SkuNo:         applyOrderGoods.SkuNo,
				SkuID:         applyOrderGoods.GoodsSkuID,
				GoodsID:       applyOrderGoods.GoodsID,
				GoodsName:     applyOrderGoods.GetSkuFullName(),
				GoodsNum:      applySkuInfo.Num,
				MainImages:    applyOrderGoods.GoodsImage,
				Type:          valobj.AfterSaleGoodsTypeApply,
				SalePrice:     applyOrderGoods.SalePrice,
				SupplierPrice: applyOrderGoods.SupplierPrice,
				RefundAmount:  0,
				ExchangeID:    0,
				SaleIntegral:  applyOrderGoods.GetSaleIntegralInt(),
				OriginalSkuNo: helper.TernaryString(applyOrderGoods.OriginalSkuNo != "", applyOrderGoods.OriginalSkuNo, applyOrderGoods.SkuNo),
			})
			if err != nil {
				return err
			}
			insertDealDo := &bizDo.OrderAfterSaleDealDo{
				ID:              0,
				SupplierID:      orderDo.SupplierID,
				Pid:             newAfterSale.ID, // afterSaleId
				OrderNumber:     applySkuInfo.SourceOrderNo,
				OriginalOrderNo: orderDo.GetOriginalOrderNo(),
				ExchangeOrderNo: "",
				Type:            reqBo.Type,
				IsDealing:       valobj.BooleanTrue,
				Enable:          valobj.BooleanTrue,
				ParentOrderNo:   helper.TernaryAny(parentDealDo == nil, orderDo.OrderNumber, parentDealDo.GetNextParentOrderNo()),
				ParentOrderNos:  helper.TernaryAny(parentDealDo == nil, orderDo.GetOrderNumberJsonString(), parentDealDo.GetNextParentOrderNos(orderDo.OrderNumber)),
				SkuNo:           applyOrderGoods.SkuNo,
				OriginalSkuNo:   applyOrderGoods.GetOriginalSkuNo(),
				GoodsNum:        applySkuInfo.Num,
				GoodsID:         applyOrderGoods.GoodsID,
				GoodsName:       applyOrderGoods.GetSkuFullName(),
				MainImage:       applyOrderGoods.GoodsImage,
				SalePrice:       applyOrderGoods.SalePrice,
				SaleIntegral:    applyOrderGoods.GetSaleIntegralInt(),
				CreateTime:      helper.GetNow(),
				UpdateTime:      helper.GetNow(),
				//OrderAfterSale:  nil,
				//Children:        nil,
				//RefundNum:       0,
				//ExchangeNum:     0,
			}
			_, err = o.orderAfterSaleDealRepo.Create(ctx, insertDealDo)
			if err != nil {
				return err
			}

			if reqBo.Type.IsAfterSaleExchange() {
				var tmpSkuInfo *bizDo.GoodsSkuDo
				if tmpSkuInfo, ok = exchangeSkuNoMap[applySkuInfo.ExchangeSkuNo]; !ok {
					return errors.New("商品信息异常")
				}
				insertGoodsDo := &bizDo.OrderAfterSaleGoodsDo{
					OrderID:       orderDo.ID,
					OrderNumber:   orderDo.OrderNumber,
					Pid:           newAfterSale.ID,
					CreateTime:    helper.GetNow(),
					SkuNo:         tmpSkuInfo.SkuNo,
					SkuID:         tmpSkuInfo.Id,
					GoodsID:       tmpSkuInfo.GoodsId,
					GoodsName:     tmpSkuInfo.GetSkuFullName(),
					GoodsNum:      applySkuInfo.ExchangeNum,
					MainImages:    tmpSkuInfo.Goods.Image,
					Type:          valobj.AfterSaleGoodsTypeExchange,
					SalePrice:     tmpSkuInfo.SalePrice,
					SupplierPrice: tmpSkuInfo.SupplierPrice,
					RefundAmount:  0,
					ExchangeID:    orderAfterSaleGoodsDo.ID,
					SaleIntegral:  tmpSkuInfo.SaleIntegral,
					OriginalSkuNo: applyOrderGoods.GetOriginalSkuNo(),
				}
				_, err = o.orderAfterSaleGoodsRepo.Create(ctx, insertGoodsDo)
				if err != nil {
					return err
				}
			}
		}

		//写入售后日志
		_, err = o.orderAfterSaleLogRepo.Create(ctx, &bizDo.OrderAfterSaleLogDo{
			OrderID:        orderDo.ID,
			OrderNumber:    orderDo.OrderNumber,
			Pid:            newAfterSale.ID,
			CreateTime:     helper.GetNow(),
			Status:         valobj.AfterSaleStatusWaitAudit,
			PlatformStatus: platformStatus,
			UserID:         reqBo.UserId,
			UserType:       valobj.AfterSaleUserTypeBuyer,
			UserName:       reqBo.UserNickName,
			Content:        "用户申请售后",
		})
		if err != nil {
			return err
		}

		//修改订单状态为售后中
		_, err = o.orderRepo.UpdateAfterSaleStatus(ctx, &bo.OrderUpdateAfterStatusBo{
			OrderId:         orderDo.ID,
			OrderNumber:     orderDo.OrderNumber,
			AfterSaleStatus: valobj.OrderAfterSaleStatusHanding,
		})
		if err != nil {
			return err
		}

		//写入订单日志
		_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			OrderId:          orderDo.ID,
			OrderNumber:      orderDo.OrderNumber,
			OrderStatus:      valobj.OrderStatusAfterSale,
			Content:          valobj.OrderStatusAfterSale.GetOperatorContent(),
			OperatorUserType: valobj.OrderOperatorLogUserTypeUser,
			OperatorUserId:   reqBo.UserId,
			OperatorUserName: reqBo.UserNickName,
		})
		if err != nil {
			return err
		}
		//// 取消订单，代表已发货，不允许一起提交
		//if isAllOrderGoods && orderDo.SupplierID == int(o.conf.Hyt.SupplierId) {
		//	_, err := o.hytClient.CloseOrder(&hytdo.CloseOrderRequest{
		//		OrderNum:         "",
		//		CustomerOrderNum: orderDo.OrderNumber,
		//		Message:          reqBo.Remark,
		//	})
		//	if err != nil {
		//		if hyt.OrderIsCloseErr(err) || hyt.OrderNotFoundErr(err) || hyt.CannotCloseOrderErr(err) {
		//			// 订单关闭或订单不存在，继续处理售后
		//		} else {
		//			return apierr.ErrorException("提交售后失败，请重试！")
		//		}
		//	}
		//}

		return err
	})
	return
}

// CheckSkuNoOnSale 校验商品是否在销售中
func (o *OrderAfterSaleBiz) CheckSkuNoOnSale(reqBo *bo.OrderAfterSaleCreateBo, skuMap map[string]*bizDo.GoodsSkuDo) error {
	if reqBo.Type.IsAfterSaleRefund() {
		return nil
	}

	for _, v := range reqBo.Skus {
		if v.ExchangeSkuNo == "" {
			return apierr.ErrorParam("请选择换货商品")
		}
		skuInfo, ok := skuMap[v.ExchangeSkuNo]
		if !ok {
			return apierr.ErrorDbNotFound("您选择的换货商品不存在或已下架,请联系客服")
		}
		if !skuInfo.Enable() {
			return apierr.ErrorDbNotFound("您选择的换货商品不存在或已下架,请联系客服")
		}
		if skuInfo.Goods == nil || skuInfo.Goods.IsOffline() {
			return apierr.ErrorDbNotFound("您选择的换货商品不存在或已下架,请联系客服")
		}
	}
	return nil
}

func (o *OrderAfterSaleBiz) CheckOriginalOrderStatus(ctx context.Context, reqBo *bo.OrderAfterSaleCreateBo) error {
	// 非仅退款订单，需要完结后才能申请售后
	if !reqBo.Type.IsRefund() {
		has, err := o.orderRepo.HasDealing(ctx, reqBo.GetSourceOrderNos()...)
		if err != nil {
			return err
		}
		if has {
			return apierr.ErrorNotAllow("您有未处理完的订单商品，不能进行退货退款")
		}
	}

	return nil
}

// CheckOriginalApplyStatus 校验是否允许创建售后
//
// 1. 检查是否有售后单未处理完成
// 2. 检查订单售后次数是否超过限制
// 3. 检查售后的商品数量是否超过订单商品数量
func (o *OrderAfterSaleBiz) CheckOriginalApplyStatus(ctx context.Context, originalOrderNo string) error {
	//校验是否还有其他售后单
	has, err := o.orderAfterSaleDealRepo.HasDealing(ctx, originalOrderNo)
	if err != nil {
		return err
	}
	if has {
		return apierr.ErrorNotAllow("您有未处理完的售后单")
	}
	ctn, err := o.orderAfterSaleDealRepo.GetAllApplyTimes(ctx, originalOrderNo)
	if err != nil {
		return err
	}
	if ctn+1 > constants.AfterSaleSkuMaxApplyTimes {
		return apierr.ErrorNotAllow("该商品最多只能申请%d次售后,请联系客服", constants.AfterSaleSkuMaxApplyTimes)
	}
	return nil
}

// CheckCreateAfterSale 校验是否允许创建售后
//
// 1. 订单是否存在
// 2. 换货订单只能申请换货
// 3. 申请用户是否与订单用户一致
// 4. 售后类型是否与订单状态匹配
// 5. 订单是否已过售后申请有效期
// 6. 未完成订单只能申请仅退款
func (o *OrderAfterSaleBiz) CheckCreateAfterSale(reqBo *bo.OrderAfterSaleCreateBo, orderDo *bizDo.OrderDo) error {
	if orderDo == nil {
		return apierr.ErrorDbNotFound("订单信息异常")
	}
	if orderDo.ExtType.IsCardBatchCoupon() {
		return apierr.ErrorNotAllow("该订单不可售后，请联系客服")
	}
	if orderDo.IsExchangeOrder() && !reqBo.Type.IsAfterSaleExchange() {
		return apierr.ErrorNotAllow("换货订单不能申请%s", reqBo.Type.String())
	}
	if !orderDo.OrderType.IsEntity() {
		return apierr.ErrorNotAllow("该订单不可售后，请联系客服")
	}

	if orderDo.UserID != reqBo.UserId {
		return apierr.ErrorNotAllow("用户信息异常")
	}
	if !reqBo.ValidateOrderStatus(orderDo.Status) {
		return apierr.ErrorNotAllow("售后类型与订单状态不匹配！")
	}
	// 如果订单已完成且已过售后有效期，不允许申请售后
	if err := orderDo.CheckAfterSaleExpired(); err != nil {
		return err
	}

	if reqBo.Type.IsAfterSaleExchange() {
		for _, skus := range reqBo.Skus {
			if skus.SourceOrderNo != orderDo.OrderNumber {
				return apierr.ErrorNotAllow("只能申请本订单下的商品进行换货")
			}
		}
	}

	// 仅退款，需全部进行退款
	if reqBo.Type.IsRefund() && orderDo.FinishTime == 0 {
		// 未完成 仅退款得售后 强制性标记未收货
		reqBo.ReceiveStatus = valobj.AfterSaleReceiveStatusNotReceived
		orderGoodsQuantityMap := orderDo.GetSkuQuantityMap()

		for _, skus := range reqBo.Skus {
			if _, ok := orderGoodsQuantityMap[skus.SkuNo]; !ok {
				return apierr.ErrorDbNotFound("售后商品不存在")
			}
			orderGoodsQuantityMap[skus.SkuNo] -= skus.Num
			if val, ok := orderGoodsQuantityMap[skus.SkuNo]; ok && val == 0 {
				delete(orderGoodsQuantityMap, skus.SkuNo)
			}
		}
		if len(orderGoodsQuantityMap) != 0 {
			return apierr.ErrorNotAllow("未完成订单必须全部退款")
		}
	}

	return nil
}

// GetOrderInfo 获取订单信息
func (o *OrderAfterSaleBiz) GetOrderInfo(ctx context.Context, orderNo string, edgeBo *bizBo.OrderWithEdgeBo) (*bizDo.OrderDo, error) {
	orderDo, err := o.bizOrderRepo.GetWithEdges(ctx, orderNo, edgeBo)
	if err != nil && ent.IsNotFound(err) {
		return nil, err
	}
	if orderDo == nil {
		return nil, apierr.ErrorDbNotFound("订单不存在")
	}
	return orderDo, nil
}

func (o *OrderAfterSaleBiz) CheckOriginalOrderApplyInfo(reqBo *bo.OrderAfterSaleCreateBo, skuBos []*bizBo.OrderAfterSaleSkuBo) (bool, error) {
	canApplyUniqueMap := make(map[string]int)
	for _, skuBo := range skuBos {
		if skuBo.GetCanApplyNum() > 0 {
			if _, ok := canApplyUniqueMap[skuBo.GetApplySkuUniqueNo()]; !ok {
				canApplyUniqueMap[skuBo.GetApplySkuUniqueNo()] = 0
			}
			canApplyUniqueMap[skuBo.GetApplySkuUniqueNo()] += skuBo.GetCanApplyNum()
		}
	}

	for _, item := range reqBo.Skus {
		val, ok := canApplyUniqueMap[item.GetApplySkuUniqueNo()]
		if !ok {
			return false, apierr.ErrorDbNotFound("商品不存在")
		}
		canApplyUniqueMap[item.GetApplySkuUniqueNo()] -= item.Num
		if val < 0 {
			return false, apierr.ErrorNotAllow("申请数量超过最大可申请数量!")
		}
		if canApplyUniqueMap[item.GetApplySkuUniqueNo()] == 0 {
			delete(canApplyUniqueMap, item.GetApplySkuUniqueNo())
		}
	}

	if len(canApplyUniqueMap) == 0 {
		return true, nil
	}

	return false, nil
}

func (o *OrderAfterSaleBiz) GetExchangeOriginalOrderNumberSku(ctx context.Context, originalOrderNumber string, skuNo string) (*bizDo.OrderAfterSaleGoodsDo, error) {
	return o.orderAfterSaleGoodsRepo.GetExchangeOriginalOrderNumberSku(ctx, originalOrderNumber, skuNo)
}
func (o *OrderAfterSaleBiz) FindByOriginalNumber(ctx context.Context, orderNumber string) ([]*do.OrderDetailDo, error) {
	return o.orderRepo.FindByOriginalNumber(ctx, orderNumber)
}
func (o *OrderAfterSaleBiz) GetByOrderIds(ctx context.Context, orderNumber []int) ([]*do.OrderGoodsDo, error) {
	return o.orderGoodsRepo.GetByOrderIds(ctx, orderNumber)
}

// Cancel 取消售后
func (o *OrderAfterSaleBiz) Cancel(ctx context.Context, in *bo.OrderAfterSaleCancelBo) error {
	err := in.Validate()
	if err != nil {
		return err
	}
	afterSale, err := o.orderAfterSaleRepo.Get(ctx, in.AfterSaleId)
	if !afterSale.Status.IsWaitAudit() && !(afterSale.Status.IsWaitBuyerDeliver() || afterSale.Status.IsWaitRefund()) {
		return errors.New("售后申请已受理，无法取消")
	}
	if !afterSale.PlatformStatus.IsWaitAudit() && !afterSale.PlatformStatus.IsWaitSupplierAudit() {
		return errors.New("售后申请已受理，无法取消")
	}

	if afterSale.SupplierID == int(o.conf.Hyt.SupplierId) {
		relDo, err := o.OrderAfterSaleRelRepo.FindByAfterSaleId(ctx, afterSale.ID)
		if err != nil && !ent.IsNotFound(err) {
			return err
		}
		if relDo != nil {
			res, err := o.hytClient.CloseAfterSale(&hytdo.CloseAfterSaleRequest{
				OrderAfterNum: relDo.HytNo,
				Remark:        "关闭售后单",
			})
			// 如果易关闭直接跳过
			if err != nil {
				returnErr := true
				if strings.Contains("已关闭", err.Error()) {
					returnErr = false
				}
				if returnErr {
					return err
				}
			} else {
				if !res.CloseSuccess() {
					return fmt.Errorf("售后申请关闭失败")
				}
			}
		}
	}

	_, err = o.orderAfterSaleRepo.UserClose(ctx, in.AfterSaleId)
	if err != nil {
		return err
	}
	orderInfo, _ := o.orderRepo.FindById(ctx, afterSale.OrderID)
	if orderInfo == nil {
		err = errors.New("订单信息异常")
		return err
	}
	//写入订单日志
	_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
		OrderId:          orderInfo.Id,
		OrderNumber:      orderInfo.OrderNumber,
		OrderStatus:      orderInfo.Status,
		Content:          "用户取消售后",
		OperatorUserType: valobj.OrderOperatorLogUserTypeUser,
		OperatorUserId:   in.UserId,
		OperatorUserName: in.UserNickName,
	})
	if err != nil {
		return err
	}
	//写入操作日志
	_, err = o.orderAfterSaleLogRepo.Create(ctx, &bizDo.OrderAfterSaleLogDo{
		OrderID:     afterSale.OrderID,
		OrderNumber: afterSale.OrderNumber,
		Pid:         afterSale.ID,
		CreateTime:  int(time.Now().Unix()),
		Status:      valobj.AfterSaleStatusBuyerClose,
		UserID:      in.UserId,
		UserType:    valobj.AfterSaleUserTypeBuyer,
		UserName:    in.UserNickName,
		Content:     "取消售后",
	})
	if err = o.orderAfterSaleDealRepo.Invalid(ctx, afterSale.ID); err != nil {
		return err
	}
	//修改订单状态为售后中
	_, err = o.orderRepo.UpdateAfterSaleStatus(ctx, &bo.OrderUpdateAfterStatusBo{
		OrderId:         afterSale.OrderID,
		OrderNumber:     afterSale.OrderNumber,
		AfterSaleStatus: valobj.OrderAfterSaleStatusClose,
	})
	if err != nil {
		return err
	}
	return err
}

// AddDeliverInfo 添加物流信息
func (o *OrderAfterSaleBiz) AddDeliverInfo(ctx context.Context, in *bo.OrderAfterSaleDeliverAddBo) (err error) {
	afterSale, _ := o.orderAfterSaleRepo.Get(ctx, in.AfterSaleId)
	if afterSale == nil {
		return errors.New("售后信息异常")
	}
	if !afterSale.Status.IsWaitBuyerDeliver() {
		return errors.New("当前售后状态不允许添加物流信息")
	}
	if !afterSale.Type.Exists() {
		return errors.New("售后类型不支持添加物流信息")
	}

	kdInfo, _ := o.kdRepo.Find(ctx, &bo.KdQueryBo{
		Id: in.KdId,
	})
	if kdInfo == nil {
		return errors.New("快递公司信息异常")
	}

	deliverInfo, _ := o.orderAfterSaleDeliverRepo.FindByPid(ctx, in.AfterSaleId)
	if deliverInfo == nil {
		return errors.New("售后物流信息异常")
	}

	status := valobj.AfterSaleStatusExchangeBuyerDelivering
	if afterSale.Type.IsRefundReturn() {
		status = valobj.AfterSaleStatusReturnBuyerDelivering
	}

	err = o.trans.Exec(ctx, func(ctx context.Context) error {
		deliverInfo.Remark = in.Remark
		deliverInfo.ExpressNo = in.ExpressNo
		deliverInfo.Images = in.ImagesJson()
		deliverInfo.KdID = in.KdId
		deliverInfo.KdCode = kdInfo.Code
		deliverInfo.KdName = kdInfo.Name
		deliverInfo.UserContactPhone = in.UserContactPhone
		_, err = o.orderAfterSaleDeliverRepo.AddDeliver(ctx, deliverInfo)
		if err != nil {
			return err
		}

		afterSale.Status = status
		afterSale.UpdateTime = helper.GetNow()
		_, err = o.orderAfterSaleRepo.Update(ctx, afterSale)
		return err
	})
	if err != nil {
		return err
	}

	// 货易通
	if afterSale.SupplierID == int(o.conf.Hyt.SupplierId) {
		relDos, err := o.OrderAfterSaleRelRepo.FindHytByAfterSaleIds(ctx, []int{afterSale.ID})
		if err != nil {
			return err
		}
		for _, item := range relDos {
			_, err = o.hytClient.AfterSaleReturn(&hytdo.AfterSaleReturnRequest{
				OrderAfterNum:         item.HytNo,
				ReturnLogistics:       kdInfo.Name,
				ReturnLogisticsOddNum: in.ExpressNo,
			})
			if err != nil {
				o.log.Errorf("货易通售后退货失败<%s-%d>：%s err: %v", afterSale.OrderNumber, afterSale.ID, item.HytNo, err)
			}
		}
	}

	//写入操作日志
	_, err = o.orderAfterSaleLogRepo.Create(ctx, &bizDo.OrderAfterSaleLogDo{
		OrderID:     afterSale.OrderID,
		OrderNumber: afterSale.OrderNumber,
		Pid:         afterSale.ID,
		CreateTime:  int(time.Now().Unix()),
		Status:      status,
		UserID:      in.UserId,
		UserType:    valobj.AfterSaleUserTypeBuyer,
		UserName:    in.UserNickName,
		Content:     "买家已发货",
	})

	//物流订阅
	if kdInfo.Code != "" {
		coroutine.Run("退货物流数据订阅", func() {
			corCtx := ctx
			logistics, corErr := o.logisticSub(corCtx, kdInfo.Code, in.ExpressNo)
			if err != nil {
				o.log.Errorf("物流订阅失败：%s", err.Error())
			}
			if logistics != nil && logistics.HasDetail() {
				for _, val := range logistics.Data.Detail {
					_, err = o.orderLogisticsRepo.Create(corCtx, &bo.OrderLogisticsCreateBo{
						OrderNumber: afterSale.OrderNumber,
						KdCode:      kdInfo.Code,
						LogisticsNo: deliverInfo.ExpressNo,
						OpTime:      val.OpTimeFormat(),
						OpMessage:   val.OpMessage,
						OpDesc:      val.OpDesc,
						OpCode:      val.OpCode,
						AddressText: val.AddressText,
						From:        valobj.OrderLogisticsFromSub,
					})
					if corErr != nil {
						o.log.Errorf("保存退货订阅物流信息失败<%s>, err: %v", deliverInfo.ExpressNo, corErr)
					}
				}

			}
		})
	}
	return err
}

// List 售后列表
func (o *OrderAfterSaleBiz) List(ctx context.Context, reqBo *bizBo.OrderAfterSaleSearchBo) ([]*bizDo.OrderAfterSaleDo, *bizBo.RespPageBo, error) {
	orderAfterSaleDos, page := o.orderAfterSaleRepo.SearchList(ctx, reqBo)
	if page.Total == 0 {
		return orderAfterSaleDos, page, nil
	}
	orderNos := make([]string, 0, len(orderAfterSaleDos))
	for _, v := range orderAfterSaleDos {
		orderNos = append(orderNos, v.OrderNumber)
	}

	orderDos, err := o.bizOrderRepo.FindWithEdges(ctx, &bizBo.OrderWithEdgeBo{}, orderNos...)
	if err != nil {
		return orderAfterSaleDos, page, nil
	}

	for _, v := range orderDos {
		orderNos = append(orderNos, v.GetOriginalOrderNo())
	}
	orderNos = slice.Unique(orderNos)

	orderGoodsDos, err := o.bizOrderGoodsRepo.FindByOrderNumber(ctx, orderNos...)
	if err != nil {
		return orderAfterSaleDos, page, err
	}
	orderGoodsMap := make(map[string][]*bizDo.OrderGoodsDo)
	for _, v := range orderGoodsDos {
		if _, ok := orderGoodsMap[v.OrderNumber]; !ok {
			orderGoodsMap[v.OrderNumber] = make([]*bizDo.OrderGoodsDo, 0)
		}
		orderGoodsMap[v.OrderNumber] = append(orderGoodsMap[v.OrderNumber], v)
	}

	// 直接使用所有订单号查询，包含换货订单不影响
	dealDos, err := o.orderAfterSaleDealRepo.FindOriginalOrderApplyItems(ctx, orderNos...)
	if err != nil {
		return orderAfterSaleDos, page, err
	}
	dealMap := make(map[string][]*bizDo.OrderAfterSaleDealDo)
	for _, v := range dealDos {
		if _, ok := dealMap[v.OriginalOrderNo]; !ok {
			dealMap[v.OriginalOrderNo] = make([]*bizDo.OrderAfterSaleDealDo, 0)
		}
		dealMap[v.OriginalOrderNo] = append(dealMap[v.OriginalOrderNo], v)
	}

	orderDosMap := slice.KeyBy(orderDos, func(item *bizDo.OrderDo) string {
		return item.OrderNumber
	})

	exchangeOrderNos := make([]string, 0)
	for _, tmpDo := range dealDos {
		if tmpDo.ExchangeOrderNo != "" {
			exchangeOrderNos = append(exchangeOrderNos, tmpDo.ExchangeOrderNo)
			exchangeOrderNos = append(exchangeOrderNos, tmpDo.OriginalOrderNo)
		}
	}
	exchangeOrderNos = slice.Unique(exchangeOrderNos)
	notFinishOrderNos := make([]string, len(exchangeOrderNos))
	if len(exchangeOrderNos) > 0 {
		notFinishOrderNos, err = o.bizOrderRepo.GetNotFinishedOrderNo(ctx, exchangeOrderNos)
		if err != nil {
			return orderAfterSaleDos, page, err
		}
	}

	for _, afterSaleInfo := range orderAfterSaleDos {
		orderDo := orderDosMap[afterSaleInfo.OrderNumber]

		afterSaleInfo.OrderAfterSaleResultDo = &bizDo.OrderAfterSaleResultDo{
			CanAfterSaleRefund:    false,
			CanAfterSaleExchange:  false,
			AfterSaleIsDealing:    false,
			CanAfterSaleRefundAll: orderDo.CanApplyRefundAll(),
		}
		tmpOrderGoodsDo := orderGoodsMap[orderDo.GetOriginalOrderNo()]
		if tmpDealDos, hasAfterSale := dealMap[orderDo.GetOriginalOrderNo()]; hasAfterSale {
			res := o.orderAfterSaleDs.GetApplyAfterSaleStatus(orderDo, tmpDealDos, tmpOrderGoodsDo, notFinishOrderNos...)
			if !orderDo.AfterSaleExpired() && orderDo.CanApplyAfterSale() {
				afterSaleInfo.OrderAfterSaleResultDo.CanAfterSaleRefund = res.CanRefund && orderDo.CanApplyAfterSaleRefund()
				afterSaleInfo.OrderAfterSaleResultDo.CanAfterSaleExchange = res.CanExchange
			}
			afterSaleInfo.OrderAfterSaleResultDo.AfterSaleIsDealing = res.IsDealing
		}

		afterSaleInfo.OrderInfo = orderDo
	}
	return orderAfterSaleDos, page, nil
}

// Detail 售后详情
func (o *OrderAfterSaleBiz) Detail(ctx context.Context, in *bo.OrderAfterSaleFindBo) (*bizDo.OrderAfterSaleDo, error) {
	var (
		afterSaleInfo *bizDo.OrderAfterSaleDo
		orderDo       *bizDo.OrderDo
		err           error
	)
	// 售后信息
	if in.OrderId != 0 {
		orderDo, err = o.bizOrderRepo.GetByIdWithEdges(ctx, in.OrderId, &bizBo.OrderWithEdgeBo{})
		if err != nil {
			return nil, errors.New("订单信息异常")
		}
		afterSaleInfo, err = o.orderAfterSaleRepo.FindLast(ctx, in.OrderId)
		if err != nil {
			return nil, errors.New("售后单信息异常")
		}
	}
	if in.AfterSaleId != 0 {
		afterSaleInfo, err = o.orderAfterSaleRepo.Get(ctx, in.AfterSaleId)
		if err != nil {
			return nil, errors.New("售后单信息异常")
		}
		orderDo, err = o.bizOrderRepo.GetByIdWithEdges(ctx, afterSaleInfo.OrderID, &bizBo.OrderWithEdgeBo{})
		if err != nil {
			return nil, errors.New("订单信息异常")
		}
	}
	if afterSaleInfo == nil {
		return nil, errors.New("售后信息异常")
	}
	if len(afterSaleInfo.OrderAfterSaleGoods) == 0 {
		return nil, errors.New("售后商品信息异常")
	}

	bos, err := o.orderAfterSaleDs.GetOrderApplyInfo(ctx, orderDo)
	if err != nil {
		return nil, err
	}
	res := o.orderAfterSaleDs.GetApplyAfterSaleStatusByBos(orderDo, bos)

	o.GetTipMsg(ctx, afterSaleInfo)
	afterSaleInfo.OrderInfo = orderDo

	afterSaleInfo.OrderAfterSaleResultDo = &bizDo.OrderAfterSaleResultDo{
		AfterSaleIsDealing:    false,
		CanAfterSaleExchange:  false,
		CanAfterSaleRefund:    res.IsDealing,
		CanAfterSaleRefundAll: orderDo.CanApplyRefundAll(),
	}

	if !orderDo.AfterSaleExpired() && orderDo.CanApplyAfterSale() {
		afterSaleInfo.OrderAfterSaleResultDo.CanAfterSaleRefund = res.CanRefund && orderDo.CanApplyAfterSaleRefund()
		afterSaleInfo.OrderAfterSaleResultDo.CanAfterSaleExchange = res.CanExchange
	}
	afterSaleInfo.OrderAfterSaleResultDo.AfterSaleIsDealing = res.IsDealing

	return afterSaleInfo, nil
}

func (o *OrderAfterSaleBiz) GetTipMsg(ctx context.Context, in *bizDo.OrderAfterSaleDo) {
	switch in.Status {
	case valobj.AfterSaleStatusWaitAudit:
		//t := constants.OrderAfterSaleAuditExpireTime - (int(time.Now().Unix()) - in.CreateTime)
		//d, h, m := helper.ConvertDayHourMinute(t)
		in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
			Title: in.Status.String(),
			//Msg:   fmt.Sprintf("<div>您的售后单已提交成功，商家将在<span style=\"color:#FF5656\">%d天%d时%d分</span>内为您审核</div>", d, h, m),
			Msg: fmt.Sprintf("<div>您的售后单已提交成功，商家将在<span style=\"color:#FF5656\">%d天</span>内为您审核</div>", 3),
		})

		var nextStepTitle, nextStepMsg string
		switch in.Type {
		case valobj.AfterSaleTypeRefund:
			nextStepTitle = "退款成功"
			nextStepMsg = "待商家审核成功后进行原路退款"
		case valobj.AfterSaleTypeRefundReturn:
			nextStepTitle = "请退货"
			nextStepMsg = "待商家审核成功后进行退货"
		case valobj.AfterSaleTypeExchange:
			nextStepTitle = "请换货"
			nextStepMsg = "待商家审核成功后进行退货"
		case valobj.AfterSaleTypeReissue:
			nextStepTitle = "补发商品"
			nextStepMsg = "待商家审核成功后进行补发商品"
		}
		in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
			Title: nextStepTitle,
			Msg:   nextStepMsg,
		})
		return
	case valobj.AfterSaleStatusWaitBuyerDeliver:
		switch in.Type {
		case valobj.AfterSaleTypeRefund:
			//t := constants.OrderAfterSaleRefundExpireTime - (int(time.Now().Unix()) - in.UpdateTime)
			//d, h, m := helper.ConvertDayHourMinute(t)
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "审核通过",
				//Msg:   fmt.Sprintf("<div>您的售后单已审核通过，商家将在<span style=\"color:#FF5656\">%d天%d时%d分</span>内为您退款</div>", d, h, m),
				Msg: fmt.Sprintf("<div>您的售后单已提交成功，商家将在<span style=\"color:#FF5656\">%d天</span>内为您审核</div>", 3),
			})
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "退款成功",
				Msg:   "退款金额将原路返回至您的账户",
			})
			return
		case valobj.AfterSaleTypeRefundReturn, valobj.AfterSaleTypeExchange:
			t := constants.OrderAfterSaleReturnExpireTime - (int(time.Now().Unix()) - in.UpdateTime)
			d, h, m := helper.ConvertDayHourMinute(t)
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "寄回商品",
				Msg:   fmt.Sprintf("<div>请您在<span style=\"color:#FF5656\">%d天%d时%d分</sapn>内将快递寄出</div>", d, h, m),
			})

			nextTip := &bizDo.AfterSaleTip{
				Title: "暂无寄回地址，请联系客服",
				Msg:   "",
				Extra: "",
			}
			if in.OrderAfterSaleDeliver != nil {
				nextTip.Title = in.OrderAfterSaleDeliver.ContactName
				nextTip.Msg = in.OrderAfterSaleDeliver.Address
				nextTip.Extra = in.OrderAfterSaleDeliver.ContactPhone
			}
			in.AfterSaleTip = append(in.AfterSaleTip, nextTip)
		}
	case valobj.AfterSaleStatusAuditRefuse:
		in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
			Title: "已拒绝",
			Msg:   "商家已拒绝您的售后申请",
		})

		operationLog, _ := o.orderAfterSaleLogRepo.FindRefuseOne(ctx, in.ID)
		nextTip := &bizDo.AfterSaleTip{
			Title: "拒绝原因",
			Msg:   "未知原因",
		}
		if operationLog != nil {
			nextTip.Msg = xss.FilterXSS(operationLog.CutRefuseContentPrefix(), xss.XssOption{})
		}
		in.AfterSaleTip = append(in.AfterSaleTip, nextTip)
	case valobj.AfterSaleStatusReturnBuyerDelivering:
		in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
			Title: "商家收货",
			Msg:   "请等待商家确认收货无误后，将退款原路返回到您的账户",
		})

		nextTip := &bizDo.AfterSaleTip{
			Title: "已发货",
			Msg:   fmt.Sprintf("%s 运单号：%s", in.OrderAfterSaleDeliver.KdName, in.OrderAfterSaleDeliver.ExpressNo),
			Extra: "暂无物流数据信息",
		}
		if in.OrderAfterSaleDeliver != nil {
			lastLogistic, _ := o.orderLogisticsRepo.FindLast(ctx, &bo.OrderLogisticsFindLastBo{
				OrderNumber: in.OrderNumber,
				LogisticsNo: in.OrderAfterSaleDeliver.ExpressNo,
			})
			if lastLogistic != nil {
				nextTip.Title = lastLogistic.OpDesc
				nextTip.Extra = lastLogistic.OpMessage
			}
		}
		in.AfterSaleTip = append(in.AfterSaleTip, nextTip)
	case valobj.AfterSaleStatusExchangeBuyerDelivering:
		in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
			Title: "商家收货",
			Msg:   "待商家收货并确认无误后，将换货商品寄出",
		})

		nextTip := &bizDo.AfterSaleTip{
			Title: "已发货",
			Msg:   fmt.Sprintf("%s 运单号：%s", in.OrderAfterSaleDeliver.KdName, in.OrderAfterSaleDeliver.ExpressNo),
			Extra: "暂无物流数据信息",
		}
		lastLogistic, _ := o.orderLogisticsRepo.FindLast(ctx, &bo.OrderLogisticsFindLastBo{
			OrderNumber: in.OrderNumber,
			LogisticsNo: in.OrderAfterSaleDeliver.ExpressNo,
		})
		if lastLogistic != nil {
			nextTip.Title = lastLogistic.OpDesc
			nextTip.Extra = lastLogistic.OpMessage
		}
		in.AfterSaleTip = append(in.AfterSaleTip, nextTip)
	case valobj.AfterSaleStatusWaitRefund:
		t := constants.OrderAfterSaleReturnRefundExpireTime - (int(time.Now().Unix()) - in.UpdateTime)
		d, h, m := helper.ConvertDayHourMinute(t)
		switch in.Type {
		case valobj.AfterSaleTypeRefund:
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "商家退款",
				Msg:   "待商家确认无误后，为您退款",
			})
		case valobj.AfterSaleTypeRefundReturn:
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "商家确认中",
				Msg:   fmt.Sprintf("<div>商家已收到退货商品，待确认无误后，将退款原路返回到您的账户，还剩：<span style=\"color:#FF5656\">%d天%d时%d分</span></div>", d, h, m),
			})
		}
	case valobj.AfterSaleStatusWaitExchange:
		t := constants.OrderAfterSaleExchangeExpireTime - (int(time.Now().Unix()) - in.UpdateTime)
		d, h, m := helper.ConvertDayHourMinute(t)
		switch in.Type {
		case valobj.AfterSaleTypeExchange:
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "商家确认中",
				Msg:   fmt.Sprintf("<div>商家已收到退货商品，待确认无误后，将为您换货，还剩：<span style=\"color:#FF5656\">%d天%d时%d分</span></div>", d, h, m),
			})
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "商家换货",
				Msg:   "待商家确认无误后，为您换货",
			})
		case valobj.AfterSaleTypeReissue:
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "商家确认中",
				Msg:   "<div>商家已审核通过，待确认无误后，将在7天内您换货</div>",
			})
			in.AfterSaleTip = append(in.AfterSaleTip, &bizDo.AfterSaleTip{
				Title: "商家换货",
				Msg:   "待商家确认无误后，为您换货",
			})
		}
	}
}

// OperationLog 售后日志
func (o *OrderAfterSaleBiz) OperationLog(ctx context.Context, pid int) ([]*bizDo.OrderAfterSaleLogDo, error) {
	return o.orderAfterSaleLogRepo.GetByPid(ctx, pid)
}

// Logistics 退货物流详情
func (o *OrderAfterSaleBiz) Logistics(ctx context.Context, id int) (
	afterSaleDeliver *bizDo.OrderAfterSaleDeliverDo,
	afterSaleGoods []*bizDo.OrderAfterSaleGoodsDo,
	logistics []*do.OrderLogisticsDo,
	err error,
) {
	afterSale, _ := o.orderAfterSaleRepo.Get(ctx, id)
	if afterSale == nil {
		err = errors.New("售后单不存在")
		return
	}
	if !afterSale.Type.Exists() {
		err = errors.New("售后单类型错误")
		return
	}
	//if !afterSale.Status.IsReturnBuyerDelivering() && !afterSale.Status.IsExchangeBuyerDelivering() {
	//	err = errors.New("售后单当前状态无法查看物流信息")
	//	return
	//}
	afterSaleDeliver, _ = o.orderAfterSaleDeliverRepo.FindByPid(ctx, id)
	if afterSaleDeliver == nil {
		err = errors.New("售后单发货信息不存在")
		return
	}
	afterSaleGoods, _ = o.orderAfterSaleGoodsRepo.GetAfterSaleGoodsByPid(ctx, id)
	if afterSaleGoods == nil || len(afterSaleGoods) == 0 {
		err = errors.New("售后单商品信息不存在")
		return
	}

	logistics, _ = o.orderLogisticsRepo.Get(ctx, &bo.OrderLogisticsFindLastBo{
		OrderNumber: afterSaleDeliver.OrderNumber,
		LogisticsNo: afterSaleDeliver.ExpressNo,
	})
	return
}

// UpdateDeliver 更新退货物流信息
func (o *OrderAfterSaleBiz) UpdateDeliver(ctx context.Context, in *bo.OrderAfterSaleDeliverUpdateBo) error {
	err := in.Validate()
	if err != nil {
		return err
	}
	afterSaleInfo, _ := o.orderAfterSaleRepo.Get(ctx, in.AfterSaleId)
	if afterSaleInfo == nil {
		return apierr.ErrorDbNotFound("售后单不存在")
	}
	if !afterSaleInfo.Status.IsReturnBuyerDelivering() && !afterSaleInfo.Status.IsExchangeBuyerDelivering() {
		return apierr.ErrorDbNotFound("售后单当前状态不允许修改退货信息")
	}
	if afterSaleInfo.SupplierID == int(o.conf.Hyt.SupplierId) {
		return apierr.ErrorException("暂不支持修改售后信息")
	}
	if !afterSaleInfo.Type.Exists() {
		return apierr.ErrorDbNotFound("售后单类型错误")
	}

	deliverInfo, _ := o.orderAfterSaleDeliverRepo.FindByPid(ctx, in.AfterSaleId)
	if deliverInfo == nil {
		return apierr.ErrorDbNotFound("退货信息不存在")
	}

	kdInfo, _ := o.kdRepo.Find(ctx, &bo.KdQueryBo{
		Id: in.KdId,
	})
	if kdInfo == nil {
		return apierr.ErrorDbNotFound("快递公司不存在")
	}

	oldExpressNo := deliverInfo.ExpressNo
	oldKdId := deliverInfo.KdID
	oldKdCode := deliverInfo.KdCode

	deliverInfo.ExpressNo = in.ExpressNo
	deliverInfo.KdID = in.KdId
	deliverInfo.KdName = kdInfo.Name
	deliverInfo.KdCode = kdInfo.Code
	deliverInfo.UserContactPhone = in.UserContactPhone
	deliverInfo.Remark = in.Remark
	deliverInfo.Images = in.ImagesJson()
	_, err = o.orderAfterSaleDeliverRepo.UpdateDeliver(ctx, deliverInfo)
	if err != nil {
		return apierr.ErrorNotAllow("更新退货信息失败")
	}
	//物流订阅
	if kdInfo.Code != "" && oldExpressNo != in.ExpressNo && oldKdId != in.KdId {
		coroutine.Run("退货物流数据订阅", func() {
			corCtx := ctx
			logistics, corErr := o.logisticSub(corCtx, kdInfo.Code, in.ExpressNo)
			if err != nil {
				o.log.Errorf("物流订阅失败：%s", err.Error())
			}
			if logistics != nil && logistics.HasDetail() {
				for _, val := range logistics.Data.Detail {
					_, err = o.orderLogisticsRepo.Create(corCtx, &bo.OrderLogisticsCreateBo{
						OrderNumber: deliverInfo.OrderNumber,
						KdCode:      kdInfo.Code,
						LogisticsNo: deliverInfo.ExpressNo,
						OpTime:      val.OpTimeFormat(),
						OpMessage:   val.OpMessage,
						OpDesc:      val.OpDesc,
						OpCode:      val.OpCode,
						AddressText: val.AddressText,
						From:        valobj.OrderLogisticsFromSub,
					})
					if corErr != nil {
						o.log.Errorf("保存退货订阅物流信息失败<%s>, err: %v", deliverInfo.ExpressNo, corErr)
					}
				}
			}
			_, corErr = o.orderLogisticsRepo.Delete(ctx, &bo.OrderLogisticsDeleteBo{
				OrderNumber: deliverInfo.OrderNumber,
				KdCode:      oldKdCode,
				LogisticsNo: oldExpressNo,
			})
			if corErr != nil {
				o.log.Errorf("删除旧的退货物流数据失败<%s>, err: %v", deliverInfo.ExpressNo, corErr)
			}
		})
	}
	//写入售后日志
	_, err = o.orderAfterSaleLogRepo.Create(ctx, &bizDo.OrderAfterSaleLogDo{
		OrderID:     afterSaleInfo.OrderID,
		OrderNumber: afterSaleInfo.OrderNumber,
		Pid:         afterSaleInfo.ID,
		CreateTime:  int(time.Now().Unix()),
		Status:      afterSaleInfo.Status,
		UserID:      in.UserId,
		UserType:    valobj.AfterSaleUserTypeBuyer,
		UserName:    in.UserNickName,
		Content:     "买家修改退货物流信息",
	})
	return nil
}

// Update 修改售后单
func (o *OrderAfterSaleBiz) Update(ctx context.Context, in *bo.OrderAfterSaleUpdateBo) error {
	if err := in.Validate(); err != nil {
		return err
	}
	afterSaleInfo, _ := o.orderAfterSaleRepo.Get(ctx, in.AfterSaleId)
	if afterSaleInfo == nil {
		return apierr.ErrorDbNotFound("售后单不存在")
	}
	if !afterSaleInfo.Status.IsWaitAudit() {
		return apierr.ErrorDbNotFound("售后单当前状态不允许修改")
	}

	afterSaleInfo.ReceiveStatus = in.ReceiveStatus
	afterSaleInfo.Reason = in.Reason
	afterSaleInfo.Images = in.ImagesJson()
	afterSaleInfo.Remark = in.Remark
	if afterSaleInfo.Type.IsRefund() || afterSaleInfo.Type.IsRefundReturn() {
		afterSaleInfo.RefundAmount = decimal.NewFromFloat(in.RefundGoodsAmount).Add(decimal.NewFromFloat(in.RefundFreightFee)).InexactFloat64()
		afterSaleInfo.RefundFreightFee = in.RefundFreightFee
		afterSaleInfo.RefundGoodsAmount = in.RefundGoodsAmount
	}
	_, err := o.orderAfterSaleRepo.Update(ctx, afterSaleInfo)
	//写入售后日志
	_, err = o.orderAfterSaleLogRepo.Create(ctx, &bizDo.OrderAfterSaleLogDo{
		OrderID:     afterSaleInfo.OrderID,
		OrderNumber: afterSaleInfo.OrderNumber,
		Pid:         afterSaleInfo.ID,
		CreateTime:  int(time.Now().Unix()),
		Status:      afterSaleInfo.Status,
		UserID:      in.UserId,
		UserType:    valobj.AfterSaleUserTypeBuyer,
		UserName:    in.UserNickName,
		Content:     "买家修改售后申请信息",
	})
	return err
}

// Goods 可以申请售后的商品
func (o *OrderAfterSaleBiz) Goods(ctx context.Context, orderNo string) (*bizDo.OrderDo, []*bizBo.OrderAfterSaleSkuBo, error) {
	orderDo, err := o.GetOrderInfo(ctx, orderNo, &bizBo.OrderWithEdgeBo{})
	if err != nil {
		return nil, nil, err
	}
	bos, err := o.orderAfterSaleDs.GetOrderApplyInfo(ctx, orderDo)
	if err != nil {
		return nil, nil, err
	}
	res := o.orderAfterSaleDs.GetApplyAfterSaleStatusByBos(orderDo, bos)

	if !orderDo.AfterSaleExpired() && orderDo.CanApplyAfterSale() {
		orderDo.CanAfterSaleRefund = res.CanRefund && orderDo.CanApplyAfterSaleRefund()
		orderDo.CanAfterSaleExchange = res.CanExchange
	}
	orderDo.AfterSaleIsDealing = res.IsDealing

	refundData := make([]*bizBo.OrderAfterSaleSkuBo, 0, len(bos))
	exchangeData := make([]*bizBo.OrderAfterSaleSkuBo, 0, len(bos))
	notAfterSaleData := make([]*bizBo.OrderAfterSaleSkuBo, 0, len(bos))
	for _, itemBo := range bos {
		tmpBo := bizBo.OrderAfterSaleSkuBo{
			OrderNo:       itemBo.OrderNo,
			ParentOrderNo: itemBo.ParentOrderNo,
			OriginalSkuNo: itemBo.OriginalSkuNo,
			SkuNo:         itemBo.SkuNo,
			GoodsNum:      1,
			ExchangeNum:   0,
			RefundNum:     0,
			GoodsName:     itemBo.GoodsName,
			MainImage:     itemBo.MainImage,
			SalePrice:     itemBo.SalePrice,
			SaleIntegral:  itemBo.SaleIntegral,
			IsDealing:     itemBo.IsDealing,
			Tags:          []string{},
			GoodsId:       itemBo.GoodsId,
			Children:      nil,
			CanApplyNum:   1,
			CanRefund:     false,
			CanExchange:   false,
		}

		for i := 0; i < itemBo.GetCanApplyNum(); i++ {
			tmpBo.CanApplyNum = 1
			tmpBo.CanRefund = itemBo.CanRefund && orderDo.CanApplyAfterSaleRefund()
			tmpBo.CanExchange = itemBo.CanExchange && itemBo.OrderNo == orderNo
			tmpBo.OrderIsComplete = itemBo.OrderIsComplete

			tmpBo.Tags = []string{}
			if itemBo.ParentOrderNo != "" {
				if itemBo.OrderIsComplete {
					tmpBo.Tags = append(tmpBo.Tags, "已换货")
				} else {
					tmpBo.Tags = append(tmpBo.Tags, "换货中")
					tmpBo.CanRefund = false
					tmpBo.CanExchange = false
				}
			}
			notAfterSaleData = append(notAfterSaleData, helper.AnyToPtr(tmpBo))
		}

		for i := 0; i < itemBo.RefundNum; i++ {
			tmpBo.Tags = []string{"已退款"}
			if itemBo.ParentOrderNo != "" {
				if itemBo.OrderIsComplete {
					tmpBo.Tags = append(tmpBo.Tags, "已换货")
				} else {
					tmpBo.Tags = append(tmpBo.Tags, "换货中")
				}
			}
			tmpBo.CanApplyNum = 1
			tmpBo.CanRefund = false
			tmpBo.CanExchange = false
			tmpBo.OrderIsComplete = true
			refundData = append(refundData, helper.AnyToPtr(tmpBo))
		}
	}

	resData := make([]*bizBo.OrderAfterSaleSkuBo, 0)
	resData = append(resData, refundData...)
	resData = append(resData, exchangeData...)
	resData = append(resData, notAfterSaleData...)
	return orderDo, resData, nil
}

// DeliverInfo 获取退货信息
func (o *OrderAfterSaleBiz) DeliverInfo(ctx context.Context, afterSaleId int) (*bizDo.OrderAfterSaleDeliverDo, error) {
	return o.orderAfterSaleDeliverRepo.FindByPid(ctx, afterSaleId)
}

func (o *OrderAfterSaleBiz) logisticSub(ctx context.Context, cpCode, mailNo string) (*acldo.LogisticsSubAclDo, error) {
	logistics, err := o.logistics.Subscribe(ctx, &aclbo.LogisticsAclBo{
		CpCode: cpCode,
		MailNo: mailNo,
	})
	if err != nil {
		o.log.Errorf("物流订阅失败：%s", err.Error())
		return nil, err
	}
	if !logistics.Success() {
		if logistics.IsSubRepeat() {
			logistics, err = o.logistics.Query(ctx, &aclbo.LogisticsAclBo{
				CpCode: cpCode,
				MailNo: mailNo,
			})
			if err != nil {
				o.log.Errorf("物流查询失败：%s", err.Error())
				return nil, apierr.ErrorSystemPanic("物流查询失败:%s", err.Error())
			}
			if !logistics.Success() {
				o.log.Errorf("物流查询失败：%s", logistics.GetErrorMsg())
				return nil, apierr.ErrorSystemPanic("物流查询失败:%s", logistics.GetErrorMsg())
			}
		} else {
			o.log.Errorf("物流订阅失败：%s", logistics.GetErrorMsg())
			return nil, apierr.ErrorSystemPanic("物流订阅失败:%s", logistics.GetErrorMsg())
		}
	}
	return logistics, nil
}
func (o *OrderAfterSaleBiz) EntityRefundAmountByUpdate(ctx context.Context, reqBo *bo.RefundAmountByUpdateBo) (*bo.OrderAfterSaleRefundResult, error) {
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, reqBo.CustomerId)
	afterSaleDo, err := o.orderAfterSaleRepo.GetWithEdges(ctx, reqBo.Id, &bizBo.OrderAfterSaleEdgesBo{
		WithOrderAfterSaleDeliver:       false,
		WithOrderAfterSaleGoods:         false,
		WithOrderAfterSaleExchangeGoods: false,
		WithOrderAfterSaleApplyGoods:    false,
		WithOrderAfterSaleLog:           false,
		WithSupplier:                    false,
	})
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, errors.New("售后单不存在")
		}
		return nil, err
	}
	if !afterSaleDo.Type.IsAfterSaleRefund() {
		return &bo.OrderAfterSaleRefundResult{
			FreightFee:        decimal.Zero,
			RefundGoodsAmount: decimal.Zero,
		}, nil
	}

	orderDo, err := o.GetOrderInfo(ctx, afterSaleDo.OrderNumber, &bizBo.OrderWithEdgeBo{WithGoods: true})
	if err != nil {
		return nil, err
	}
	goodsDos, err := o.orderAfterSaleDs.GetOrderApplyInfo(ctx, orderDo)
	if err != nil {
		return nil, err
	}
	virtualBo := &bo.OrderAfterSaleCreateBo{
		Skus:              make([]*bo.OrderAfterSaleEntitySku, 0),
		OrderNo:           afterSaleDo.OrderNumber,
		OrderId:           afterSaleDo.OrderID,
		UserId:            reqBo.UserId,
		UserNickName:      reqBo.UserName,
		Type:              afterSaleDo.Type,
		ReceiveStatus:     afterSaleDo.ReceiveStatus,
		Reason:            "",
		Images:            []string{},
		Remark:            "",
		RefundGoodsAmount: decimal.Zero,
		FreightFee:        decimal.Zero,
	}

	for _, item := range afterSaleDo.OrderAfterSaleGoods {
		virtualBo.Skus = append(virtualBo.Skus, &bo.OrderAfterSaleEntitySku{
			SkuNo:         item.SkuNo,
			Num:           item.GoodsNum,
			SourceOrderNo: afterSaleDo.OriginalOrderNo,
			OriginalSkuNo: item.OriginalSkuNo,
		})
	}
	var isAll bool
	if isAll, err = o.CheckOriginalOrderApplyInfo(virtualBo, goodsDos); err != nil {
		return nil, err
	}
	return o.CalculateApplyGoodsMaxRefundData(ctx, virtualBo, orderDo, orderDo.OrderGoods, isAll)
}
func (o *OrderAfterSaleBiz) EntityRefundAmount(ctx context.Context, reqBo *bo.OrderAfterSaleCreateBo) (*bo.OrderAfterSaleRefundResult, error) {
	orderDo, err := o.GetOrderInfo(ctx, reqBo.OrderNo, &bizBo.OrderWithEdgeBo{WithGoods: true})
	if err != nil {
		return nil, err
	}
	goodsDos, err := o.orderAfterSaleDs.GetOrderApplyInfo(ctx, orderDo)
	if err != nil {
		return nil, err
	}
	var isAll bool
	if isAll, err = o.CheckOriginalOrderApplyInfo(reqBo, goodsDos); err != nil {
		return nil, err
	}
	return o.CalculateApplyGoodsMaxRefundData(ctx, reqBo, orderDo, orderDo.OrderGoods, isAll)
}

// CalculateApplyGoodsMaxRefundData 计算最大退款金额
func (o *OrderAfterSaleBiz) CalculateApplyGoodsMaxRefundData(ctx context.Context, reqBo *bo.OrderAfterSaleCreateBo, orderDo *bizDo.OrderDo, orderGoodsDos []*bizDo.OrderGoodsDo, isAll bool) (*bo.OrderAfterSaleRefundResult, error) {
	applySkuMap := make(map[string]int)
	for _, item := range reqBo.Skus {
		if _, ok := applySkuMap[item.OriginalSkuNo]; !ok {
			applySkuMap[item.OriginalSkuNo] = 0
		}
		applySkuMap[item.OriginalSkuNo] += item.Num
	}

	refundAmount := decimal.Zero
	maxRefundGoodsAmount := decimal.NewFromFloat(orderDo.PayAmount).
		Sub(decimal.NewFromFloat(orderDo.RefundGoodsAmount)).
		Sub(decimal.NewFromFloat(orderDo.FreightFee))

	maxRefundFreightFee := decimal.Zero

	var isOrderCardGiftDiscount bool
	isOrderCardGiftDiscount = o.giftCardDs.IsOrderGiftCardDiscount(ctx, orderDo.PayOrderNumber, orderDo.OrderNumber)

	//礼品卡金额
	refundCardGiftAmount := decimal.Zero

	for _, itemDo := range orderGoodsDos {
		if applyNum, ok := applySkuMap[itemDo.SkuNo]; ok {
			if applyNum == 0 {
				continue
			}
			if isOrderCardGiftDiscount {
				//计算礼品卡退款金额
				goodsCardGiftRefundAmount, err := o.giftCardDs.GetRefundAmount(ctx, orderDo.PayOrderNumber, itemDo, applyNum)
				if err != nil {
					return nil, err
				}
				refundCardGiftAmount = refundCardGiftAmount.Add(decimal.NewFromFloat(goodsCardGiftRefundAmount))
			}

			payAmount := decimal.NewFromFloat(itemDo.GoodsPayAmount)
			quantity := decimal.NewFromInt(int64(itemDo.Quantity))
			if payAmount.LessThanOrEqual(decimal.Zero) {
				continue
			}
			if quantity.LessThanOrEqual(decimal.Zero) {
				continue
			}
			refundAmount = refundAmount.Add(
				payAmount.Mul(decimal.NewFromInt(int64(applyNum))).
					Div(quantity).RoundFloor(2),
			)
		}
	}
	res := &bo.OrderAfterSaleRefundResult{
		RefundGoodsAmount:        decimal.Zero,
		FreightFee:               maxRefundFreightFee.RoundFloor(2),
		RefundCardGiftAmount:     refundCardGiftAmount,
		RefundCardGiftFreightFee: decimal.Zero,
		IsOrderCardGiftDiscount:  isOrderCardGiftDiscount,
	}

	// 退款金额大于最大退款金额
	if maxRefundGoodsAmount.Sub(refundAmount).LessThanOrEqual(decimal.Zero) || isAll {
		res.RefundGoodsAmount = maxRefundGoodsAmount.RoundFloor(2)
		//待发货申请售后且全部退款，返回运费抵扣
		if orderDo.Status == valobj.OrderStatusAwaitingShip {
			orderCardGiftObj := o.giftCardDs.GetFreightFee(ctx, orderDo.PayOrderNumber, orderDo.OrderNumber)
			res.RefundCardGiftFreightFee = orderCardGiftObj.
				GetFreightFeeTotalAmount(orderDo.OrderNumber).
				Sub(orderCardGiftObj.GetFreightFeeTotalRefundAmount(orderDo.OrderNumber))
			res.FreightFee = decimal.NewFromFloat(orderDo.FreightFee).
				Sub(decimal.NewFromFloat(orderDo.RefundFreightFee))
		}
		return res, nil
	} else {
		res.RefundGoodsAmount = refundAmount.RoundFloor(2)
		return res, nil
	}
}
