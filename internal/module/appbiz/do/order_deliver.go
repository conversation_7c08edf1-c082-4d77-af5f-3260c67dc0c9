package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
)

type OrderDeliverDo struct {
	Id              int
	OrderId         int
	OrderNumber     string
	KdId            int
	KdCode          string
	KdName          string
	LogisticsNo     string
	LogisticsOpDesc string
	LogisticsOpCode valobj.OrderLogisticsOpCodeObj
	CreateTime      int
	DeliverGoods    []*OrderDeliverGoodsDo
	UpdateTime      int
	CustomerId      int
	ShopId          int
}

func (o *OrderDeliverDo) GetImage() string {
	if o.DeliverGoods == nil || len(o.DeliverGoods) == 0 {
		return ""
	}
	return o.DeliverGoods[0].GoodsImage
}

func (o *OrderDeliverDo) GetNowSubFromUpdateTime() int {
	return helper.GetNow() - o.UpdateTime
}
