package do

import (
	"cardMall/internal/biz/valobj"
)

type GoodsBrandInfoDo struct {
	Id        int
	Name      string
	Logo      string
	Sort      int
	Label     string
	Status    valobj.GoodsBrandStatusObj
	Recommend valobj.GoodsBrandRecommendObj
	Code      string
}

type GoodsBrandRecommendInfoDo struct {
	Id         int
	Name       string
	Logo       string
	Sort       int
	Label      string
	Status     valobj.GoodsBrandStatusObj
	Recommend  valobj.GoodsBrandRecommendObj
	CategoryId int
	Discount   float64
}

func (g *GoodsBrandInfoDo) IsEnable() bool {
	if g == nil || !g.Status.IsEnable() {
		return false
	}
	return true
}
