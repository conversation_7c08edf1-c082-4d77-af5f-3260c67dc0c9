package do

import (
	"github.com/shopspring/decimal"
)

type CartItemDo struct {
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 商品ID
	GoodsID int `json:"goods_id,omitempty"`
	// 商品SKUID
	GoodsSkuID int `json:"goods_sku_id,omitempty"`
	// 商品sku编码
	GoodsSkuNo string `json:"goods_sku_no,omitempty"`
	// 用户ID
	UserID int `json:"user_id,omitempty"`
	// 购买数量
	Quantity int `json:"quantity,omitempty"`
	// 添加到购物车的价格
	Price decimal.Decimal `json:"price,omitempty" mapstructure:"price"`
	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`
}
type CartGoodsInfoDo struct {
	// 商品类型:1=虚拟，2=实物
	Type int `json:"type,omitempty"`
	// ID of the ent.
	GoodsSkuID int `json:"goods_sku_id,omitempty"`
	// 商品名称
	GoodsSkuName string `json:"goods_sku_name,omitempty"`
	// 商品名称
	GoodsName string `json:"goods_name,omitempty"`
	// 商品ID
	GoodsID int `json:"goods_id,omitempty"`

	// 市场价
	MarketPrice decimal.Decimal `json:"market_price,omitempty"`
	// 售价【建议零售价】
	SalePrice decimal.Decimal `json:"sale_price,omitempty"`

	// 购买数量限制：0=不限
	NumLimit int `json:"num_limit,omitempty"`
	// 状态:0=下架,1=上架
	Status int `json:"status,omitempty"`

	// 状态:0=下架,1=上架
	GoodsSkuStatus int `json:"goods_sku_status,omitempty"`

	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`

	// 商品图片
	Image string `json:"image,omitempty"`
	// 规格图片商品图片
	GoodsSkuImage string `json:"goods_sku_image,omitempty"`

	CategoryId int `json:"category_id,omitempty"` // 所属分类
	//CategoryName string `json:"category_name,omitempty"` // 类目
	BrandId   int    `json:"brand_id,omitempty"`   // 品牌名称
	BrandName string `json:"brand_name,omitempty"` // 品牌名称

	// 库存
	Stock int `json:"stock,omitempty"`

	// 映射商品 【虚拟商品映射使用】
	ProductID string `json:"product_id,omitempty"`

	// 虚拟商品类型:1=直充,2=卡密
	ProductType int `json:"product_type,omitempty"`

	GoodsSkuNo string `json:"goods_sku_no,omitempty"`

	// 售价-积分
	SaleIntegral int `json:"sale_integral,omitempty"`
}
