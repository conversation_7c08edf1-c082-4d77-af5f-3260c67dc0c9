package do

import "cardMall/internal/biz/valobj"

type BannerInfoDo struct {
	Id               int
	Name             string
	Image            string
	Sort             int
	RelationType     valobj.BannerRelationTypeObj
	RelationValue    string
	Status           valobj.BannerStatusObj
	RelationValueIds []int
}

func (b *BannerInfoDo) IsEnable() bool {
	if b == nil {
		return false
	}
	return b.Status == valobj.BannerStatusEnable
}
