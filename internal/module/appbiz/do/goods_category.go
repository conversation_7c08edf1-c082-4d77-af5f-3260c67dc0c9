package do

import (
	"cardMall/internal/biz/valobj"
)

type GoodsCategoryDo struct {
	Id     int                           `json:"id"`
	Name   string                        `json:"name"`
	Sort   int                           `json:"sort"`
	Status valobj.GoodsCategoryStatusObj `json:"status"`
	Type   valobj.GoodsCategoryTypeObj   `json:"type"`
	Pid    int                           `json:"pid"`
	Level  int                           `json:"level"`
	Image  string                        `json:"image"`
	Code   string                        `json:"code"`
}

type GoodsCategoryBrandDo struct {
	CategoryId   int    `json:"category_id,omitempty"`   // 类目
	CategoryName string `json:"category_name,omitempty"` // 类目名称
	CategorySort int    `json:"category_sort,omitempty"`

	BrandId    int    `json:"brand_id,omitempty"`   // 品牌名称
	BrandName  string `json:"brand_name,omitempty"` // 品牌名称
	BrandLogo  string `json:"brand_logo,omitempty"`
	BrandLabel string `json:"brand_label,omitempty"`
	BrandSort  int    `json:"brand_sort,omitempty"`
}

func (g *GoodsCategoryDo) IsEnable() bool {
	if g == nil || !g.Status.IsEnable() {
		return false
	}
	return true
}
