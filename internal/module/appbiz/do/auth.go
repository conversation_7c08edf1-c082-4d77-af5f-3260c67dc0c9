package do

type AuthLoginDo struct {
	// 登录token
	Token string
	// 用户ID
	UserId int
	//微信昵称
	NickName string
	//微信头像
	AvatarUrl string
	//手机号
	PhoneNumber string
}

type AuthWxDo struct {
	OpenId     string `json:"openid"`
	SessionKey string `json:"session_key"`
	UnionId    string `json:"unionid"`
	ErrorCode  int    `json:"errcode"`
	ErrorMsg   string `json:"errmsg"`
}

type AuthAlipayDo struct {
	OpenId string
}
