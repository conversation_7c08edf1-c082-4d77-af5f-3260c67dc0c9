package repository

import (
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type OrderGoodsRepo interface {
	Get(ctx context.Context, in *bo.OrderGoodsQueryBo) ([]*do.OrderGoodsDo, error)

	GetMap(ctx context.Context, in *bo.OrderGoodsQueryBo) (map[string]*do.OrderGoodsDo, error)

	FindByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderGoodsDo, error)
	FindByOrderNumbers(ctx context.Context, orderNumber ...string) ([]*do.OrderGoodsDo, error)

	Find(ctx context.Context, orderNumber string) ([]*do.OrderGoodsDo, error)

	GetByOrderIds(ctx context.Context, orderIds []int) ([]*do.OrderGoodsDo, error)

	UpdatePayAmount(ctx context.Context, id int, payAmount float64) (int, error)
}
