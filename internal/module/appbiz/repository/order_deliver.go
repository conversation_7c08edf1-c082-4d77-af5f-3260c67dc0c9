package repository

import (
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type OrderDeliverRepo interface {
	GetByOrderNumber(ctx context.Context, orderNumber string) ([]*do.OrderDeliverDo, error)

	UpdateLogisticsState(ctx context.Context, id int, in *bo.OrderDeliverUpdateLogisticsBo) (int, error)

	FindByLogisticsNo(ctx context.Context, logisticsNo string) (*do.OrderDeliverDo, error)

	GetQueryLogisticsData(ctx context.Context, startTime int, endTime int, lastId int) ([]*do.OrderDeliverDo, error)

	FindNotSign(ctx context.Context, orderNumber string) (*do.OrderDeliverDo, error)

	FindOrderDeliverCount(ctx context.Context, orderNumber string) (int, error)

	FindLast(ctx context.Context, orderNumber string) (*do.OrderDeliverDo, error)
}
