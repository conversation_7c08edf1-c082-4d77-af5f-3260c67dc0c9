package repository

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type PayConfigRepo interface {
	Find(ctx context.Context, in *bo.PayConfigBo) (*do.PayConfigDo, error)

	FindChannel(ctx context.Context, channelType valobj.PayConfigChannelTypeObj) (*do.PayConfigDo, error)

	All(ctx context.Context) ([]*do.PayConfigDo, error)
}
