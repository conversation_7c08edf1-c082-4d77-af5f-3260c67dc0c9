package repository

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type OrderRepo interface {
	List(ctx context.Context, in *bo.OrderListBo) (int, []*do.OrderListDo, error)

	Detail(ctx context.Context, in *bo.OrderDetailBo) (*do.OrderDetailDo, error)

	Create(ctx context.Context, in *bo.OrderCreateBo) (int, error)

	UpdateStatus(ctx context.Context, in *bo.OrderUpdateStatusBo) (int, error)

	// HasDealing 判断订单是否正在处理中
	HasDealing(ctx context.Context, orderNumber ...string) (bool, error)

	// Recharged 根据充值结果修改订单状态
	Recharged(ctx context.Context, in *bo.OrderUpdateRechargeBo) (int, error)

	FindByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderDetailDo, error)
	FindByRequestId(ctx context.Context, requestId string) (*do.OrderDetailDo, error)

	FindByOriginalNumber(ctx context.Context, originalNumber string) ([]*do.OrderDetailDo, error)

	FindById(ctx context.Context, id int) (*do.OrderDetailDo, error)

	GetAwaitingShipOrder(ctx context.Context, payOrderNumber string) ([]*do.OrderDetailDo, error)
	GetEntityAwaitingShipOrder(ctx context.Context, payOrderNumber string) ([]*do.OrderDetailDo, error)

	// GetRefundCount 获取某个支付单下已退款充值单的数量
	GetRefundCount(ctx context.Context, payOrderNumber string) (int, error)

	// GetExceptOrderNumberRefundCount 获取某个支付单下已退款的数量，排除某个订单
	GetExceptOrderNumberRefundCount(ctx context.Context, payOrderNumber string, orderNumber string) (int, error)

	// GetExceptOrderTotalAmount 获取某个支付单订单的总金额，排除某个订单
	GetExceptOrderTotalAmount(ctx context.Context, payOrderNumber string, orderNumber string) (float64, error)

	// Refunding 修改订单状态为退款中
	Refunding(ctx context.Context, in *bo.OrderRefundingBo) (int, error)

	// RefundSuccess 退款成功
	// orderStatus 选传，传入时，订单最终状态为orderStatus[0]
	RefundSuccess(ctx context.Context, in *bo.OrderRefundSuccessBo, orderStatus ...valobj.OrderStatusObj) (int, error)

	// CanceledOrderByPayOrderNumber 取消充值订单
	CanceledOrderByPayOrderNumber(ctx context.Context, payOrderNumber string) (int, error)

	// GetNeedReceivedOrder 获取需要系统自动收货的订单
	GetNeedReceivedOrder(ctx context.Context, in *bo.NeedReceivedOrderBo) ([]*do.OrderListDo, error)

	GetByPayOrderNumber(ctx context.Context, payOrderNumbers []string) ([]*do.OrderDetailDo, error)

	GetByPayOrderNumberMap(ctx context.Context, payOrderNumbers []string) (map[string]*do.OrderDetailDo, error)

	// CanceledPayOrder 修改超时未支付单为已取消状态
	CanceledPayOrder(ctx context.Context, payOrderNumber string) (int, error)

	// SetPayOrderSuccess 修改支付单为已支付状态
	SetPayOrderSuccess(ctx context.Context, payOrderNumber string) (int, error)

	// UpdateOrderStatusByPayOrderNumber 修改订单为已支付状态
	UpdateOrderStatusByPayOrderNumber(ctx context.Context, in *bo.UpdateOrderStatusByPayOrderNumber) (int, error)

	// UpdateOrderSiteByPayOrderNumber 修改订单站点--适用于美团订单
	UpdateOrderSiteByPayOrderNumber(ctx context.Context, in *bo.OrderUpdateSiteBo) (int, error)

	// UpdateReceiveStatus 修改订单签收状态
	UpdateReceiveStatus(ctx context.Context, orderNumber string, receiveStatus valobj.OrderReceiveStatusObj) (int, error)

	// GetNeedReceivedEntity 获取需要系统自动签收的实物订单
	GetNeedReceivedEntity(ctx context.Context, timeOut int) ([]*do.OrderListDo, error)

	// UserSkuQuantitySum 获取用户在某段时间内购买的商品数量
	UserSkuQuantitySum(ctx context.Context, in *bo.UserSkuQuantitySumBo) (int, error)

	// FindDetailByPayOrderNumber 根据支付单号查询订单详情，支付宝积分商城订单详情使用
	FindDetailByPayOrderNumber(ctx context.Context, payOrderNumber string) (*do.OrderDetailDo, error)

	// FindPayOrderDetailByPayOrderNumber 根据支付单号查询订单详情，支付宝积分商城订单详情使用
	FindPayOrderDetailByPayOrderNumber(ctx context.Context, payOrderNumber string) (*do.OrderDetailDo, error)
	UpdateAfterSaleStatus(ctx context.Context, in *bo.OrderUpdateAfterStatusBo) (int, error)

	// GetExceptOrderCouponDiscountAmount 获取订单退款金额
	GetExceptOrderCouponDiscountAmount(ctx context.Context, payOrderNumber string, orderNumber string) (float64, error)

	GetExceptOrderRefundCouponDiscountAmount(ctx context.Context, payOrderNumber string, orderNumber string) (float64, error)

	StatusCount(ctx context.Context, in *bo.OrderStatusCountBo) ([]*do.OrderStatusCountDo, error)

	AfterSaleHandingCount(ctx context.Context, userId int) (int, error)

	// UpdateDiscount 更新订单优惠信息，场景订单使用
	UpdateDiscount(ctx context.Context, in *bo.OrderDiscountBo) (int, error)

	GetDetailByPayOrderNumber(ctx context.Context, payOrderNumber string) ([]*do.OrderDetailDo, error)
}
