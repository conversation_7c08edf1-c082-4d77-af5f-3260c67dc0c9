package repository

import (
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type UserAddressRepo interface {
	Get(ctx context.Context, in *bo.UserAddressQueryBo) ([]*do.UserAddressDo, error)

	Add(ctx context.Context, in *bo.UserAddressAddBo) (int, error)

	Update(ctx context.Context, in *bo.UserAddressUpdateBo) (int, error)

	Del(ctx context.Context, in *bo.UserAddressDelBo) (int, error)

	Find(ctx context.Context, in *bo.UserAddressQueryBo) (*do.UserAddressDo, error)

	GetOne(ctx context.Context, id int) (*do.UserAddressDo, error)

	SetDefault(ctx context.Context, in *bo.UserAddressSetDefaultBo) (int, error)

	Count(ctx context.Context, userId int) int
}
