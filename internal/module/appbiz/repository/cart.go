package repository

import (
	"cardMall/internal/data/ent"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type CartRepo interface {
	CartItemList(ctx context.Context, in *bo.CartItemListBo) (int, []*do.CartItemDo, error)

	CartItemTotal(ctx context.Context, in *bo.CartItemListBo) (int, error)

	FindOneByWhere(ctx context.Context, in *bo.CartItemQueryBo) (*do.CartItemDo, error)

	AddQuantity(ctx context.Context, in *ent.CartItem) (lastInsertId, rowsAffected int64, err error)

	UpdateQuantity(ctx context.Context, in *ent.CartItem) (int, error)

	DeleteCartItem(ctx context.Context, in *bo.CartItemDeleteBo) (int, error)

	GetAll(ctx context.Context, in *bo.CartItemAllBo) ([]*do.CartItemDo, error)
}
