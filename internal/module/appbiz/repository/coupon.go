package repository

import (
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"context"
)

type CouponRepo interface {
	List(ctx context.Context, in *bo.CouponListBo) (int, []*do.CouponListDo, error)

	Find(ctx context.Context, in *bo.CouponQueryBo) (*do.CouponListDo, error)

	Collection(ctx context.Context, in *bo.CouponCollectionBo) (int, error)

	GetByIds(ctx context.Context, ids []int) ([]*do.CouponListDo, error)

	// OnCodeUsed 优惠券使用时，修改已使用和已领取的数量
	OnCodeUsed(ctx context.Context, id, num int) (int, error)

	// OnCodeReturn 优惠券退还时，修改已使用和已领取的数量
	OnCodeReturn(ctx context.Context, id, num int) (int, error)

	// GetProductLimitNone 获取不限商品的可使用优惠券
	GetProductLimitNone(ctx context.Context) ([]*do.CouponListDo, error)
}
