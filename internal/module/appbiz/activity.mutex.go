package appbiz

import (
	"context"
	"sync"
	"time"
)

var (
	globalAM *ActivityMutex
	once     sync.Once
)

type ActivityMutex struct {
	cleanupTimer *time.Ticker
	Locks        sync.Map
}

func InitActivityMutex(f func(context.Context, string) bool) *ActivityMutex {
	once.Do(func() {
		globalAM = &ActivityMutex{}
		go globalAM.cleanup(f)
	})
	return globalAM
}

func (am *ActivityMutex) GetLock(cacheKey string) *sync.Mutex {
	lock, _ := am.Locks.LoadOrStore(cacheKey, &sync.Mutex{})
	return lock.(*sync.Mutex)
}

func (am *ActivityMutex) DeleteLock(cacheKey string) {
	am.Locks.Delete(cacheKey)
}

// cleanup 删除过期的锁
func (am *ActivityMutex) cleanup(f func(context.Context, string) bool) {
	am.cleanupTimer = time.NewTicker(time.Minute * 30)
	defer am.cleanupTimer.Stop()
	for range am.cleanupTimer.C {
		am.Locks.Range(func(key, value interface{}) bool {
			cacheKey, ok := key.(string)
			if !ok {
				return true
			}
			//ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
			//defer cancel()
			ctx := context.Background()

			if f(ctx, cacheKey) {
				am.DeleteLock(cacheKey)
			}
			return true
		})
	}
}
