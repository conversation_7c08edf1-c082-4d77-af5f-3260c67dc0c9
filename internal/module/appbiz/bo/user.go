package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"errors"
	"golang.org/x/crypto/bcrypt"
)

type UserUpdateBo struct {
	Id          int
	WxNickName  string
	WxAvatarUrl string
	PhoneNumber string
	Password    string

	NickName   string
	ClientType valobj.UserClientTypeObj
	// 微信openId
	WxOfficialOpenId string
	// 微信小程序openId
	WxOpenId string
	// 支付宝小程序openId
	AlipayOpenId string
}

type UserCreateBo struct {
	WxOpenId     string
	NickName     string
	AvatarUrl    string
	AlipayOpenId string
	ClientType   valobj.UserClientTypeObj
}

type UserQueryBo struct {
	Id           int
	OpenId       string
	AlipayOpenId string
}

func (u *UserUpdateBo) Validate() error {
	if u.WxNickName == "" && u.WxAvatarUrl == "" && u.PhoneNumber == "" {
		return apierr.ErrorParam("请填写个人信息")
	}
	if u.PhoneNumber != "" && !helper.IsAccountPhone(u.PhoneNumber) {
		return apierr.ErrorParam("手机号格式错误")
	}
	if u.Id <= 0 {
		return apierr.ErrorParam("缺少参数")
	}
	return nil
}

type CreateUserBo struct {
	PhoneNumber string
	NickName    string
	ClientType  valobj.UserClientTypeObj

	Username string
	// 微信openId
	WxOfficialOpenId string
	// 微信小程序openId
	WxOpenId string
	// 支付宝小程序openId
	AlipayOpenId string
}
type RegisterBo struct {
	Username        string
	PhoneNumber     string
	Password        string
	ConfirmPassword string
	VerifyCode      string
	VerifyId        string
	ClientType      valobj.UserClientTypeObj

	AesKey string
}

func (l *RegisterBo) Validate() error {
	if val := len(l.Username); val < 2 || val > 20 {
		err := errors.New("账号长度需在2-20位之间")
		return err
	}
	if !l.ValidateUsername() {
		return errors.New("用户名不能包含中文")
	}
	if l.GetDecryptPassword() == "" {
		err := errors.New("密码不符合规则")
		return err
	}
	if l.GetDecryptConfirmPassword() == "" {
		err := errors.New("密码不符合规则")
		return err
	}
	if !l.ValidatePassword() {
		return errors.New("密码长度需在8-20位之间，且")
	}
	if !l.ValidatePasswordConfirm() {
		return errors.New("两次输入的密码不一致，且不包含连续")
	}
	if val := len(l.GetDecryptPassword()); val <= 0 || val > 20 {
		err := errors.New("密码不符合规则")
		return err
	}
	return nil
}
func (l *RegisterBo) ValidateUsername() bool {
	return !helper.HasChineseCharacters(l.Username)
}

func (a *RegisterBo) GetPwdHash() (string, error) {
	if a.Password == "" {
		return "", nil
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(a.GetDecryptPassword()), bcrypt.DefaultCost)
	return string(hashedPassword), err
}
func (l *RegisterBo) ValidatePassword() bool {
	return helper.ValidatePassword(l.GetDecryptPassword())
}
func (l *RegisterBo) ValidatePasswordConfirm() bool {
	return l.GetDecryptPassword() == l.GetDecryptConfirmPassword()
}

// GetDecryptPassword .
func (l *RegisterBo) GetDecryptPassword() string {
	return helper.AesEcbDecrypt(l.AesKey, l.Password)
}

// GetConfirmPassword .
func (l *RegisterBo) GetDecryptConfirmPassword() string {
	return helper.AesEcbDecrypt(l.AesKey, l.ConfirmPassword)
}

type BindPasswordBo struct {
	PhoneNumber     string
	Password        string
	ConfirmPassword string
	VerifyCode      string
	VerifyId        string
	ClientType      valobj.UserClientTypeObj

	AesKey string
}

func (l *BindPasswordBo) Validate() error {
	if !helper.IsAccountPhone(l.PhoneNumber) {
		err := errors.New("手机号格式错误")
		return err
	}
	if l.GetDecryptPassword() == "" {
		err := errors.New("密码不符合规则")
		return err
	}
	if l.GetDecryptConfirmPassword() == "" {
		err := errors.New("密码不符合规则")
		return err
	}

	if val := len(l.GetDecryptPassword()); val <= 0 || val > 20 {
		err := errors.New("密码不符合规则")
		return err
	}
	if !l.ValidatePassword() {
		return errors.New("密码不符合规则")
	}
	if !l.ValidatePasswordConfirm() {
		return errors.New("两次输入的密码不一致")
	}
	return nil
}

func (a *BindPasswordBo) GetPwdHash() (string, error) {
	if a.Password == "" {
		return "", nil
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(a.GetDecryptPassword()), bcrypt.DefaultCost)
	return string(hashedPassword), err
}
func (l *BindPasswordBo) ValidatePasswordConfirm() bool {
	return l.GetDecryptPassword() == l.GetDecryptConfirmPassword()
}

func (l *BindPasswordBo) ValidatePassword() bool {
	return helper.ValidatePassword(l.GetDecryptPassword())
}

// GetDecryptPassword .
func (l *BindPasswordBo) GetDecryptPassword() string {
	return helper.AesEcbDecrypt(l.AesKey, l.Password)
}

// GetDecryptConfirmPassword .
func (l *BindPasswordBo) GetDecryptConfirmPassword() string {
	return helper.AesEcbDecrypt(l.AesKey, l.ConfirmPassword)
}

type UserUpdateV2Bo struct {
	Id          int
	PhoneNumber string
	Password    string

	NickName   string
	ClientType valobj.UserClientTypeObj
	// 微信openId
	WxOfficialOpenId string
	// 微信小程序openId
	WxOpenId string
	// 支付宝小程序openId
	AlipayOpenId string
}
