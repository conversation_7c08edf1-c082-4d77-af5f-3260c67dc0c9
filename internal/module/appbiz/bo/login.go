package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"errors"
)

type SendSMSBo struct {
	PhoneNumber string
}

type LoginBo struct {
	PhoneNumber string
	VerifyCode  string
	VerifyId    string
	ClientType  valobj.UserClientTypeObj
}

func (s *SendSMSBo) PhoneIsValid() bool {
	return helper.IsAccountPhone(s.PhoneNumber)
}

func (l *LoginBo) PhoneIsValid() bool {
	return helper.IsAccountPhone(l.PhoneNumber)
}

func (l *LoginBo) Validate() error {
	if !l.PhoneIsValid() {
		return errors.New("请输入正确手机号")
	}
	if !l.ClientType.IsValid() {
		return errors.New("参数错误")
	}
	if l.VerifyCode == "" || l.VerifyId == "" {
		return errors.New("验证码错误")
	}
	return nil
}

type LoginV2Bo struct {
	Username    string
	Password    string
	PhoneNumber string
	VerifyCode  string
	VerifyId    string
	ClientType  valobj.UserClientTypeObj

	AesKey string
}

func (l *LoginV2Bo) ValidatePhoneSmsLogin() error {
	if !l.PhoneIsValid() {
		return errors.New("请输入正确手机号")
	}
	if !l.ClientType.IsValid() {
		return errors.New("参数错误")
	}
	if l.VerifyCode == "" || l.VerifyId == "" {
		return errors.New("验证码错误")
	}
	return nil
}

func (l *LoginV2Bo) PhoneIsValid() bool {
	return helper.IsAccountPhone(l.PhoneNumber)
}
func (l *LoginV2Bo) ValidatePassword() bool {
	return helper.ValidatePassword(l.GetDecryptPassword())
}
func (l *LoginV2Bo) ValidatePhonePasswordLogin() error {
	if !l.PhoneIsValid() {
		return errors.New("请输入正确手机号")
	}
	if l.Password == "" {
		return errors.New("密码不能为空")
	}
	if !l.ValidatePassword() {
		return errors.New("密码格式错误")
	}
	return nil
}

func (l *LoginV2Bo) ValidateAccountPasswordLogin() error {
	if l.Username == "" {
		return errors.New("用户名不能为空")
	}
	if l.Password == "" {
		return errors.New("密码不能为空")
	}
	if !l.ValidatePassword() {
		return errors.New("密码错误")
	}
	return nil
}

// GetDecryptPassword .
func (l *LoginV2Bo) GetDecryptPassword() string {
	return helper.AesEcbDecrypt(l.AesKey, l.Password)
}

// GetDecryptUsername .
func (l *LoginV2Bo) GetDecryptUsername() string {
	return helper.AesEcbDecrypt(l.AesKey, l.Username)
}

type OpenIdLoginBo struct {
	PhoneNumber string

	OpenId string

	VerifyCode string
	VerifyId   string
	ClientType valobj.UserClientTypeObj
}

func (l *OpenIdLoginBo) Validate() error {
	if !helper.IsAccountPhone(l.PhoneNumber) {
		return errors.New("请输入正确手机号")
	}

	return nil
}

type QuickLoginBo struct {
	PhoneNumber string
	OpenId      string
	ClientType  valobj.UserClientTypeObj
}

func (l *QuickLoginBo) Validate() error {
	if !helper.IsAccountPhone(l.PhoneNumber) {
		return apierr.ErrorParam("手机号格式错误")
	}
	if l.OpenId == "" {
		return apierr.ErrorParam("参数错误")
	}
	return nil
}
