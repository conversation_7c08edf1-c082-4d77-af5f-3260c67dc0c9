package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
)

type OrderLogisticsFindLastBo struct {
	OrderNumber string
	LogisticsNo string
}

type OrderLogisticsCreateBo struct {
	OrderNumber string
	KdCode      string
	LogisticsNo string
	OpTime      int
	OpMessage   string
	OpDesc      string
	OpCode      valobj.OrderLogisticsOpCodeObj
	AddressText string
	From        valobj.OrderLogisticsFromObj
}

type OrderLogisticsFindOneBo struct {
	OrderNumber string
	KdCode      string
	LogisticsNo string
	OpTime      int
}

func (o *OrderLogisticsFindLastBo) Validate() error {
	if o.OrderNumber == "" {
		return apierr.ErrorParam("缺少参数")
	}
	return nil
}

type OrderLogisticsDeleteBo struct {
	OrderNumber string
	KdCode      string
	LogisticsNo string
}
