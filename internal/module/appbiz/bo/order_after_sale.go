package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"encoding/json"
	"github.com/shopspring/decimal"
)

type OrderAfterSaleBo struct {
	OrderId     int
	OrderNumber string
	UserId      int
	Reason      string
	Images      string
	UserName    string
}

type OrderAfterSaleFindBo struct {
	OrderId     int
	OrderNumber string
	UserId      int
	AfterSaleId int
}

func (o *OrderAfterSaleBo) Validate() error {
	if o.OrderId <= 0 || o.UserId <= 0 || len(o.Reason) == 0 || len(o.Images) == 0 {
		return apierr.ErrorParam("缺少参数")
	}
	return nil
}

type OrderAfterSaleEntitySku struct {
	SkuNo         string // 申请的sku
	Num           int    // 申请的数量
	ExchangeSkuNo string // 换货的sku
	ExchangeNum   int    // 换货的数量

	SourceOrderNo string // 来源单号
	OriginalSkuNo string // 原始sku
}

// GetApplySkuUniqueNo 获取商品skuNo
func (o *OrderAfterSaleEntitySku) GetApplySkuUniqueNo() string {
	return o.SourceOrderNo + "-" + o.OriginalSkuNo
}

func (o *OrderAfterSaleEntitySku) Validate() error {
	if o.Num <= 0 {
		return apierr.ErrorParam("售后商品数量必须大于0")
	}
	if o.SkuNo == "" {
		return apierr.ErrorParam("缺少参数:skuNo")
	}
	if o.OriginalSkuNo == "" {
		return apierr.ErrorParam("缺少参数:originalSkuNo")
	}
	if o.SourceOrderNo == "" {
		return apierr.ErrorParam("缺少参数:sourceOrderNo")
	}
	return nil
}

func (o *OrderAfterSaleEntitySku) ValidateExchange() error {
	if o.ExchangeNum == 0 {
		return apierr.ErrorParam("换货商品数量不能为0")
	}
	if o.ExchangeSkuNo == "" {
		return apierr.ErrorParam("缺少参数:skus.exchangeSkuNo")
	}
	return nil
}

//func (o *OrderAfterSaleEntitySku) GetRefundAmount() (refundAmount float64) {
//	refundAmount = decimal.NewFromFloat(o.SignalRefundAmount).Mul(decimal.NewFromInt(int64(o.Num))).InexactFloat64()
//	return
//}

type OrderAfterSaleCreateBo struct {
	Skus []*OrderAfterSaleEntitySku

	OrderNo       string
	OrderId       int
	UserId        int
	UserNickName  string
	Type          valobj.AfterSaleType
	ReceiveStatus valobj.AfterSaleReceiveStatus
	Reason        string
	Images        []string
	Remark        string
	// RefundAmount      decimal.Decimal // 可退总金额
	RefundGoodsAmount        decimal.Decimal // 可退商品总金额
	FreightFee               decimal.Decimal // 可退运费金额
	RefundCardGiftAmount     decimal.Decimal // 可退礼品卡金额
	RefundCardGiftFreightFee decimal.Decimal // 可退礼品卡运费金额

	AfterSaleNo    string // hyt拆单使用
	HytAfterSaleNo string // hyt拆单使用
}

type OrderAfterSaleRefundResult struct {
	// RefundAmount      decimal.Decimal // 可退总金额
	RefundGoodsAmount        decimal.Decimal // 可退商品总金额
	FreightFee               decimal.Decimal // 可退运费金额
	RefundCardGiftAmount     decimal.Decimal // 可退礼品卡金额
	RefundCardGiftFreightFee decimal.Decimal // 可退礼品卡运费金额
	IsOrderCardGiftDiscount  bool
}

// GetRefundAmount 计算最大
func (o *OrderAfterSaleRefundResult) GetRefundAmount() decimal.Decimal {
	return o.RefundGoodsAmount.Add(o.FreightFee)
}

// GetTotalRefundGoodsAmount 商品退款总额 = 退款商品金额 + 礼品卡退款金额
func (o *OrderAfterSaleRefundResult) GetTotalRefundGoodsAmount() decimal.Decimal {
	return o.RefundGoodsAmount.Add(o.RefundCardGiftAmount)
}

// GetTotalRefundFreightFee 运费 = 运费 + 礼品卡运费
func (o *OrderAfterSaleRefundResult) GetTotalRefundFreightFee() decimal.Decimal {
	return o.FreightFee.Add(o.RefundCardGiftFreightFee)
}

// GetRefundAmount 计算最大
func (o *OrderAfterSaleCreateBo) GetRefundAmount() decimal.Decimal {
	return o.RefundGoodsAmount.Add(o.FreightFee)
}

// MergeSkus 合并相同sku
func (o *OrderAfterSaleCreateBo) MergeSkus() {
	applyMap := make(map[OrderAfterSaleEntitySku][]int)
	var tmpItem OrderAfterSaleEntitySku
	for _, item := range o.Skus {
		tmpItem = OrderAfterSaleEntitySku{SkuNo: item.SkuNo, ExchangeSkuNo: item.ExchangeSkuNo, SourceOrderNo: item.SourceOrderNo, OriginalSkuNo: item.OriginalSkuNo}
		if _, ok := applyMap[tmpItem]; !ok {
			applyMap[tmpItem] = []int{0, 0}
		}
		applyMap[tmpItem][0] += item.Num
		applyMap[tmpItem][1] += item.ExchangeNum
	}
	o.Skus = make([]*OrderAfterSaleEntitySku, 0)
	for k, v := range applyMap {
		o.Skus = append(o.Skus, &OrderAfterSaleEntitySku{
			SkuNo:         k.SkuNo,
			Num:           v[0],
			ExchangeSkuNo: k.ExchangeSkuNo,
			ExchangeNum:   v[1],
			SourceOrderNo: k.SourceOrderNo,
			OriginalSkuNo: k.OriginalSkuNo,
		})
	}
}

// ApplyTypeIsRefund 申请类型需要进行退款
func (o *OrderAfterSaleCreateBo) ApplyTypeIsRefund() bool {
	return o.Type.IsRefund() || o.Type.IsRefundReturn()
}

// ValidateToGetRefundAmount 计算最大退款金额时的参数校验
func (o *OrderAfterSaleCreateBo) ValidateToGetRefundAmount() error {
	return nil
}

func (o *OrderAfterSaleCreateBo) Validate() error {
	if o.UserId <= 0 {
		return apierr.ErrorParam("未登录")
	}
	if o.OrderNo == "" {
		return apierr.ErrorParam("缺少参数")
	}
	if len(o.Skus) == 0 {
		return apierr.ErrorParam("请选择售后商品")
	}
	if !o.Type.Exists() {
		return apierr.ErrorParam("请选择售后类型")
	}
	if o.Type.IsAfterSaleExchange() {
		o.ReceiveStatus = valobj.AfterSaleReceiveStatusReceived
	}
	for _, item := range o.Skus {
		if err := item.Validate(); err != nil {
			return err
		}
		if o.Type.IsAfterSaleExchange() {
			if err := item.ValidateExchange(); err != nil {
				return err
			}
		}
	}
	if o.Type.IsAfterSaleRefund() && o.GetRefundAmount().LessThan(decimal.Zero) {
		return apierr.ErrorParam("退款金额不能为负数")
	}
	if !o.ReceiveStatus.Exists() {
		return apierr.ErrorParam("请选择收货状态")
	}
	if len(o.Images) > 4 {
		return apierr.ErrorParam("最多上传4张售后凭证")
	}

	if o.Reason == "" {
		return apierr.ErrorParam("请输入售后原因")
	}
	if helper.Utf8StrLength(o.Remark) > constants.AfterSaleApplyRemarkMaxLength {
		return apierr.ErrorParam("售后描述不能超过200字")
	}
	return nil
}

func (o *OrderAfterSaleCreateBo) ValidateOrderStatus(status valobj.OrderStatusObj) bool {
	for _, v := range o.Type.OrderStatus() {
		if v == status {
			return true
		}
	}
	return false
}

func (o *OrderAfterSaleCreateBo) ImagesJson() string {
	b, _ := json.Marshal(o.Images)
	return string(b)
}

// GetExchangeSkuNos 换货单号
func (o *OrderAfterSaleCreateBo) GetExchangeSkuNos() []string {
	if o.Type.IsAfterSaleRefund() {
		return []string{}
	}
	var exchangeSkuNos []string
	for _, sku := range o.Skus {
		exchangeSkuNos = append(exchangeSkuNos, sku.ExchangeSkuNo)
	}
	return exchangeSkuNos
}

// GetSourceOrderNos 获取来源订单号
func (o *OrderAfterSaleCreateBo) GetSourceOrderNos() []string {
	var sourceOrderNos []string
	for _, sku := range o.Skus {
		sourceOrderNos = append(sourceOrderNos, sku.SourceOrderNo)
	}
	return sourceOrderNos
}

type OrderAfterSaleCancelBo struct {
	UserId       int
	UserNickName string
	AfterSaleId  int
}

func (o *OrderAfterSaleCancelBo) Validate() error {
	if o.UserId <= 0 || o.AfterSaleId <= 0 {
		return apierr.ErrorParam("缺少参数")
	}
	return nil
}

type OrderAfterSaleDeliverAddBo struct {
	AfterSaleId      int
	UserId           int
	UserNickName     string
	Remark           string
	Images           []string
	ExpressNo        string
	KdId             int
	UserContactPhone string
}

func (o *OrderAfterSaleDeliverAddBo) Validate() error {
	if o.AfterSaleId <= 0 || o.UserId <= 0 {
		return apierr.ErrorParam("缺少参数")
	}
	if o.KdId <= 0 || o.ExpressNo == "" {
		return apierr.ErrorParam("请填写快递信息")
	}
	if o.UserContactPhone == "" {
		return apierr.ErrorParam("请填写您的联系方式")
	}
	if !helper.IsAccountPhone(o.UserContactPhone) {
		return apierr.ErrorParam("请填写正确的联系方式")
	}
	if len(o.Images) > 4 {
		return apierr.ErrorParam("最多上传4张物流凭证")
	}
	if len([]rune(o.Remark)) > 200 {
		return apierr.ErrorParam("补充描述不能超过200字")
	}
	return nil
}

func (o *OrderAfterSaleDeliverAddBo) ImagesJson() string {
	b, _ := json.Marshal(o.Images)
	return string(b)
}

type OrderAfterSaleDeliverUpdateBo struct {
	AfterSaleId      int
	ExpressNo        string
	KdId             int
	UserContactPhone string
	Remark           string
	Images           []string
	UserId           int
	UserNickName     string
}

func (o *OrderAfterSaleDeliverUpdateBo) Validate() error {
	if o.AfterSaleId <= 0 {
		return apierr.ErrorParam("缺少参数")
	}
	if o.KdId <= 0 || o.ExpressNo == "" {
		return apierr.ErrorParam("请填写快递信息")
	}
	if len(o.ExpressNo) > 50 {
		return apierr.ErrorParam("快递单号过长")
	}
	if o.UserContactPhone == "" {
		return apierr.ErrorParam("请填写您的联系方式")
	}
	if !helper.IsAccountPhone(o.UserContactPhone) {
		return apierr.ErrorParam("请填写正确的联系方式")
	}
	if len(o.Images) > 4 {
		return apierr.ErrorParam("最多上传4张物流凭证")
	}
	if len([]rune(o.Remark)) > 200 {
		return apierr.ErrorParam("补充描述不能超过200字")
	}
	return nil
}

func (o *OrderAfterSaleDeliverUpdateBo) ImagesJson() string {
	if len(o.Images) == 0 {
		return ""
	}
	b, _ := json.Marshal(o.Images)
	return string(b)
}

type OrderAfterSaleUpdateBo struct {
	AfterSaleId       int
	RefundGoodsAmount float64
	RefundFreightFee  float64
	ReceiveStatus     valobj.AfterSaleReceiveStatus
	Reason            string
	Images            []string
	Remark            string
	UserId            int
	UserNickName      string
}

func (o *OrderAfterSaleUpdateBo) Validate() error {
	if o.AfterSaleId <= 0 {
		return apierr.ErrorParam("缺少参数")
	}
	if !o.ReceiveStatus.Exists() {
		return apierr.ErrorParam("请选择收货状态")
	}
	if o.Reason == "" {
		return apierr.ErrorParam("请输入售后原因")
	}
	//if len(o.Images) == 0 {
	//	return errors.New("请上传售后凭证")
	//}
	if len(o.Images) > 4 {
		return apierr.ErrorParam("最多上传4张售后凭证")
	}
	//if o.Remark == "" {
	//	return errors.New("请输入售后描述")
	//}
	if len([]rune(o.Remark)) > 200 {
		return apierr.ErrorParam("售后描述不能超过200字")
	}
	return nil
}

func (o *OrderAfterSaleUpdateBo) ImagesJson() string {
	b, _ := json.Marshal(o.Images)
	return string(b)
}

type OrderAfterSaleRefundAmountBo struct {
	OrderId int
	UserId  int
	Sku     []*OrderAfterSaleEntitySku
}

type RefundAmountByUpdateBo struct {
	Id         int
	UserId     int
	UserName   string
	ShopId     int
	CustomerId int
}
