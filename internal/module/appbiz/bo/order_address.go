package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
)

type ModifyAddressBo struct {
	OrderNo    string //订单no
	Name       string // 收货人
	Phone      string // 收货人电话
	ProvinceId int    // 省一级ID
	CityId     int    // 市一级ID
	RegionId   int    // 区一级ID
	Province   string // 省一级名称
	City       string // 市一级名称
	Region     string // 区一级名称
	Detail     string // 地址详情

	UserId int
}

// GetArea 获取省市区
func (m *ModifyAddressBo) GetArea() string {
	return m.Province + m.City + m.Region
}

// Validate 校验
func (m *ModifyAddressBo) Validate() error {
	if m.Name == "" {
		return apierr.ErrorParam("收货人不能为空")
	}
	if helper.Utf8StrLength(m.Name) > constants.MaxUserAddressNameLen {
		return apierr.ErrorParam("收货人不能为空")
	}
	if m.Phone == "" {
		return apierr.ErrorParam("收货人电话不能为空")
	}
	if !helper.IsAccountPhone(m.Phone) {
		return apierr.ErrorParam("请输入正确的手机号")
	}
	if m.ProvinceId <= 0 {
		return apierr.ErrorParam("请选择收货地址")
	}
	if m.Province == "" {
		return apierr.ErrorParam("请选择收货地址")
	}
	if m.CityId <= 0 {
		return apierr.ErrorParam("请选择收货地址")
	}
	if m.RegionId <= 0 {
		return apierr.ErrorParam("请选择收货地址")
	}
	if m.Detail == "" {
		return apierr.ErrorParam("请输入详细地址")
	}
	if m.OrderNo == "" {
		return apierr.ErrorParam("订单号不能为空")
	}

	return nil
}

type QueryOrderAddressBo struct {
	OrderNo string
	UserId  int
}
