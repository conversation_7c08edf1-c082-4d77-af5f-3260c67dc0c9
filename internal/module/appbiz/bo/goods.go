package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"encoding/json"

	"github.com/shopspring/decimal"
)

type GoodsQueryBo struct {
	Id                 int
	BrandIds           []int
	Recommend          *int
	Status             *int
	OrderSort          bool
	Name               string
	CategoryIds        []int
	Size               int
	Page               *bo.ReqPageBo
	IsIntegralShop     bool
	IntegralShopEnable bool
	PayIntegralMin     int
	PayIntegralMax     int
	IntegralCash       valobj.IntegralConfigCashObj
	IntegralExchange   valobj.IntegralConfigExchangeObj
	GoodsShowType      valobj.SysConfigGoodsShowTypeObj
	OrderBy            valobj.GoodsClientSearchOrderObj
}

func (g *GoodsQueryBo) IsRecommend() bool {
	if g == nil {
		return false
	}
	if g.Recommend == nil {
		return false
	}
	return *g.Recommend == valobj.GoodsSkuRecommendYes.GetInt()
}

type GoodsReduceStockBo struct {
	GoodsId  int
	StockNum int
}

type GoodsDiscountQueryBo struct {
	Size               int
	IsIntegralShop     bool
	IntegralShopEnable bool
}

type GoodsFreightFeeBo struct {
	SkuNo  string
	AreaId int
	Num    int
}

// GetNum 商品数量
func (g *GoodsFreightFeeBo) GetNum() int {
	if g.Num <= 0 {
		return 1
	}
	return g.Num
}

// Validate 校验
func (g *GoodsFreightFeeBo) Validate() error {
	if g.AreaId <= 0 {
		return apierr.ErrorParam("请选择配送地点")
	}
	if g.SkuNo == "" {
		return apierr.ErrorParam("请选择商品")
	}
	return nil
}

type GoodsFreightFeeResultBo struct {
	FreightFee decimal.Decimal
	IsFree     bool
}

type FlashSaleListBo struct {
	ActivityId int           `json:"ActivityId"`
	Page       *bo.ReqPageBo `json:"Page"`
}

func (g *FlashSaleListBo) ToJson() string {
	b, _ := json.Marshal(g)
	return string(b)
}

func (g *FlashSaleListBo) Validate() error {
	if g.ActivityId <= 0 {
		return apierr.ErrorParam("参数错误")
	}
	return nil
}
