package appbiz

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
)

type ActivityBiz struct {
	activityRepo       repository.ActivityRepo
	flashSaleTimeRepo  repository.FlashSaleTimeRepo
	flashSaleGoodsRepo repository.FlashSaleGoodsRepo
	data               *data.Data
	log                *log.Helper
	CacheMutex         *ActivityMutex
}

func NewActivityBiz(
	activityRepo repository.ActivityRepo,
	flashSaleTimeRepo repository.FlashSaleTimeRepo,
	flashSaleGoodsRepo repository.FlashSaleGoodsRepo,
	data *data.Data,
	log *log.Helper,
) *ActivityBiz {
	a := &ActivityBiz{
		activityRepo:       activityRepo,
		flashSaleTimeRepo:  flashSaleTimeRepo,
		flashSaleGoodsRepo: flashSaleGoodsRepo,
		data:               data,
		log:                log,
	}
	a.CacheMutex = InitActivityMutex(func(ctx context.Context, cacheKey string) bool {
		exists, _ := a.data.Rdb.Exists(ctx, cacheKey).Result()
		return exists == 0
	})
	return a
}

func (a *ActivityBiz) FindOne(ctx context.Context, id int) (*do.ActivityDo, error) {
	var err error
	activityDo, _ := a.FindOneFromCache(ctx, a.getCacheKey(ctx, id))
	if activityDo != nil {
		return activityDo, nil
	}

	mutex := a.CacheMutex.GetLock(a.getCacheKey(ctx, id))
	mutex.Lock()
	defer mutex.Unlock()

	//再次尝试从缓存中获取
	activityDo, _ = a.FindOneFromCache(ctx, a.getCacheKey(ctx, id))
	if activityDo != nil {
		return activityDo, nil
	}

	activityDo = a.activityRepo.Get(ctx, id)
	if activityDo == nil {
		activityDo = &do.ActivityDo{}
	}
	//秒杀活动，获取活动时间段
	if activityDo.ID > 0 && activityDo.ActivityType.IsFlashSale() {
		activityDo.FlashSaleTime, err = a.GetFlashSaleTimes(ctx, id)
		if err != nil {
			return nil, err
		}
	}
	//写入缓存
	err = a.setFlashSaleCache(ctx, activityDo, id)
	if err != nil {
		a.log.Errorf("秒杀活动【%d】缓存失败:%s", activityDo.ID, err)
	}
	return activityDo, nil
}

func (a *ActivityBiz) setFlashSaleCache(ctx context.Context, activityDo *do.ActivityDo, activityId int) error {
	nearNowOne := do.FlashSaleTimeSlice(activityDo.FlashSaleTime).FindNearest()
	cacheData, err := activityDo.ToJson()
	if err != nil {
		return err
	}
	if cacheData != "" {
		expireTime := constants.ActivityFlashSaleKeyPrefixTpl.GetTTL()
		if nearNowOne.DistanceStartLGT(int(expireTime.Seconds())) {
			expireTime = expireTime * 2
		}
		_, err = a.data.Rdb.Set(ctx, a.getCacheKey(ctx, activityId), cacheData, expireTime).Result()
		if err == redis.Nil {
			err = nil
		}
		return err
	}
	return fmt.Errorf("json编码失败")
}

func (a *ActivityBiz) FindOneFromCache(ctx context.Context, key string) (*do.ActivityDo, error) {
	cacheData, err := a.data.Rdb.Get(ctx, key).Result()
	if err != nil && err != redis.Nil {
		return nil, err
	}
	if cacheData != "" {
		activityDo := &do.ActivityDo{}
		err = json.Unmarshal([]byte(cacheData), activityDo)
		if err != nil {
			return nil, err
		}
		return activityDo, nil
	}
	return nil, nil
}

func (a *ActivityBiz) GetFlashSaleTimes(ctx context.Context, activityId int) ([]*do.FlashSaleTimeDo, error) {
	times, err := a.flashSaleTimeRepo.GetToday(ctx, activityId)
	if err != nil {
		return nil, err
	}
	//if len(times) == 0 {
	//	return nil, apierr.ErrorDbNotFound("活动时间段为空")
	//}
	return times, nil
}

func (a *ActivityBiz) getCacheKey(ctx context.Context, id int) string {
	return constants.ActivityFlashSaleKeyPrefixTpl.GetKey(ctx, id)
}
