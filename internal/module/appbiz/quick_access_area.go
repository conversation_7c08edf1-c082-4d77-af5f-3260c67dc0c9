package appbiz

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data/ent"
	"context"
	"time"
)

type QuickAccessAreaBiz struct {
	quickAccessAreaRepo repository.QuickAccessAreaRepo
	porcelainAreaRepo   repository.PorcelainAreaRepo
	sysConfigRepo       repository.SysConfigRepo
	goodsCategoryRepo   repository.GoodsCategoryRepo
	goodsBrandRepo      repository.GoodsBrandRepo
	activityRepo        repository.ActivityRepo
}

func NewQuickAccessAreaBiz(
	quickAccessAreaRepo repository.QuickAccessAreaRepo,
	porcelainAreaRepo repository.PorcelainAreaRepo,
	sysConfigRepo repository.SysConfigRepo,
	goodsCategoryRepo repository.GoodsCategoryRepo,
	goodsBrandRepo repository.GoodsBrandRepo,
	activityRepo repository.ActivityRepo,
) *QuickAccessAreaBiz {
	return &QuickAccessAreaBiz{
		quickAccessAreaRepo: quickAccessAreaRepo,
		porcelainAreaRepo:   porcelainAreaRepo,
		sysConfigRepo:       sysConfigRepo,
		goodsCategoryRepo:   goodsCategoryRepo,
		goodsBrandRepo:      goodsBrandRepo,
		activityRepo:        activityRepo,
	}
}

func (q *QuickAccessAreaBiz) All(ctx context.Context) ([]*do.QuickAccessAreaDo, error) {
	return q.quickAccessAreaRepo.All(ctx, &bo.QuickAccessAreaQueryBo{Status: valobj.QuickAccessAreaStatusEnable, IsClient: true})
}
func (q *QuickAccessAreaBiz) FindOne(ctx context.Context, id int) (*do.QuickAccessAreaDo, error) {
	return q.quickAccessAreaRepo.FineOne(ctx, &bo.QuickAccessAreaQueryBo{Id: id})
}

func (q *QuickAccessAreaBiz) Porcelains(ctx context.Context) ([]*do.PorcelainAreaDo, error) {
	// sysConfigDo, err := q.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigShopPorcelainStatus)
	// if err != nil {
	// 	return nil, err
	// }
	// if sysConfigDo != nil && sysConfigDo.ConfigValue != "1" {
	// 	return nil, nil
	// }
	porcelainAreaDos, _ := q.porcelainAreaRepo.SearchList(ctx, &bo.PorcelainAreaQueryBo{
		Status:    valobj.QuickAccessAreaStatusEnable,
		ReqPageBo: nil,
		ReqSortBo: &bo.ReqSortBo{
			OrderBy:   "sort",
			OrderType: 2,
		},
	})
	return porcelainAreaDos, nil
}

func (q *QuickAccessAreaBiz) PorcelainCheck(ctx context.Context, id int) error {
	porcelainAreaDo, err := q.porcelainAreaRepo.GetE(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorNotAllow("未找到详情")
		}
		return err
	}
	status := valobj.QuickAccessAreaStatusObj(porcelainAreaDo.Status)
	if !status.IsEnable() {
		return apierr.ErrorNotAllow("活动已下架")
	}

	ids := porcelainAreaDo.GetRelationValueIds()
	switch valobj.QuickAccessAreaRelationTypeObj(porcelainAreaDo.RelationType) {
	case valobj.QuickAccessAreaRelationTypeCategory:
		categories, err := q.goodsCategoryRepo.FindByIds(ctx, ids)
		if err != nil {
			return apierr.ErrorDbNotFound("活动已下架")
		}
		if len(categories) != len(ids) {
			return apierr.ErrorDbNotFound("活动已下架")
		}
		for _, category := range categories {
			if !category.IsEnable() {
				return apierr.ErrorDbNotFound("活动已下架")
			}
		}
	case valobj.QuickAccessAreaRelationTypeBrand:
		brands, err := q.goodsBrandRepo.FindByIds(ctx, ids)
		if err != nil {
			return apierr.ErrorDbNotFound("活动已下架")
		}
		for _, brand := range brands {
			if !brand.IsEnable() {
				return apierr.ErrorDbNotFound("活动已下架")
			}
		}
	case valobj.QuickAccessAreaRelationTypeActivity:
		return q.GetActivityByIds(ctx, ids)
	}

	return nil
}

func (q *QuickAccessAreaBiz) GetActivityByIds(ctx context.Context, ids []int) error {
	activityDos := q.activityRepo.Find(ctx, ids...)
	if len(activityDos) != len(ids) {
		return apierr.ErrorDbNotFound("活动不存在")
	}
	for _, activityDo := range activityDos {
		if activityDo.IsFinish() {
			return apierr.ErrorDbNotFound("活动已结束")
		}
		if !activityDo.ActivityStatus.IsEnable() {
			return apierr.ErrorDbNotFound("活动未启用")
		}
		if activityDo.IsNotStart() {
			return apierr.ErrorDbNotFound("活动未开始").WithMetadata(map[string]string{
				"name":      activityDo.Name,
				"startTime": activityDo.StartTime.Format(time.DateTime),
				"endTime":   activityDo.EndTime.Format(time.DateTime),
			})
		}
	}
	return nil
}

func (q *QuickAccessAreaBiz) ActivityDetail(ctx context.Context, id int) (*do.ActivityDo, error) {
	activityDo := q.activityRepo.Get(ctx, id)
	if activityDo == nil {
		return nil, apierr.ErrorDbNotFound("活动不存在")
	}
	if !activityDo.IsInProgress() {
		return nil, apierr.ErrorDbNotFound("活动已下架")
	}
	if !activityDo.ActivityStatus.IsEnable() {
		return nil, apierr.ErrorDbNotFound("活动已下架")
	}
	return activityDo, nil
}
