package appbiz

import (
	"cardMall/api/apierr"
	bizBo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/aclbo"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/helper"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/coroutine"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/retry"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"strings"
	"time"
)

type DanGaoShuShuBiz struct {
	dgssRpc              rpc.DanGaoShuShuRepo
	orderRepo            repository.OrderRepo
	payOrderRepo         repository.PayOrderRepo
	orderOperatorLogRepo bizRepo.OrderOperatorLogRepo
	orderRefundLogRepo   repository.OrderRefundLogRepo
	siteRepo             bizRepo.SiteRepo
	quickAccessAreaRepo  bizRepo.QuickAccessAreaRepo
	trans                bizRepo.TransactionRepo
	payDs                *ds.PayDs
	orderDs              *ds.OrderDs
	giftCardDs           *ds.GiftCardDs
	resellerDs           *ds.ResellerDs
	log                  *log.Helper
	conf                 *conf.Bootstrap
	idGenerator          *idgenerator.Generator
}

func NewDanGaoShuShuBiz(dgssRpc rpc.DanGaoShuShuRepo, orderRepo repository.OrderRepo, payOrderRepo repository.PayOrderRepo, orderOperatorLogRepo bizRepo.OrderOperatorLogRepo, orderRefundLogRepo repository.OrderRefundLogRepo, siteRepo bizRepo.SiteRepo, quickAccessAreaRepo bizRepo.QuickAccessAreaRepo, trans bizRepo.TransactionRepo, payDs *ds.PayDs, orderDs *ds.OrderDs, giftCardDs *ds.GiftCardDs, resellerDs *ds.ResellerDs, log *log.Helper, conf *conf.Bootstrap, idGenerator *idgenerator.Generator) *DanGaoShuShuBiz {
	return &DanGaoShuShuBiz{dgssRpc: dgssRpc, orderRepo: orderRepo, payOrderRepo: payOrderRepo, orderOperatorLogRepo: orderOperatorLogRepo, orderRefundLogRepo: orderRefundLogRepo, siteRepo: siteRepo, quickAccessAreaRepo: quickAccessAreaRepo, trans: trans, payDs: payDs, orderDs: orderDs, giftCardDs: giftCardDs, resellerDs: resellerDs, log: log, conf: conf, idGenerator: idGenerator}
}

func (d *DanGaoShuShuBiz) GetLoginLink(ctx context.Context, in *bo.DanGaoShuShuLoginBo) (string, error) {
	reseller, err := d.resellerDs.GetPlatformReseller(ctx)
	if err != nil {
		return "", err
	}
	loginAclBo := &aclbo.DanGaoShuShuLoginAclBo{
		Mobile: in.Mobile,
		Uid:    in.GetUid(ctx),
	}
	res, err := d.dgssRpc.Login(ctx, loginAclBo, reseller)
	if err != nil {
		return "", err
	}
	if !res.IsRspSuccess() {
		return "", apierr.ErrorException(res.GetErrMessage())
	}
	return res.GetUrl(), nil
}

func (d *DanGaoShuShuBiz) PaySuccessHandle(ctx context.Context, payOrderNumber string) error {
	payOrder, err := d.payOrderRepo.FindByOrderNumber(ctx, payOrderNumber)
	if err != nil {
		return err
	}
	if !payOrder.Status.IsUnPaid() {
		return apierr.ErrorException("订单不是待支付或已取消状态")
	}

	thirdOrderInfo, err := d.Query(ctx, payOrder.ThirdTradeNo)
	if err != nil {
		return err
	}

	fn := func(ctx context.Context, requestId string) error {
		err = d.trans.Exec(ctx, func(ctx context.Context) error {
			//创建子订单操作日志
			order, _ := d.orderRepo.FindByOrderNumber(ctx, payOrderNumber)
			_, err = d.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
				OrderId:          order.Id,
				OrderNumber:      order.OrderNumber,
				OrderStatus:      valobj.OrderStatusShipping,
				Content:          valobj.OrderStatusShipping.GetOperatorContent(),
				OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
				OperatorUserId:   0,
				OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
			})
			//修改订单状态
			status := valobj.OrderStatusShipping
			if thirdOrderInfo.OrderType.IsCard() {
				status = valobj.OrderStatusFinish
			}
			_, err = d.orderRepo.UpdateStatus(ctx, &bo.OrderUpdateStatusBo{
				OrderNumber: payOrderNumber,
				Status:      status,
				RequestId:   requestId,
			})
			if err != nil {
				return err
			}
			_, err = d.payOrderRepo.PaySuccess(ctx, payOrderNumber)
			return err
		})
		return err
	}
	err = d.resellerDs.DeductBalance(ctx, payOrder.OrderNumber, payOrder.TotalAmount, fn)
	if err != nil {
		return err
	}

	//如果订单支付成功通知失败，并且是余额不足，就退款
	err = d.payNotify(ctx, payOrder)
	if err != nil {
		coroutine.Run("蛋糕叔叔订单支付成功通知", func() {
			// 延迟5秒后再触发
			time.Sleep(5 * time.Second)
			_ = retry.Retry(func() error {
				err = d.payNotify(ctx, payOrder)
				return err
			}, retry.RetryTimes(15), retry.RetryDuration(time.Minute*2))
			// 通知15次都没成功，订单已经超时，退款
			if err != nil {
				_, err = d.Refund(ctx, &bo.DanGaoShuShuRefundBo{
					ThirdOrderNumber: payOrder.ThirdTradeNo,
					ThirdRefundNo:    helper.GetRefundNo(d.idGenerator),
					RefundAmount:     payOrder.TotalAmount,
				})
				if err != nil {
					d.log.Errorf("【%s】千猪订单支付成功后通知失败 退款处理失败:%s", payOrder.OrderNumber, err.Error())
				}
			}
		})
	}
	return err
}

func (d *DanGaoShuShuBiz) payNotify(ctx context.Context, payOrder *do.PayOrderDo) error {
	reseller, err := d.resellerDs.GetPlatformReseller(ctx)
	if err != nil {
		return err
	}
	notifyRsp := &acldo.DaoGaoShuShuPayAclDo{}
	notifyRsp, err = d.dgssRpc.PaySuccessNotify(ctx, &aclbo.DanGaoShuShuPayAclBo{OrderNo: payOrder.ThirdTradeNo}, reseller)
	if err != nil {
		d.log.Errorf("【%s】蛋糕叔叔订单支付成功通知失败:%s", payOrder.OrderNumber, err)
		return err
	}
	if !notifyRsp.IsSuccess() {
		d.log.Errorf("【%s】蛋糕叔叔订单支付成功通知失败:%s", payOrder.OrderNumber, notifyRsp.GetErrMessage())
		if notifyRsp.IsResellerBalanceErr() {
			_, err = d.Refund(ctx, &bo.DanGaoShuShuRefundBo{
				ThirdOrderNumber: payOrder.ThirdTradeNo,
				ThirdRefundNo:    helper.GetRefundNo(d.idGenerator),
				RefundAmount:     payOrder.TotalAmount,
			})
			if err != nil {
				d.log.Errorf("【%s】蛋糕叔叔订单支付成功，返回供应商余额不足后, 退款失败:%s", payOrder.OrderNumber, err)
			}
		} else {
			return errors.New(notifyRsp.GetErrMessage())
		}
	}
	return nil
}

func (d *DanGaoShuShuBiz) Query(ctx context.Context, thirdTradeNo string) (*acldo.DaoGaoShuShuQueryAclDo, error) {
	reseller, err := d.resellerDs.GetPlatformReseller(ctx)
	if err != nil {
		return nil, err
	}
	queryAclBo := &aclbo.DanGaoShuShuQueryAclBo{
		OrderNo: thirdTradeNo,
	}
	res, err := d.dgssRpc.Query(ctx, queryAclBo, reseller)
	if err != nil {
		return nil, err
	}
	if !res.IsRspSuccess() {
		return nil, apierr.ErrorException(res.GetErrMessage())
	}
	return res, nil
}

func (d *DanGaoShuShuBiz) OrderCreate(ctx context.Context, in *bo.DanGaoShuShuOrderCreateBo) (*do.DaoGaoShuShuOrderCreateDo, error) {
	quickData, _ := d.quickAccessAreaRepo.FineOne(ctx, &bizBo.QuickAccessAreaQueryBo{
		RelationType: valobj.QuickAccessAreaRelationTypeDGSS,
		Status:       valobj.QuickAccessAreaStatusEnable,
		IsClient:     true,
	})
	if !quickData.IsEnable() {
		return nil, apierr.ErrorNotAllow("下单通道已关闭")
	}
	res := &do.DaoGaoShuShuOrderCreateDo{}
	//幂等
	payOrder, err := d.payOrderRepo.FindByThirdTradeNo(ctx, in.ThirdTradeNo)
	if err != nil {
		return nil, err
	}
	if payOrder != nil {
		if payOrder.PayUrl == "" {
			query := fmt.Sprintf("orderNumber=%s", payOrder.OrderNumber)
			payOrder.PayUrl = fmt.Sprintf("%s%s?%s", d.conf.GetSite().GetDomain(), strings.Trim(d.conf.GetDanGaoShuShu().GetH5PayPath(), "/"), query)
			//保存支付中心订单号,支付参数
			payOrderUpdateBo := &bo.PayOrderUpdateBo{
				OrderId:      payOrder.Id,
				ThirdTradeNo: in.ThirdTradeNo,
				PayUrl:       payOrder.PayUrl,
			}
			_, err = d.payOrderRepo.UpdateByOrderNumber(ctx, payOrderUpdateBo)
		}
		res.PayUrl = payOrder.PayUrl
		res.OrderNumber = payOrder.OrderNumber
		return res, nil
	}

	OrderInfo, err := d.Query(ctx, in.ThirdTradeNo)
	if err != nil {
		return nil, err
	}
	if OrderInfo == nil {
		return nil, apierr.ErrorSystemPanic("获取订单信息失败")
	}
	if !helper.IsProduction(d.conf.GetServer().GetEnv()) {
		OrderInfo.TotalAmount = 0.01
		OrderInfo.PayAmount = 0.01
	}

	err = d.trans.Exec(ctx, func(ctx context.Context) error {
		createPayOrderParams := &ds.CreatePayOrderParams{
			UserId:               in.UserId,
			Num:                  1,
			CouponCodeId:         0,
			PayAmount:            OrderInfo.PayAmount,
			TotalAmount:          OrderInfo.TotalAmount,
			CouponDiscountAmount: 0,
			PayOrderType:         valobj.PayOrderTypeDGSS,
			IntegralLog:          nil,
			PayExpireTime:        in.ExpireTime,
			ThirdTradeNo:         in.ThirdTradeNo,
		}
		payOrder, err = d.orderDs.CreatePayOrder(ctx, createPayOrderParams)
		if err != nil {
			d.log.Errorf("创建蛋糕叔叔支付订单失败: %v", err)
			return apierr.ErrorSystemPanic("创建蛋糕叔叔支付订单失败")
		}

		goodsNum := 0
		for _, goods := range OrderInfo.Goods {
			goodsNum += goods.Num
		}
		//创建订单
		createOrderParams := &ds.CreateThirdParams{
			UserId:         in.UserId,
			Num:            goodsNum,
			PayAmount:      OrderInfo.PayAmount,
			TotalAmount:    OrderInfo.TotalAmount,
			Integral:       0,
			PayOrderNumber: payOrder.OrderNumber,
			OrderType:      valobj.OrderTypeDGSS,
			Goods:          make([]*ds.CreateThirdOrderGoodsParams, 0),
			SiteId:         0,
		}
		//订单商品信息
		if len(OrderInfo.Goods) == 0 {
			return apierr.ErrorException("订单商品信息为空")
		}
		for _, goods := range OrderInfo.Goods {
			createOrderParams.Goods = append(createOrderParams.Goods, &ds.CreateThirdOrderGoodsParams{
				Quantity:   goods.Num,
				SalePrice:  goods.Price,
				PayAmount:  goods.GetTotalAmount(),
				SkuNo:      "",
				GoodsId:    0,
				GoodsName:  goods.Name,
				GoodsImage: goods.Image,
				//GoodsSkuName: goods.Name,
				OriginPrice:   goods.OriginPrice,
				SupplierPrice: goods.Price,
			})
		}
		err = d.orderDs.CreateThirdOrder(ctx, createOrderParams)
		if err != nil {
			d.log.Errorf("创建蛋糕叔叔订单失败: %v", err)
			return apierr.ErrorSystemPanic("创建蛋糕叔叔订单失败")
		}
		//获取默认站点
		site, _ := d.siteRepo.FindDefault(ctx)
		if !site.IsEnable() {
			return apierr.ErrorNotAllow("默认站点不存在")
		}
		//生成支付链接
		res.OrderNumber = payOrder.OrderNumber
		query := fmt.Sprintf("orderNumber=%s", payOrder.OrderNumber)
		res.PayUrl = fmt.Sprintf("%s?%s", site.GetUrl(strings.Trim(d.conf.GetDanGaoShuShu().GetH5PayPath(), "/")), query)
		//if in.ClientType == valueobj.UserClientTypeWxApplet {
		//	res.PayUrl, err = m.appletRepo.GetAppletUrl(ctx, &bo.AppletUrlBo{
		//		ExpireTime: in.ExpireTime,
		//		Get:      query,
		//	})
		//	if err != nil {
		//		m.log.Errorf("获取小程序支付页面链接失败: %v", err)
		//		return errors.New("获取支付页面链接失败")
		//	}
		//}

		//保存支付中心订单号,支付参数
		payOrderUpdateBo := &bo.PayOrderUpdateBo{
			OrderId:      payOrder.Id,
			ThirdTradeNo: in.ThirdTradeNo,
			PayUrl:       res.PayUrl,
		}
		_, err = d.payOrderRepo.UpdateByOrderNumber(ctx, payOrderUpdateBo)
		return err
	})
	return res, err
}

// Refund 退款
func (d *DanGaoShuShuBiz) Refund(ctx context.Context, in *bo.DanGaoShuShuRefundBo) (refundNo string, err error) {
	//幂等
	refundLog, _ := d.orderRefundLogRepo.FindByThirdRefundNo(ctx, in.ThirdRefundNo)
	if refundLog != nil && refundLog.Status != valobj.OrderRefundLogStatusFail {
		return refundLog.RefundNo, nil
	}

	payOrder, err := d.payOrderRepo.FindByThirdTradeNo(ctx, in.ThirdOrderNumber)
	if err != nil {
		return
	}
	if payOrder == nil {
		err = apierr.ErrorException("[%s]支付单不存在", in.ThirdOrderNumber)
		return
	}
	if payOrder.TotalPayAmount > 0 && in.RefundAmount <= 0 {
		err = apierr.ErrorParam("退款金额不能小于等于0")
		return
	}
	if !payOrder.IsCanRefund() {
		err = apierr.ErrorException("支付单状态不允许退款")
		return
	}
	if !helper.IsProduction(d.conf.GetServer().GetEnv()) {
		in.RefundAmount = 0.01
	}

	resellerRefundAmount := in.RefundAmount
	refundRatio, err := d.getRefundAmountRatio(in, payOrder)
	if err != nil {
		return
	}
	in.RefundAmount = decimal.NewFromFloat(payOrder.TotalPayAmount).Mul(refundRatio).Round(2).InexactFloat64()

	//支付单退款状态
	payOrderStatus := valobj.PayOrderStatusRefundedPart
	totalRefundDecimal := decimal.NewFromFloat(payOrder.RefundAmount).Add(decimal.NewFromFloat(in.RefundAmount))
	totalRefundAmount, _ := totalRefundDecimal.Float64()
	if totalRefundAmount == payOrder.TotalPayAmount {
		payOrderStatus = valobj.PayOrderStatusRefundedAll
	}
	if totalRefundAmount > payOrder.TotalPayAmount {
		err = apierr.ErrorException("退款总金额大于支付金额")
		return
	}

	var orderRefundLog *do.OrderRefundLogQueryDo
	orderRefundLog, err = d.payDs.Refund(ctx, payOrder, &ds.PayDsRefundExtendParams{
		OrderNumber:  payOrder.OrderNumber,
		RefundAmount: in.RefundAmount,
		RefundReason: "用户退款",
	})
	if err != nil {
		return
	}

	order, _ := d.orderRepo.FindByOrderNumber(ctx, payOrder.OrderNumber)
	fn := func(ctx context.Context) error {
		err = d.trans.Exec(ctx, func(ctx context.Context) error {
			//保存退款单号
			_, err = d.orderRefundLogRepo.Update(ctx, &bo.OrderRefundLogUpdateBo{
				Id:            orderRefundLog.Id,
				Status:        valobj.OrderRefundLogStatusSuccess,
				ThirdRefundNo: in.ThirdRefundNo,
			})
			if err != nil {
				return err
			}
			refundNo = orderRefundLog.RefundNo

			//写入订单操作日志
			orderStatus := valobj.OrderStatusRefundPart
			if payOrderStatus == valobj.PayOrderStatusRefundedAll {
				orderStatus = valobj.OrderStatusRefunded
			}
			_, err = d.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
				OrderId:          order.Id,
				OrderNumber:      order.OrderNumber,
				OrderStatus:      orderStatus,
				Content:          orderStatus.GetOperatorContent(),
				OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
				OperatorUserId:   0,
				OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
			})

			isRefundAll := payOrderStatus == valobj.PayOrderStatusRefundedAll
			//修改订单状态为已退款
			orderRefundSuccessBo := &bo.OrderRefundSuccessBo{
				OrderNumber:       payOrder.OrderNumber,
				RefundAmount:      in.RefundAmount,
				RefundGoodsAmount: helper.TernaryAny(isRefundAll, payOrder.TotalPayAmount, in.RefundAmount),
				RefundRemark:      "用户退款",
				IsRefundAll:       isRefundAll,
			}
			_, err = d.orderRepo.RefundSuccess(ctx, orderRefundSuccessBo)
			if err != nil {
				d.log.Errorf("[refund]order:order_number:%s修改订单状态失败:%s", payOrder.OrderNumber, err.Error())
				return apierr.ErrorSystemPanic("系统异常，请联系客服处理")
			}

			//修改支付单累计退款金额,订单状态，累计退款积分
			payRefundSuccessBo := &bo.PayOrderRefundSuccessBo{
				OrderId:      payOrder.Id,
				RefundAmount: in.RefundAmount,
				OrderStatus:  payOrderStatus,
			}
			_, err = d.payOrderRepo.RefundSuccess(ctx, payRefundSuccessBo)
			if err != nil {
				d.log.Errorf("[refund]pay_order:order_number:%s修改累计退款金额失败:%s", payOrder.OrderNumber, err.Error())
				return apierr.ErrorSystemPanic("系统异常，请联系客服处理")
			}

			//如果有使用礼品卡抵扣则返回礼品卡
			if payOrder.IsCardGift() {
				bizPayOrder := &bizdo.PayOrderDo{}
				_ = mapstructure.Decode(payOrder, &bizPayOrder)
				err = d.giftCardDs.RefundAllByAmountRatio(ctx, bizPayOrder, refundRatio, true)
			}
			return err
		})
		return err
	}
	orderDo := &bizdo.OrderDo{}
	_ = mapstructure.Decode(order, &orderDo)
	err = d.resellerDs.Refund(ctx, orderDo, refundNo, resellerRefundAmount, fn)
	return
}

func (d *DanGaoShuShuBiz) getRefundAmountRatio(in *bo.DanGaoShuShuRefundBo, payOrder *do.PayOrderDo) (decimal.Decimal, error) {
	if !payOrder.OrderType.IsDGSS() {
		return decimal.Zero, apierr.ErrorException("订单类型错误")
	}
	//refundRatio := decimal.NewFromFloat(in.RefundAmount).Div(decimal.NewFromFloat(payOrder.TotalAmount))
	//return refundRatio, nil
	//只支持全额退款
	return decimal.NewFromFloat(1), nil
}

func (d *DanGaoShuShuBiz) GetOrderDetailLink(ctx context.Context, orderNumber string) (string, error) {
	order, err := d.orderRepo.FindByOrderNumber(ctx, orderNumber)
	if err != nil {
		return "", err
	}
	payOrder, err := d.payOrderRepo.FindByOrderNumber(ctx, order.PayOrderNumber)
	if err != nil {
		return "", err
	}
	orderDetail, err := d.Query(ctx, payOrder.ThirdTradeNo)
	if err != nil {
		return "", err
	}
	return orderDetail.GetOrderDetailUrl(), nil
}

func (d *DanGaoShuShuBiz) Received(ctx context.Context, thirdTradeNo string) error {
	payOrder, err := d.payOrderRepo.FindByThirdTradeNo(ctx, thirdTradeNo)
	if err != nil {
		return err
	}
	if payOrder == nil {
		return apierr.ErrorException("[%s]支付单不存在", thirdTradeNo)
	}

	order, err := d.orderRepo.FindByOrderNumber(ctx, payOrder.OrderNumber)
	if err != nil {
		return err
	}
	err = d.trans.Exec(ctx, func(ctx context.Context) error {
		_, err = d.orderRepo.UpdateStatus(ctx, &bo.OrderUpdateStatusBo{
			OrderId:     order.Id,
			OrderNumber: order.OrderNumber,
			Status:      valobj.OrderStatusFinish,
			UserId:      order.UserId,
		})
		if err != nil {
			return err
		}
		// 写入订单操作日志
		_, err = d.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			OrderId:          order.Id,
			OrderNumber:      order.OrderNumber,
			OrderStatus:      valobj.OrderStatusFinish,
			Content:          "系统自动收货",
			OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
			OperatorUserId:   0,
			OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
		})
		return err
	})
	return err
}
