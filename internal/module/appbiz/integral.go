package appbiz

import (
	"cardMall/api/apierr"
	bizDo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/helper"
	"context"
	"errors"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/shopspring/decimal"
)

type IntegralBiz struct {
	goodsRepo       repository.GoodsRepo
	goodsSkuRepo    repository.GoodsSkuRepo
	userRepo        repository.UserRepo
	couponRepo      repository.CouponRepo
	couponCodeRepo  repository.CouponCodeRepo
	couponGoodsRepo repository.CouponGoodsRepo
	couponDs        *ds.CouponDs
}

func NewIntegralBiz(
	goodsRepo repository.GoodsRepo,
	goodsSkuRepo repository.GoodsSkuRepo,
	userRepo repository.UserRepo,
	couponRepo repository.CouponRepo,
	couponCodeRepo repository.CouponCodeRepo,
	couponGoodsRepo repository.CouponGoodsRepo,
	couponDs *ds.CouponDs,
) *IntegralBiz {
	return &IntegralBiz{
		goodsRepo:       goodsRepo,
		goodsSkuRepo:    goodsSkuRepo,
		userRepo:        userRepo,
		couponRepo:      couponRepo,
		couponCodeRepo:  couponCodeRepo,
		couponGoodsRepo: couponGoodsRepo,
		couponDs:        couponDs,
	}
}

func (i *IntegralBiz) UseList(ctx context.Context, in *bo.IntegralUseListBo, config *bizDo.IntegralConfigDo) (int, int, []*do.IntegralUseListDo, error) {
	if err := in.Validate(); err != nil {
		return 0, 0, nil, err
	}
	skuNos := slice.Map(in.Goods, func(_ int, item *bo.IntegralUseGoodsBo) string { return item.SkuNo })
	skus, _ := i.goodsSkuRepo.GetBySkuNo(ctx, skuNos)
	totalAmount := decimal.NewFromFloat(0)

	skuNums := slice.KeyBy(in.Goods, func(item *bo.IntegralUseGoodsBo) string { return item.SkuNo })
	skuNoAmountMap := make(map[string]float64)

	for _, skuInfo := range skus {
		goodsAmount := decimal.NewFromFloat(skuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(skuNums[skuInfo.SkuNo].Num)))

		skuNoAmountMap[skuInfo.SkuNo] = goodsAmount.InexactFloat64()
	}
	couponDiscountAmount := decimal.Zero
	if in.CouponCodeId > 0 {
		couponCodeInfo, _ := i.couponCodeRepo.FindById(ctx, int(in.CouponCodeId))
		if couponCodeInfo == nil {
			return 0, 0, nil, errors.New("优惠券不存在")
		}
		couponInfo, _ := i.couponRepo.Find(ctx, &bo.CouponQueryBo{Id: couponCodeInfo.CouponId})
		if couponInfo == nil {
			return 0, 0, nil, errors.New("优惠活动不存在")
		}
		couponGoods, _ := i.couponGoodsRepo.GetByCouponId(ctx, couponCodeInfo.CouponId)
		err := i.couponDs.UseValidate(couponInfo, couponCodeInfo, couponGoods, skuNoAmountMap, 0)
		if err != nil {
			return 0, 0, nil, err
		}
		couponDiscountAmount = couponDiscountAmount.Add(decimal.NewFromFloat(couponInfo.DiscountAmount))
	}

	for _, skuInfo := range skus {
		if skuInfo.IsIntegralSku() {
			return 0, 0, nil, nil
		}
		//计算订单总金额
		goodsAmount := decimal.NewFromFloat(skuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(skuNums[skuInfo.SkuNo].Num)))
		supplierAmount := decimal.NewFromFloat(skuInfo.SupplierPrice).Mul(decimal.NewFromInt32(int32(skuNums[skuInfo.SkuNo].Num)))
		if config.DeductionTypeIsPayAmount() {
			totalAmount = totalAmount.Add(goodsAmount)
		} else if config.DeductionTypeIsOrderProfit() {
			totalAmount = totalAmount.Add(goodsAmount.Sub(supplierAmount))
		}
	}
	if decimal.Zero.GreaterThanOrEqual(totalAmount) {
		return 0, 0, nil, nil
	}
	// 商品金额扣除优惠券金额
	totalAmount = totalAmount.Sub(couponDiscountAmount)
	if decimal.Zero.GreaterThanOrEqual(totalAmount) {
		return 0, 0, nil, nil
	}

	//最大使用积分数量
	totalAmount = decimal.NewFromFloat(config.GetDiscountAmount(totalAmount.InexactFloat64()))
	if totalAmount.InexactFloat64() <= 0 {
		return 0, 0, nil, nil
	}
	//记录积分最大的实际抵扣金额
	actualTotalAmount := totalAmount

	// 向上取整
	totalAmount = totalAmount.RoundCeil(0)
	useIntegralMax := config.GetIntegral(totalAmount.InexactFloat64())
	userInfo, _ := i.userRepo.Query(ctx, &bo.UserQueryBo{Id: in.UserId})
	userIntegral := userInfo.Integral
	if userIntegral < config.ExchangeRate {
		return userIntegral, 0, nil, nil
	}
	if useIntegralMax%config.ExchangeRate != 0 {
		useIntegralMax = int(decimal.NewFromInt32(int32(useIntegralMax)).Div(decimal.NewFromInt32(int32(config.ExchangeRate))).RoundCeil(0).IntPart() * int64(config.ExchangeRate))
	}

	if useIntegralMax < config.ExchangeRate {
		useIntegralMax = config.ExchangeRate
	}
	if userIntegral < useIntegralMax {
		useIntegralMax = int(decimal.NewFromInt32(int32(userIntegral)).Div(decimal.NewFromInt32(int32(config.ExchangeRate))).IntPart() * int64(config.ExchangeRate))
	}

	batchSize := config.ExchangeRate

	useIntegralUnit := (useIntegralMax / batchSize) / 10
	var res = make([]*do.IntegralUseListDo, 0)
	if useIntegralUnit == 0 {
		for i := 0; i < useIntegralMax; i += batchSize {
			end := i + batchSize
			if end > useIntegralMax {
				end = useIntegralMax
			}

			amount := decimal.NewFromInt32(int32(end)).Div(decimal.NewFromInt32(int32(config.ExchangeRate)))
			if helper.HasFractionalPart(amount) {
				continue
			}
			res = append(res, &do.IntegralUseListDo{
				Amount:   amount.InexactFloat64(),
				Integral: end,
			})
		}
	} else {
		for i := 0; i < useIntegralMax; i += (batchSize * 10) {
			end := i + (batchSize * 10)
			if end > useIntegralMax {
				end = useIntegralMax
			}

			amount := decimal.NewFromInt32(int32(end)).Div(decimal.NewFromInt32(int32(config.ExchangeRate)))
			if helper.HasFractionalPart(amount) {
				continue
			}
			res = append(res, &do.IntegralUseListDo{
				Amount:   amount.InexactFloat64(),
				Integral: end,
			})
		}
	}

	//计算实际抵扣金额
	for _, re := range res {
		re.ActualAmount = re.Amount
		if re.Amount > actualTotalAmount.InexactFloat64() {
			re.ActualAmount = actualTotalAmount.InexactFloat64()
		}
	}
	return userIntegral, useIntegralMax, res, nil
}

func (i *IntegralBiz) Calculate(ctx context.Context, in *bo.IntegralCalculateBo, config *bizDo.IntegralConfigDo) (float64, error) {
	skuNos := slice.Map(in.Goods, func(_ int, item *bo.IntegralUseGoodsBo) string { return item.SkuNo })
	skus, _ := i.goodsSkuRepo.GetBySkuNo(ctx, skuNos)
	totalAmount := decimal.NewFromFloat(0)

	skuNums := slice.KeyBy(in.Goods, func(item *bo.IntegralUseGoodsBo) string { return item.SkuNo })
	for _, skuInfo := range skus {
		if skuInfo.IsIntegralSku() {
			return 0, apierr.ErrorParam("商品不支持使用%s抵扣", config.GetName())
		}

		//计算订单总金额
		goodsAmount := decimal.NewFromFloat(skuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(skuNums[skuInfo.SkuNo].Num)))
		supplierAmount := decimal.NewFromFloat(skuInfo.SupplierPrice).Mul(decimal.NewFromInt32(int32(skuNums[skuInfo.SkuNo].Num)))
		if config.DeductionTypeIsPayAmount() {
			totalAmount = totalAmount.Add(goodsAmount)
		} else if config.DeductionTypeIsOrderProfit() {
			totalAmount = totalAmount.Add(goodsAmount.Sub(supplierAmount))
		}
	}
	if totalAmount.InexactFloat64() <= 0 {
		return 0, apierr.ErrorParam("暂不支持使用%s抵扣", config.GetName())
	}

	couponDiscountAmount := decimal.Zero
	if in.CouponCodeId > 0 {
		skuNoAmountMap := make(map[string]float64)
		for _, skuInfo := range skus {
			goodsAmount := decimal.NewFromFloat(skuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(skuNums[skuInfo.SkuNo].Num)))

			skuNoAmountMap[skuInfo.SkuNo] = goodsAmount.InexactFloat64()
		}

		couponCodeInfo, _ := i.couponCodeRepo.FindById(ctx, in.CouponCodeId)
		if couponCodeInfo == nil {
			return 0, errors.New("优惠券不存在")
		}
		couponInfo, _ := i.couponRepo.Find(ctx, &bo.CouponQueryBo{Id: couponCodeInfo.CouponId})
		if couponInfo == nil {
			return 0, errors.New("优惠活动不存在")
		}
		couponGoods, _ := i.couponGoodsRepo.GetByCouponId(ctx, couponCodeInfo.CouponId)
		err := i.couponDs.UseValidate(couponInfo, couponCodeInfo, couponGoods, skuNoAmountMap, 0)
		if err != nil {
			return 0, err
		}
		couponDiscountAmount = couponDiscountAmount.Add(decimal.NewFromFloat(couponInfo.DiscountAmount))
		totalAmount = totalAmount.Sub(couponDiscountAmount)
	}
	if totalAmount.InexactFloat64() <= 0 {
		return 0, apierr.ErrorParam("暂不支持使用%s抵扣", config.GetName())
	}
	if totalAmount.InexactFloat64() <= 0 {
		return 0, apierr.ErrorParam("暂不支持使用%s抵扣", config.GetName())
	}
	//最大使用积分数量
	totalAmount = decimal.NewFromFloat(config.GetDiscountAmount(totalAmount.InexactFloat64()))
	if totalAmount.InexactFloat64() == 0 {
		return 0, apierr.ErrorParam("暂不支持使用%s抵扣", config.GetName())
	}
	//记录积分最大的实际抵扣金额
	actualTotalAmount := totalAmount

	// 向上取整
	totalAmount = totalAmount.RoundCeil(0)
	useIntegralMax := int(totalAmount.Mul(decimal.NewFromInt32(int32(config.ExchangeRate))).IntPart())

	userInfo, _ := i.userRepo.Query(ctx, &bo.UserQueryBo{Id: in.UserId})
	userIntegral := userInfo.Integral

	if userIntegral < useIntegralMax {
		//useIntegralMax = int(decimal.NewFromInt32(int32(userIntegral)).Div(decimal.NewFromInt32(int32(config.ExchangeRate))).IntPart() * int64(config.ExchangeRate))
		useIntegralMax = userIntegral
	}
	if useIntegralMax < config.ExchangeRate {
		return 0, apierr.ErrorParam("%s不足", config.GetName())
	}
	useIntegralMax = useIntegralMax / config.ExchangeRate * config.ExchangeRate
	if in.Integral > useIntegralMax {
		return 0, apierr.ErrorParam("最多可使用%d%s", useIntegralMax, config.GetName())
	}
	if in.Integral < config.ExchangeRate {
		return 0, apierr.ErrorParam("最少使用%d%s", config.ExchangeRate, config.GetName())
	}

	amount := config.GetIntegralAmount(in.Integral)
	if amount > actualTotalAmount.InexactFloat64() {
		amount = actualTotalAmount.InexactFloat64()
	}
	return amount, nil
}
