package appbiz

import (
	"context"

	bizDo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	repository2 "cardMall/internal/biz/repository"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
)

type GoodsBrandBiz struct {
	repo                repository.GoodsBrandRepo
	goodsRepo           repository.GoodsRepo
	goodsBrandCategory  repository.GoodsCategoryBrandRepo
	goodsBrandExtraRepo repository2.GoodsBrandExtraRepo
	goodsDs             *ds.GoodsCategoryDs
}

func NewGoodsBrandBiz(repo repository.GoodsBrandRepo, goodsRepo repository.GoodsRepo, goodsBrandCategory repository.GoodsCategoryBrandRepo, goodsBrandExtraRepo repository2.GoodsBrandExtraRepo, goodsDs *ds.GoodsCategoryDs) *GoodsBrandBiz {
	return &GoodsBrandBiz{repo: repo, goodsRepo: goodsRepo, goodsBrandCategory: goodsBrandCategory, goodsBrandExtraRepo: goodsBrandExtraRepo, goodsDs: goodsDs}
}

const GoodsBrandRecommendNum = 2

func (b *GoodsBrandBiz) All(ctx context.Context, in *bo.GoodsBrandQueryBo) ([]*do.GoodsBrandInfoDo, error) {
	return b.repo.All(ctx, in)
	//res, err := b.repo.All(ctx, in)
	//if err != nil {
	//	return nil, err
	//}
	//data := make([]*do.GoodsBrandInfoDo, 0, len(res))

	//brandIds := make([]int, 0, len(res))
	//for _, val := range res {
	//	brandIds = append(brandIds, val.ShopId)
	//}
	//
	//status := valobj.GoodsStatusEnable
	//brandGoods, _ := b.goodsRepo.GetListByWhere(ctx, &bo.GoodsQueryBo{Status: &status, BrandIds: brandIds})
	//brandGoodsMap := make(map[int]struct{}, len(res))
	//for _, val := range brandGoods {
	//	brandGoodsMap[val.BrandId] = struct{}{}
	//}
	//
	//for _, val := range res {
	//	if ok := b.goodsDs.IsEnable(ctx, val.ShopId); !ok {
	//		continue
	//	}
	//	if _, ok := brandGoodsMap[val.ShopId]; ok {
	//		data = append(data, val)
	//	}
	//}

	//return data, nil
}

func (b *GoodsBrandBiz) Recommend(ctx context.Context, in *bo.GoodsBrandQueryBo) ([]*do.GoodsBrandInfoDo, error) {
	return b.repo.All(ctx, in)
	//res, err := b.repo.All(ctx, in)
	//if err != nil {
	//	return nil, err
	//}
	//
	//data := make([]*do.GoodsBrandRecommendInfoDo, 0, len(res))
	//brandIds := make([]int, 0, len(res))
	//for _, val := range res {
	//	brandIds = append(brandIds, val.ShopId)
	//}
	//
	//status := valobj.GoodsStatusEnable
	//brandGoods, _ := b.goodsRepo.GetListByWhere(ctx, &bo.GoodsQueryBo{Status: &status, BrandIds: brandIds})
	//brandGoodsMap := make(map[int]*do.GoodsInfoDo, len(brandGoods))
	//for _, val := range brandGoods {
	//	brandGoodsMap[val.BrandId] = val
	//}
	//
	//for _, val := range res {
	//	if goodsInfo, ok := brandGoodsMap[val.ShopId]; ok {
	//		item := &do.GoodsBrandRecommendInfoDo{
	//			ShopId:         val.ShopId,
	//			Name:       val.Name,
	//			Logo:       val.Logo,
	//			Sort:       val.Sort,
	//			Label:      val.Label,
	//			Status:     val.Status,
	//			Recommend:  val.Recommend,
	//			CategoryId: 0,
	//			Discount:   goodsInfo.Discount,
	//		}
	//		data = append(data, item)
	//	}
	//}
	////只取两个
	//if len(data) > GoodsBrandRecommendNum {
	//	data = data[0:GoodsBrandRecommendNum]
	//}
	////推荐品牌需要返回品牌类型ID
	//if *in.Recommend == valobj.GoodsBrandRecommendYes {
	//	categoryBrandsMap := make(map[int]int, 0)
	//	categoryBrands, _ := b.goodsBrandCategory.GetByBrandIds(ctx, brandIds)
	//	for _, val := range categoryBrands {
	//		categoryBrandsMap[val.BrandId] = val.CategoryId
	//	}
	//	for _, val := range data {
	//		val.CategoryId = categoryBrandsMap[val.ShopId]
	//	}
	//}
	//
	//return data, nil
}

func (b *GoodsBrandBiz) FindOne(ctx context.Context, id int) (*do.GoodsBrandInfoDo, error) {
	return b.repo.FindById(ctx, id)
}

func (b *GoodsBrandBiz) FindByIds(ctx context.Context, ids []int) ([]*do.GoodsBrandInfoDo, error) {
	return b.repo.GetById(ctx, ids)
}

func (b *GoodsBrandBiz) FindExtraByCode(ctx context.Context, code string) (*bizDo.GoodsBrandExtraDo, error) {
	return b.goodsBrandExtraRepo.FindByCode(ctx, code)
}

func (b *GoodsBrandBiz) FindExtraByCodes(ctx context.Context, codes ...string) ([]*bizDo.GoodsBrandExtraDo, error) {
	return b.goodsBrandExtraRepo.GetByCode(ctx, codes...)
}
