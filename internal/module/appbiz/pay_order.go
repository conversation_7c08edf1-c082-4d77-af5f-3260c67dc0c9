package appbiz

import (
	repository2 "cardMall/internal/biz/repository"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type PayOrderBiz struct {
	repo        repository.PayOrderRepo
	payOrderLog repository2.PayOrderLogRepo
}

func NewPayOrderBiz(repo repository.PayOrderRepo, payOrderLog repository2.PayOrderLogRepo) *PayOrderBiz {
	return &PayOrderBiz{repo: repo, payOrderLog: payOrderLog}
}

func (p *PayOrderBiz) FindByOrderNumber(ctx context.Context, orderNumber string) (*do.PayOrderDo, error) {
	serialLog, _ := p.payOrderLog.FindBySerialNumber(ctx, orderNumber)
	if serialLog != nil {
		return p.repo.FindByOrderNumber(ctx, serialLog.OrderNumber)
	}
	return p.repo.FindByOrderNumber(ctx, orderNumber)
}

func (p *PayOrderBiz) SavePaySuccessLog(ctx context.Context, serialNo string) (int, error) {
	return p.payOrderLog.PaySuccess(ctx, serialNo)
}

func (p *PayOrderBiz) GetByOrderNumber(ctx context.Context, orderNumber string) (*do.PayOrderDo, error) {
	return p.repo.FindByOrderNumber(ctx, orderNumber)
}
