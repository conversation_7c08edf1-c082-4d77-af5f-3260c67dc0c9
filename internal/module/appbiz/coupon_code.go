package appbiz

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type CouponCodeBiz struct {
	repo repository.CouponCodeRepo
}

func NewCouponCodeBiz(repo repository.CouponCodeRepo) *CouponCodeBiz {
	return &CouponCodeBiz{repo: repo}
}

func (c *CouponCodeBiz) UserCouponTotal(ctx context.Context, userId int) (int, error) {
	return c.repo.UserCount(ctx, &bo.CouponCodeUserCountBo{UserId: userId, Status: valobj.CouponCodeStatusClaimed, IsValid: true})
}
