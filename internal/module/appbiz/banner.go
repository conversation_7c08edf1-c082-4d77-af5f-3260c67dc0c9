package appbiz

import (
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type BannerBiz struct {
	repo repository.BannerRepo
}

func NewBannerBiz(repo repository.BannerRepo) *BannerBiz {
	return &BannerBiz{repo: repo}
}

func (b *BannerBiz) All(ctx context.Context) ([]*do.BannerInfoDo, error) {
	return b.repo.All(ctx)
}

func (b *BannerBiz) FindOne(ctx context.Context, id int) (*do.BannerInfoDo, error) {
	return b.repo.FindOne(ctx, id)
}
