package appbiz

import (
	"cardMall/api/apierr"
	bbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/module/commonbiz"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"encoding/json"
	"errors"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
)

type GoodsBiz struct {
	repo                      repository.GoodsRepo
	baseGoodsRepo             repository.BaseGoodsRepo
	categoryBrand             repository.GoodsCategoryBrandRepo
	goodsBrand                repository.GoodsBrandRepo
	goodsSkuRepo              repository.GoodsSkuRepo
	goodsSpecItemRepo         bizRepo.GoodsSpecItemRepo
	goodsSpecRepo             bizRepo.GoodsSpecRepo
	orderGoodsRepo            repository.OrderGoodsRepo
	cartRepo                  repository.CartRepo
	activityRepo              bizRepo.ActivityRepo
	userAddressRepo           repository.UserAddressRepo
	supplierGoodsSku          bizRepo.SupplierGoodsSkuRepo
	supplierTransportCityRepo bizRepo.SupplierTransportCityRepo
	supplierTransportRepo     bizRepo.SupplierTransportRepo
	flashSaleGoodsRepo        bizRepo.FlashSaleGoodsRepo
	transportBaseDs           *ds.TransportBaseDs
	activityCommonBiz         *commonbiz.ActivityCommonBiz
	CacheMutex                *ActivityMutex
	data                      *data.Data
	log                       *log.Helper
}

func NewGoodsBiz(
	repo repository.GoodsRepo,
	baseGoodsRepo repository.BaseGoodsRepo,
	categoryBrand repository.GoodsCategoryBrandRepo,
	goodsBrand repository.GoodsBrandRepo,
	goodsSkuRepo repository.GoodsSkuRepo,
	goodsSpecItemRepo bizRepo.GoodsSpecItemRepo,
	goodsSpecRepo bizRepo.GoodsSpecRepo,
	orderGoodsRepo repository.OrderGoodsRepo,
	cartRepo repository.CartRepo,
	userAddressRepo repository.UserAddressRepo,
	supplierGoodsSku bizRepo.SupplierGoodsSkuRepo,
	supplierTransportCityRepo bizRepo.SupplierTransportCityRepo,
	supplierTransportRepo bizRepo.SupplierTransportRepo,
	activityRepo bizRepo.ActivityRepo,
	transportBaseDs *ds.TransportBaseDs,
	flashSaleGoodsRepo bizRepo.FlashSaleGoodsRepo,
	activityCommonBiz *commonbiz.ActivityCommonBiz,
	data *data.Data,
	log *log.Helper,
) *GoodsBiz {
	g := &GoodsBiz{
		repo:                      repo,
		baseGoodsRepo:             baseGoodsRepo,
		categoryBrand:             categoryBrand,
		goodsBrand:                goodsBrand,
		goodsSkuRepo:              goodsSkuRepo,
		goodsSpecItemRepo:         goodsSpecItemRepo,
		goodsSpecRepo:             goodsSpecRepo,
		orderGoodsRepo:            orderGoodsRepo,
		cartRepo:                  cartRepo,
		userAddressRepo:           userAddressRepo,
		supplierGoodsSku:          supplierGoodsSku,
		supplierTransportCityRepo: supplierTransportCityRepo,
		supplierTransportRepo:     supplierTransportRepo,
		flashSaleGoodsRepo:        flashSaleGoodsRepo,
		activityRepo:              activityRepo,
		transportBaseDs:           transportBaseDs,
		activityCommonBiz:         activityCommonBiz,
		data:                      data,
		log:                       log,
	}
	g.CacheMutex = InitActivityMutex(func(ctx context.Context, cacheKey string) bool {
		exists, _ := g.data.Rdb.Exists(ctx, cacheKey).Result()
		return exists == 0
	})
	return g
}

func (g *GoodsBiz) Recommend(ctx context.Context, in *bo.GoodsQueryBo) (int, []*do.GoodsListDo, error) {
	goods, err := g.repo.GetListByWhere(ctx, in)
	if err != nil || len(goods) == 0 {
		return 0, nil, err
	}
	count := 0
	if in.Page != nil {
		count, _ = g.repo.GetCountByWhere(ctx, in)
	}

	return count, goods, nil
}

func (g *GoodsBiz) List(ctx context.Context, in *bo.GoodsQueryBo) (int, []*do.GoodsListDo, error) {
	goods, _ := g.repo.GetListByWhere(ctx, in)
	if len(goods) == 0 {
		return 0, nil, nil
	}
	count, _ := g.repo.GetCountByWhere(ctx, in)
	return count, goods, nil
}

func (g *GoodsBiz) Detail(ctx context.Context, id int) (*do.GoodsDetailDo, error) {
	status := valobj.GoodsStatusEnable
	in := &bo.GoodsQueryBo{
		Id:     id,
		Status: &status,
	}
	data, err := g.repo.FindByWhere(ctx, in)
	if err != nil {
		return nil, err
	}
	if data == nil {
		return nil, errors.New("商品已下架,请联系客服")
	}
	res := &do.GoodsDetailDo{
		Id:          data.Id,
		Type:        data.Type,
		Image:       data.Image,
		Name:        data.Name,
		Images:      data.Images,
		Detail:      data.Detail,
		SalesVolume: data.SalesVolume,
		Sku:         nil,
	}
	res.Sku, _ = g.goodsSkuRepo.GetByGoodsId(ctx, data.Id)
	if len(res.Sku) == 0 {
		return nil, errors.New("商品已下架,请联系客服")
	}
	//查询供应商商品库存
	res.Sku = g.getStock(ctx, res.Sku)

	//获取规格
	specItemIds := res.Sku[0].GetSpecIds()
	specItem, _ := g.goodsSpecItemRepo.GetMapByIds(ctx, specItemIds)
	specIds := make([]int, 0)
	for _, itemId := range specItemIds {
		if item, ok := specItem[itemId]; ok {
			specIds = append(specIds, item.SpecId)
		}
	}
	spec, _ := g.goodsSpecRepo.GetMapByIds(ctx, specIds)
	for _, v := range specIds {
		res.Spec = append(res.Spec, spec[v].Name)
	}

	return res, nil
}

func (g *GoodsBiz) getBrandCategoryMap(ctx context.Context, brandIds []int) (map[int]int, error) {
	var res = make(map[int]int, len(brandIds))
	if len(brandIds) == 0 {
		return res, nil
	}
	data, err := g.categoryBrand.GetByBrandIds(ctx, brandIds)
	if err != nil {
		return nil, err
	}
	for _, val := range data {
		res[val.BrandId] = val.CategoryId
	}
	return res, nil
}

func (g *GoodsBiz) Search(ctx context.Context, in *bo.GoodsQueryBo) (int, []*do.GoodsListDo, error) {
	goods, _ := g.repo.GetListByWhere(ctx, in)
	if len(goods) == 0 {
		return 0, nil, nil
	}
	count, _ := g.repo.GetCountByWhere(ctx, in)
	return count, goods, nil
}

func (g *GoodsBiz) DetailParams(ctx context.Context, goodsId int) (*do.GoodsDetailParamsDo, error) {
	status := valobj.GoodsStatusEnable
	in := &bo.GoodsQueryBo{
		Id:     goodsId,
		Status: &status,
	}
	data, err := g.repo.FindByWhere(ctx, in)
	if err != nil {
		return nil, err
	}
	if data == nil {
		return nil, errors.New("商品不存在或已下架")
	}

	brandCategoryMap, err := g.getBrandCategoryMap(ctx, []int{data.BrandId})
	if err != nil {
		return nil, err
	}

	return &do.GoodsDetailParamsDo{
		GoodsId:    goodsId,
		BrandId:    data.BrandId,
		CategoryId: brandCategoryMap[data.BrandId],
	}, nil
}

func (g *GoodsBiz) SkuInfo(ctx context.Context, in *bo.GoodsSkuCreateOrderBo) ([]*do.GoodsSkuDo, error) {
	skuInfo := make([]*do.GoodsSkuDo, 0)
	if in.HasGoodsItems() {
		var err error
		skuInfo, err = g.goodsSkuRepo.GetBySkuNo(ctx, in.GetSkuNos())
		if err != nil {
			return nil, err
		}
		if len(skuInfo) == 0 {
			return nil, apierr.ErrorDbNotFound("商品不存在或已下架")
		}
		inSkuMap := slice.KeyBy(in.Goods, func(item *bo.CreateOrderGoodsSkuItemBo) string { return item.SkuNo })

		for _, skuDo := range skuInfo {
			if _, ok := inSkuMap[skuDo.SkuNo]; !ok {
				return nil, apierr.ErrorDbNotFound("商品已下架")
			}
			skuDo.Quantity = inSkuMap[skuDo.SkuNo].Num
		}
	} else {
		if in.Source.IsGoodsDetail() {
			sku, _ := g.goodsSkuRepo.FindEnableBySkuNo(ctx, in.SkuNo)
			if sku == nil {
				return nil, errors.New("商品已下架")
			}
			skuInfo = append(skuInfo, sku)
		} else if in.Source.IsCart() {
			//获取购物车数据
			cartSku, _ := g.cartRepo.GetAll(ctx, &bo.CartItemAllBo{
				UserId:     in.UserId,
				CartItemId: in.CartId,
			})
			skuNos := slice.Map(cartSku, func(_ int, item *do.CartItemDo) string {
				return item.GoodsSkuNo
			})
			cartSkuMap := slice.KeyBy(cartSku, func(item *do.CartItemDo) string {
				return item.GoodsSkuNo
			})
			//获取购物车商品数量
			cartSkuInfo, _ := g.goodsSkuRepo.GetBySkuNo(ctx, skuNos)
			for _, sku := range cartSkuInfo {
				if sku == nil {
					continue
				}
				if s, ok := cartSkuMap[sku.SkuNo]; ok {
					sku.Quantity = s.Quantity
				}
				skuInfo = append(skuInfo, sku)
			}
		} else if in.Source.IsOrder() {
			orderGoods, _ := g.orderGoodsRepo.GetByOrderIds(ctx, []int{in.OrderId})
			skuNos := in.OrderSkuNo
			if len(in.OrderSkuNo) == 0 {
				skuNos = slice.Map(orderGoods, func(_ int, item *do.OrderGoodsDo) string {
					return item.SkuNo
				})
			}
			orderSkuMap := slice.KeyBy(orderGoods, func(item *do.OrderGoodsDo) string {
				return item.SkuNo
			})

			//获取购物车商品数量
			skuInfo, _ = g.goodsSkuRepo.GetBySkuNo(ctx, skuNos)
			for _, sku := range skuInfo {
				if sku == nil {
					continue
				}
				if s, ok := orderSkuMap[sku.SkuNo]; ok {
					sku.Quantity = s.Quantity
				}
			}
		}
	}

	skuInfo = g.getStock(ctx, skuInfo)

	// 参与活动，要修改库存、价格等信息
	if in.IsActivity() {
		var err error
		skuInfo, err = g.activityCommonBiz.AppSkuHandler(ctx, in.ActivityId, skuInfo)
		if err != nil {
			return nil, err
		}
	}

	goodsIds := make([]int, 0)
	for _, sku := range skuInfo {
		goodsIds = append(goodsIds, sku.GoodsID)
	}

	goods, err := g.repo.GetMapByIds(ctx, goodsIds)
	if err != nil {
		return nil, err
	}

	for _, sku := range skuInfo {
		d, ok := goods[sku.GoodsID]
		if !ok || d.Status == valobj.GoodsStatusDisable {
			sku.Status = int(valobj.GoodsSkuStatusDisable)
		}
		if sku.Image == "" {
			sku.Image = d.Image
		}
		sku.GoodsName = d.Name
		sku.GoodsType = d.Type
	}

	return skuInfo, nil
}
func (g *GoodsBiz) GetDiscount(ctx context.Context, in *bo.GoodsDiscountQueryBo) ([]*do.GoodsSkuDo, error) {
	return g.goodsSkuRepo.GetDiscount(ctx, in)
}

func (g *GoodsBiz) getStock(ctx context.Context, skus []*do.GoodsSkuDo) []*do.GoodsSkuDo {
	skuSupplierGroup := slice.GroupWith(skus, func(item *do.GoodsSkuDo) int { return item.SupplierCustomerId })
	for supplierCustomerId, skuDos := range skuSupplierGroup {
		skuNos := slice.Map(skuDos, func(i int, item *do.GoodsSkuDo) string { return item.SkuNo })
		supSkuMap, _ := g.supplierGoodsSku.GetMapBySkuNo(isolationcustomer.WithCustomerAndDisableShopCtx(ctx, supplierCustomerId), skuNos)
		for _, skuDo := range skus {
			if sku, ok := supSkuMap[skuDo.SkuNo]; ok {
				skuDo.Stock = sku.Stock
			}
		}
	}
	return skus
}

func (g *GoodsBiz) GoodsFreightFee(ctx context.Context, reqBo *bo.GoodsFreightFeeBo) (*bo.GoodsFreightFeeResultBo, error) {

	res := &bo.GoodsFreightFeeResultBo{
		FreightFee: decimal.Decimal{},
		IsFree:     false,
	}
	skuInfo, _ := g.goodsSkuRepo.FindBySkuNo(ctx, reqBo.SkuNo)
	if skuInfo == nil {
		return res, apierr.ErrorDbNotFound("商品不存在！")
	}
	goodsPo, err := g.repo.FindById(ctx, skuInfo.GoodsID)
	if err != nil {
		if ent.IsNotFound(err) {
			return res, apierr.ErrorDbNotFound("商品不存在！")
		}
		return res, apierr.ErrorDbNotFound("请求失败，请重试！")
	}
	if goodsPo.Type == valobj.SupplierGoodsTypeVirtual.ToInt() {
		return res, nil
	}

	supplierCtx := isolationcustomer.WithCustomerAndDisableShopCtx(ctx, skuInfo.SupplierCustomerId)

	skuDo, err := g.supplierGoodsSku.GetWithGoodsBySkuNo(supplierCtx, reqBo.SkuNo)
	if err != nil {
		if ent.IsNotFound(err) {
			return res, apierr.ErrorDbNotFound("商品不存在！")
		}
		return res, apierr.ErrorDbNotFound("请求失败，请重试！")
	}
	if skuDo.Goods.TransportID == 0 {
		return res, nil
	}
	transportDo, err := g.supplierTransportRepo.Get(supplierCtx, skuDo.Goods.TransportID)
	if err != nil {
		if ent.IsNotFound(err) {
			return res, apierr.ErrorDbNotFound("物流模板不存在！")
		}
		return res, apierr.ErrorDbNotFound("请求失败，请重试！")
	}
	if transportDo.PricingMode.IsFree() {
		res.IsFree = true
		return res, nil
	}

	transportCityDo, err := g.supplierTransportCityRepo.FindOneByCityId(supplierCtx, reqBo.AreaId, transportDo.ID)
	if err != nil {
		err = nil
	}

	num, err := skuDo.GetTransportNum(transportDo.PricingMode, reqBo.GetNum())
	if err != nil {
		return res, err
	}

	transportSkuItems := []*ds.OrderTransportSkuItem{
		{
			SkuNo:                 reqBo.SkuNo,
			TransportCalculateNum: num,
			SupplierId:            skuDo.Goods.SupplierID,
			TransportId:           skuDo.Goods.TransportID,
			TransportItem:         transportCityDo.GetTransportItem(),
			Transport:             transportDo,
		},
	}
	feeResult, err := g.transportBaseDs.CalculateFreight(transportSkuItems)
	if err != nil {
		return res, err
	}
	if len(feeResult) == 0 {
		return res, nil
	}
	res.FreightFee = feeResult[0].FreightAmount

	return res, err
}

func (g *GoodsBiz) SkuGroupCount(ctx context.Context, goodsId ...int) ([]*do.GoodsSkuCountDo, error) {
	return g.goodsSkuRepo.GroupByGoodsId(ctx, goodsId...)
}

func (g *GoodsBiz) GetGoodsSkuListByActivityId(ctx context.Context, in *bo.GoodsSkuListByActivityIdQueryBo) ([]*do.GoodsSkuDo, *bbo.RespPageBo, error) {
	activityDo := g.activityRepo.Get(ctx, in.ActivityId)
	if activityDo == nil {
		return nil, nil, apierr.ErrorDbNotFound("活动不存在")
	}
	//if len(activityDo.GoodsIds) == 0 {
	//	return nil, nil, apierr.ErrorParam("活动【%s】没有可用的关联商品", activityDo.Name)
	//}
	params := &bo.GoodsSkuListByIdsPageBo{
		ActivityID: in.ActivityId,
		Page:       in.Page,
	}
	goodsSkuList, pageRsp, err := g.goodsSkuRepo.GetActivityGoodsSkuList(ctx, params)
	if err != nil {
		return nil, nil, err
	}
	return goodsSkuList, pageRsp, nil
}

func (g *GoodsBiz) FlashSaleSkuList(ctx context.Context, in *bo.FlashSaleListBo) (int, []*do.FlashSaleGoodsSkuDo, error) {
	res, count, err := g.GetFlashSaleSkuCache(ctx, in)
	if err != nil {
		if err != redis.Nil {
			g.log.Errorf("从缓存获取活动商品失败,params:%s：%s", in.ToJson(), err)
		}
		mutex := g.CacheMutex.GetLock(g.GetFlashSaleSkuCacheKey(ctx, in))
		mutex.Lock()
		defer mutex.Unlock()

		//再次尝试从缓存中获取
		res, count, err = g.GetFlashSaleSkuCache(ctx, in)
		if err != nil {
			count, res, err = g.goodsSkuRepo.GetFlashSaleList(ctx, in)
			if err != nil {
				return 0, nil, err
			}

			if count == 0 || len(res) == 0 {
				return count, res, nil
			}

			goodsIds := slice.Map(res, func(index int, item *do.FlashSaleGoodsSkuDo) int {
				return item.GoodsID
			})
			goods, _ := g.repo.GetMapByIds(ctx, goodsIds)
			for _, sku := range res {
				d := goods[sku.GoodsID]
				if d != nil {
					sku.Name = helper.GetSkuName(d.Name, sku.Name)
				}
			}
			err = g.SetFlashSaleSkuCache(ctx, in, res, count)
			if err != nil {
				g.log.Errorf("缓存获取活动商品失败,params:%s：%s", in.ToJson(), err)
			}
		}
	}
	return count, res, nil
}

func (g *GoodsBiz) SetFlashSaleSkuCache(ctx context.Context, in *bo.FlashSaleListBo, list []*do.FlashSaleGoodsSkuDo, count int) error {
	d := &do.FlashSaleGoodsSkuCacheDO{
		Count: count,
		Data:  list,
	}
	key := g.GetFlashSaleSkuCacheKey(ctx, in)
	_, err := g.data.Rdb.Set(ctx, key, d.ToJson(), constants.ActivityFlashSaleGoodsKeyPrefixTpl.GetTTL()).Result()
	return err
}

func (g *GoodsBiz) GetFlashSaleSkuCache(ctx context.Context, in *bo.FlashSaleListBo) ([]*do.FlashSaleGoodsSkuDo, int, error) {
	key := g.GetFlashSaleSkuCacheKey(ctx, in)
	res, err := g.data.Rdb.Get(ctx, key).Result()
	if err != nil {
		return nil, 0, err
	}
	d := &do.FlashSaleGoodsSkuCacheDO{}
	if err = json.Unmarshal([]byte(res), &d); err != nil {
		return nil, 0, err
	}
	return d.Data, d.Count, nil
}

func (g *GoodsBiz) GetFlashSaleSkuCacheKey(ctx context.Context, in *bo.FlashSaleListBo) string {
	return constants.ActivityFlashSaleGoodsKeyPrefixTpl.GetKey(ctx, in.ToJson())
}

func (g *GoodsBiz) FlashSaleSkuDetail(ctx context.Context, activityId int, skuNo string) (*do.GoodsDetailDo, error) {
	sku, _ := g.goodsSkuRepo.FindBySkuNo(ctx, skuNo)
	if !sku.Enable() {
		return nil, errors.New("商品已下架")
	}

	status := valobj.GoodsStatusEnable
	in := &bo.GoodsQueryBo{
		Id:     sku.GoodsID,
		Status: &status,
	}
	goods, err := g.repo.FindByWhere(ctx, in)
	if err != nil {
		return nil, err
	}
	if !goods.Enable() {
		return nil, errors.New("商品已下架")
	}
	res := &do.GoodsDetailDo{
		Id:          goods.Id,
		Type:        goods.Type,
		Image:       goods.Image,
		Name:        goods.Name,
		Images:      goods.Images,
		Detail:      goods.Detail,
		SalesVolume: goods.SalesVolume,
		Sku:         []*do.GoodsSkuDo{sku},
	}

	skus, err := g.activityCommonBiz.AppSkuHandler(ctx, activityId, res.Sku)
	if err != nil {
		return nil, err
	}
	res.Sku = skus

	return res, nil
}
