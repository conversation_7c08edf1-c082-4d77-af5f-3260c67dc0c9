package customerbiz

import (
	bizbo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

type SupplierGoodsDraftBiz struct {
	supplierRepo              bizRepo.SupplierRepo
	supplierGoodsRepo         bizRepo.SupplierGoodsRepo
	supplierGoodsAuditLogRepo bizRepo.SupplierGoodsAuditLogRepo
	supplierGoodsDraftRepo    bizRepo.SupplierGoodsDraftRepo
	supplierGoodsSkuRepo      bizRepo.SupplierGoodsSkuRepo
	supplierGoodsSpecRepo     bizRepo.SupplierGoodsSpecRepo
	supplierGoodsSpecItemRepo bizRepo.SupplierGoodsSpecItemRepo
	goodsRepo                 bizRepo.GoodsRepo
	goodsSkuRepo              bizRepo.GoodsSkuRepo
	goodsBrandRepo            bizRepo.GoodsBrandRepo
	goodsCategoryRepo         bizRepo.GoodsCategoryRepo
	goodsCategoryBrandRepo    bizRepo.GoodsCategoryBrandRepo
	goodsCategoryDs           *ds.GoodsCategoryDs
}

func NewSupplierGoodsDraftBiz(supplierRepo bizRepo.SupplierRepo, goodsCategoryDs *ds.GoodsCategoryDs, supplierGoodsRepo bizRepo.SupplierGoodsRepo, supplierGoodsAuditLogRepo bizRepo.SupplierGoodsAuditLogRepo, supplierGoodsDraftRepo bizRepo.SupplierGoodsDraftRepo, supplierGoodsSkuRepo bizRepo.SupplierGoodsSkuRepo, supplierGoodsSpecRepo bizRepo.SupplierGoodsSpecRepo, supplierGoodsSpecItemRepo bizRepo.SupplierGoodsSpecItemRepo, goodsRepo bizRepo.GoodsRepo, goodsSkuRepo bizRepo.GoodsSkuRepo, goodsBrandRepo bizRepo.GoodsBrandRepo, goodsCategoryRepo bizRepo.GoodsCategoryRepo, goodsCategoryBrandRepo bizRepo.GoodsCategoryBrandRepo) *SupplierGoodsDraftBiz {
	return &SupplierGoodsDraftBiz{supplierRepo: supplierRepo, goodsCategoryDs: goodsCategoryDs, supplierGoodsRepo: supplierGoodsRepo, supplierGoodsAuditLogRepo: supplierGoodsAuditLogRepo, supplierGoodsDraftRepo: supplierGoodsDraftRepo, supplierGoodsSkuRepo: supplierGoodsSkuRepo, supplierGoodsSpecRepo: supplierGoodsSpecRepo, supplierGoodsSpecItemRepo: supplierGoodsSpecItemRepo, goodsRepo: goodsRepo, goodsSkuRepo: goodsSkuRepo, goodsBrandRepo: goodsBrandRepo, goodsCategoryRepo: goodsCategoryRepo, goodsCategoryBrandRepo: goodsCategoryBrandRepo}
}
func (pari *SupplierGoodsDraftBiz) getCustomerCtx(ctx context.Context) context.Context {
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	return ctx
}
func (g *SupplierGoodsDraftBiz) DraftList(ctx context.Context, in *bizbo.SupplierGoodsDraftListBo) (int, []*bizdo.SupplierGoodsDraftListDo, error) {
	// 取当前客户id
	in.CustomerID = isolationcustomer.GetCustomerIdZero(ctx)
	// 取当前客户id
	in.ShopID = 0

	count, data, err := g.supplierGoodsDraftRepo.DraftList(ctx, in)
	if err != nil {
		return 0, nil, err
	}
	if count == 0 {
		return 0, nil, nil
	}
	categoryMap, _ := g.GetCategoryMap(g.getCustomerCtx(ctx), slice.Map(data, func(index int, item *bizdo.SupplierGoodsDraftListDo) int {
		return item.CategoryId
	}))
	brandMap, _ := g.GetBrandMap(g.getCustomerCtx(ctx), slice.Map(data, func(index int, item *bizdo.SupplierGoodsDraftListDo) int {
		return item.BrandId
	}))
	for _, val := range data {
		if category, ex := categoryMap[val.CategoryId]; ex {
			val.GoodsCategoryAllNodeDo = category
		}
		if brand, ex := brandMap[val.BrandId]; ex {
			val.GoodsBrandDo = brand
		}
	}
	return count, data, nil
}
func (g *SupplierGoodsDraftBiz) Detail(ctx context.Context, id int32) (*bizdo.SupplierGoodsDraftDo, error) {
	detail, err := g.supplierGoodsDraftRepo.Detail(ctx, id)
	if err != nil {
		return nil, err
	}
	return detail, nil
}
func (g *SupplierGoodsDraftBiz) GetBrandMap(ctx context.Context, id []int) (map[int]*bizdo.GoodsBrandDo, error) {
	goodsBrandDos, err := g.goodsBrandRepo.Find(ctx, id...)
	if err != nil {
		return nil, err
	}
	return slice.KeyBy(goodsBrandDos, func(item *bizdo.GoodsBrandDo) int {
		return item.Id
	}), nil
}
func (g *SupplierGoodsDraftBiz) GetCategoryMap(ctx context.Context, id []int) (map[int]*bizdo.GoodsCategoryAllNodeDo, error) {
	if len(id) == 0 {
		return nil, nil
	}
	goodsCategoryDos, err := g.goodsCategoryRepo.GetAllNodeByChild(ctx, id)
	if err != nil {
		return nil, err
	}
	return slice.KeyBy(goodsCategoryDos, func(item *bizdo.GoodsCategoryAllNodeDo) int {
		return item.ID
	}), nil
}
func (g *SupplierGoodsDraftBiz) GetBrandName(ctx context.Context, id int) (*bizdo.GoodsBrandDo, error) {
	return g.goodsBrandRepo.GetOneByID(ctx, id)
}
func (g *SupplierGoodsDraftBiz) GetSupplierName(ctx context.Context, id int) (*bizdo.SupplierDo, error) {
	ctx = isolationcustomer.WithSupplierAndDisableShopCtx(ctx, id)
	return g.supplierRepo.Get(ctx, id)
}
func (g *SupplierGoodsDraftBiz) GetSupplierGoodsSpecItem(ctx context.Context, ids []int) (map[int]*bizdo.SupplierGoodsSpecItemDo, error) {
	return g.supplierGoodsSpecItemRepo.FindMapByIds(ctx, ids)
}
