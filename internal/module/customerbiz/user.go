package customerbiz

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"context"
)

type UserBiz struct {
	userRepo         repository.UserRepo
	customerShopRepo repository.CustomerShopRepo
}

func NewUserBiz(userRepo repository.UserRepo,
	customerShopRepo repository.CustomerShopRepo) *UserBiz {
	return &UserBiz{userRepo: userRepo,
		customerShopRepo: customerShopRepo}
}

func (ub *UserBiz) ShopCount(ctx context.Context, shopId ...int) ([]*do.ShopUserCountDo, error) {
	return ub.userRepo.ShopCount(ctx, shopId...)
}

func (u *UserBiz) SearchList(ctx context.Context, in *bo.UserSearchBo) (dos []*do.UserDo, respPage *bo.RespPageBo) {
	return u.userRepo.SearchList(ctx, in)
}
func (g *UserBiz) GetShopName(ctx context.Context, id ...int) ([]*do.CustomerShopDo, error) {
	return g.customerShopRepo.GetWithDelete(ctx, id...)
}
func (u *UserBiz) GetOneUserById(ctx context.Context, userId int) (*do.UserDo, error) {
	return u.userRepo.Get(ctx, userId)
}
