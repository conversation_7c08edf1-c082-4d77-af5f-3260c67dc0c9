package customerbiz

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"errors"
)

type SupplierAccountBiz struct {
	adminRepo    repository.AdminRepo
	supplierRepo repository.SupplierRepo
}

func NewSupplierAccountBiz(adminRepo repository.AdminRepo, supplierRepo repository.SupplierRepo) *SupplierAccountBiz {
	return &SupplierAccountBiz{adminRepo: adminRepo, supplierRepo: supplierRepo}
}

func (s *SupplierAccountBiz) List(ctx context.Context, in *bo.SupplierAccountListBo) (int, []*do.AdminDo, error) {

	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	reqBo := bo.NewAdminListBo(in)
	count, d, err := s.adminRepo.ListSupplier(ctx, reqBo)
	if err != nil || count == 0 {
		return 0, nil, err
	}

	supplierIds := make([]int, 0, len(d))
	for _, v := range d {
		supplierIds = append(supplierIds, v.SupplierID)
	}
	supplierIds = helper.SliceIntUnique(supplierIds)

	supplierMap, err := s.supplierRepo.GetAllMap(ctx, supplierIds)
	if err != nil {
		return 0, nil, err
	}

	for i, v := range d {
		if supplier, ok := supplierMap[v.SupplierID]; ok {
			d[i].Supplier = supplier
		}
	}

	return count, d, nil
}

func (s *SupplierAccountBiz) Add(ctx context.Context, in *bo.SupplierAccountAddBo) (int, error) {
	//ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	supplier, _ := s.supplierRepo.Get(isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0)), in.SupplierId)
	if supplier == nil {
		return 0, errors.New("企业供应商不存在")
	}
	exists, err := s.adminRepo.AccountExist(isolationcustomer.WithDisableShopCtx(ctx), in.Account, 0)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, errors.New("企业供应商账号已存在")
	}

	mobileExist, err := s.adminRepo.MobileExist(isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0)), in.PhoneNumber, 0, valobj.AdminTypeCustomerSupplier)
	if err != nil {
		return 0, err
	}
	if mobileExist {
		return 0, errors.New("企业供应商手机号已存在")
	}

	reqBo := bo.NewAddSupplierBo(in)
	//reqBo.Password, err = helper.GetPwdHash(in.Password)
	//if err != nil {
	//	return 0, err
	//}

	return s.adminRepo.AddSupplier(ctx, reqBo)
}

func (s *SupplierAccountBiz) Update(ctx context.Context, in *bo.SupplierAccountUpdateBo) (int, error) {
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	reqBo := bo.NewUpdateSupplierBo(in)
	//if in.Password != "" {
	//	var err error
	//	reqBo.Password, err = helper.GetPwdHash(in.Password)
	//	if err != nil {
	//		return 0, err
	//	}
	//}
	exists, err := s.adminRepo.AccountExist(ctx, in.Account, in.Id)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, apierr.ErrorNotAllow("企业供应商账号已存在")
	}

	mobileExist, err := s.adminRepo.MobileExist(isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0)), in.PhoneNumber, in.Id, valobj.AdminTypeCustomerSupplier)
	if err != nil {
		return 0, err
	}
	if mobileExist {
		return 0, errors.New("企业供应商手机号已存在")
	}

	return s.adminRepo.UpdateSupplier(ctx, reqBo)
}

func (s *SupplierAccountBiz) Del(ctx context.Context, id int) (int, error) {
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	return s.adminRepo.DelSupplier(ctx, id)
}
