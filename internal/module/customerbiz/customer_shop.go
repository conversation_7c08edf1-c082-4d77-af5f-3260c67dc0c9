package customerbiz

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/data/ent"
	bo2 "cardMall/internal/module/adminbiz/bo"
	do2 "cardMall/internal/module/adminbiz/do"
	repository2 "cardMall/internal/module/adminbiz/repository"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"strconv"
	"time"
)

type CustomerShopBiz struct {
	customerShopRepo    repository.CustomerShopRepo
	siteRepo            repository.SiteRepo
	integralConfigRepo  repository.IntegralConfigRepo
	sysConfigRepo       repository.SysConfigRepo
	bannerRepo          repository2.BannerRepo
	adminRepo           repository.AdminRepo
	quickAccessAreaRepo repository.QuickAccessAreaRepo
	orderRepo           repository.OrderRepo
	sysRoleRepo         repository.SysRoleRepo
	sysRoleMenuRepo     repository.SysRoleMenuRepo
	sysMenuRepo         repository.SysMenuRepo
	trans               repository.TransactionRepo
	conf                *conf.Bootstrap
	saasShopAppletRepo  repository.SaasShopAppletRepo
	sysConfigDs         *ds.SysConfigDs
}

func NewCustomerShopBiz(customerShopRepo repository.CustomerShopRepo, siteRepo repository.SiteRepo, integralConfigRepo repository.IntegralConfigRepo, sysConfigRepo repository.SysConfigRepo, bannerRepo repository2.BannerRepo, adminRepo repository.AdminRepo, quickAccessAreaRepo repository.QuickAccessAreaRepo, orderRepo repository.OrderRepo, sysRoleRepo repository.SysRoleRepo, sysRoleMenuRepo repository.SysRoleMenuRepo, sysMenuRepo repository.SysMenuRepo, trans repository.TransactionRepo, conf *conf.Bootstrap, saasShopAppletRepo repository.SaasShopAppletRepo, sysConfigDs *ds.SysConfigDs) *CustomerShopBiz {
	return &CustomerShopBiz{customerShopRepo: customerShopRepo, siteRepo: siteRepo, integralConfigRepo: integralConfigRepo, sysConfigRepo: sysConfigRepo, bannerRepo: bannerRepo, adminRepo: adminRepo, quickAccessAreaRepo: quickAccessAreaRepo, orderRepo: orderRepo, sysRoleRepo: sysRoleRepo, sysRoleMenuRepo: sysRoleMenuRepo, sysMenuRepo: sysMenuRepo, trans: trans, conf: conf, saasShopAppletRepo: saasShopAppletRepo, sysConfigDs: sysConfigDs}
}

func (o *CustomerShopBiz) Add(ctx context.Context, in *bo.CustomerShopAddBo, admin *bo.AdminAddBo) (*do.CustomerShopDo, error) {
	//判断商城是否已存在
	exist, _ := o.customerShopRepo.FindByName(ctx, in.Name)
	if exist != nil {
		return nil, apierr.ErrorParam("商城名称已存在")
	}

	// 默认C端用户联系电话
	if in.SysConfigPartnerPhone == "" {
		in.SysConfigPartnerPhone = admin.Mobile
	}
	if in.SysConfigAfterSalePhone == "" {
		in.SysConfigAfterSalePhone = admin.Mobile
	}
	if in.SysConfigCustomerSupportPhone == "" {
		in.SysConfigCustomerSupportPhone = admin.Mobile
	}
	if in.SysConfigService == "" {
		in.SysConfigService = in.GetDefaultSysConfigService(in.Name, admin.Mobile)
	}
	if in.SysConfigQA == "" {
		in.SysConfigQA = in.GetDefaultSysConfigQA(in.Name, admin.Mobile)
	}

	var shop *do.CustomerShopDo
	var err error
	cpCtx := ctx
	err = o.trans.Exec(ctx, func(ctx context.Context) error {
		//创建商城
		shop, err = o.customerShopRepo.Create(ctx, &do.CustomerShopDo{
			Name:       in.Name,
			Remark:     in.Remark,
			CreateTime: helper.GetNow(),
			CustomerID: in.CustomerId,
			Status:     in.Status,
			UpdateTime: helper.GetNow(),
		})
		if err != nil {
			return apierr.ErrorParam("创建商城失败:%s", err.Error())
		}
		shopIdCtx := isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(shop.ID))

		//初始化金刚区配置
		err = o.sceneRefresh(shopIdCtx, in.Scene)
		if err != nil {
			return err
		}

		//将配置数据插入到sys_config表中
		now := helper.GetNow()

		//初始化分销站点数据
		_, err = o.siteRepo.Create(shopIdCtx, &do.SiteDo{
			Name: in.Name,
			//Domain:     fmt.Sprintf("%s?customerId=%d&shopId=%d", o.conf.GetSite().GetDomain(), shop.CustomerID, shop.ID),
			Domain:     fmt.Sprintf("%s?c=%s", o.conf.GetSite().GetDomain(), isolationcustomer.EncodeC(shop.CustomerID, shop.ID)),
			UniqueStr:  "",
			Status:     valobj.SiteStatusEnable,
			CustomerId: shop.CustomerID,
			ShopId:     shop.ID,
			IsDefault:  valobj.SiteIsDefaultYes,
			CreateTime: now,
			UpdateTime: now,
		})

		// 插入默认banner
		if in.DefaultBanner != "" {
			status := valobj.BannerStatusEnable
			_, err = o.bannerRepo.Add(shopIdCtx, &bo2.BannerAddBo{
				Name:       "默认轮播图",
				Image:      in.DefaultBanner,
				Sort:       1,
				Status:     &status,
				IsDefault:  valobj.BannerIsDefaultYes,
				CustomerId: shop.CustomerID,
				ShopId:     shop.ID,
			})
		}

		//------------------------- 写入配置 -----------------------------
		// 经营场景
		configs := make([]*do.SysConfigDo, 0, 10)
		var sceneJson string
		sceneJson, err = in.Scene.ToString()
		if err != nil {
			return apierr.ErrorParam("数据格式错误：经营场景")
		}
		configs = append(configs, o.buildConfig(in.Scene.GetKey(), sceneJson, shop.CustomerID, shop.ID))
		// 登录平台
		var loginModeJson string
		loginModeJson, err = in.LoginPlatform.ToString()
		if err != nil {
			return apierr.ErrorParam("数据格式错误：登录平台")
		}
		configs = append(configs, o.buildConfig(in.LoginPlatform.GetKey(), loginModeJson, shop.CustomerID, shop.ID))
		// 登录模式
		configs = append(configs, o.buildConfig(valobj.SysConfigLoginMode, strconv.Itoa(int(in.LoginModel)), shop.CustomerID, shop.ID))
		// 登录方式
		configs = append(configs, o.buildConfig(valobj.SysConfigLoginType, strconv.Itoa(int(in.LoginType)), shop.CustomerID, shop.ID))
		// 注册是否绑定手机号
		configs = append(configs, o.buildConfig(valobj.SysConfigRegisterBindPhone, strconv.Itoa(int(in.RegisterBindPhone)), shop.CustomerID, shop.ID))
		// Logo
		if in.ShopLogo != "" {
			configs = append(configs, o.buildConfig(valobj.SysConfigShopLogo, in.ShopLogo, shop.CustomerID, shop.ID))
		}
		//主题色
		configs = append(configs, o.buildConfig(valobj.SysConfigShopColor, in.ShopColor, shop.CustomerID, shop.ID))
		// 写入服务条款与服务政策、常见问题、商务合作电话、售后电话、客服电话
		configs = append(configs, o.buildConfig(valobj.SysConfigService, in.SysConfigService, shop.CustomerID, shop.ID))
		configs = append(configs, o.buildConfig(valobj.SysConfigQA, in.SysConfigQA, shop.CustomerID, shop.ID))
		configs = append(configs, o.buildConfig(valobj.SysConfigPartnerPhone, in.SysConfigPartnerPhone, shop.CustomerID, shop.ID))
		configs = append(configs, o.buildConfig(valobj.SysConfigAfterSalePhone, in.SysConfigAfterSalePhone, shop.CustomerID, shop.ID))
		configs = append(configs, o.buildConfig(valobj.SysConfigCustomerSupportPhone, in.SysConfigCustomerSupportPhone, shop.CustomerID, shop.ID))
		// 商品显示配置
		configs = append(configs, o.buildConfig(valobj.SysConfigGoodsShowType, strconv.Itoa(valobj.SysConfigGoodsShowTypeDefault.GetValue()), shop.CustomerID, shop.ID))
		// 是否透传企业余额授信
		configs = append(configs, o.buildConfig(valobj.SysConfigShopShowBalance, strconv.Itoa(in.ShopShowBalance.GetValue()), shop.CustomerID, shop.ID))
		// C端分类限制默认 1=默认分类 2=自定义分类
		configs = append(configs, o.buildConfig(valobj.SysConfigShopClientCategoryMode, strconv.Itoa(valobj.SysConfigShopClientCategoryModeDefault.GetValue()), shop.CustomerID, shop.ID))
		_, err = o.sysConfigRepo.CreateBulk(shopIdCtx, configs)
		if err != nil {
			return apierr.ErrorParam("初始化系统配置异常:%s", err)
		}

		// 写入超级管理权限
		var roleId int
		roleId, err = o.AddShopSuperSysRole(shopIdCtx, shop)
		admin.RoleId = roleId
		if admin.Account == "" {
			admin.Account = fmt.Sprintf("%d-%d-%s", shop.CustomerID, shop.ID, admin.Account)
		}

		//创建超级管理员
		err = admin.RootCreateValidate()
		if err != nil {
			return apierr.ErrorParam("参数错误:%s", err.Error())
		}
		_, err = o.adminRepo.AddAdmin(shopIdCtx, admin)
		if err != nil {
			return apierr.ErrorParam("创建管理员失败:%s", err.Error())
		}
		//保存小程序appid
		if in.LoginPlatform.IsAppletEnable() && len(in.Applet) > 0 {
			appletDos := make([]*do.SaasShopAppletDo, 0, len(in.Applet))
			for _, appletBo := range in.Applet {
				appExist, _ := o.saasShopAppletRepo.ExistsCheckByAppId(cpCtx, appletBo.AppId, 0)
				if appExist {
					return apierr.ErrorParam("小程序【%s】已存在", appletBo.AppId)
				}
				appletDos = append(appletDos, &do.SaasShopAppletDo{
					CustomerID: shop.CustomerID,
					ShopID:     shop.ID,
					AppID:      appletBo.AppId,
					AppType:    appletBo.AppType,
					AppSecret:  appletBo.AppSecret,
				})
			}
			_, err = o.saasShopAppletRepo.CreateBulk(cpCtx, appletDos)
			if err != nil {
				return apierr.ErrorParam("保存小程序信息失败:%s", err.Error())
			}
		}

		//创建一个saas平台越级登录的超管账号
		saasAdmin := &bo.AdminAddBo{
			Name:     helper.GetTransferToShopAccount(isolationcustomer.EncodeC(shop.CustomerID, shop.ID)),
			Account:  helper.GetTransferToShopAccount(isolationcustomer.EncodeC(shop.CustomerID, shop.ID)),
			Status:   valobj.AdminStatusEnable,
			Password: "",
			RoleId:   0,
			Mobile:   "",
			IsRoot:   valobj.AdminIsRootYes,
			Type:     valobj.AdminTypeSaasToShopAdmin,
		}
		_, err = o.adminRepo.AddAdmin(shopIdCtx, saasAdmin)
		if err != nil {
			return apierr.ErrorParam("创建管理员失败:%s", err.Error())
		}
		return nil
	})
	return shop, err
}

// buildConfig 构建系统配置
func (o *CustomerShopBiz) buildConfig(key, value string, customerId, shopId int) *do.SysConfigDo {
	now := int(time.Now().Unix())
	return &do.SysConfigDo{
		ConfigKey:   key,
		ConfigValue: value,
		CreateTime:  now,
		UpdateTime:  now,
		CustomerId:  customerId,
		ShopId:      shopId,
	}
}

func (o *CustomerShopBiz) sceneRefresh(ctx context.Context, scene valobj.SysConfigShopSceneSlice) error {
	var err error
	//肯德基
	KfcBo := bo.NewQuickAccessAreaAddDefaultBo(valobj.QuickAccessAreaRelationTypeKFC, scene.IsKfcEnable())
	KfcInfo, _ := o.quickAccessAreaRepo.FineOne(ctx, &bo.QuickAccessAreaQueryBo{RelationType: valobj.QuickAccessAreaRelationTypeKFC})
	if KfcInfo == nil {
		_, err = o.quickAccessAreaRepo.Add(ctx, KfcBo)
	} else {
		KfcBo.Id = KfcInfo.Id
		KfcBo.Sort = KfcInfo.Sort
		_, err = o.quickAccessAreaRepo.Update(ctx, KfcBo)
	}
	if err != nil {
		return apierr.ErrorSystemPanic("初始化系统配置失败:%s", err.Error())
	}

	// 美团外卖
	meiTuanBo := bo.NewQuickAccessAreaAddDefaultBo(valobj.QuickAccessAreaRelationTypeMeiTuan, scene.IsMeiTuanEnable())
	meiTuanInfo, _ := o.quickAccessAreaRepo.FineOne(ctx, &bo.QuickAccessAreaQueryBo{RelationType: valobj.QuickAccessAreaRelationTypeMeiTuan})
	if meiTuanInfo == nil {
		_, err = o.quickAccessAreaRepo.Add(ctx, meiTuanBo)
	} else {
		meiTuanBo.Id = meiTuanInfo.Id
		meiTuanBo.Sort = meiTuanInfo.Sort
		_, err = o.quickAccessAreaRepo.Update(ctx, meiTuanBo)
	}
	if err != nil {
		return apierr.ErrorSystemPanic("初始化系统配置失败:%s", err.Error())
	}

	// 电影票
	CinemaBo := bo.NewQuickAccessAreaAddDefaultBo(valobj.QuickAccessAreaRelationTypeCinema, scene.IsCinemaEnable())
	CinemaInfo, _ := o.quickAccessAreaRepo.FineOne(ctx, &bo.QuickAccessAreaQueryBo{RelationType: valobj.QuickAccessAreaRelationTypeCinema})
	if CinemaInfo == nil {
		_, err = o.quickAccessAreaRepo.Add(ctx, CinemaBo)
	} else {
		CinemaBo.Id = CinemaInfo.Id
		CinemaBo.Sort = CinemaInfo.Sort
		_, err = o.quickAccessAreaRepo.Update(ctx, CinemaBo)
	}
	if err != nil {
		return apierr.ErrorSystemPanic("初始化系统配置失败:%s", err.Error())
	}

	// 蛋糕叔叔
	DGSSBo := bo.NewQuickAccessAreaAddDefaultBo(valobj.QuickAccessAreaRelationTypeDGSS, scene.IsDGSSEnable())
	DGSSInfo, _ := o.quickAccessAreaRepo.FineOne(ctx, &bo.QuickAccessAreaQueryBo{RelationType: valobj.QuickAccessAreaRelationTypeDGSS})
	if DGSSInfo == nil {
		_, err = o.quickAccessAreaRepo.Add(ctx, DGSSBo)
	} else {
		DGSSBo.Id = CinemaInfo.Id
		DGSSBo.Sort = CinemaInfo.Sort
		_, err = o.quickAccessAreaRepo.Update(ctx, CinemaBo)
	}
	if err != nil {
		return apierr.ErrorSystemPanic("初始化系统配置失败:%s", err.Error())
	}

	// 星巴克
	StarBucksBo := bo.NewQuickAccessAreaAddDefaultBo(valobj.QuickAccessAreaRelationTypeStarBucks, scene.IsStarBucksEnable())
	StarBucksInfo, _ := o.quickAccessAreaRepo.FineOne(ctx, &bo.QuickAccessAreaQueryBo{RelationType: valobj.QuickAccessAreaRelationTypeStarBucks})
	if StarBucksInfo == nil {
		_, err = o.quickAccessAreaRepo.Add(ctx, StarBucksBo)
	} else {
		StarBucksBo.Id = StarBucksInfo.Id
		StarBucksBo.Sort = StarBucksInfo.Sort
		_, err = o.quickAccessAreaRepo.Update(ctx, StarBucksBo)
	}

	//积分配置
	integralConfAddBo := bo.NewIntegralConfigAddDefaultBo(scene.IsIntegralEnable())
	integralConfInfo, _ := o.integralConfigRepo.Find(ctx, isolationcustomer.GetShopIdZero(ctx))
	if integralConfInfo == nil {
		integralConfAddBo.ShopId = isolationcustomer.GetShopIdZero(ctx)
		integralConfAddBo.CustomerId = isolationcustomer.GetCustomerIdZero(ctx)
		_, err = o.integralConfigRepo.Add(ctx, integralConfAddBo)
	} else {
		_, err = o.integralConfigRepo.Update(ctx, isolationcustomer.GetShopIdZero(ctx), &bo.IntegralConfigUpdateBo{
			Id:                 integralConfInfo.Id,
			ExchangeRate:       integralConfInfo.ExchangeRate,
			Name:               integralConfInfo.Name,
			DeductionType:      integralConfAddBo.DeductionType,
			DeductionRate:      integralConfAddBo.DeductionRate,
			ExpireType:         integralConfInfo.ExpireType,
			ExpireYear:         integralConfInfo.ExpireYear,
			IntegralExchange:   integralConfAddBo.IntegralExchange,
			IntegralCash:       integralConfAddBo.IntegralCash,
			IntegralShopStatus: integralConfAddBo.IntegralShopStatus,
		})
	}
	if err != nil {
		return apierr.ErrorParam("初始化积分配置失败:%s", err.Error())
	}

	//站点分销
	if !scene.IsDistributionEnable() {
		_, err = o.siteRepo.DisableAll(ctx)
		if err != nil {
			return apierr.ErrorParam("初始化站点分销失败:%s", err.Error())
		}
	}
	return nil
}

func (o *CustomerShopBiz) AddValidate(ctx context.Context, in *bo.CustomerShopAddBo) error {
	if err := in.Validate(); err != nil {
		return err
	}
	//判断商城是否已存在
	exist, _ := o.customerShopRepo.FindByName(ctx, in.Name)
	if exist != nil {
		return apierr.ErrorParam("商城名称已存在")
	}
	return nil
}

func (o *CustomerShopBiz) List(ctx context.Context, in *bo.CustomerShopSearchBo) ([]*do.CustomerShopDo, *bo.RespPageBo) {
	return o.customerShopRepo.SearchList(ctx, in)
}

func (o *CustomerShopBiz) Detail(ctx context.Context, id int) (*do.CustomerShopDo, []*do.SysConfigDo, error) {
	shop, err := o.customerShopRepo.Get(ctx, id)
	if err != nil {
		return nil, nil, err
	}
	if shop == nil {
		return nil, nil, apierr.ErrorParam("商城不存在")
	}
	keys := []string{
		valobj.SysConfigShopScene,
		valobj.SysConfigLoginPlatform,
		valobj.SysConfigLoginMode,
		valobj.SysConfigLoginType,
		valobj.SysConfigRegisterBindPhone,
		valobj.SysConfigShopColor,
		valobj.SysConfigShopLogo,
		valobj.SysConfigShopShowBalance,
	}
	configs, err := o.sysConfigRepo.GetByShopId(ctx, shop.ID, keys...)
	if err != nil {
		return nil, nil, err
	}
	configs = o.sysConfigDs.LoadDefault(ctx, configs, keys...)
	return shop, configs, err
}

func (o *CustomerShopBiz) UpdateAmountLimit(ctx context.Context, id int, amountLimit float64) (int, error) {
	shop, err := o.customerShopRepo.Get(ctx, id)
	if err != nil {
		return 0, err
	}
	if shop == nil {
		return 0, apierr.ErrorParam("商城不存在")
	}
	return o.customerShopRepo.AmountLimitIncr(ctx, id, amountLimit)
}

func (o *CustomerShopBiz) Update(ctx context.Context, in *bo.CustomerShopUpdateBo, admin *bo.AdminUpdateBo) (int, error) {
	shopInfo, _ := o.customerShopRepo.Get(ctx, in.Id)
	if shopInfo == nil {
		return 0, apierr.ErrorParam("商城不存在")
	}
	if shopInfo.Name != in.Name {
		exist, _ := o.customerShopRepo.FindByName(ctx, in.Name)
		if exist != nil {
			return 0, apierr.ErrorParam("商城名称已存在")
		}
	}
	//场景不能减少校验

	var err error
	cpCtx := ctx
	err = o.trans.Exec(ctx, func(ctx context.Context) error {
		now := helper.GetNow()
		ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(shopInfo.ID))

		shopInfo.Name = in.Name
		shopInfo.UpdateTime = now
		shopInfo.Remark = in.Remark
		_, err = o.customerShopRepo.Update(ctx, shopInfo)
		if err != nil {
			return apierr.ErrorParam("商城信息修改失败:%s", err.Error())
		}
		//将配置数据插入到sys_config表中
		// 经营场景
		sceneConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, in.Scene.GetKey())
		if sceneConf == nil {
			return apierr.ErrorDbNotFound("经营场景配置不存在")
		}
		for _, scene := range sceneConf.GetShopScene() {
			if !helper.InSlice(scene, in.Scene) {
				return apierr.ErrorParam("经营场景配置不能减少")
			}
		}
		var sceneJson string
		sceneJson, err = in.Scene.ToString()
		if err != nil {
			return apierr.ErrorParam("数据格式错误：经营场景")
		}
		sceneConf.ConfigValue = sceneJson
		sceneConf.UpdateTime = now
		_, err = o.sysConfigRepo.Update(ctx, sceneConf)
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s", err.Error())
		}

		//修改金刚区配置
		err = o.sceneRefresh(ctx, in.Scene)
		if err != nil {
			return err
		}

		// 登录平台
		loginPlatformConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigLoginPlatform)
		if loginPlatformConf == nil {
			return apierr.ErrorDbNotFound("登录平台配置不存在")
		}
		loginPlatformConf.ConfigValue, err = in.LoginPlatform.ToString()
		if err != nil {
			return apierr.ErrorParam("数据格式错误：登录平台")
		}
		loginPlatformConf.UpdateTime = now
		_, err = o.sysConfigRepo.Update(ctx, loginPlatformConf)
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s", err.Error())
		}

		// 登录模式
		loginModeConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigLoginMode)
		if loginModeConf == nil {
			return apierr.ErrorDbNotFound("登录模式配置不存在")
		}
		loginModeConf.ConfigValue = strconv.Itoa(int(in.LoginModel))
		loginModeConf.UpdateTime = now
		_, err = o.sysConfigRepo.Update(ctx, loginModeConf)
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s")
		}

		// 登录方式
		loginTypeConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigLoginType)
		if loginTypeConf == nil {
			return apierr.ErrorDbNotFound("登录方式配置不存在")
		}
		oldLoginType := loginTypeConf.GetLoginTypeType()
		if (oldLoginType.IsUpdateDisable() || in.LoginType.IsUpdateDisable()) && oldLoginType != in.LoginType {
			return apierr.ErrorParam("当前登录方式不可切换")
		}
		loginTypeConf.ConfigValue = strconv.Itoa(int(in.LoginType))
		loginTypeConf.UpdateTime = now
		_, err = o.sysConfigRepo.Update(ctx, loginTypeConf)
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s", err.Error())
		}

		// 注册是否绑定手机号
		registerBindPhoneConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigRegisterBindPhone)
		if registerBindPhoneConf == nil {
			_, err = o.sysConfigRepo.Create(ctx, &do.SysConfigDo{
				ConfigKey:   valobj.SysConfigRegisterBindPhone,
				ConfigValue: strconv.Itoa(int(in.RegisterBindPhone)),
				CreateTime:  now,
				UpdateTime:  now,
				CustomerId:  shopInfo.CustomerID,
				ShopId:      shopInfo.ID,
			})
		} else {
			registerBindPhoneConf.ConfigValue = strconv.Itoa(int(in.RegisterBindPhone))
			registerBindPhoneConf.UpdateTime = now
			_, err = o.sysConfigRepo.Update(ctx, registerBindPhoneConf)
		}
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s", err.Error())
		}

		//商城皮肤
		shopColorConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigShopColor)
		if shopColorConf == nil {
			return apierr.ErrorDbNotFound("商城皮肤配置不存在")
		}
		shopColorConf.ConfigValue = in.ShopColor
		shopColorConf.UpdateTime = now
		_, err = o.sysConfigRepo.Update(ctx, shopColorConf)
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s", err.Error())
		}

		shopLogoConf, _ := o.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigShopLogo)
		if shopLogoConf == nil {
			return apierr.ErrorDbNotFound("商城logo配置不存在")
		}
		shopLogoConf.ConfigValue = in.ShopLogo
		shopLogoConf.UpdateTime = now
		_, err = o.sysConfigRepo.Update(ctx, shopLogoConf)
		if err != nil {
			return apierr.ErrorParam("系统配置修改失败:%s", err.Error())
		}

		// 是否透传企业余额授信
		err = o.UpdateShopConfig(ctx, shopInfo.ID, valobj.SysConfigShopShowBalance, strconv.Itoa(in.ShopShowBalance.GetValue()))
		if err != nil {
			return apierr.ErrorParam("是否透传企业余额授信配置修改失败:%s", err.Error())
		}

		//保存小程序appid
		if in.LoginPlatform.IsAppletEnable() && len(in.Applet) > 0 {
			var applet []*do.SaasShopAppletDo
			applet, err = o.saasShopAppletRepo.GetByShopId(cpCtx, shopInfo.CustomerID, shopInfo.ID)
			if err != nil {
				return apierr.ErrorParam("小程序配置获取失败:%s", err.Error())
			}
			appletMap := slice.KeyBy(applet, func(item *do.SaasShopAppletDo) valobj.SaasShopAppletTypeObj {
				return item.AppType
			})
			for _, item := range in.Applet {
				if v, ok := appletMap[item.AppType]; ok {
					appExist, _ := o.saasShopAppletRepo.ExistsCheckByAppId(cpCtx, item.AppId, v.CustomerID)
					if appExist {
						return apierr.ErrorParam("小程序【%s】已存在", item.AppId)
					}
					v.AppSecret = item.AppSecret
					v.AppID = item.AppId
					_, err = o.saasShopAppletRepo.Update(cpCtx, v)
					if err != nil {
						return apierr.ErrorParam("小程序信息更新失败:%s", err.Error())
					}
				} else {
					appExist, _ := o.saasShopAppletRepo.ExistsCheckByAppId(cpCtx, item.AppId, 0)
					if appExist {
						return apierr.ErrorParam("小程序【%s】已存在", item.AppId)
					}
					_, err = o.saasShopAppletRepo.Create(cpCtx, &do.SaasShopAppletDo{
						CustomerID: shopInfo.CustomerID,
						ShopID:     shopInfo.ID,
						AppID:      item.AppId,
						AppType:    item.AppType,
						AppSecret:  item.AppSecret,
					})
				}
			}
		}
		//修改管理员手机号
		_, err = o.UpdateShopAdminPhone(ctx, shopInfo.ID, admin.Mobile)
		return err
	})
	return shopInfo.ID, err
}

func (o *CustomerShopBiz) UpdateStatus(ctx context.Context, shopId int, status valobj.CustomerShopStatusObj) (int, error) {
	if shopId <= 0 || !status.Exists() {
		return 0, apierr.ErrorParam("参数错误")
	}
	shop, _ := o.customerShopRepo.Get(ctx, shopId)
	if shop == nil {
		return 0, apierr.ErrorParam("商城不存在")
	}
	if status.IsDisable() {
		order, _ := o.orderRepo.FindHandingOne(ctx, shop.CustomerID, shopId)
		if order != nil {
			return 0, apierr.ErrorNotAllow("商城有未完成订单，不能停用")
		}
	}
	shop.Status = status
	return o.customerShopRepo.Update(ctx, shop)
}

func (o *CustomerShopBiz) DefaultBanner(ctx context.Context, customerId, shopId int) (*do2.BannerInfoDo, error) {
	return o.bannerRepo.FindDefault(ctx, customerId, shopId)
}

func (o *CustomerShopBiz) AddShopSuperSysRole(ctx context.Context, shop *do.CustomerShopDo) (int, error) {
	menus, _ := o.sysMenuRepo.SysMenuList(ctx, valobj.SysMenuTypeShop)
	if len(menus) == 0 {
		return 0, apierr.ErrorDbNotFound("菜单不存在")
	}

	roleId, err := o.sysRoleRepo.AddSysRole(ctx, &ent.SysRole{
		RoleName:   "超级管理员",
		CreateTime: helper.GetNow(),
		UpdateTime: helper.GetNow(),
		CustomerID: shop.CustomerID,
		ShopID:     shop.ID,
	})

	roleMenus := make([]*ent.SysRoleMenu, 0)
	for _, menu := range menus {
		roleMenus = append(roleMenus, &ent.SysRoleMenu{
			RoleID:     roleId,
			MenuID:     menu.Id,
			CustomerID: shop.CustomerID,
			ShopID:     shop.ID,
		})
	}
	_, err = o.sysRoleMenuRepo.AddSysRoleMenu(ctx, roleMenus)
	if err != nil {
		return 0, err
	}
	return roleId, err
}

func (o *CustomerShopBiz) UpdateShopAdminPhone(ctx context.Context, shopId int, phone string) (int, error) {
	if !helper.IsAccountPhone(phone) {
		return 0, apierr.ErrorParam("手机号格式错误")
	}
	admin, _ := o.adminRepo.FindShopRoot(ctx, shopId)
	if len(admin) == 0 {
		return 0, apierr.ErrorParam("商城管理员不存在")
	}
	return o.adminRepo.UpdatePhone(ctx, admin[0].Id, phone)
}

func (o *CustomerShopBiz) Del(ctx context.Context, shopId int) (int, error) {
	order, _ := o.orderRepo.FindAnyOne(isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(shopId)))
	if order != nil {
		return 0, apierr.ErrorNotAllow("商城已产生订单，不能删除")
	}
	admin, _ := o.adminRepo.FindShopRoot(ctx, shopId)
	if len(admin) != 0 {
		_, err := o.adminRepo.DelAdmin(ctx, admin[0].Id)
		if err != nil {
			return 0, err
		}
	}
	return o.customerShopRepo.Delete(ctx, shopId)
}

func (o *CustomerShopBiz) ResetAdminPwd(ctx context.Context, shopId int, pwd string) (int, error) {
	if shopId <= 0 || pwd == "" {
		return 0, apierr.ErrorParam("参数错误")
	}
	admin, err := o.adminRepo.FindShopRoot(ctx, shopId)
	if err != nil {
		return 0, err
	}
	if len(admin) == 0 {
		return 0, apierr.ErrorParam("商城管理员不存在")
	}

	in := &bo.AdminUpdateBo{
		Id:       admin[0].Id,
		Password: pwd,
	}
	pwdHash, err := in.GetPwdHash()
	if err != nil {
		return 0, apierr.ErrorParam("密码加密失败:%s", err.Error())
	}
	in.Password = pwdHash
	return o.adminRepo.UpdateAdmin(ctx, in)
}

func (o *CustomerShopBiz) GetShopConfigs(ctx context.Context, shopIds []int, keys ...string) ([]*do.SysConfigDo, error) {
	return o.sysConfigRepo.GetByShopIdsByKeys(ctx, shopIds, keys...)
}

func (o *CustomerShopBiz) UpdateShopConfig(ctx context.Context, shopId int, key, val string) error {
	configs, err := o.sysConfigRepo.GetByShopIdByKeys(ctx, shopId, key)
	if err != nil {
		return err
	}
	if len(configs) == 0 {
		config := o.buildConfig(key, val, isolationcustomer.GetCustomerIdZero(ctx), shopId)
		config, err = o.sysConfigRepo.Create(ctx, config)
	} else {
		config := configs[0]
		config.ConfigValue = val
		config.UpdateTime = helper.GetNow()
		_, err = o.sysConfigRepo.Update(ctx, config)
	}
	return err
}

func (o *CustomerShopBiz) FindById(ctx context.Context, shopId int) (*do.CustomerShopDo, error) {
	return o.customerShopRepo.Get(ctx, shopId)
}
