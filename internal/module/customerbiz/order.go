package customerbiz

import (
	"context"
	"errors"
	"fmt"

	bizBo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizrepo "cardMall/internal/biz/repository"
	"cardMall/internal/conf"
	"cardMall/internal/pkg/isolationcustomer"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/jinzhu/copier"
)

type OrderBiz struct {
	repo                 bizrepo.OrderRepo
	trans                bizrepo.TransactionRepo
	orderOperatorLogRepo bizrepo.OrderOperatorLogRepo
	orderDeliverRepo     bizrepo.OrderDeliverRepo
	orderGoodsRepo       bizrepo.OrderGoodsRepo
	supplierRepo         bizrepo.SupplierRepo
	goodsBrandRepo       bizrepo.GoodsBrandRepo
	goodsCategoryRepo    bizrepo.GoodsCategoryRepo
	goodsSkuRepo         bizrepo.GoodsSkuRepo
	customerShopRepo     bizrepo.CustomerShopRepo
	siteRepo             bizrepo.SiteRepo
	payOrderRepo         bizrepo.PayOrderRepo

	couponCodeRepo bizrepo.CouponCodeRepo
	couponRepo     bizrepo.CouponRepo
	afterSaleRepo  bizrepo.OrderAfterSaleRepo

	refundDs *ds.RefundDs
	conf     *conf.Bootstrap
}

func NewOrderBiz(repo bizrepo.OrderRepo, trans bizrepo.TransactionRepo, orderOperatorLogRepo bizrepo.OrderOperatorLogRepo, orderDeliverRepo bizrepo.OrderDeliverRepo, orderGoodsRepo bizrepo.OrderGoodsRepo, supplierRepo bizrepo.SupplierRepo, goodsBrandRepo bizrepo.GoodsBrandRepo, goodsCategoryRepo bizrepo.GoodsCategoryRepo, goodsSkuRepo bizrepo.GoodsSkuRepo, customerShopRepo bizrepo.CustomerShopRepo, siteRepo bizrepo.SiteRepo, payOrderRepo bizrepo.PayOrderRepo, couponCodeRepo bizrepo.CouponCodeRepo, couponRepo bizrepo.CouponRepo, afterSaleRepo bizrepo.OrderAfterSaleRepo, refundDs *ds.RefundDs, conf *conf.Bootstrap) *OrderBiz {
	return &OrderBiz{repo: repo, trans: trans, orderOperatorLogRepo: orderOperatorLogRepo, orderDeliverRepo: orderDeliverRepo, orderGoodsRepo: orderGoodsRepo, supplierRepo: supplierRepo, goodsBrandRepo: goodsBrandRepo, goodsCategoryRepo: goodsCategoryRepo, goodsSkuRepo: goodsSkuRepo, customerShopRepo: customerShopRepo, siteRepo: siteRepo, payOrderRepo: payOrderRepo, couponCodeRepo: couponCodeRepo, couponRepo: couponRepo, afterSaleRepo: afterSaleRepo, refundDs: refundDs, conf: conf}
}

func (o *OrderBiz) CustomerList(ctx context.Context, in *bizBo.OrderListBo) (int, []*bizdo.OrderDo, error) {
	count, data, err := o.repo.CustomerRechargeList(ctx, in)

	siteIds := make([]int, 0, len(data))
	for _, order := range data {
		siteIds = append(siteIds, order.SiteID)
	}

	siteMap, err := o.siteRepo.GetMapByIds(ctx, siteIds)
	if err != nil {
		return 0, nil, err
	}
	for _, order := range data {

		if site, ex := siteMap[order.SiteID]; ex {
			order.SiteInfo = site
		}
	}

	shopIDs := slice.Map(data, func(index int, item *bizdo.OrderDo) int {
		return item.ShopID
	})
	customerShopDos, err := o.customerShopRepo.Find(ctx, shopIDs...)
	if err != nil {
		return 0, nil, err
	}
	shopMap := slice.KeyBy(customerShopDos, func(item *bizdo.CustomerShopDo) int {
		return item.ID
	})
	for _, order := range data {

		if customerShopDo, ex := shopMap[order.ShopID]; ex {
			order.CustomerShopDo = customerShopDo
		}
	}

	payOrderNumbers := slice.Map(data, func(index int, item *bizdo.OrderDo) string {
		return item.PayOrderNumber
	})
	payOrderDos, err := o.payOrderRepo.ListByOrderNumberWithPayTypes(ctx, payOrderNumbers, in.PayChannelType.ToTypes()...)
	if err != nil {
		return 0, nil, err
	}

	payOrderMap := slice.KeyBy(payOrderDos, func(item *bizdo.PayOrderDo) string {
		return item.OrderNumber
	})

	for _, order := range data {

		if payOrderDo, ex := payOrderMap[order.PayOrderNumber]; ex {
			order.PayOrderInfo = payOrderDo
		}
	}
	return count, data, err
}

func (o *OrderBiz) CustomerThirdList(ctx context.Context, in *bizBo.OrderListBo) (int, []*bizdo.OrderDo, error) {
	count, data, err := o.repo.CustomerThirdList(ctx, in)

	siteIds := make([]int, 0, len(data))
	for _, order := range data {
		siteIds = append(siteIds, order.SiteID)
	}

	siteMap, err := o.siteRepo.GetMapByIds(ctx, siteIds)
	if err != nil {
		return 0, nil, err
	}
	for _, order := range data {

		if site, ex := siteMap[order.SiteID]; ex {
			order.SiteInfo = site
		} else {
			fmt.Println(order.SiteID)
		}
	}

	shopIDs := slice.Map(data, func(index int, item *bizdo.OrderDo) int {
		return item.ShopID
	})
	customerShopDos, err := o.customerShopRepo.Find(ctx, shopIDs...)
	if err != nil {
		return 0, nil, err
	}
	shopMap := slice.KeyBy(customerShopDos, func(item *bizdo.CustomerShopDo) int {
		return item.ID
	})
	for _, order := range data {

		if customerShopDo, ex := shopMap[order.ShopID]; ex {
			order.CustomerShopDo = customerShopDo
		}
	}

	payOrderNumbers := slice.Map(data, func(index int, item *bizdo.OrderDo) string {
		return item.PayOrderNumber
	})
	payOrderDos, err := o.payOrderRepo.ListByOrderNumberWithPayTypes(ctx, payOrderNumbers, in.PayChannelType.ToTypes()...)
	if err != nil {
		return 0, nil, err
	}

	payOrderMap := slice.KeyBy(payOrderDos, func(item *bizdo.PayOrderDo) string {
		return item.OrderNumber
	})

	for _, order := range data {

		if payOrderDo, ex := payOrderMap[order.PayOrderNumber]; ex {
			order.PayOrderInfo = payOrderDo
		}
	}

	return count, data, err

}

func (o *OrderBiz) CustomerRealOrderList(ctx context.Context, in *bizBo.RealOrderListBo) (int, []*bizdo.OrderDo, error) {
	return o.repo.CustomerRealOrderList(ctx, in)
}

func (o *OrderBiz) CustomerRealOrderMainList(ctx context.Context, in *bizBo.RealOrderMainListBo) ([]*bizdo.OrderDo, error) {

	data, err := o.repo.CustomerRealOrderMainList(ctx, in)
	if err != nil {
		return nil, err
	}
	siteIds := make([]int, 0, len(data))
	for _, order := range data {
		siteIds = append(siteIds, order.SiteID)
	}

	siteMap, err := o.siteRepo.GetMapByIds(ctx, siteIds)
	if err != nil {
		return nil, err
	}
	for _, order := range data {

		if site, ex := siteMap[order.SiteID]; ex {
			order.SiteInfo = site
		}
	}

	shopIDs := slice.Map(data, func(index int, item *bizdo.OrderDo) int {
		return item.ShopID
	})
	customerShopDos, err := o.customerShopRepo.Find(ctx, shopIDs...)
	if err != nil {
		return nil, err
	}
	shopMap := slice.KeyBy(customerShopDos, func(item *bizdo.CustomerShopDo) int {
		return item.ID
	})

	for _, order := range data {

		if customerShopDo, ex := shopMap[order.ShopID]; ex {
			order.CustomerShopDo = customerShopDo
		}
	}

	payOrderNumbers := slice.Map(data, func(index int, item *bizdo.OrderDo) string {
		return item.PayOrderNumber
	})
	payOrderDos, err := o.payOrderRepo.ListByOrderNumber(ctx, payOrderNumbers)
	if err != nil {
		return nil, err
	}

	payOrderMap := slice.KeyBy(payOrderDos, func(item *bizdo.PayOrderDo) string {
		return item.OrderNumber
	})

	for _, order := range data {

		if payOrderDo, ex := payOrderMap[order.PayOrderNumber]; ex {
			order.PayOrderInfo = payOrderDo
		}
	}

	return data, nil
}

func (o *OrderBiz) CustomerExportRealOrderList(ctx context.Context, in *bizBo.ExportRealOrderListBo) ([]*bizdo.OrderDo, error) {

	data, err := o.repo.CustomerExportRealOrderList(ctx, in)
	if err != nil {
		return nil, err
	}
	siteIds := make([]int, 0, len(data))
	for _, order := range data {
		siteIds = append(siteIds, order.SiteID)
	}

	siteMap, err := o.siteRepo.GetMapByIds(ctx, siteIds)
	if err != nil {
		return nil, err
	}
	for _, order := range data {

		if site, ex := siteMap[order.SiteID]; ex {
			order.SiteInfo = site
		}
	}

	shopIDs := slice.Map(data, func(index int, item *bizdo.OrderDo) int {
		return item.ShopID
	})
	customerShopDos, err := o.customerShopRepo.Find(ctx, shopIDs...)
	if err != nil {
		return nil, err
	}
	shopMap := slice.KeyBy(customerShopDos, func(item *bizdo.CustomerShopDo) int {
		return item.ID
	})

	for _, order := range data {

		if customerShopDo, ex := shopMap[order.ShopID]; ex {
			order.CustomerShopDo = customerShopDo
		}
	}

	payOrderNumbers := slice.Map(data, func(index int, item *bizdo.OrderDo) string {
		return item.PayOrderNumber
	})
	payOrderDos, err := o.payOrderRepo.ListByOrderNumber(ctx, payOrderNumbers)
	if err != nil {
		return nil, err
	}

	payOrderMap := slice.KeyBy(payOrderDos, func(item *bizdo.PayOrderDo) string {
		return item.OrderNumber
	})

	for _, order := range data {

		if payOrderDo, ex := payOrderMap[order.PayOrderNumber]; ex {
			order.PayOrderInfo = payOrderDo
		}
	}

	return data, nil
}

func (o *OrderBiz) CustomerRechargeAll(ctx context.Context, in *bizBo.ExportOrderListBo) ([]*bizdo.OrderDo, error) {
	data, err := o.repo.CustomerRechargeAll(ctx, in)
	if err != nil {
		return nil, err
	}

	siteIds := make([]int, 0, len(data))
	for _, order := range data {
		siteIds = append(siteIds, order.SiteID)
	}

	siteMap, err := o.siteRepo.GetMapByIds(ctx, siteIds)
	if err != nil {
		return nil, err
	}
	for _, order := range data {

		if site, ex := siteMap[order.SiteID]; ex {
			order.SiteInfo = site
		}
	}

	shopIDs := slice.Map(data, func(index int, item *bizdo.OrderDo) int {
		return item.ShopID
	})
	customerShopDos, err := o.customerShopRepo.Find(ctx, shopIDs...)
	if err != nil {
		return nil, err
	}
	shopMap := slice.KeyBy(customerShopDos, func(item *bizdo.CustomerShopDo) int {
		return item.ID
	})

	for _, order := range data {

		if customerShopDo, ex := shopMap[order.ShopID]; ex {
			order.CustomerShopDo = customerShopDo
		}
	}

	payOrderNumbers := slice.Map(data, func(index int, item *bizdo.OrderDo) string {
		return item.PayOrderNumber
	})
	payOrderDos, err := o.payOrderRepo.ListByOrderNumber(ctx, payOrderNumbers)
	if err != nil {
		return nil, err
	}

	payOrderMap := slice.KeyBy(payOrderDos, func(item *bizdo.PayOrderDo) string {
		return item.OrderNumber
	})

	for _, order := range data {

		if payOrderDo, ex := payOrderMap[order.PayOrderNumber]; ex {
			order.PayOrderInfo = payOrderDo
		}
	}

	return data, nil
}

func (o *OrderBiz) CustomerThirdAll(ctx context.Context, in *bizBo.ExportOrderListBo) ([]*bizdo.OrderDo, error) {
	data, err := o.repo.CustomerThirdAll(ctx, in)
	if err != nil {
		return nil, err
	}

	siteIds := make([]int, 0, len(data))
	for _, order := range data {
		siteIds = append(siteIds, order.SiteID)
	}

	siteMap, err := o.siteRepo.GetMapByIds(ctx, siteIds)
	if err != nil {
		return nil, err
	}
	for _, order := range data {

		if site, ex := siteMap[order.SiteID]; ex {
			order.SiteInfo = site
		}
	}

	shopIDs := slice.Map(data, func(index int, item *bizdo.OrderDo) int {
		return item.ShopID
	})
	customerShopDos, err := o.customerShopRepo.Find(ctx, shopIDs...)
	if err != nil {
		return nil, err
	}
	shopMap := slice.KeyBy(customerShopDos, func(item *bizdo.CustomerShopDo) int {
		return item.ID
	})

	for _, order := range data {

		if customerShopDo, ex := shopMap[order.ShopID]; ex {
			order.CustomerShopDo = customerShopDo
		}
	}

	payOrderNumbers := slice.Map(data, func(index int, item *bizdo.OrderDo) string {
		return item.PayOrderNumber
	})
	payOrderDos, err := o.payOrderRepo.ListByOrderNumber(ctx, payOrderNumbers)
	if err != nil {
		return nil, err
	}

	payOrderMap := slice.KeyBy(payOrderDos, func(item *bizdo.PayOrderDo) string {
		return item.OrderNumber
	})

	for _, order := range data {

		if payOrderDo, ex := payOrderMap[order.PayOrderNumber]; ex {
			order.PayOrderInfo = payOrderDo
		}
	}

	return data, nil
}

func (g *OrderBiz) GetBrandName(ctx context.Context, id int) (*bizdo.GoodsBrandDo, error) {
	return g.goodsBrandRepo.GetOneByID(ctx, id)
}
func (g *OrderBiz) GetSupplierName(ctx context.Context, id int) (*bizdo.SupplierDo, error) {
	return g.supplierRepo.Get(ctx, id)
}
func (g *OrderBiz) GetCategoryName(ctx context.Context, id int) (*bizdo.GoodsCategoryDo, error) {
	return g.goodsCategoryRepo.FindById(ctx, id)
}
func (g *OrderBiz) GetGoods(ctx context.Context, skuNo string) (*bizdo.GoodsSkuDo, error) {
	return g.goodsSkuRepo.FindBySkuNo(ctx, skuNo)
}
func (g *OrderBiz) GetShopName(ctx context.Context, id int) (*bizdo.CustomerShopDo, error) {
	return g.customerShopRepo.Get(ctx, id)
}
func (o *OrderBiz) Detail(ctx context.Context, orderNumber string) (*bizdo.OrderDo, error) {
	data, err := o.repo.FindByOrderNumber(ctx, orderNumber)
	if err != nil {
		return nil, err
	}
	if data == nil {
		return nil, errors.New("订单不存在")
	}
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, data.CustomerID)
	payOrder, _ := o.payOrderRepo.FindByOrderNumber(ctx, data.PayOrderNumber)
	if payOrder == nil {
		return nil, errors.New("支付单不存在")
	}
	data.PayOrderInfo = payOrder

	ctx = isolationcustomer.WithCustomerIdCtx(ctx, data.CustomerID)

	//展示优惠券信息
	if payOrder.CouponCodeID > 0 {
		couponCodeInfo, err := o.couponCodeRepo.Get(ctx, payOrder.CouponCodeID)
		if err != nil {
			return nil, err
		}
		couponInfo, err := o.couponRepo.Get(ctx, couponCodeInfo.CouponID)
		if err != nil {
			return nil, err
		}
		data.CouponDo = couponInfo
		data.CouponCodeDo = couponCodeInfo
		data.CouponDiscountAmount = payOrder.CouponDiscountAmount / float64(payOrder.Num)
	}

	//售后信息
	afterSaleInfo, _ := o.afterSaleRepo.FindByOrderNumber(ctx, data.OrderNumber)
	if afterSaleInfo != nil {
		data.OrderAfterSaleDo = afterSaleInfo
	}

	site, _ := o.siteRepo.Get(ctx, data.SiteID)
	if site != nil {
		data.SiteDo = site
	}

	//订单商品信息
	orderGoods, _ := o.orderGoodsRepo.FindByOrderNumber(ctx, []string{data.OrderNumber}...)
	if len(orderGoods) == 0 {
		return nil, errors.New("订单商品信息错误")
	}
	//if orderGoods[0].SkuNo != "" {
	//	var (
	//		skuDo *bizdo.GoodsSkuDo
	//	)
	//	skuDo, err = o.goodsSkuRepo.FindBySkuNo(ctx, orderGoods[0].SkuNo)
	//	if err != nil {
	//		return nil, errors.New("订单商品信息错误")
	//	}
	//	if skuDo != nil {
	//		data.SaleIntegral = skuDo.SaleIntegral
	//	}
	//}
	//data.GoodsImage = orderGoods[0].GoodsImage
	//data.SalePrice = orderGoods[0].SalePrice
	//data.ChannelPrice = orderGoods[0].SupplierPrice
	//data.BrandName = orderGoods[0].BrandName
	//data.GoodsName = orderGoods[0].GoodsName
	//data.GoodsSkuName = orderGoods[0].GoodsSkuName
	data.OrderGoods = orderGoods
	return data, nil
}
func (o *OrderBiz) SubRealOrderByOrderNumber(ctx context.Context, orderNumber string) (*bizdo.SubRealOrderDetailDo, error) {
	return o.repo.SubRealOrderByOrderNumber(ctx, orderNumber)
}
func (o *OrderBiz) MainRealOrderByPayOrderNumber(ctx context.Context, payOrderNumber string) (*bizdo.MainRealOrderDetailDo, error) {
	return o.repo.MainRealOrderByPayOrderNumber(ctx, payOrderNumber)
}
func (o *OrderBiz) RealOrderGoodsList(ctx context.Context, in *bizBo.RealOrderGoodsListBo) ([]*bizdo.RealOrderGoodsListDo, error) {

	data, err := o.repo.RealOrderGoodsList(ctx, in)
	if err != nil {
		return nil, err
	}
	skuNos := slice.Map(data, func(_ int, item *bizdo.RealOrderGoodsListDo) string {
		return item.SkuNo
	})
	goodsSkuDos, err := o.goodsSkuRepo.FindWithGoodsBySkuNos(ctx, skuNos)
	if err != nil {
		return nil, err
	}
	skuMap := slice.KeyBy(goodsSkuDos, func(item *bizdo.GoodsSkuDo) string {
		return item.SkuNo
	})
	for _, val := range data {
		if skuInfo, ok := skuMap[val.SkuNo]; ok {
			val.SaleIntegral = skuInfo.SaleIntegral
		}
	}
	return data, nil
}
func (o *OrderBiz) RealOrderOperatorLog(ctx context.Context, orderNumbers []string) ([]*bizdo.OrderOperatorLogDo, error) {
	orderOperatorLogs, err := o.orderOperatorLogRepo.FindByOrderNumberIn(ctx, orderNumbers)
	if err != nil {
		return nil, err
	}
	orderOperatorLogDos := make([]*bizdo.OrderOperatorLogDo, 0, len(orderOperatorLogs))
	err = copier.Copy(&orderOperatorLogDos, &orderOperatorLogs)
	if err != nil {
		return nil, err
	}
	return orderOperatorLogDos, err
}

func (o *OrderBiz) RealOrderDeliverOne(ctx context.Context, orderNumber string) ([]*bizdo.RealOrderDeliverDo, error) {
	realOrderDeliverDos, err := o.orderDeliverRepo.FindOneByOrderNumber(ctx, orderNumber)
	if err != nil {
		return nil, err
	}
	return realOrderDeliverDos, nil
}
