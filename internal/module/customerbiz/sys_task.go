package customerbiz

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strconv"
	"time"

	bizBo "cardMall/internal/biz/bo"
	bizDo "cardMall/internal/biz/do"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/data"
	"cardMall/internal/pkg"
	"cardMall/internal/pkg/build"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"github.com/xuri/excelize/v2"
)

type SysTaskBiz struct {
	sysTaskRepo    bizRepo.SysTaskRepo
	sysTaskLogRepo bizRepo.SysTaskLogRepo
	oss            *pkg.AliyunOss
	hLog           *log.Helper
	data           *data.Data
	trans          bizRepo.TransactionRepo
	messageBus     bizRepo.EventBusRepo
	conf           *conf.Bootstrap
	orderBiz       *OrderBiz
}

func NewSysTaskBiz(
	sysTaskRepo bizRepo.SysTaskRepo,
	sysTaskLogRepo bizRepo.SysTaskLogRepo,
	oss *pkg.AliyunOss,
	trans bizRepo.TransactionRepo,
	data *data.Data,
	conf *conf.Bootstrap,
	orderBiz *OrderBiz,
	bus bizRepo.EventBusRepo,
	hLog *log.Helper) *SysTaskBiz {
	c := &SysTaskBiz{
		sysTaskRepo:    sysTaskRepo,
		sysTaskLogRepo: sysTaskLogRepo,
		oss:            oss,
		hLog:           hLog,
		trans:          trans,
		data:           data,
		messageBus:     bus,
		conf:           conf,
		orderBiz:       orderBiz,
	}
	_ = bus.Subscribe("executeExportTaskCustomer", func(ctx context.Context, id int) {
		c.ExecuteExportTask(ctx, id)
	})
	_ = bus.Subscribe("executeImportTaskCustomer", func(ctx context.Context, id int) {
		c.ExecuteImportTask(ctx, id)
	})
	return c
}

// SysTaskList 任务-查询-分页
func (o *SysTaskBiz) SysTaskList(ctx context.Context, reqBo *bizBo.SysTaskSearchBo) ([]*bizDo.SysTaskDo, *bizBo.RespPageBo) {
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	return o.sysTaskRepo.SearchList(ctx, reqBo)
}

// SysTaskLogList 任务日志-查询-分页
func (o *SysTaskBiz) SysTaskLogList(ctx context.Context, sysTaskID int) ([]*bizDo.SysTaskLogDo, error) {
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	return o.sysTaskLogRepo.FindByTaskId(ctx, sysTaskID)
}
func (o *SysTaskBiz) GenerateTask(ctx context.Context, sysTaskExecuteBo *bizBo.SysTaskExecuteBo) (sysTaskDo *bizDo.SysTaskDo, err error) {
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	err = o.trans.Exec(ctx, func(ctx context.Context) error {
		exportTaskDo := &bizDo.SysTaskDo{
			SysTaskName:   sysTaskExecuteBo.SysTaskName,
			SysTaskNumber: helper.GenExportTaskNo(),
			SysTaskType:   sysTaskExecuteBo.SysTaskType,
			SysTaskSource: sysTaskExecuteBo.SysTaskSource,
			Parameter:     sysTaskExecuteBo.Parameter,
			RelatedNo:     sysTaskExecuteBo.RelatedNo,
			DownloadURL:   "",
			Status:        valobj.SysTaskStatusNo,
			Remark:        sysTaskExecuteBo.Remark,
			FailReason:    "",
			UserID:        sysTaskExecuteBo.UserID,
			UserName:      sysTaskExecuteBo.UserName,
			CreateTime:    int(time.Now().Unix()),
			UpdateTime:    int(time.Now().Unix()),
		}
		sysTaskDo, err = o.sysTaskRepo.Create(ctx, exportTaskDo)
		if err != nil {
			return err
		}
		sysTaskLogDo := &bizDo.SysTaskLogDo{
			SysTaskID:  sysTaskDo.ID,
			Content:    "导出任务-创建",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		}
		_, err = o.sysTaskLogRepo.Create(ctx, sysTaskLogDo)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return
	}
	ctx2 := util.CopyValueCtx(ctx)
	if sysTaskExecuteBo.SysTaskType == valobj.SysTaskTypeExport {
		o.PublishExport(ctx2, sysTaskDo.ID)
	}
	if sysTaskExecuteBo.SysTaskType == valobj.SysTaskTypeImport {
		o.PublishImport(ctx2, sysTaskDo.ID)
	}
	return
}
func (s *SysTaskBiz) Publish(ctx context.Context, id int) {
	taskDo, err := s.sysTaskRepo.Get(ctx, id)
	if err != nil {
		log.Infof("SysTaskBiz.Publish fail id:%d err:{%v}", id, err)
		return
	}
	ctx2 := util.CopyValueCtx(ctx)
	if taskDo.SysTaskType == valobj.SysTaskTypeExport {
		s.PublishExport(ctx2, taskDo.ID)
	}
	if taskDo.SysTaskType == valobj.SysTaskTypeImport {
		s.PublishImport(ctx2, taskDo.ID)
	}
}

func (s *SysTaskBiz) PublishExport(ctx context.Context, id int) {
	s.messageBus.Publish("executeExportTaskCustomer", ctx, id)
}
func (s *SysTaskBiz) PublishImport(ctx context.Context, id int) {
	s.messageBus.Publish("executeImportTaskCustomer", ctx, id)
}
func (s *SysTaskBiz) ExecuteImportTask(ctx context.Context, id int) {
	start := time.Now()
	importTaskDo, err := s.sysTaskRepo.Get(ctx, id)
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}
	log.Infof("SysTaskBiz.ExecuteImportTask start ImportTaskNumber:%s ImportTaskName:%s", importTaskDo.SysTaskName, importTaskDo.SysTaskNumber)
	_, err = s.sysTaskRepo.UpdateStatus(ctx, id, valobj.SysTaskStatusInProgress)
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  importTaskDo.ID,
		Content:    "导入任务-开始执行",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}

	err = s.importTask(ctx, importTaskDo.SysTaskSource, importTaskDo.Parameter)
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
			ID:          importTaskDo.ID,
			DownloadURL: "",
			Status:      valobj.SysTaskStatusFail,
			FailReason:  err.Error(),
			UpdateTime:  int(time.Now().Unix()),
			FinishTime:  int(time.Now().Unix()),
		})
		_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
			SysTaskID:  importTaskDo.ID,
			Content:    "导入任务-执行失败",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})

		return
	}

	_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
		ID:          importTaskDo.ID,
		DownloadURL: "",
		Status:      valobj.SysTaskStatusDone,
		FailReason:  importTaskDo.FailReason,
		UpdateTime:  int(time.Now().Unix()),
		FinishTime:  int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  importTaskDo.ID,
		Content:    "导入任务-执行完毕",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}

	log.Infof("SysTaskBiz.ExecuteImportTask end ImportTaskNumber:%s ImportTaskName:%s 耗时:{%v}", importTaskDo.SysTaskName, importTaskDo.SysTaskNumber, time.Since(start))
	return

}
func (s *SysTaskBiz) ExecuteExportTask(ctx context.Context, id int) {
	start := time.Now()
	exportTaskDo, err := s.sysTaskRepo.Get(ctx, id)
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}
	log.Infof("SysTaskBiz.executeExportTask start ExportTaskNumber:%s ExportTaskName:%s", exportTaskDo.SysTaskName, exportTaskDo.SysTaskNumber)
	_, err = s.sysTaskRepo.UpdateStatus(ctx, id, valobj.SysTaskStatusInProgress)
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  exportTaskDo.ID,
		Content:    "导出任务-开始执行",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}

	fileName, buff, err := s.generateExcel(ctx, exportTaskDo.SysTaskSource, exportTaskDo.Parameter)
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
			ID:          exportTaskDo.ID,
			DownloadURL: "",
			Status:      valobj.SysTaskStatusFail,
			FailReason:  err.Error(),
			UpdateTime:  int(time.Now().Unix()),
			FinishTime:  int(time.Now().Unix()),
		})
		_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
			SysTaskID:  exportTaskDo.ID,
			Content:    "导出任务-执行失败",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})

		return
	}
	url, err := s.oss.Upload(isolationcustomer.WithCustomerAndShopCtx(ctx, exportTaskDo.CustomerID, exportTaskDo.ShopID), fileName, buff.Bytes())
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
			ID:          exportTaskDo.ID,
			DownloadURL: "",
			Status:      valobj.SysTaskStatusFail,
			FailReason:  err.Error(),
			UpdateTime:  int(time.Now().Unix()),
			FinishTime:  int(time.Now().Unix()),
		})
		_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
			SysTaskID:  exportTaskDo.ID,
			Content:    "导出任务-执行失败",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})
		return
	}
	_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
		ID:          exportTaskDo.ID,
		DownloadURL: url,
		Status:      valobj.SysTaskStatusDone,
		FailReason:  exportTaskDo.FailReason,
		UpdateTime:  int(time.Now().Unix()),
		FinishTime:  int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  exportTaskDo.ID,
		Content:    "导出任务-执行完毕",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}

	log.Infof("SysTaskBiz.executeExportTask end ExportTaskNumber:%s ExportTaskName:%s 耗时:{%v}", exportTaskDo.SysTaskName, exportTaskDo.SysTaskNumber, time.Since(start))
	return

}

func (s *SysTaskBiz) RealOrderExport(ctx context.Context, in *bizBo.ExportRealOrderListBo) (fileName string, buff *bytes.Buffer, err error) {
	data, err := s.orderBiz.CustomerExportRealOrderList(ctx, in)
	if err != nil {
		return
	}
	realOrderSubListDoMap, realOrderMainListDoMap, err := s.getRealOrderDataList(ctx, data)
	if err != nil {
		return
	}
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return
	}

	_ = f.SetCellStr("sheet1", "A1", "主订单编号")
	_ = f.SetCellStr("sheet1", "B1", "订单编号")
	_ = f.SetCellStr("sheet1", "C1", "商城名称")
	_ = f.SetCellStr("sheet1", "D1", "品牌名称")
	_ = f.SetCellStr("sheet1", "E1", "商品名称")
	_ = f.SetCellStr("sheet1", "F1", "上游成本价")
	_ = f.SetCellStr("sheet1", "G1", "下游结算价")
	_ = f.SetCellStr("sheet1", "H1", "用户订单价")
	_ = f.SetCellStr("sheet1", "I1", "状态")
	_ = f.SetCellStr("sheet1", "J1", "来源站点")
	_ = f.SetCellStr("sheet1", "K1", "创建时间")
	_ = f.SetCellStr("sheet1", "L1", "支付方式")
	_ = f.SetCellStr("sheet1", "M1", "支付时间")
	_ = f.SetCellStr("sheet1", "N1", "完成时间")

	realOrderMainListDos := maputil.Values(realOrderMainListDoMap)
	sort.Slice(realOrderMainListDos, func(i, j int) bool {
		return realOrderMainListDos[i].CreateTime > realOrderMainListDos[j].CreateTime
	})
	var row = 2
	for _, orderMainListDo := range realOrderMainListDos {

		realOrderSubListDos := realOrderSubListDoMap[orderMainListDo.PayOrderNumber]
		sort.Slice(realOrderSubListDos, func(i, j int) bool {
			return realOrderSubListDos[i].CreateTime > realOrderSubListDos[j].CreateTime
		})
		for _, realOrderSubListDo := range realOrderSubListDos {

			for _, realOrderGoodsListDo := range realOrderSubListDo.OrderGoods {
				strRwo := strconv.Itoa(row)
				_ = f.SetCellStr("sheet1", "A"+strRwo, realOrderSubListDo.PayOrderNumber)
				_ = f.SetCellStr("sheet1", "B"+strRwo, realOrderSubListDo.OrderNumber)
				_ = f.SetCellStr("sheet1", "C"+strRwo, realOrderSubListDo.CustomerShopDo.Name)
				_ = f.SetCellStr("sheet1", "D"+strRwo, realOrderGoodsListDo.BrandName)
				_ = f.SetCellStr("sheet1", "E"+strRwo, build.GenGoodsNameWithSKU(realOrderGoodsListDo.GoodsName, realOrderGoodsListDo.GoodsSkuName))
				_ = f.SetCellStr("sheet1", "F"+strRwo, helper.Float64ToString(realOrderGoodsListDo.ChannelPrice, 4))
				_ = f.SetCellStr("sheet1", "G"+strRwo, helper.Float64ToString(realOrderGoodsListDo.SupplierPrice, 4))
				_ = f.SetCellStr("sheet1", "H"+strRwo, helper.Float64ToString(realOrderSubListDo.TotalAmount, 2))
				_ = f.SetCellStr("sheet1", "I"+strRwo, realOrderSubListDo.Status.GetName())
				_ = f.SetCellStr("sheet1", "J"+strRwo, orderMainListDo.SiteInfo.Name)
				_ = f.SetCellStr("sheet1", "K"+strRwo, helper.GetTimeDate(orderMainListDo.CreateTime))
				_ = f.SetCellStr("sheet1", "L"+strRwo, orderMainListDo.PayOrderInfo.PayType.GetOuterPayChannel().String())
				_ = f.SetCellStr("sheet1", "M"+strRwo, helper.GetTimeDate(orderMainListDo.PayOrderInfo.PayTime))
				_ = f.SetCellStr("sheet1", "N"+strRwo, helper.GetTimeDate(orderMainListDo.FinishTime))

				row++
			}
		}
	}
	f.SetActiveSheet(index)
	fileName = "order_" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	buff, err = f.WriteToBuffer()
	if err != nil {
		return
	}
	return

}

func (s *SysTaskBiz) VirtualOrderExport(ctx context.Context, in *bizBo.ExportOrderListBo) (fileName string, buff *bytes.Buffer, err error) {
	orderListDos, err := s.orderBiz.CustomerRechargeAll(ctx, in)
	if err != nil {
		return
	}

	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return
	}

	_ = f.SetCellStr("sheet1", "A1", "主订单编号")
	_ = f.SetCellStr("sheet1", "B1", "订单编号")
	_ = f.SetCellStr("sheet1", "C1", "商城名称")
	_ = f.SetCellStr("sheet1", "D1", "品牌名称")
	_ = f.SetCellStr("sheet1", "E1", "商品名称")
	_ = f.SetCellStr("sheet1", "F1", "上游成本价")
	_ = f.SetCellStr("sheet1", "G1", "下游结算价")
	_ = f.SetCellStr("sheet1", "H1", "用户订单价")
	_ = f.SetCellStr("sheet1", "I1", "状态")
	_ = f.SetCellStr("sheet1", "J1", "来源站点")
	_ = f.SetCellStr("sheet1", "K1", "创建时间")
	_ = f.SetCellStr("sheet1", "L1", "支付方式")
	_ = f.SetCellStr("sheet1", "M1", "支付时间")
	_ = f.SetCellStr("sheet1", "N1", "完成时间")

	var row = 2
	for _, orderListDo := range orderListDos {
		for _, orderGoodsListDo := range orderListDo.OrderGoods {
			strRwo := strconv.Itoa(row)
			_ = f.SetCellStr("sheet1", "A"+strRwo, orderListDo.PayOrderNumber)
			_ = f.SetCellStr("sheet1", "B"+strRwo, orderListDo.OrderNumber)
			_ = f.SetCellStr("sheet1", "C"+strRwo, orderListDo.CustomerShopDo.Name)
			_ = f.SetCellStr("sheet1", "D"+strRwo, orderGoodsListDo.BrandName)
			_ = f.SetCellStr("sheet1", "E"+strRwo, build.GenGoodsNameWithSKU(orderGoodsListDo.GoodsName, orderGoodsListDo.GoodsSkuName))
			_ = f.SetCellStr("sheet1", "F"+strRwo, helper.Float64ToString(orderGoodsListDo.ChannelPrice, 4))
			_ = f.SetCellStr("sheet1", "G"+strRwo, helper.Float64ToString(orderGoodsListDo.SupplierPrice, 4))
			_ = f.SetCellStr("sheet1", "H"+strRwo, helper.Float64ToString(orderGoodsListDo.SalePrice, 2))
			_ = f.SetCellStr("sheet1", "I"+strRwo, orderListDo.Status.GetName())
			_ = f.SetCellStr("sheet1", "J"+strRwo, orderListDo.SiteInfo.Name)
			_ = f.SetCellStr("sheet1", "K"+strRwo, helper.GetTimeDate(orderListDo.CreateTime))
			_ = f.SetCellStr("sheet1", "L"+strRwo, orderListDo.PayOrderInfo.PayType.GetOuterPayChannel().String())
			_ = f.SetCellStr("sheet1", "M"+strRwo, helper.GetTimeDate(orderListDo.PayOrderInfo.PayTime))
			_ = f.SetCellStr("sheet1", "N"+strRwo, helper.GetTimeDate(orderListDo.FinishTime))

			row++
		}
	}
	f.SetActiveSheet(index)
	fileName = "order_" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	buff, err = f.WriteToBuffer()
	if err != nil {
		return
	}
	return

}

func (s *SysTaskBiz) ThirdAllExport(ctx context.Context, in *bizBo.ExportOrderListBo) (fileName string, buff *bytes.Buffer, err error) {
	orderListDos, err := s.orderBiz.CustomerThirdAll(ctx, in)
	if err != nil {
		return
	}

	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return
	}

	_ = f.SetCellStr("sheet1", "A1", "订单编号")
	_ = f.SetCellStr("sheet1", "B1", "商城名称")
	_ = f.SetCellStr("sheet1", "C1", "品牌名称")
	_ = f.SetCellStr("sheet1", "D1", "商品名称")
	_ = f.SetCellStr("sheet1", "E1", "状态")
	_ = f.SetCellStr("sheet1", "F1", "支付金额")
	_ = f.SetCellStr("sheet1", "G1", "售价")
	_ = f.SetCellStr("sheet1", "H1", "订单总价")
	_ = f.SetCellStr("sheet1", "I1", "来源站点")
	_ = f.SetCellStr("sheet1", "J1", "创建时间")
	_ = f.SetCellStr("sheet1", "K1", "支付方式")
	_ = f.SetCellStr("sheet1", "L1", "支付时间")
	_ = f.SetCellStr("sheet1", "M1", "完成时间")

	var row = 2
	for _, orderListDo := range orderListDos {
		for _, orderGoodsListDo := range orderListDo.OrderGoods {
			strRwo := strconv.Itoa(row)
			totalAmount := decimal.NewFromFloat(orderGoodsListDo.SalePrice).Mul(decimal.NewFromInt(int64(orderGoodsListDo.Quantity)))
			_ = f.SetCellStr("sheet1", "A"+strRwo, orderListDo.OrderNumber)
			_ = f.SetCellStr("sheet1", "B"+strRwo, orderListDo.CustomerShopDo.Name)
			_ = f.SetCellStr("sheet1", "C"+strRwo, orderGoodsListDo.BrandName)
			_ = f.SetCellStr("sheet1", "D"+strRwo, orderGoodsListDo.GoodsName+"_"+orderGoodsListDo.GoodsSkuName)
			_ = f.SetCellStr("sheet1", "E"+strRwo, orderListDo.Status.GetName())
			_ = f.SetCellStr("sheet1", "F"+strRwo, helper.Float64ToString(orderGoodsListDo.PayAmount, 2))
			_ = f.SetCellStr("sheet1", "G"+strRwo, helper.Float64ToString(orderGoodsListDo.SalePrice, 2))
			_ = f.SetCellStr("sheet1", "H"+strRwo, helper.Float64ToString(totalAmount.InexactFloat64(), 2))
			_ = f.SetCellStr("sheet1", "I"+strRwo, orderListDo.SiteInfo.Name)
			_ = f.SetCellStr("sheet1", "J"+strRwo, helper.GetTimeDate(orderListDo.CreateTime))
			_ = f.SetCellStr("sheet1", "K"+strRwo, orderListDo.PayOrderInfo.PayType.GetOuterPayChannel().String())
			_ = f.SetCellStr("sheet1", "L"+strRwo, helper.GetTimeDate(orderListDo.PayOrderInfo.PayTime))
			_ = f.SetCellStr("sheet1", "M"+strRwo, helper.GetTimeDate(orderListDo.FinishTime))

			row++
		}
	}
	f.SetActiveSheet(index)
	fileName = "order_" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	buff, err = f.WriteToBuffer()
	if err != nil {
		return
	}
	return

}

func (o *SysTaskBiz) getRealOrderDataList(ctx context.Context, realOrderListDos []*bizDo.OrderDo) (map[string][]*bizDo.OrderDo, map[string]*bizDo.OrderDo, error) {

	var (
		payOrderNumbers        = make([]string, 0, len(realOrderListDos))
		realOrderSubListDoMap  = make(map[string][]*bizDo.OrderDo)
		realOrderMainListDoMap = make(map[string]*bizDo.OrderDo)
	)
	// 子订单
	for _, val := range realOrderListDos {
		realOrderSubListDoMap[val.PayOrderNumber] = append(realOrderSubListDoMap[val.PayOrderNumber], val)
		payOrderNumbers = append(payOrderNumbers, val.PayOrderNumber)
	}
	// 主订单
	orderMainList, err := o.orderBiz.CustomerRealOrderMainList(ctx, &bizBo.RealOrderMainListBo{
		PayOrderNumbers: payOrderNumbers,
	})
	if err != nil {
		return nil, nil, err
	}
	for _, val := range orderMainList {
		realOrderMainListDoMap[val.PayOrderNumber] = val
	}

	return realOrderSubListDoMap, realOrderMainListDoMap, nil
}

func (s *SysTaskBiz) generateExcel(ctx context.Context, sysTaskSource valobj.SysTaskSource, parameter string) (fileName string, buff *bytes.Buffer, err error) {

	switch sysTaskSource {
	case valobj.SysTaskSourceCardBatchCouponExport:
		return
	case valobj.SysTaskSourceRealOrderExport:
		in := &bizBo.ExportRealOrderListBo{}
		err = json.Unmarshal([]byte(parameter), in)
		if err != nil {
			return
		}
		ctx = isolationcustomer.WithDisableShopCtx(ctx)
		return s.RealOrderExport(ctx, in)
	case valobj.SysTaskSourceVirtualOrderExport:
		in := &bizBo.ExportOrderListBo{}
		err = json.Unmarshal([]byte(parameter), in)
		if err != nil {
			return
		}
		ctx = isolationcustomer.WithDisableShopCtx(ctx)
		return s.VirtualOrderExport(ctx, in)
	case valobj.SysTaskSourceThirdOrderExport:
		in := &bizBo.ExportOrderListBo{}
		err = json.Unmarshal([]byte(parameter), in)
		if err != nil {
			return
		}
		ctx = isolationcustomer.WithDisableShopCtx(ctx)
		return s.ThirdAllExport(ctx, in)
	default:
		err = fmt.Errorf("SysTaskBiz.generateExcel - fail sysTaskSource(%v), db's data error, parameter(%s)", sysTaskSource, parameter)
		return
	}
}
func (s *SysTaskBiz) importTask(ctx context.Context, sysTaskSource valobj.SysTaskSource, parameter string) (err error) {

	switch sysTaskSource {
	case valobj.SysTaskSourceImportAccount:

		return
	default:
		err = fmt.Errorf("SysTaskBiz.importTask - fail sysTaskSource(%v), db's data error, parameter(%s)", sysTaskSource, parameter)
		return
	}
}
