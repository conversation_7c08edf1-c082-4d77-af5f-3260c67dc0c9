package customerbiz

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data/ent"
	"cardMall/internal/pkg/helper"
	"context"
)

type CustomerBiz struct {
	saasCustomerSettingRepo repository.SaasCustomerSettingRepo
	shopSettingRepo         repository.ShopSettingRepo
}

func NewCustomerBiz(saasCustomerSettingRepo repository.SaasCustomerSettingRepo, shopSettingRepo repository.ShopSettingRepo) *CustomerBiz {
	return &CustomerBiz{saasCustomerSettingRepo: saasCustomerSettingRepo, shopSettingRepo: shopSettingRepo}
}

// GetSetting 获取客户设置
func (c *CustomerBiz) GetSetting(ctx context.Context, customerId int) (*do.SaasCustomerSettingDo, error) {
	settingDo, err := c.saasCustomerSettingRepo.GetByCustomerId(ctx, customerId)
	if err != nil {
		if ent.IsNotFound(err) {
			return helper.AnyToPtr(do.GetDefaultSaasCustomerSetting()), nil
		}
		return nil, err
	}
	return settingDo, nil
}

func (c *CustomerBiz) SaveSetting(ctx context.Context, req *bo.SaveCustomerSettingBo) error {
	exists, err := c.saasCustomerSettingRepo.ExistByCustomerId(ctx, req.CustomerId)
	if err != nil {
		return err
	}
	if exists {
		_, err = c.saasCustomerSettingRepo.Update(ctx, req)
	} else {
		_, err = c.saasCustomerSettingRepo.Create(ctx, req)
	}
	if err != nil {
		return err
	}
	return nil
}

func (c *CustomerBiz) GetShopSetting(ctx context.Context, customerId int, shopId int) (*do.ShopSettingDo, error) {
	settingDo, err := c.shopSettingRepo.GetByShopId(ctx, customerId, shopId)
	if err != nil {
		if ent.IsNotFound(err) {
			return helper.AnyToPtr(do.GetDefaultShopSetting(customerId, shopId)), nil
		}
		return nil, err
	}
	return settingDo, nil
}

func (c *CustomerBiz) SaveShopSetting(ctx context.Context, req *bo.SaveShopSettingBo) error {
	exists, err := c.shopSettingRepo.ExistByShopId(ctx, req.CustomerId, req.ShopId)
	if err != nil {
		return err
	}
	if exists {
		_, err = c.shopSettingRepo.Update(ctx, req)
	} else {
		_, err = c.shopSettingRepo.Create(ctx, req)
	}
	if err != nil {
		return err
	}
	return nil
}
