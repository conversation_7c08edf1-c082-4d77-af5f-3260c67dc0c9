package clibiz

import (
	bizbo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	repository2 "cardMall/internal/module/appbiz/repository"
	"cardMall/internal/module/clibiz/repository"
	"cardMall/internal/pkg/helper"
	"context"
	"encoding/json"
	"fmt"
	"github.com/shopspring/decimal"
	"sort"
	"strings"
	"time"
)

const supplierDefaultId = 1

type GoodsBiz struct {
	goodsOldRepo              repository.GoodsOldRepo
	goodsRepo                 bizRepo.GoodsRepo
	goodsSkuRepo              bizRepo.GoodsSkuRepo
	goodsSpecRepo             bizRepo.GoodsSpecRepo
	goodsSpecItemRpo          bizRepo.GoodsSpecItemRepo
	supplierGoodsRepo         bizRepo.SupplierGoodsRepo
	supplierGoodsSkuRepo      bizRepo.SupplierGoodsSkuRepo
	supplierGoodsSpecRepo     bizRepo.SupplierGoodsSpecRepo
	supplierGoodsSpecItemRepo bizRepo.SupplierGoodsSpecItemRepo
	goodsCategoryRepo         bizRepo.GoodsCategoryRepo
	goodsCategoryBrandRepo    bizRepo.GoodsCategoryBrandRepo
	goodsBrandRepo            bizRepo.GoodsBrandRepo
	trans                     bizRepo.TransactionRepo
	supplierGoodsSkuNoRepo    bizRepo.SupplierGoodsSkuNoRepo
	baseGoodsRepo             repository2.BaseGoodsRepo
	goodsDs                   *ds.GoodsDs
}

func NewGoodsBiz(goodsOldRepo repository.GoodsOldRepo, goodsRepo bizRepo.GoodsRepo, goodsSkuRepo bizRepo.GoodsSkuRepo, goodsSpecRepo bizRepo.GoodsSpecRepo, goodsSpecItemRpo bizRepo.GoodsSpecItemRepo, supplierGoodsRepo bizRepo.SupplierGoodsRepo, supplierGoodsSkuRepo bizRepo.SupplierGoodsSkuRepo, supplierGoodsSpecRepo bizRepo.SupplierGoodsSpecRepo, supplierGoodsSpecItemRepo bizRepo.SupplierGoodsSpecItemRepo, goodsCategoryRepo bizRepo.GoodsCategoryRepo, goodsCategoryBrandRepo bizRepo.GoodsCategoryBrandRepo, goodsBrandRepo bizRepo.GoodsBrandRepo, trans bizRepo.TransactionRepo, supplierGoodsSkuNoRepo bizRepo.SupplierGoodsSkuNoRepo, baseGoodsRepo repository2.BaseGoodsRepo, goodsDs *ds.GoodsDs) *GoodsBiz {
	return &GoodsBiz{goodsOldRepo: goodsOldRepo, goodsRepo: goodsRepo, goodsSkuRepo: goodsSkuRepo, goodsSpecRepo: goodsSpecRepo, goodsSpecItemRpo: goodsSpecItemRpo, supplierGoodsRepo: supplierGoodsRepo, supplierGoodsSkuRepo: supplierGoodsSkuRepo, supplierGoodsSpecRepo: supplierGoodsSpecRepo, supplierGoodsSpecItemRepo: supplierGoodsSpecItemRepo, goodsCategoryRepo: goodsCategoryRepo, goodsCategoryBrandRepo: goodsCategoryBrandRepo, goodsBrandRepo: goodsBrandRepo, trans: trans, supplierGoodsSkuNoRepo: supplierGoodsSkuNoRepo, baseGoodsRepo: baseGoodsRepo, goodsDs: goodsDs}
}

func (s *GoodsBiz) MigrateToSupplier(ctx context.Context) (err error) {
	now := int(time.Now().Unix())
	specType, _ := s.supplierGoodsSpecRepo.GetByName(ctx, supplierDefaultId, "会员类型")
	if specType == nil {
		specType, _ = s.supplierGoodsSpecRepo.Create(ctx, &bizdo.SupplierGoodsSpecDo{
			SupplierID:     supplierDefaultId,
			Name:           "类型",
			Remark:         "",
			IsTpl:          valobj.SupplierGoodsSpecTplNo,
			Sort:           1,
			CreateTime:     now,
			UpdateTime:     now,
			GoodsSpecItems: nil,
		})
	}
	specVal, _ := s.supplierGoodsSpecRepo.GetByName(ctx, supplierDefaultId, "面额")
	if specVal == nil {
		specVal, _ = s.supplierGoodsSpecRepo.Create(ctx, &bizdo.SupplierGoodsSpecDo{
			SupplierID:     supplierDefaultId,
			Name:           "面额",
			Remark:         "",
			IsTpl:          valobj.SupplierGoodsSpecTplNo,
			Sort:           1,
			CreateTime:     now,
			UpdateTime:     now,
			GoodsSpecItems: nil,
		})
	}
	goods, _ := s.goodsOldRepo.All(ctx)
	for _, g := range goods {
		err = s.trans.Exec(ctx, func(ctx context.Context) error {
			names := strings.Split(g.Name, "/")
			goodsName := names[0] + names[1] + names[2]
			goodsSpecTypeName := names[1]
			goodsSpecValName := names[2]
			status := valobj.SupplierGoodsStatusEnable
			if g.Status == 0 {
				status = valobj.SupplierGoodsStatusDisable
			}
			brandInfo, _ := s.goodsBrandRepo.GetOneByID(ctx, g.BrandId)
			categoryInfo, _ := s.goodsCategoryRepo.FindByName(ctx, brandInfo.Name)
			if categoryInfo == nil {
				return fmt.Errorf("未找到商品分类：%s", brandInfo.Name)
			}
			supplierGoods, _ := s.supplierGoodsRepo.SearchList(ctx, &bizbo.SupplierGoodsSearchBo{
				Name: g.Name,
				Type: valobj.SupplierGoodsTypeVirtual,
				Page: &bizbo.ReqPageBo{
					PageSize: 1,
					Page:     1,
				},
			})

			supGoods := &bizdo.SupplierGoodsDo{}
			// 创建商品， 如果商品已存在，则更新库存
			if len(supplierGoods) == 0 {
				images, _ := json.Marshal([]string{g.Image})
				supGoods, err = s.supplierGoodsRepo.Create(ctx, &bizdo.SupplierGoodsDo{
					Type:        valobj.SupplierGoodsTypeVirtual,
					SpecJson:    "",
					CategoryID:  categoryInfo.Id,
					BrandID:     g.BrandId,
					SupplierID:  supplierDefaultId,
					Name:        goodsName,
					MainImage:   g.Image,
					Images:      string(images),
					Detail:      g.Detail,
					Status:      status,
					Stock:       g.Stock,
					Sort:        g.Sort,
					TaxRate:     0,
					TaxRemark:   "",
					SaleArea:    "",
					NotSaleArea: "",
					CreateTime:  now,
					UpdateTime:  now,
					DeliverAddr: "",
					CustomerID:  constants.SaasPlatformCustomer,
					ShopID:      0,
				}, status)
				if err != nil {
					return err
				}
			} else {
				supGoods = supplierGoods[0]
				err = s.supplierGoodsRepo.IncrStock(ctx, supGoods.ID, g.Stock)
				if err != nil {
					return err
				}
			}
			//查询规格值是否存在
			specTypeItem, _ := s.supplierGoodsSpecItemRepo.GetByName(ctx, specType.ID, goodsSpecTypeName)
			if specTypeItem == nil {
				specTypeItem, _ = s.supplierGoodsSpecItemRepo.Create(ctx, &bizdo.SupplierGoodsSpecItemDo{
					SpecID:     specType.ID,
					Name:       goodsSpecTypeName,
					SpecName:   specType.Name,
					Sort:       g.Sort,
					IsTpl:      valobj.SupplierGoodsSpecTplNo,
					CreateTime: now,
					UpdateTime: now,
				})
			}

			specValItem, _ := s.supplierGoodsSpecItemRepo.GetByName(ctx, specVal.ID, goodsSpecValName)
			if specValItem == nil {
				specValItem, _ = s.supplierGoodsSpecItemRepo.Create(ctx, &bizdo.SupplierGoodsSpecItemDo{
					SpecID:     specVal.ID,
					Name:       goodsSpecValName,
					SpecName:   specVal.Name,
					Sort:       g.Sort,
					IsTpl:      valobj.SupplierGoodsSpecTplNo,
					CreateTime: now,
					UpdateTime: now,
				})
			}

			//创建Sku
			Profit, _ := decimal.NewFromFloat(g.SalePrice).Sub(decimal.NewFromFloat(g.ChannelPrice)).Float64()
			ProfitRate, _ := decimal.NewFromFloat(Profit).Div(decimal.NewFromFloat(g.SalePrice)).Mul(decimal.NewFromInt32(100)).Float64()
			productIdStr := g.ProductId
			baseGoods, _ := s.baseGoodsRepo.First(ctx, g.ProductId)
			specItemIds := []int{specTypeItem.ID, specValItem.ID}
			sort.Ints(specItemIds)
			skuDo := &bizdo.SupplierGoodsSkuDo{
				GoodsID:          supGoods.ID,
				Name:             fmt.Sprintf("%s,%s", specTypeItem.Name, specValItem.Name),
				Image:            g.Image,
				SpecItemIds:      strings.Join(helper.SliceInt2String(specItemIds), ","),
				MarketPrice:      g.SalePrice,
				SalePrice:        g.SalePrice,
				SupplierPrice:    g.ChannelPrice,
				ChannelPrice:     g.ChannelPrice,
				FreePostagePrice: g.SalePrice,
				Barcode:          "",
				SupplierBarcode:  productIdStr,
				ProductID:        g.ProductId,
				Stock:            g.Stock,
				StockAlarmNum:    0,
				Status:           status,
				Sort:             g.Sort,
				SalesVolume:      0,
				Profit:           Profit,
				ProfitRate:       ProfitRate,
				CreateTime:       now,
				UpdateTime:       now,
				DeleteTime:       0,
				CustomerID:       constants.SaasPlatformCustomer,
				ShopID:           0,
			}
			if baseGoods != nil {
				skuDo.ProductName = baseGoods.ProductName
				if baseGoods.Type == 1 {
					skuDo.ProductType = valobj.VirtualProductTypeRecharge
				} else {
					skuDo.ProductType = valobj.VirtualProductTypeCard
				}
			}
			skuDo.SkuNo, _ = s.goodsDs.GenSkuNo(ctx, skuDo.GetSkuUniqueStr(supGoods.ID))
			_, _ = s.supplierGoodsSkuRepo.Create(ctx, supGoods.ID, skuDo, valobj.SupplierGoodsStatusEnable)
			return nil
		})
		if err != nil {
			fmt.Printf("[%d]migrate goods to supplier error: %v", g.Id, err.Error())
		}
	}
	return s.updateSupplierGoods(ctx)
}

func (s *GoodsBiz) updateSupplierGoods(ctx context.Context) error {
	goods, _ := s.supplierGoodsRepo.SearchList(ctx, &bizbo.SupplierGoodsSearchBo{
		Type:    valobj.SupplierGoodsTypeVirtual,
		WithSku: true,
	})
	for _, g := range goods {
		specItemIds := make([]int, 0)
		specJsonMap := make(map[int]*bizbo.Spec)
		specJsonSlice := make([]*bizbo.Spec, 0)
		for _, sku := range g.GoodsSku {
			fmt.Println(sku.GetSpecItemIds())
			specItemIds = append(specItemIds, sku.GetSpecItemIds()...)
		}
		specItemIds = helper.SliceIntUnique(specItemIds)
		sort.Ints(specItemIds)
		specItems, _ := s.supplierGoodsSpecItemRepo.FindByIds(ctx, specItemIds)
		for _, item := range specItems {
			_, ok := specJsonMap[item.SpecID]
			if !ok {
				specJsonMap[item.SpecID] = &bizbo.Spec{
					Id:       item.SpecID,
					Name:     item.SpecName,
					AddImage: false,
					Items:    make([]*bizbo.SpecItems, 0),
				}
			}
			specJsonItem := &bizbo.SpecItems{
				Id:       item.ID,
				SpecId:   item.SpecID,
				Name:     item.Name,
				SpecName: item.SpecName,
			}
			specJsonMap[item.SpecID].Items = append(specJsonMap[item.SpecID].Items, specJsonItem)
		}
		for _, spec := range specJsonMap {
			specJsonSlice = append(specJsonSlice, spec)
		}
		sort.Slice(specJsonSlice, func(i, j int) bool {
			return specJsonSlice[i].Id < specJsonSlice[j].Id
		})
		specJsonSlice[0].AddImage = true
		specJsonSlice[0].Items[0].Image = g.MainImage
		specJson, err := json.Marshal(specJsonSlice)
		if err != nil {
			return err
		}
		g.SpecJson = string(specJson)
		_, _ = s.supplierGoodsRepo.UpdateV2(ctx, g)
	}
	return nil
}
