package commonbiz

import (
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/appbiz/bo"
	appDo "cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
)

type ActivityCommonBiz struct {
	activityRepo       bizRepo.ActivityRepo
	flashSaleGoodsRepo bizRepo.FlashSaleGoodsRepo
	orderRepo          repository.OrderRepo
	flashSaleTimeRepo  bizRepo.FlashSaleTimeRepo
	refundDs           *ds.RefundDs
	goodsDs            *ds.GoodsDs
}

func NewActivityCommonBiz(
	activityRepo bizRepo.ActivityRepo,
	flashSaleGoodsRepo bizRepo.FlashSaleGoodsRepo,
	orderRepo repository.OrderRepo,
	flashSaleTimeRepo bizRepo.FlashSaleTimeRepo,
	refundDs *ds.RefundDs,
	goodsDs *ds.GoodsDs,
) *ActivityCommonBiz {
	obj := &ActivityCommonBiz{
		activityRepo:       activityRepo,
		flashSaleGoodsRepo: flashSaleGoodsRepo,
		orderRepo:          orderRepo,
		flashSaleTimeRepo:  flashSaleTimeRepo,
		refundDs:           refundDs,
		goodsDs:            goodsDs,
	}
	refundDs.ActivityCommonBiz = obj
	goodsDs.ActivityCommonBiz = obj
	return obj
}

func (a *ActivityCommonBiz) AppSkuHandler(ctx context.Context, activityId int, skus []*appDo.GoodsSkuDo) ([]*appDo.GoodsSkuDo, error) {
	activity := a.activityRepo.Get(ctx, activityId)
	if activity == nil {
		return nil, apierr.ErrorDbNotFound("活动不存在")
	}
	switch activity.ActivityType {
	case valobj.ActivityTypeNormal:
		return skus, nil
	case valobj.ActivityTypeFlashSale:
		return a.flashSaleSkuHandler(ctx, activityId, skus)
	default:
		return nil, apierr.ErrorNotAllow("活动类型错误")
	}
}

func (a *ActivityCommonBiz) flashSaleSkuHandler(ctx context.Context, activityId int, skus []*appDo.GoodsSkuDo) ([]*appDo.GoodsSkuDo, error) {
	skuNos := slice.Map(skus, func(_ int, item *appDo.GoodsSkuDo) string {
		return item.SkuNo
	})
	flashSaleGoods, _ := a.flashSaleGoodsRepo.GetBySkuNo(ctx, activityId, skuNos...)
	flashSaleGoodsMap := slice.KeyBy(flashSaleGoods, func(item *do.FlashSaleGoodsDo) string {
		return item.SkuNo
	})
	for _, sku := range skus {
		flashSaleGoodsSku, _ := flashSaleGoodsMap[sku.SkuNo]
		if flashSaleGoodsSku == nil {
			continue
		}
		if flashSaleGoodsSku.Stock <= sku.Stock {
			sku.Stock = flashSaleGoodsSku.Stock
		}
		sku.SalePrice = flashSaleGoodsSku.Price
		sku.NumLimit = flashSaleGoodsSku.LimitNum
	}
	return skus, nil
}

func (a *ActivityCommonBiz) AppStockReduce(ctx context.Context, activityId int, skus []*appDo.GoodsSkuDo) error {
	activity := a.activityRepo.Get(ctx, activityId)
	if activity == nil {
		return apierr.ErrorDbNotFound("活动不存在")
	}

	var err error
	switch activity.ActivityType {
	case valobj.ActivityTypeNormal:
		return nil
	case valobj.ActivityTypeFlashSale:
		for _, sku := range skus {
			err = a.flashSaleGoodsRepo.DecrStock(ctx, activityId, sku.SkuNo, sku.Quantity)
			if err != nil {
				return err
			}
		}
	default:
		return apierr.ErrorNotAllow("活动类型错误")
	}
	return err
}

func (a *ActivityCommonBiz) AppStockAdd(ctx context.Context, activityId int, skuNo string, quantity int) error {
	activity := a.activityRepo.Get(ctx, activityId)
	if activity == nil {
		return apierr.ErrorDbNotFound("活动不存在")
	}

	var err error
	switch activity.ActivityType {
	case valobj.ActivityTypeNormal:
		return nil
	case valobj.ActivityTypeFlashSale:
		err = a.flashSaleGoodsRepo.IncrStock(ctx, activityId, skuNo, quantity)
		if err != nil {
			return err
		}
	default:
		return apierr.ErrorNotAllow("活动类型错误")
	}
	return err
}

// OrderPreValidate 下单前校验
func (a *ActivityCommonBiz) OrderPreValidate(ctx context.Context, in *bo2.ActivityOrderValidateBo) error {
	activity := a.activityRepo.Get(ctx, in.ActivityId)
	if activity == nil {
		return apierr.ErrorDbNotFound("活动不存在")
	}

	var err error
	switch activity.ActivityType {
	case valobj.ActivityTypeNormal:
		return nil
	case valobj.ActivityTypeFlashSale:
		err = a.FlashSaleOrderPreValidate(ctx, in)
		if err != nil {
			return err
		}
	default:
		return apierr.ErrorNotAllow("活动类型错误")
	}
	return err
}

// FlashSaleOrderPreValidate 下单前校验
func (a *ActivityCommonBiz) FlashSaleOrderPreValidate(ctx context.Context, in *bo2.ActivityOrderValidateBo) error {
	//下单时检测活动是否结束
	if !in.IsPay() {
		flashSaleTime, err := a.flashSaleTimeRepo.FindStared(ctx, in.ActivityId)
		if err != nil {
			return err
		}
		if flashSaleTime == nil {
			return apierr.ErrorNotAllow("活动已结束")
		}
	}

	sku, err := a.flashSaleGoodsRepo.FindBySkuNo(ctx, in.ActivityId, in.SkuNo)
	if err != nil {
		return err
	}
	if sku.LimitNum <= 0 {
		return nil
	}
	buySum, _ := a.orderRepo.UserSkuQuantitySum(ctx, &bo.UserSkuQuantitySumBo{
		UserId:      in.UserId,
		GoodsSkuNo:  in.SkuNo,
		OrderNumber: in.OrderNumber,
		ActivityId:  in.ActivityId,
	})

	if (buySum + in.Num) > sku.LimitNum {
		return fmt.Errorf("下单的商品数量不能超过%d件,您已下单%d件,请修改商品数量或取消待支付的订单", sku.LimitNum, buySum)
	}
	return nil
}
