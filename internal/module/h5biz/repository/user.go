package repository

import (
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/h5biz/bo"
	"context"
)

type UserRepo interface {
	FindByPhone(ctx context.Context, phone string) (*do.UserDo, error)

	FindByPhoneIN(ctx context.Context, phoneS []string) ([]*do.UserDo, error)

	Create(ctx context.Context, in *bo.CreateUserBo) (*do.UserDo, error)

	Update(ctx context.Context, in *bo.UserUpdateBo) (int, error)

	Find(ctx context.Context, in *bo.UserQueryBo) (*do.UserDo, error)
	UpsertBulk(ctx context.Context, in []*bo.CreateUserBo) ([]*do.UserDo, error)
}
