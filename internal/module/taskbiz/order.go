package taskbiz

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/aclbo"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	adminRepo "cardMall/internal/module/adminbiz/repository"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/module/commonbiz"
	"cardMall/internal/pkg/isolationcustomer"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/mapstructure"
	"context"
	"github.com/go-kratos/kratos/v2/log"
)

type OrderBiz struct {
	orderRepo            repository.OrderRepo
	orderGoodsRepo       repository.OrderGoodsRepo
	payOrderRepo         repository.PayOrderRepo
	goodsRepo            repository.GoodsRepo
	goodsSkuRepo         repository.GoodsSkuRepo
	supplierGoodsSkuRepo bizRepo.SupplierGoodsSkuRepo
	userRepo             repository.UserRepo
	adminUserRepo        adminRepo.UserRepo
	userIntegralLogRepo  adminRepo.UserIntegralLogRepo
	meiTuanRepo          rpc.MeiTuanRepo
	qianZhuRepo          rpc.QianZhuRepo
	orderOperatorLogRepo bizRepo.OrderOperatorLogRepo
	alipayMiniOrderRepo  rpc.AlipayMiniOrderRepo
	danGaoShuShuRepo     rpc.DanGaoShuShuRepo
	orderCommonBiz       *commonbiz.OrderCommonBiz
	integralDs           *ds.IntegralDs
	couponDs             *ds.CouponDs
	goodsDs              *ds.GoodsDs
	resellerDs           *ds.ResellerDs
	giftCardDs           *ds.GiftCardDs
	trans                bizRepo.TransactionRepo
	log                  *log.Helper
}

func NewOrderBiz(orderRepo repository.OrderRepo, orderGoodsRepo repository.OrderGoodsRepo, payOrderRepo repository.PayOrderRepo, goodsRepo repository.GoodsRepo, goodsSkuRepo repository.GoodsSkuRepo, supplierGoodsSkuRepo bizRepo.SupplierGoodsSkuRepo, userRepo repository.UserRepo, adminUserRepo adminRepo.UserRepo, userIntegralLogRepo adminRepo.UserIntegralLogRepo, meiTuanRepo rpc.MeiTuanRepo, qianZhuRepo rpc.QianZhuRepo, orderOperatorLogRepo bizRepo.OrderOperatorLogRepo, alipayMiniOrderRepo rpc.AlipayMiniOrderRepo, danGaoShuShuRepo rpc.DanGaoShuShuRepo, orderCommonBiz *commonbiz.OrderCommonBiz, integralDs *ds.IntegralDs, couponDs *ds.CouponDs, goodsDs *ds.GoodsDs, resellerDs *ds.ResellerDs, giftCardDs *ds.GiftCardDs, trans bizRepo.TransactionRepo, log *log.Helper) *OrderBiz {
	return &OrderBiz{orderRepo: orderRepo, orderGoodsRepo: orderGoodsRepo, payOrderRepo: payOrderRepo, goodsRepo: goodsRepo, goodsSkuRepo: goodsSkuRepo, supplierGoodsSkuRepo: supplierGoodsSkuRepo, userRepo: userRepo, adminUserRepo: adminUserRepo, userIntegralLogRepo: userIntegralLogRepo, meiTuanRepo: meiTuanRepo, qianZhuRepo: qianZhuRepo, orderOperatorLogRepo: orderOperatorLogRepo, alipayMiniOrderRepo: alipayMiniOrderRepo, danGaoShuShuRepo: danGaoShuShuRepo, orderCommonBiz: orderCommonBiz, integralDs: integralDs, couponDs: couponDs, goodsDs: goodsDs, resellerDs: resellerDs, giftCardDs: giftCardDs, trans: trans, log: log}
}

// Cancel 批量取消超时未支付订单
func (o *OrderBiz) Cancel(ctx context.Context, timeOut int) (err error) {
	payOrders, _ := o.payOrderRepo.GetNeedCanceledOrder(isolationcustomer.WithDisableCustomerCtx(ctx), timeOut)
	for _, order := range payOrders {
		ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, order.CustomerId, order.ShopId)
		_, err = o.orderCommonBiz.Cancel(ctx, order.OrderNumber, order.UserId, true)
		if err != nil {
			o.log.Errorf("[%s]订单取消订单失败:%s", order.OrderNumber, err)
		}
	}
	return nil
}

// ThirdCancel 批量取消第三方超时未支付订单
func (o *OrderBiz) ThirdCancel(ctx context.Context) error {
	//orders, _ := o.payOrderRepo.GetMeiTuanNeedCanceledOrder(ctx)
	//err := error(nil)
	//for _, order := range orders {
	//	err = o.trans.Exec(ctx, func(ctx context.Context) error {
	//		var row int
	//		row, err = o.payOrderRepo.Canceled(ctx, order.OrderNumber)
	//		if err != nil {
	//			return err
	//		}
	//		if row == 0 {
	//			return nil
	//		}
	//		// 写入订单操作日志
	//		_, _ = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
	//			OrderId:          order.Id,
	//			OrderNumber:      order.OrderNumber,
	//			OrderStatus:      valobj.OrderStatusPayCanceled,
	//			Content:          "订单超时取消",
	//			OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
	//			OperatorUserId:   0,
	//			OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
	//		})
	//
	//		_, err = o.orderRepo.CanceledPayOrder(ctx, order.OrderNumber)
	//		return err
	//	})
	//	if err != nil {
	//		o.log.Errorf("[%s]订单取消订单失败:%s", order.OrderNumber, err)
	//	}
	//}
	//
	//orders, _ = o.payOrderRepo.GetQianZhuNeedCanceledOrder(ctx)
	//for _, order := range orders {
	//	err = o.trans.Exec(ctx, func(ctx context.Context) error {
	//		var row int
	//		row, err = o.payOrderRepo.Canceled(ctx, order.OrderNumber)
	//		if err != nil {
	//			return err
	//		}
	//		if row == 0 {
	//			return nil
	//		}
	//		// 写入订单操作日志
	//		_, _ = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
	//			OrderId:          order.Id,
	//			OrderNumber:      order.OrderNumber,
	//			OrderStatus:      valobj.OrderStatusPayCanceled,
	//			Content:          "订单超时取消",
	//			OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
	//			OperatorUserId:   0,
	//			OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
	//		})
	//
	//		_, err = o.orderRepo.CanceledPayOrder(ctx, order.OrderNumber)
	//		if err != nil {
	//			return err
	//		}
	//		if order.OrderType.IsQianZhuCinema() && order.IsCardGift() {
	//			err = o.giftCardDs.ReturnAll(ctx, order.OrderNumber)
	//		}
	//		return err
	//	})
	//	if err != nil {
	//		o.log.Errorf("[%s]订单取消订单失败:%s", order.OrderNumber, err)
	//	}
	//}
	return nil
}

// RechargeReceived 直充自动收货
func (o *OrderBiz) RechargeReceived(ctx context.Context, timeOut int) (err error) {
	in := &bo.NeedReceivedOrderBo{
		TimeOut:   timeOut,
		OrderType: valobj.OrderTypeRecharge,
	}
	orders, _ := o.orderRepo.GetNeedReceivedOrder(isolationcustomer.WithDisableCustomerCtx(ctx), in)
	for _, order := range orders {
		ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, order.CustomerId, order.ShopId)
		//err = o.trans.Exec(ctx, func(ctx context.Context) error {
		//	_, _ = o.orderRepo.UpdateStatus(ctx, &bo.OrderUpdateStatusBo{
		//		OrderId:     order.Id,
		//		OrderNumber: order.OrderNumber,
		//		Status:      valobj.OrderStatusFinish,
		//		UserId:      order.UserId,
		//	})
		//
		//	// 写入订单操作日志
		//	_, _ = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
		//		OrderId:          order.Id,
		//		OrderNumber:      order.OrderNumber,
		//		OrderStatus:      valobj.OrderStatusFinish,
		//		Content:          "系统自动收货",
		//		OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
		//		OperatorUserId:   0,
		//		OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
		//	})
		//
		//	orderGoods, _ := o.orderGoodsRepo.Get(ctx, &bo.OrderGoodsQueryBo{OrderNumber: order.OrderNumber})
		//	integral := 0
		//	for _, good := range orderGoods {
		//		goodsInfo, _ := o.goodsSkuRepo.FindById(ctx, good.GoodsSkuId)
		//		if goodsInfo != nil && goodsInfo.Integral > 0 {
		//			integral += goodsInfo.Integral
		//		}
		//	}
		//	if integral > 0 {
		//		_, err = o.adminUserRepo.IncrIntegral(ctx, order.UserId, integral)
		//		if err != nil {
		//			return err
		//		}
		//		_, err = o.userIntegralLogRepo.Add(ctx, &adminBo.UserIntegralAddBo{
		//			UserId:   order.UserId,
		//			Remark:   "确认收货",
		//			Type:     valobj.UserIntegralLogTypeIncr,
		//			Integral: integral,
		//			Status:   valobj.UserIntegralLogStatusFinish,
		//		})
		//	}
		//	return err
		//})
		//if err != nil {
		//	o.log.Errorf("[%s]自动收货失败:%s", order.OrderNumber, err)
		//}
		orderInfo := &do.OrderDo{}
		_ = mapstructure.Decode(order, &orderInfo)
		err = o.orderCommonBiz.Received(ctx, orderInfo, nil)
		if err != nil {
			o.log.Errorf("【%s】虚拟订单自动收货失败:%s", order.OrderNumber, err)
		}
	}
	return nil
}

// MeiTuanReceived 美团自动收货
func (o *OrderBiz) MeiTuanReceived(ctx context.Context, timeOut int) error {
	in := &bo.NeedReceivedOrderBo{
		TimeOut:   timeOut,
		OrderType: valobj.OrderTypeMeiTuan,
	}
	orders, _ := o.orderRepo.GetNeedReceivedOrder(isolationcustomer.WithDisableCustomerCtx(ctx), in)
	for _, order := range orders {
		ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, order.CustomerId, order.ShopId)
		//获取分销商
		reseller, err := o.resellerDs.GetPlatformReseller(ctx)
		if err != nil {
			o.log.Errorf("订单同步美团订单状态获取分销商信息失败:%s", err)
			continue
		}

		payOrder, _ := o.payOrderRepo.FindByOrderNumber(ctx, order.PayOrderNumber)
		mtOrderInfo, _ := o.meiTuanRepo.GetOrderDetailInfo(ctx, payOrder.ThirdTradeNo, reseller)
		if mtOrderInfo.IsFinish() {
			//_, _ = o.orderRepo.UpdateStatus(ctx, &bo.OrderUpdateStatusBo{
			//	OrderId:     order.Id,
			//	OrderNumber: order.OrderNumber,
			//	Status:      valobj.OrderStatusFinish,
			//	UserId:      order.UserId,
			//})
			//// 写入订单操作日志
			//_, _ = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			//	OrderId:          order.Id,
			//	OrderNumber:      order.OrderNumber,
			//	OrderStatus:      valobj.OrderStatusFinish,
			//	Content:          "系统自动收货",
			//	OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
			//	OperatorUserId:   0,
			//	OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
			//})
			orderInfo := &do.OrderDo{}
			_ = mapstructure.Decode(order, &orderInfo)
			err = o.orderCommonBiz.Received(ctx, orderInfo, nil)
			if err != nil {
				o.log.Errorf("【%s】美团订单自动收货失败:%s", order.OrderNumber, err)
			}
		}
	}
	return nil
}

// ThirdUnpaidSync 美团千猪待支付订单状态同步--用户手动取消时，不会通知，需要定时主动查询
func (o *OrderBiz) ThirdUnpaidSync(ctx context.Context) error {
	//两分钟未支付的美团千猪订单，每分钟同步一次订单状态
	err := error(nil)
	//获取分销商
	var reseller *acldo.ResellerDo
	reseller, err = o.resellerDs.GetPlatformReseller(ctx)
	if err != nil {
		return apierr.ErrorException("获取分销商信息失败", err)
	}
	orders, _ := o.payOrderRepo.GetUnpaidQueryOrder(isolationcustomer.WithDisableCustomerCtx(ctx), 2*60)
	for _, order := range orders {
		//不处理超时取消的订单，只处理手动取消的订单
		if order.IsExpire() {
			continue
		}
		ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, order.CustomerId, order.ShopId)

		orderStatus := valobj.PayOrderStatusUnpaid
		switch order.OrderType {
		case valobj.PayOrderTypeMeiTuan:
			mtOrderInfo, err := o.meiTuanRepo.GetOrderDetailInfo(ctx, order.ThirdTradeNo, reseller)
			if err != nil {
				o.log.Errorf("[%s]订单同步美团订单状态失败:%s", order.OrderNumber, err)
				continue
			}
			if mtOrderInfo.IsCancel() {
				orderStatus = valobj.PayOrderStatusCancel
			}
			break
		case valobj.PayOrderTypeQianZhuCinema:
			qzOrderInfo, err := o.qianZhuRepo.GetCinemaOrderInfo(ctx, order.ThirdTradeNo, reseller)
			if err != nil {
				o.log.Errorf("[%s]订单同步千猪电影票订单状态失败:%s", order.OrderNumber, err)
				continue
			}
			if qzOrderInfo.IsCancel() {
				orderStatus = valobj.PayOrderStatusCancel
			}
			break
		case valobj.PayOrderTypeQianZhuKFC:
			qzOrderInfo, err := o.qianZhuRepo.GetKFCOrderInfo(ctx, order.ThirdTradeNo, reseller)
			if err != nil {
				o.log.Errorf("[%s]订单同步千猪肯德基订单状态失败:%s", order.OrderNumber, err)
				continue
			}
			if qzOrderInfo.IsCancel() {
				orderStatus = valobj.PayOrderStatusCancel
			}
			break
		case valobj.PayOrderTypeDGSS:
			DGSSOrderInfo, err := o.danGaoShuShuRepo.Query(ctx, &aclbo.DanGaoShuShuQueryAclBo{
				OrderNo: order.ThirdTradeNo,
			}, reseller)
			if err != nil {
				o.log.Errorf("[%s]订单同步蛋糕叔叔订单状态失败:%s", order.OrderNumber, err)
				continue
			}
			if DGSSOrderInfo.IsCancel() {
				orderStatus = valobj.PayOrderStatusCancel
			}
		}
		if orderStatus == valobj.PayOrderStatusCancel {
			_, err = o.orderCommonBiz.Cancel(ctx, order.OrderNumber, order.UserId, false)
			if err != nil {
				o.log.Errorf("[%s]订单取消订单失败:%s", order.OrderNumber, err)
				continue
			}
		}

	}
	return nil
}

func (o *OrderBiz) EntityReceived(ctx context.Context, timeOut int) error {
	orders, _ := o.orderRepo.GetNeedReceivedEntity(isolationcustomer.WithDisableCustomerCtx(ctx), timeOut)
	for _, order := range orders {
		ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, order.CustomerId, order.ShopId)
		//_, _ = o.orderRepo.UpdateStatus(ctx, &bo.OrderUpdateStatusBo{
		//	OrderId:     order.Id,
		//	OrderNumber: order.OrderNumber,
		//	Status:      valobj.OrderStatusFinish,
		//	UserId:      order.UserId,
		//})
		//// 写入订单操作日志
		//_, _ = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
		//	OrderId:          order.Id,
		//	OrderNumber:      order.OrderNumber,
		//	OrderStatus:      valobj.OrderStatusFinish,
		//	Content:          "系统自动收货",
		//	OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
		//	OperatorUserId:   0,
		//	OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
		//})
		//// 支付宝小程序订单收货
		//payOrder, _ := o.payOrderRepo.FindByOrderNumber(ctx, order.PayOrderNumber)
		//if payOrder.IsAlipayMiniOrder() {
		//	//retryErr := retry.Retry(func() error {
		//	user, _ := o.userRepo.Query(ctx, &bo.UserQueryBo{Id: order.UserId})
		//	rsp, err := o.alipayMiniOrderRepo.Receive(ctx, &aclbo.AlipayMiniOrderReceiveBo{
		//		OutOrderId: payOrder.OrderNumber,
		//		OpenId:     user.AlipayOpenId,
		//	})
		//	//if err != nil {
		//	//	return err
		//	//}
		//	if !rsp.IsSuccess() {
		//		err = fmt.Errorf("[%s]支付宝小程序订单收货失败:%s", order.OrderNumber, rsp.GetSubMsg())
		//	}
		//	//return nil
		//	//}, retry.RetryTimes(3), retry.RetryDuration(time.Minute))
		//	if err != nil {
		//		o.log.Errorf("[%s]支付宝小程序订单收货失败:%s", payOrder.OrderNumber, err)
		//	}
		//}
		orderInfo := &do.OrderDo{}
		_ = mapstructure.Decode(order, &orderInfo)
		err := o.orderCommonBiz.Received(ctx, orderInfo, nil)
		if err != nil {
			o.log.Errorf("【%s】实物订单自动收货失败:%s", order.OrderNumber, err)
		}
	}
	return nil
}

// QianZhuStarBucksTakeOutOrder 千猪星巴克外卖订单不会通知结果，需要定时任务轮询
func (o *OrderBiz) QianZhuStarBucksTakeOutOrder(ctx context.Context) error {
	in := &bo.NeedReceivedOrderBo{
		TimeOut:   constants.QianZhuStarBucksTakeOutTimeOut,
		OrderType: valobj.OrderTypeQianZhuStarBucks,
	}
	orders, _ := o.orderRepo.GetNeedReceivedOrder(isolationcustomer.WithDisableCustomerCtx(ctx), in)

	reseller, err := o.resellerDs.GetPlatformReseller(ctx)
	if err != nil {
		return apierr.ErrorException("获取分销商信息失败", err)
	}
	for _, order := range orders {
		ctx = isolationcustomer.WithCustomerAndShopCtx(ctx, order.CustomerId, order.ShopId)
		payOrder, _ := o.payOrderRepo.FindByOrderNumber(ctx, order.PayOrderNumber)
		qianZhuOrderInfo, _ := o.qianZhuRepo.GetStarBucksOrderInfo(ctx, payOrder.ThirdTradeNo, reseller)
		if qianZhuOrderInfo.TakeOut && qianZhuOrderInfo.Status.IsFinished() {
			orderInfo := &do.OrderDo{}
			_ = mapstructure.Decode(order, &orderInfo)
			err = o.orderCommonBiz.Received(ctx, orderInfo, nil)
			if err != nil {
				o.log.Errorf("【%s】星巴克订单自动收货失败:%s", order.OrderNumber, err)
			}
		}

	}
	return nil
}
