package taskbiz

import (
	"cardMall/internal/data"
	"cardMall/internal/module/taskbiz/do"
	"cardMall/internal/module/taskbiz/repository"
	"context"
	"github.com/redis/go-redis/v9"
)

type TaskBiz struct {
	taskRepo repository.TaskRepo
	data     *data.Data
}

func NewTaskBiz(taskRepo repository.TaskRepo, data *data.Data) *TaskBiz {
	return &TaskBiz{taskRepo: taskRepo, data: data}
}

func (t *TaskBiz) FindByTaskName(ctx context.Context, taskName string) (*do.TaskDo, error) {
	return t.taskRepo.FindByTaskName(ctx, taskName)
}

func (t *TaskBiz) Lock(ctx context.Context, taskName string) error {
	err := t.data.Rdb.SetNX(ctx, taskName, 1, 0).Err()
	if err != nil && err != redis.Nil {
		return err
	}
	return nil
}

func (t *TaskBiz) UnLock(ctx context.Context, taskName string) error {
	err := t.data.Rdb.Del(ctx, taskName).Err()
	if err != nil && err != redis.Nil {
		return err
	}
	return nil
}

func (t *TaskBiz) IsLock(ctx context.Context, taskName string) bool {
	return t.data.Rdb.Exists(ctx, taskName).Val() > 0
}
