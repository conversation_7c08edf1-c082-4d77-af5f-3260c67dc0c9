package taskbiz

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"context"
)

type CustomerResellerBiz struct {
	customerResellerRepo repository.CustomerResellerRepo
}

func NewCustomerResellerBiz(customerResellerRepo repository.CustomerResellerRepo) *CustomerResellerBiz {
	return &CustomerResellerBiz{customerResellerRepo: customerResellerRepo}
}

func (b *CustomerResellerBiz) Get(ctx context.Context, id int, limit int) ([]*do.CustomerResellerDo, error) {
	return b.customerResellerRepo.GetByLastIdLimit(ctx, id, limit)
}
