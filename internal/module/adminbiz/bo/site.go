package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
)

type SiteListBo struct {
	bo.ReqPageBo
	Id     int
	Name   string
	Status valobj.SiteStatusObj
}

type SiteAddBo struct {
	Name      string
	Domain    string
	UniqueStr string
	Status    valobj.SiteStatusObj
}

func (s *SiteAddBo) Validate() error {
	if len([]rune(s.Name)) > 50 {
		return apierr.ErrorParam("站点名称不能超过50个字符")
	}
	if len(s.Domain) > 1024 {
		return apierr.ErrorParam("站点域名不能超过1024个字符")
	}
	if !s.Status.IsValid() {
		return apierr.ErrorParam("参数错误:status")
	}
	return nil
}

type SiteUpdateBo struct {
	Id     int
	Name   string
	Status valobj.SiteStatusObj
}
