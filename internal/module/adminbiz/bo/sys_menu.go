package bo

import (
	"cardMall/api/adminv1"
	"cardMall/api/apierr"
	"cardMall/internal/constants"
	"errors"
	"unicode/utf8"
)

type SysMenuListBo struct {
}

type SysMenuBo struct {
	Id             int    `json:"id"`              // 编号
	MenuName       string `json:"menu_name"`       // 菜单名称
	ParentID       int    `json:"parent_id"`       // 父菜单ID，一级菜单为0
	MenuPath       string `json:"menu_path"`       // 前端路由
	MenuIcon       string `json:"menu_icon"`       // 菜单图标
	MenuSort       int    `json:"menu_sort"`       // 菜单排序
	MenuType       int    `json:"menu_type"`       // 类型 1：顶部菜单,2：二级菜单,3：按钮
	APIPath        string `json:"api_path"`        // 后端api的path地址
	APIMethod      int    `json:"api_method"`      // 后端 api请求的 http method:1-GET,2-POST,3-PUT,4-DELETE
	FrontComponent string `json:"front_component"` // 前端组件
	FrontURL       string `json:"front_url"`       // 前端路由
	ProcessType    int    `json:"process_type"`    // 处理类型，按二进制位表示，第1位-需要登录，2-需要鉴权，3-需要日志，4-需要显示
}

func (m *SysMenuBo) Validate() error {
	if m == nil {
		return nil
	}

	if l := utf8.RuneCountInString(m.MenuName); l <= 0 || l > 50 {
		err := errors.New("菜单名称需小于50个字符")
		return err
	}

	if l := utf8.RuneCountInString(m.MenuPath); l > 200 {
		err := errors.New("菜单路径需小于200个字符")
		return err
	}
	if l := utf8.RuneCountInString(m.MenuIcon); l > 200 {
		err := errors.New("菜单图标需小于200个字符")
		return err
	}
	if l := m.MenuSort; l > 999 {
		err := errors.New("菜单排序需小于999")
		return err
	}
	if l := utf8.RuneCountInString(m.APIPath); l > 200 {
		err := errors.New("后端类库需小于200个字符")
		return err
	}
	if l := utf8.RuneCountInString(m.FrontComponent); l > 200 {
		err := errors.New("前端组件需小于200个字符")
		return err
	}
	if l := utf8.RuneCountInString(m.FrontURL); l > 200 {
		err := errors.New("前端路由需小于200个字符")
		return err
	}
	return nil
}
func (m *SysMenuBo) ProcessTypeValuesToMPart(processTypeValues []int32) (*adminv1.MenuProcessTypePart, error) {
	res := &adminv1.MenuProcessTypePart{}
	for _, v := range processTypeValues {
		switch v {
		case 1:
			res.IsLogin = true
		case 2:
			res.IsAuth = true
		case 3:
			res.IsLog = true
		case 4:
			res.IsShow = true
		default:
			return nil, apierr.ErrorParam("处理类型 %d 不存在", v)
		}
	}
	return res, nil
}

// ConvertProcessType 将ProcessTypePart转换为ProcessType
func (m *SysMenuBo) ConvertProcessType(req *adminv1.MenuProcessTypePart) int {
	m.ProcessType = 0
	if req.GetIsLogin() {
		m.AddLogin()
	}
	if req.GetIsAuth() {
		m.AddAuth()
	}
	if req.GetIsLog() {
		m.AddLog()
	}
	if req.GetIsShow() {
		m.AddShow()
	}

	return m.ProcessType
}

// AddLogin 添加登录
func (a *SysMenuBo) AddLogin() {
	a.ProcessType = a.ProcessType | constants.AdminMenuIsLogin
}

// AddAuth 添加鉴权
func (a *SysMenuBo) AddAuth() {
	a.ProcessType = a.ProcessType | constants.AdminMenuIsAuth
}

// AddLog 添加日志
func (a *SysMenuBo) AddLog() {
	a.ProcessType = a.ProcessType | constants.AdminMenuIsLog
}

// AddShow 添加显示
func (a *SysMenuBo) AddShow() {
	a.ProcessType = a.ProcessType | constants.AdminMenuIsShow
}
