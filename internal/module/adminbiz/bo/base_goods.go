package bo

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"time"
)

type BaseGoodsQueryBo struct {
	ProductName string
	ProductId   string
	Status      *int
	Type        []int
	bo.ReqPageBo
}

type BaseGoodsPullBo struct {
	IsProd bool
	Time   time.Duration
	ApiUrl string
}

type BaseGoodsUpdateBo struct {
	ProductId     string
	ProductName   string
	ChannelPrice  float64
	OriginalPrice float64
	Type          int
	AccountType   valobj.BaseGoodsAccountTypeObj
}

type BaseGoodsCardExpireBo struct {
	ProductId      string
	CardExpireTime int
}

type BaseGoodsUpdateStatusBo struct {
	ProductId string
	Status    *int
}
