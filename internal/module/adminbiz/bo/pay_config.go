package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"encoding/json"
	"fmt"
)

type PayConfigQueryBo struct {
	Status      valobj.PayConfigStatusObj
	ChannelType valobj.PayConfigChannelTypeObj
	IsAvailable bool
}

type PayConfigAddBo struct {
	Alipay       valobj.PayMerchantPayStatusObj
	WxPay        valobj.PayMerchantPayStatusObj
	AlipayApplet valobj.PayMerchantPayStatusObj
	WxApplet     valobj.PayMerchantPayStatusObj
	Channel      []*PayConfigChannelBo
}

type PayConfigChannelBo struct {
	PayMerchantId     int
	PayCenterMchId    int
	ChannelType       valobj.PayConfigChannelTypeObj
	AppId             string
	AppSecret         string
	PublicKey         string
	PrivateKey        string
	Status            valobj.PayConfigStatusObj
	MchId             string
	SerialNo          string
	AlipaySignType    valobj.PayConfigAlipaySignTypeObj
	AlipayCertificate *PayConfigChannelAlipayCertificateBo
}

type PayConfigChannelAlipayCertificateBo struct {
	AlipayCertPublicKey string `json:"aliPayPublicCertContent"`
	AlipayRootCert      string `json:"AliPayRootCertContent"`
	AppCertPublicKey    string `json:"appCertContent"`
}

type PayConfigUpdateBo struct {
	Id     int
	Status valobj.PayConfigStatusObj
}

func (p *PayConfigAddBo) Validate(isOfficial bool) error {
	if !p.Alipay.IsValid() || !p.WxPay.IsValid() {
		return apierr.ErrorParam("必要参数类型错误")
	}

	if !isOfficial {
		if p.WxApplet.IsEnable() {
			exists := false
			for _, c := range p.Channel {
				if c.ChannelType.IsWxApplet() {
					exists = true
				}
			}
			if !exists {
				return apierr.ErrorParam("请输入微信小程序支付配置")
			}
		}
		if p.AlipayApplet.IsEnable() {
			exists := false
			for _, c := range p.Channel {
				if c.ChannelType.IsAlipayApplet() {
					exists = true
				}
			}
			if !exists {
				return apierr.ErrorParam("请输入支付宝小程序支付配置")
			}
		}
	}

	//if p.Alipay == valueobj.PayMerchantPayStatusEnable {
	//	exist := false
	//	for _, c := range p.Channel {
	//		if c.ChannelType == valueobj.PayConfigChannelTypeAliPay {
	//			exist = true
	//		}
	//	}
	//	if !exist {
	//		return fmt.Errorf("支付宝支付渠道未配置")
	//	}
	//}
	//if p.WxPay == valueobj.PayMerchantPayStatusEnable {
	//	exist := false
	//	for _, c := range p.Channel {
	//		if c.ChannelType == valueobj.PayConfigChannelTypeWxPay {
	//			exist = true
	//		}
	//	}
	//	if !exist {
	//		return fmt.Errorf("微信支付渠道未配置")
	//	}
	//}
	return nil
}

func (p *PayConfigChannelBo) GetExtJson() string {
	if p.ChannelType == valobj.PayConfigChannelTypeWxPay || p.ChannelType == valobj.PayConfigChannelTypeWxApplet {
		return fmt.Sprintf(`{"serialNo":"%s","mchid":"%s"}`, p.SerialNo, p.MchId)
	}
	if p.ChannelType.IsAlipayApplet() {
		b, err := json.Marshal(p.AlipayCertificate)
		if err != nil {
			return ""
		}
		return string(b)
	}
	return ""
}

func (p *PayConfigChannelBo) Validate() error {
	if !p.ChannelType.IsValid() {
		return apierr.ErrorParam("支付渠道类型错误")
	}
	if p.ChannelType == valobj.PayConfigChannelTypeWxPay || p.ChannelType == valobj.PayConfigChannelTypeWxApplet {
		if p.SerialNo == "" {
			return apierr.ErrorParam("微信支付序列号不能为空")
		}
		if p.MchId == "" {
			return apierr.ErrorParam("微信支付商户号不能为空")
		}
		if p.AppSecret == "" {
			return apierr.ErrorParam("微信支付应用密钥不能为空")
		}
	}
	if p.ChannelType.IsAlipayApplet() {
		if p.AppSecret == "" {
			return apierr.ErrorParam("支付宝小程序应用密钥不能为空")
		}
		if p.AlipayCertificate == nil {
			return apierr.ErrorParam("支付宝小程序支付证书不能为空")
		}
		if p.AlipayCertificate.AlipayCertPublicKey == "" {
			return apierr.ErrorParam("支付宝小程序支付证书公钥不能为空")
		}
		if p.AlipayCertificate.AlipayRootCert == "" {
			return apierr.ErrorParam("支付宝小程序支付根证书不能为空")
		}
		if p.AlipayCertificate.AppCertPublicKey == "" {
			return apierr.ErrorParam("支付宝小程序支付应用公钥不能为空")
		}
		if _, err := json.Marshal(p.AlipayCertificate); err != nil {
			return apierr.ErrorParam("支付宝小程序支付证书格式有误:%s", err)
		}
	}
	if p.AppId == "" {
		return apierr.ErrorParam("应用id不能为空")
	}
	if p.PublicKey == "" {
		return apierr.ErrorParam("公钥不能为空")
	}
	if p.PrivateKey == "" {
		return apierr.ErrorParam("私钥不能为空")
	}
	if !p.Status.IsValid() {
		return apierr.ErrorParam("支付配置状态错误")
	}
	return nil
}
