package adminbiz

import (
	"cardMall/api/apierr"
	bizBo "cardMall/internal/biz/bo"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/calvalobj"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/adminbiz/repository"
	"context"
	"errors"
	"github.com/go-kratos/kratos/v2/log"
)

type PayConfigBiz struct {
	payConfigRepo   repository.PayConfigRepo
	payMerchantRepo repository.PayMerchantRepo
	PayCenterRepo   rpc.PayCenterRepo
	trans           bizRepo.TransactionRepo
	config          *conf.Bootstrap
	log             *log.Helper
}

func NewPayConfigBiz(
	payConfigRepo repository.PayConfigRepo,
	payMerchantRepo repository.PayMerchantRepo,
	payCenterRepo rpc.PayCenterRepo,
	trans bizRepo.TransactionRepo,
	conf *conf.Bootstrap,
	log *log.Helper,
) *PayConfigBiz {
	return &PayConfigBiz{
		payConfigRepo:   payConfigRepo,
		payMerchantRepo: payMerchantRepo,
		PayCenterRepo:   payCenterRepo,
		trans:           trans,
		config:          conf,
		log:             log,
	}
}

func (p *PayConfigBiz) Add(ctx context.Context, in *bo.PayConfigAddBo) (payMerchantId int, err error) {
	err = p.trans.Exec(ctx, func(ctx context.Context) error {
		//先查询现有支付配置，有，则先删除再添加
		payMerchant, _ := p.payMerchantRepo.Find(ctx, &bo.PayMerchantQueryBo{IsAvailable: true, PayType: valobj.PayMerchantPayTypeH5})
		if payMerchant != nil {
			_, err = p.payMerchantRepo.Del(ctx, payMerchant.Id)
			if err != nil {
				return err
			}
			//删除支付配置
			_, err = p.payConfigRepo.Del(ctx, payMerchant.Id)
			if err != nil {
				return err
			}
		}

		//筛选出h5支付的配置
		channels := make([]*bo.PayConfigChannelBo, 0)
		for _, channelBo := range in.Channel {
			if channelBo.ChannelType.IsH5() {
				channels = append(channels, channelBo)
			}
		}
		//添加新配置
		merchantBo := &bo.PayMerchantAddBo{
			PayMerchantId: 0,
			NotifyUrl:     p.config.GetPayCenter().GetNotifyUrl(),
			PublicKey:     p.config.GetPayCenter().GetMyPublicKey(),
			PrivateKey:    p.config.GetPayCenter().GetPrivateKey(),
			PayPublicKey:  p.config.GetPayCenter().GetPublicKey(),
			Alipay:        in.Alipay,
			WxPay:         in.WxPay,
			PayType:       valobj.PayMerchantPayTypeH5,
		}
		//表示使用系统默认支付配置
		if len(channels) == 0 {
			merchantBo.PayMerchantId = int(p.config.GetPayCenter().GetMerchantId())
		}
		payMerchantId, err = p.payMerchantRepo.Add(ctx, merchantBo)
		if err != nil {
			return err
		}
		//表示不使用系统默认支付配置
		if len(channels) > 0 {
			for _, addBo := range channels {
				addBo.PayMerchantId = payMerchantId
				_, err = p.payConfigRepo.Add(ctx, addBo)
				if err != nil {
					return err
				}
			}

			//请求支付中心添加配置
			payCenterParams := &bizBo.PayCenterConfigAddBo{
				Name:           "蓝熊卡券",
				ShortName:      "蓝熊卡券",
				Status:         calvalobj.PayCenterMerchantStatusEnable,
				Remark:         "蓝熊卡券",
				Contact:        "袁粒桃",
				Phone:          "17384082748",
				NotifyUrl:      p.config.GetPayCenter().GetNotifyUrl(),
				PublicKey:      p.config.GetPayCenter().GetMyPublicKey(),
				Logo:           "",
				PayChannelList: nil,
			}
			payCenterParams.PayChannelList = make([]*bizBo.PayCenterConfigChannel, 0, len(channels))
			for _, val := range channels {
				item := &bizBo.PayCenterConfigChannel{
					Type:           val.ChannelType.GetPayCenterChannelType(),
					AppId:          val.AppId,
					PublicKey:      val.PublicKey,
					PrivateKey:     val.PrivateKey,
					ExtJson:        val.GetExtJson(),
					Id:             0,
					AlipaySignType: val.AlipaySignType,
				}
				payCenterParams.PayChannelList = append(payCenterParams.PayChannelList, item)
			}
			res, err := p.PayCenterRepo.PayConfigAdd(ctx, payCenterParams)
			if err != nil {
				return err
			}
			if res.Id == 0 {
				return errors.New("请求支付中心失败")
			}

			_, err = p.payMerchantRepo.Update(ctx, &bo.PayMerchantUpdateBo{
				Id:            payMerchantId,
				PayMerchantId: res.Id,
			})
			if err != nil {
				return err
			}

		}
		_, err = p.AddApplet(ctx, in)
		return err
	})
	if err == nil {
		_ = p.payConfigRepo.DelCache(ctx)
		_ = p.payMerchantRepo.DelCache(ctx, valobj.PayMerchantPayTypeH5)
	}
	return
}

func (p *PayConfigBiz) AddApplet(ctx context.Context, in *bo.PayConfigAddBo) (payMerchantId int, err error) {
	//先查询现有支付配置，有，则先删除再添加
	payMerchant, _ := p.payMerchantRepo.Find(ctx, &bo.PayMerchantQueryBo{IsAvailable: true, PayType: valobj.PayMerchantPayTypeApplet})
	if payMerchant != nil {
		_, err = p.payMerchantRepo.Del(ctx, payMerchant.Id)
		if err != nil {
			return
		}
		//删除支付配置
		_, err = p.payConfigRepo.Del(ctx, payMerchant.Id)
		if err != nil {
			return
		}
	}

	//筛选出小程序支付的配置
	channels := make([]*bo.PayConfigChannelBo, 0)
	for _, channelBo := range in.Channel {
		if channelBo.ChannelType.IsApplet() {
			channels = append(channels, channelBo)
		}
	}

	//添加新配置
	merchantBo := &bo.PayMerchantAddBo{
		PayMerchantId: 0,
		NotifyUrl:     p.config.GetPayCenter().GetNotifyUrl(),
		PublicKey:     p.config.GetPayCenter().GetMyPublicKey(),
		PrivateKey:    p.config.GetPayCenter().GetPrivateKey(),
		PayPublicKey:  p.config.GetPayCenter().GetPublicKey(),
		Alipay:        in.AlipayApplet,
		WxPay:         in.WxApplet,
		PayType:       valobj.PayMerchantPayTypeApplet,
	}
	//表示使用系统默认支付配置
	if len(channels) == 0 {
		err = apierr.ErrorParam("小程序支付配置不能为空")
		return
	}
	payMerchantId, err = p.payMerchantRepo.Add(ctx, merchantBo)
	if err != nil {
		return
	}
	for _, addBo := range channels {
		addBo.PayMerchantId = payMerchantId
		_, err = p.payConfigRepo.Add(ctx, addBo)
		if err != nil {
			return
		}
	}

	//请求支付中心添加配置
	payCenterParams := &bizBo.PayCenterConfigAddBo{
		Name:           "蓝熊卡券-小程序支付",
		ShortName:      "蓝熊卡券",
		Status:         calvalobj.PayCenterMerchantStatusEnable,
		Remark:         "蓝熊卡券小程序支付",
		Contact:        "袁粒桃",
		Phone:          "17384082748",
		NotifyUrl:      p.config.GetPayCenter().GetNotifyUrl(),
		PublicKey:      p.config.GetPayCenter().GetMyPublicKey(),
		Logo:           "",
		PayChannelList: nil,
	}
	payCenterParams.PayChannelList = make([]*bizBo.PayCenterConfigChannel, 0, len(channels))
	for _, val := range channels {
		item := &bizBo.PayCenterConfigChannel{
			Type:           val.ChannelType.GetPayCenterChannelType(),
			AppId:          val.AppId,
			PublicKey:      val.PublicKey,
			PrivateKey:     val.PrivateKey,
			ExtJson:        val.GetExtJson(),
			Id:             0,
			AlipaySignType: val.AlipaySignType,
		}
		payCenterParams.PayChannelList = append(payCenterParams.PayChannelList, item)
	}
	res, err := p.PayCenterRepo.PayConfigAdd(ctx, payCenterParams)
	if err != nil {
		return
	}
	if res.Id == 0 {
		err = apierr.ErrorParam("请求支付中心失败")
	}

	_, err = p.payMerchantRepo.Update(ctx, &bo.PayMerchantUpdateBo{
		Id:            payMerchantId,
		PayMerchantId: res.Id,
	})
	if err == nil {
		_ = p.payMerchantRepo.DelCache(ctx, valobj.PayMerchantPayTypeApplet)
	}
	return
}

func (p *PayConfigBiz) All(ctx context.Context) ([]*do.PayMerchantDo, []*do.PayConfigDo, error) {
	payMerchant, _ := p.payMerchantRepo.Get(ctx, &bo.PayMerchantQueryBo{IsAvailable: true})
	if payMerchant == nil {
		return nil, nil, nil
	}
	payConfig, _ := p.payConfigRepo.All(ctx, &bo.PayConfigQueryBo{IsAvailable: true})
	return payMerchant, payConfig, nil
}

func (p *PayConfigBiz) Update(ctx context.Context, in *bo.PayConfigUpdateBo) (int, error) {
	row, err := p.payConfigRepo.Update(ctx, in)
	if err != nil {
		return 0, err
	}
	_ = p.payConfigRepo.DelCache(ctx)
	_ = p.payMerchantRepo.DelCache(ctx, valobj.PayMerchantPayTypeH5)
	_ = p.payMerchantRepo.DelCache(ctx, valobj.PayMerchantPayTypeApplet)
	return row, err
}
