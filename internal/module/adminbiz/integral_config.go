package adminbiz

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"context"
	"errors"
)

type IntegralConfigBiz struct {
	repo         repository.IntegralConfigRepo
	GoodsSkuRepo repository.GoodsSkuRepo
	sysConfigDs  *ds.SysConfigDs
}

func NewIntegralConfigBiz(repo repository.IntegralConfigRepo, goodsSkuRepo repository.GoodsSkuRepo, sysConfigDs *ds.SysConfigDs) *IntegralConfigBiz {
	return &IntegralConfigBiz{repo: repo, GoodsSkuRepo: goodsSkuRepo, sysConfigDs: sysConfigDs}
}

func (i *IntegralConfigBiz) Query(ctx context.Context, shopId int) (*do.IntegralConfigDo, error) {
	return i.repo.Find(ctx, shopId)
}

func (i *IntegralConfigBiz) Add(ctx context.Context, in *bo.IntegralConfigAddBo) (int, error) {
	if err := in.Validate(); err != nil {
		return 0, err
	}
	if !i.sysConfigDs.IsSceneIntegralEnable(ctx) {
		return 0, errors.New("积分功能未开启")
	}
	check, _ := i.repo.Find(ctx, in.ShopId)
	if check != nil {
		return 0, errors.New("配置已存在")
	}
	row, err := i.repo.Add(ctx, in)
	if err != nil {
		return 0, err
	}
	return row, nil
}

func (i *IntegralConfigBiz) Update(ctx context.Context, shopId int, in *bo.IntegralConfigUpdateBo) (int, error) {
	if err := in.Validate(); err != nil {
		return 0, err
	}
	if !i.sysConfigDs.IsSceneIntegralEnable(ctx) {
		return 0, errors.New("积分功能未开启")
	}
	check, _ := i.repo.Find(ctx, shopId)
	if check == nil || check.Id != in.Id {
		return 0, errors.New("配置不存在")
	}
	row, err := i.repo.Update(ctx, shopId, in)
	if err != nil {
		return 0, err
	}

	settlementType := make([]valobj.GoodsSkuSettlementTypeObj, 0)
	if check.IntegralCash.IsEnable() && in.IntegralCash.IsDisable() {
		settlementType = append(settlementType, valobj.GoodsSkuSettlementTypeCashIntegral)
	}
	if check.IntegralExchange.IsEnable() && in.IntegralExchange.IsDisable() {
		settlementType = append(settlementType, valobj.GoodsSkuSettlementTypeIntegral)
	}
	if len(settlementType) > 0 {
		status := valobj.GoodsSkuStatusDisable.ToInt()
		_, err = i.GoodsSkuRepo.PoolGoodsUpdate(ctx, &do.PoolGoodsUpdateDo{
			GoodsSkuIds:    nil,
			Status:         &status,
			SettlementType: settlementType,
		})
	}
	return row, err
}
