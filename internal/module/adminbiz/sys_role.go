package adminbiz

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/conf"
	"cardMall/internal/data/ent"
	"context"
	"errors"
)

type SysRoleBiz struct {
	repo        repository.SysRoleRepo
	sysRoleMenu repository.SysRoleMenuRepo
	conf        *conf.Bootstrap
	trans       repository.TransactionRepo
	adminRepo   repository.AdminRepo
}

func NewSysRoleBiz(repo repository.SysRoleRepo,
	trans repository.TransactionRepo,
	sysRoleMenu repository.SysRoleMenuRepo,
	adminRepo repository.AdminRepo,
	conf *conf.Bootstrap) *SysRoleBiz {
	return &SysRoleBiz{
		repo:        repo,
		trans:       trans,
		sysRoleMenu: sysRoleMenu,
		adminRepo:   adminRepo,
		conf:        conf,
	}
}
func (g *SysRoleBiz) GetSysRoleList(ctx context.Context, in *bo.SysRoleListBo) (int, []*do.SysRoleDo, error) {

	count, sysRoleList, err := g.repo.SysRoleList(ctx, in)
	if err != nil {
		return 0, nil, err
	}
	return count, sysRoleList, nil
}

func (g *SysRoleBiz) FindByRoleIds(ctx context.Context, ids []int) ([]*do.SysRoleDo, error) {

	sysRoleList, err := g.repo.FindByRoleIds(ctx, ids)
	if err != nil {
		return nil, err
	}
	return sysRoleList, nil
}
func (g *SysRoleBiz) SysRoleDetail(ctx context.Context, sysRoleId int) (*bo.SysRoleDetailBo, error) {
	sysRoleDo, err := g.repo.FindById(ctx, sysRoleId)
	if err != nil {
		return nil, err
	}
	if sysRoleDo == nil {
		return nil, errors.New("角色不存在")
	}
	sysRoleMenuDos, err := g.sysRoleMenu.SysRoleMenuListByRoleId(ctx, sysRoleId)
	if err != nil {
		return nil, err
	}
	menuIds := make([]int, 0, len(sysRoleMenuDos))
	for _, sysRoleMenuDo := range sysRoleMenuDos {
		menuIds = append(menuIds, sysRoleMenuDo.MenuId)
	}
	return &bo.SysRoleDetailBo{
		Id:         sysRoleDo.Id,
		RoleName:   sysRoleDo.RoleName,
		UpdateTime: sysRoleDo.UpdateTime,
		MenuIds:    menuIds,
	}, nil
}

func (g *SysRoleBiz) AddSysRole(ctx context.Context, in *bo.SysRoleBo) (int, error) {
	var (
		err    error
		roleId int
	)
	roleDo, err := g.repo.FindByRoleName(ctx, in.RoleName, 0)
	if err != nil {
		return 0, err
	}
	if roleDo != nil && roleDo.Id != 0 {
		return 0, errors.New("角色名已存在")
	}
	err = g.trans.Exec(ctx, func(ctx context.Context) error {

		roleId, err = g.repo.AddSysRole(ctx, &ent.SysRole{
			RoleName: in.RoleName,
		})
		if err != nil {
			return err
		}
		sysRoleMenus := make([]*ent.SysRoleMenu, 0, len(in.MenuIds))
		for _, menuId := range in.MenuIds {
			menu := &ent.SysRoleMenu{
				RoleID: roleId,
				MenuID: menuId,
			}
			sysRoleMenus = append(sysRoleMenus, menu)
		}
		_, err = g.sysRoleMenu.AddSysRoleMenu(ctx, sysRoleMenus)
		if err != nil {
			return err
		}
		return nil

	})
	if err != nil {
		return 0, err
	}
	return roleId, nil

}
func (g *SysRoleBiz) UpdateSysRole(ctx context.Context, in *bo.SysRoleBo) (int, error) {
	var (
		err error
	)
	roleDo, err := g.repo.FindByRoleName(ctx, in.RoleName, in.Id)
	if err != nil {
		return 0, err
	}
	if roleDo != nil && roleDo.Id != 0 {
		return 0, errors.New("角色名已存在")
	}
	err = g.trans.Exec(ctx, func(ctx context.Context) error {

		_, err = g.repo.UpdateSysRole(ctx, &ent.SysRole{
			RoleName: in.RoleName,
			ID:       in.Id,
		})
		if err != nil {
			return err
		}
		// 先删除后全量增加
		_, err = g.sysRoleMenu.DeleteSysRoleByRoleIds(ctx, []int{in.Id})
		if err != nil {
			return err
		}
		sysRoleMenus := make([]*ent.SysRoleMenu, 0, len(in.MenuIds))
		for _, menuId := range in.MenuIds {
			menu := &ent.SysRoleMenu{
				RoleID: in.Id,
				MenuID: menuId,
			}
			sysRoleMenus = append(sysRoleMenus, menu)
		}
		_, err = g.sysRoleMenu.AddSysRoleMenu(ctx, sysRoleMenus)
		if err != nil {
			return err
		}
		return nil

	})
	if err != nil {
		return 0, err
	}
	return 1, nil
}
func (g *SysRoleBiz) DeleteSysRole(ctx context.Context, sysRoleId int) (int, error) {
	var (
		err error
	)

	existRole, err := g.adminRepo.ExistRole(ctx, sysRoleId)
	if err != nil {
		return 0, err
	}
	if existRole {
		return 0, errors.New("角色已有账户绑定")
	}
	err = g.trans.Exec(ctx, func(ctx context.Context) error {

		_, err = g.repo.DeleteSysRole(ctx, []int{sysRoleId})
		if err != nil {
			return err
		}
		// 删除角色权限
		_, err = g.sysRoleMenu.DeleteSysRoleByRoleIds(ctx, []int{sysRoleId})
		if err != nil {
			return err
		}
		return nil

	})
	if err != nil {
		return 0, err
	}
	return 1, nil
}
