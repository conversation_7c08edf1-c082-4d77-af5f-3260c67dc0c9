package adminbiz

import (
	"context"

	"cardMall/api/apierr"
	repository2 "cardMall/internal/biz/repository"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/adminbiz/repository"
)

type BannerBiz struct {
	repo                   repository.BannerRepo
	goodsBrandRepo         repository2.GoodsBrandRepo
	goodsCategoryRepo      repository2.GoodsCategoryRepo
	activityRepo           repository2.ActivityRepo
	goodsCategoryExtraRepo repository2.GoodsCategoryExtraRepo
	goodsBrandExtraRepo    repository2.GoodsBrandExtraRepo
}

func NewBannerBiz(
	repo repository.BannerRepo,
	goodsBrandRepo repository2.GoodsBrandRepo,
	goodsCategoryRepo repository2.GoodsCategoryRepo,
	activityRepo repository2.ActivityRepo,
	goodsCategoryExtraRepo repository2.GoodsCategoryExtraRepo,
	goodsBrandExtraRepo repository2.GoodsBrandExtraRepo,

) *BannerBiz {
	return &BannerBiz{
		repo:                   repo,
		goodsBrandRepo:         goodsBrandRepo,
		goodsCategoryRepo:      goodsCategoryRepo,
		activityRepo:           activityRepo,
		goodsCategoryExtraRepo: goodsCategoryExtraRepo,
		goodsBrandExtraRepo:    goodsBrandExtraRepo,
	}
}

func (b *BannerBiz) Add(ctx context.Context, in *bo.BannerAddBo) (int, error) {
	if len(in.RelationValueIds) > 0 {
		if in.RelationType.IsBrand() {
			check, err := b.goodsBrandRepo.GetByID(ctx, in.RelationValueIds)
			if err != nil {
				return 0, err
			}
			if len(check) != len(in.RelationValueIds) {
				return 0, apierr.ErrorParam("关联品牌不存在或已被禁用")
			}
			codes := make([]string, 0, len(check))
			codeMap := make(map[string]string)
			for _, v := range check {
				if !v.IsEnable() {
					return 0, apierr.ErrorParam("关联品牌【%s】不存在或已被禁用", v.Name)
				}
				codes = append(codes, v.Code)
				codeMap[v.Code] = v.Name
			}
			brandExtra, err := b.goodsBrandExtraRepo.GetByCode(ctx, codes...)
			if err != nil {
				return 0, err
			}
			for _, extraDo := range brandExtra {
				if !extraDo.IsEnable() {
					return 0, apierr.ErrorParam("关联品牌扩展【%s】已被禁用", codeMap[extraDo.Code])
				}
			}
		} else if in.RelationType.IsCategory() {
			check, err := b.goodsCategoryRepo.FindByIds(ctx, in.RelationValueIds)
			if err != nil {
				return 0, err
			}
			if len(check) != len(in.RelationValueIds) {
				return 0, apierr.ErrorParam("关联商品分类不存在或已被禁用")
			}
			codes := make([]string, 0, len(check))
			codeMap := make(map[string]string)
			for _, v := range check {
				if v == nil {
					return 0, apierr.ErrorParam("关联商品分类不存在")
				}
				if !v.IsEnable() {
					return 0, apierr.ErrorParam("关联商品分类【%s】不存在或已被禁用", v.Name)
				}
				codes = append(codes, v.Code)
				codeMap[v.Code] = v.Name
			}
			categoryExtra, err := b.goodsCategoryExtraRepo.GetByCode(ctx, codes...)
			if err != nil {
				return 0, err
			}
			for _, extraDo := range categoryExtra {
				if !extraDo.IsEnable() {
					return 0, apierr.ErrorParam("关联商品分类扩展【%s】已被禁用", codeMap[extraDo.Code])
				}
			}
		} else if in.RelationType.IsActivity() {
			check := b.activityRepo.Find(ctx, in.RelationValueIds...)

			if len(check) != len(in.RelationValueIds) {
				return 0, apierr.ErrorParam("关联活动不存在或已结束")
			}
			for _, v := range check {
				if v.IsFinish() {
					return 0, apierr.ErrorParam("关联活动【%s】已结算", v.Name)
				}
			}
		}
	}
	return b.repo.Add(ctx, in)
}

func (b *BannerBiz) List(ctx context.Context, in *bo.BannerListBo) (int, []*do.BannerInfoDo, error) {
	return b.repo.List(ctx, in)
}

func (b *BannerBiz) Update(ctx context.Context, in *bo.BannerUpdateBo) (int, error) {
	if len(in.RelationValueIds) > 0 {
		if in.RelationType.IsBrand() {
			check, err := b.goodsBrandRepo.GetByID(ctx, in.RelationValueIds)
			if err != nil {
				return 0, err
			}
			if len(check) != len(in.RelationValueIds) {
				return 0, apierr.ErrorParam("关联品牌不存在或已被禁用")
			}
			codes := make([]string, 0, len(check))
			codeMap := make(map[string]string)
			for _, v := range check {
				if !v.IsEnable() {
					return 0, apierr.ErrorParam("关联品牌【%s】不存在或已被禁用", v.Name)
				}
				codes = append(codes, v.Code)
				codeMap[v.Code] = v.Name
			}
			brandExtra, err := b.goodsBrandExtraRepo.GetByCode(ctx, codes...)
			if err != nil {
				return 0, err
			}
			for _, extraDo := range brandExtra {
				if !extraDo.IsEnable() {
					return 0, apierr.ErrorParam("关联品牌扩展【%s】已被禁用", codeMap[extraDo.Code])
				}
			}
		} else if in.RelationType.IsCategory() {
			check, err := b.goodsCategoryRepo.Find(ctx, in.RelationValueIds...)
			if err != nil {
				return 0, err
			}
			if len(check) != len(in.RelationValueIds) {
				return 0, apierr.ErrorParam("关联商品分类不存在或已被禁用")
			}
			codes := make([]string, 0, len(check))
			codeMap := make(map[string]string)
			for _, v := range check {
				if !v.IsEnable() {
					return 0, apierr.ErrorParam("关联商品分类【%s】不存在或已被禁用", v.Name)
				}
				codes = append(codes, v.Code)
				codeMap[v.Code] = v.Name
			}
			categoryExtra, err := b.goodsCategoryExtraRepo.GetByCode(ctx, codes...)
			if err != nil {
				return 0, err
			}
			for _, extraDo := range categoryExtra {
				if !extraDo.IsEnable() {
					return 0, apierr.ErrorParam("关联商品分类扩展【%s】已被禁用", codeMap[extraDo.Code])
				}
			}
		} else if in.RelationType.IsActivity() {
			check := b.activityRepo.Find(ctx, in.RelationValueIds...)
			if len(check) != len(in.RelationValueIds) {
				return 0, apierr.ErrorParam("关联活动不存在或已结束")
			}
			for _, v := range check {
				if v.IsFinish() {
					return 0, apierr.ErrorParam("关联活动【%s】已结算", v.Name)
				}
			}
		}
	}
	return b.repo.Update(ctx, in)
}

func (b *BannerBiz) Del(ctx context.Context, id int) (int, error) {
	return b.repo.Del(ctx, id)
}

func (b *BannerBiz) UpdateStatus(ctx context.Context, in *bo.BannerUpdateStatusBo) (int, error) {
	return b.repo.UpdateStatus(ctx, in)
}

func (b *BannerBiz) Detail(ctx context.Context, id int) (*do.BannerInfoDo, error) {
	return b.repo.FindById(ctx, id)
}
