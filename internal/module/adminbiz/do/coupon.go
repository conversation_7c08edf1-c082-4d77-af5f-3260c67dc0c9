package do

import (
	"cardMall/internal/biz/valobj"
)

type CouponInfo struct {
	Id                  int
	Quantity            int
	Title               string
	Remark              string
	CollectionStartTime int
	CollectionEndTime   int
	EffectTimeType      int
	EffectStartTime     int
	EffectEndTime       int
	EffectStartDay      int
	EffectEndDay        int
	LimitAmount         float64
	DiscountAmount      float64
	CollectionQuantity  int
	CollectionType      int
	ProductLimit        int
	CollectionNum       int
	UsedNum             int
	AbolishNum          int
	SurplusNum          int
	CreateTime          int
	UpdateTime          int
	IsAbolish           int
	Sku                 []*CouponGoodsDo
	Status              valobj.CouponStatusObj
}
