package do

import (
	"cardMall/internal/biz/valobj"
)

type PayOrderDo struct {
	Id                   int
	OrderNumber          string
	OutTradeNo           string
	UserId               int
	Account              string
	GoodsId              int
	TotalPayAmount       float64
	TotalPayIntegral     int
	Num                  int
	Status               valobj.PayOrderStatusObj
	PayTime              int
	RefundTime           int
	RefundAmount         float64
	RefundIntegral       int
	CouponCodeId         int
	CouponDiscountAmount float64
	CreateTime           int
	UpdateTime           int
	PayType              valobj.PayOrderPayTypeObj
	CardCouponNumber     string
	SettlementType       int32
	OrderType            valobj.PayOrderTypeObj
	CardGiftAmount       float64
}

func (p *PayOrderDo) IsThird() bool {
	return p.OrderType.IsThird()
}

func (p *PayOrderDo) CardGift() bool {
	return p.CardGiftAmount > 0
}
