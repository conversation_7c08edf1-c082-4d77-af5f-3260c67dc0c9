package do

import (
	"cardMall/internal/biz/valobj"
	"encoding/json"
)

type PayConfigDo struct {
	Id             int
	PayMerchantId  int
	PayCenterMchId int
	ChannelType    valobj.PayConfigChannelTypeObj
	AppId          string
	AppSecret      string
	PublicKey      string
	PrivateKey     string
	ExtJson        string
	Status         valobj.PayConfigStatusObj
	CreateTime     int
	UpdateTime     int
	DeleteTime     int
	*PayConfigExtJson
}

type PayConfigExtJson struct {
	SerialNo            string `json:"serialNo"`
	MchId               string `json:"mchid"`
	AlipayCertPublicKey string `json:"aliPayPublicCertContent"`
	AlipayRootCert      string `json:"AliPayRootCertContent"`
	AppCertPublicKey    string `json:"appCertContent"`
}

func (p *PayConfigDo) ExtJsonToObj() error {
	if p.ExtJson == "" {
		return nil
	}
	return json.Unmarshal([]byte(p.ExtJson), &p.PayConfigExtJson)
}

func (p *PayConfigDo) GetMchId() string {
	if !p.ChannelType.IsWxH5() && !p.ChannelType.IsWxApplet() {
		return ""
	}
	if p.PayConfigExtJson == nil || p.MchId == "" {
		d := &PayConfigExtJson{}
		err := json.Unmarshal([]byte(p.ExtJson), &d)
		if err != nil {
			panic(err)
		}
		p.PayConfigExtJson = d
	}
	return p.MchId
}

func (p *PayConfigDo) GetSerialNo() string {
	if !p.ChannelType.IsWxH5() && !p.ChannelType.IsWxApplet() {
		return ""
	}
	if p.PayConfigExtJson == nil || p.SerialNo == "" {
		d := &PayConfigExtJson{}
		_ = json.Unmarshal([]byte(p.ExtJson), &d)
		p.PayConfigExtJson = d
	}
	return p.SerialNo
}

func (p *PayConfigDo) GetAlipayCertPublicKey() string {
	if !p.ChannelType.IsAlipayApplet() {
		return ""
	}
	if p.PayConfigExtJson == nil {
		d := &PayConfigExtJson{}
		err := json.Unmarshal([]byte(p.ExtJson), &d)
		if err != nil {
			panic(err)
		}
		p.PayConfigExtJson = d
	}
	return p.AlipayCertPublicKey
}

func (p *PayConfigDo) GetAlipayRootCert() string {
	if !p.ChannelType.IsAlipayApplet() {
		return ""
	}
	if p.PayConfigExtJson == nil {
		d := &PayConfigExtJson{}
		err := json.Unmarshal([]byte(p.ExtJson), &d)
		if err != nil {
			panic(err)
		}
		p.PayConfigExtJson = d
	}
	return p.AlipayRootCert
}

func (p *PayConfigDo) GetAppCertPublicKey() string {
	if !p.ChannelType.IsAlipayApplet() {
		return ""
	}
	if p.PayConfigExtJson == nil {
		d := &PayConfigExtJson{}
		err := json.Unmarshal([]byte(p.ExtJson), &d)
		if err != nil {
			panic(err)
		}
		p.PayConfigExtJson = d
	}
	return p.AppCertPublicKey
}

func (p *PayConfigDo) DataHidden() {
	p.PublicKey = "********"
	p.PrivateKey = "********"
	p.AppId = p.AppId[0:3] + "********" + p.AppId[len(p.AppId)-3:]
	if p.ChannelType.IsWxH5() || p.ChannelType.IsWxApplet() {
		p.MchId = p.GetMchId()[0:3] + "********" + p.GetMchId()[len(p.MchId)-3:]
		p.SerialNo = p.GetSerialNo()[0:3] + "********" + p.GetSerialNo()[len(p.SerialNo)-3:]
		p.AppSecret = p.AppSecret[0:3] + "********" + p.AppSecret[len(p.AppSecret)-3:]
	}
	if p.ChannelType.IsAlipayApplet() {
		p.AppSecret = p.AppSecret[0:3] + "********" + p.AppSecret[len(p.AppSecret)-3:]
		p.AlipayCertPublicKey = p.GetAlipayCertPublicKey()[0:3] + "********" + p.GetAlipayCertPublicKey()[len(p.GetAlipayCertPublicKey())-3:]
		p.AlipayRootCert = p.GetAlipayRootCert()[0:3] + "********" + p.GetAlipayRootCert()[len(p.GetAlipayRootCert())-3:]
		p.AppCertPublicKey = p.GetAppCertPublicKey()[0:3] + "********" + p.GetAppCertPublicKey()[len(p.GetAppCertPublicKey())-3:]
	}
}
