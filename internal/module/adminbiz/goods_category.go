package adminbiz

import (
	bizbo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"errors"
	"github.com/duke-git/lancet/v2/slice"
)

type GoodsCategoryBiz struct {
	categoryRepo           bizRepo.GoodsCategoryRepo
	CategoryBrandRepo      bizRepo.GoodsCategoryBrandRepo
	trans                  bizRepo.TransactionRepo
	data                   *data.Data
	categoryDs             *ds.GoodsCategoryDs
	goodsRepo              bizRepo.GoodsRepo
	supplierGoodsRepo      bizRepo.SupplierGoodsRepo
	productAuthorizeRepo   bizRepo.ProductAuthorizeRepo
	orderGoodsRepo         bizRepo.OrderGoodsRepo
	goodsCategoryExtraRepo bizRepo.GoodsCategoryExtraRepo
}

func NewGoodsCategoryBiz(
	categoryRepo bizRepo.GoodsCategoryRepo,
	CategoryBrandRepo bizRepo.GoodsCategoryBrandRepo,
	trans bizRepo.TransactionRepo,
	data *data.Data,
	categoryDs *ds.GoodsCategoryDs,
	goodsRepo bizRepo.GoodsRepo,
	supplierGoodsRepo bizRepo.SupplierGoodsRepo,
	productAuthorizeRepo bizRepo.ProductAuthorizeRepo,
	orderGoodsRepo bizRepo.OrderGoodsRepo,
	goodsCategoryExtraRepo bizRepo.GoodsCategoryExtraRepo,
) *GoodsCategoryBiz {
	return &GoodsCategoryBiz{
		categoryRepo:           categoryRepo,
		CategoryBrandRepo:      CategoryBrandRepo,
		data:                   data,
		trans:                  trans,
		categoryDs:             categoryDs,
		goodsRepo:              goodsRepo,
		supplierGoodsRepo:      supplierGoodsRepo,
		productAuthorizeRepo:   productAuthorizeRepo,
		orderGoodsRepo:         orderGoodsRepo,
		goodsCategoryExtraRepo: goodsCategoryExtraRepo,
	}
}

func (g *GoodsCategoryBiz) SyncCategory(ctx context.Context, sourceType int) ([]*bizdo.GoodsCategoryDo, error) {
	ctx2 := isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	var (
		goodsCategoryDos []*bizdo.GoodsCategoryDo
		err              error
	)
	if sourceType == 1 {
		goodsCategoryDos, err = g.categoryRepo.All(isolationcustomer.WithOfficialSupplierPlatformCtx(ctx2))
	} else {
		goodsCategoryDos, err = g.categoryRepo.All(ctx2)
	}
	if err != nil {
		return nil, err
	}
	goodsCategoryDos = slice.Filter(goodsCategoryDos, func(index int, item *bizdo.GoodsCategoryDo) bool {
		if item.Status == valobj.GoodsCategoryStatusEnable {
			return true
		}
		return false
	})
	if len(goodsCategoryDos) == 0 {
		return nil, nil
	}

	return g.categoryDs.GetTreeData(ctx, goodsCategoryDos), nil
}

func (g *GoodsCategoryBiz) Sync(ctx context.Context, ids []int, sourceType int) (err error) {
	ctx2 := isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(0))
	var (
		goodsCategoryDos []*bizdo.GoodsCategoryDo
	)
	if sourceType == 1 {
		if len(ids) > 0 {
			goodsCategoryDos, err = g.categoryRepo.FindByIds(isolationcustomer.WithOfficialSupplierPlatformCtx(ctx2), ids)
		} else {
			goodsCategoryDos, err = g.categoryRepo.All(isolationcustomer.WithOfficialSupplierPlatformCtx(ctx2))
		}
	} else {
		if len(ids) > 0 {
			goodsCategoryDos, err = g.categoryRepo.FindByIds(ctx2, ids)
		} else {
			goodsCategoryDos, err = g.categoryRepo.All(ctx2)
		}
	}
	if err != nil {
		return err
	}
	categoryDos := g.categoryDs.GetTreeData(ctx, goodsCategoryDos)
	for _, goodsCategoryDoOne := range categoryDos {
		var (
			categoryAddDoOne          *bizdo.GoodsCategoryAddDo
			goodsCategoryOneNameExist bool
		)

		goodsCategoryOneNameExist, err = g.categoryRepo.NameExist(ctx, goodsCategoryDoOne.Name, 0, 0)

		if err != nil {
			return err
		}
		if !goodsCategoryOneNameExist {
			categoryAddDoOne, err = g.categoryRepo.Add(ctx, &bizbo.GoodsCategoryAddBo{
				Name:      goodsCategoryDoOne.Name,
				Status:    int(goodsCategoryDoOne.Status),
				Sort:      goodsCategoryDoOne.Sort,
				Pid:       goodsCategoryDoOne.Pid,
				Level:     goodsCategoryDoOne.Level,
				Type:      goodsCategoryDoOne.Type,
				Image:     goodsCategoryDoOne.Image,
				Recommend: goodsCategoryDoOne.Recommend,
			})
		} else {
			goodsCategoryDo, _ := g.categoryRepo.FindByNameAndLevel(ctx, goodsCategoryDoOne.Name, 1)
			categoryAddDoOne = &bizdo.GoodsCategoryAddDo{Id: goodsCategoryDo.Id}
		}

		if err != nil {
			return err
		}
		for _, goodsCategoryDoTwo := range goodsCategoryDoOne.Children {
			var (
				categoryAddDoTwo          *bizdo.GoodsCategoryAddDo
				goodsCategoryTwoNameExist bool
			)
			goodsCategoryTwoNameExist, err = g.categoryRepo.NameExist(ctx, goodsCategoryDoTwo.Name, categoryAddDoOne.Id, 0)
			if err != nil {
				return err
			}
			if !goodsCategoryTwoNameExist || !goodsCategoryOneNameExist {
				categoryAddDoTwo, err = g.categoryRepo.Add(ctx, &bizbo.GoodsCategoryAddBo{
					Name:      goodsCategoryDoTwo.Name,
					Status:    int(goodsCategoryDoTwo.Status),
					Sort:      goodsCategoryDoTwo.Sort,
					Pid:       categoryAddDoOne.Id,
					Level:     goodsCategoryDoTwo.Level,
					Type:      goodsCategoryDoTwo.Type,
					Image:     goodsCategoryDoTwo.Image,
					Recommend: goodsCategoryDoTwo.Recommend,
				})
			} else {
				goodsCategoryDo, _ := g.categoryRepo.FindByNameAndLevel(ctx, goodsCategoryDoTwo.Name, 2)
				categoryAddDoTwo = &bizdo.GoodsCategoryAddDo{Id: goodsCategoryDo.Id}
			}
			for _, goodsCategoryDoThree := range goodsCategoryDoTwo.Children {
				var (
					goodsCategoryThreeNameExist bool
				)
				goodsCategoryThreeNameExist, err = g.categoryRepo.NameExist(ctx, goodsCategoryDoThree.Name, categoryAddDoTwo.Id, 0)
				if err != nil {
					return err
				}
				if !goodsCategoryThreeNameExist || !goodsCategoryTwoNameExist || !goodsCategoryOneNameExist {
					var res *bizdo.GoodsCategoryAddDo
					res, err = g.categoryRepo.Add(ctx, &bizbo.GoodsCategoryAddBo{
						Name:      goodsCategoryDoThree.Name,
						Status:    int(goodsCategoryDoThree.Status),
						Sort:      goodsCategoryDoThree.Sort,
						Pid:       categoryAddDoTwo.Id,
						Level:     goodsCategoryDoThree.Level,
						Type:      goodsCategoryDoThree.Type,
						Image:     goodsCategoryDoThree.Image,
						Recommend: goodsCategoryDoThree.Recommend,
					})
					if err != nil {
						return err
					}
					if goodsCategoryDoThree.Level == constants.GoodsCategoryLevelMax {
						categoryId := goodsCategoryDoThree.Id
						if sourceType == 1 {
							var customerCategory *bizdo.GoodsCategoryDo
							customerCategory, err = g.categoryRepo.FindByNameAndLevel(ctx2, goodsCategoryDoThree.Name, constants.GoodsCategoryLevelMax)
							if err != nil {
								return err
							}
							categoryId = customerCategory.Id
						}
						_, err = g.productAuthorizeRepo.UpdateOnCategorySync(ctx, categoryId, res.Id)
						if err != nil {
							return err
						}
					}
				}
			}

		}
	}
	_ = g.categoryDs.DelCache(ctx)
	return nil
}

func (g *GoodsCategoryBiz) Add(ctx context.Context, in *bizbo.GoodsCategoryAddBo) (res *bizdo.GoodsCategoryAddDo, err error) {
	in.Level = 1
	if in.Pid > 0 {
		pData, _ := g.categoryRepo.FindById(ctx, in.Pid)
		if pData == nil {
			err = errors.New("父级分类不存在")
			return
		}

		if pData.Status == valobj.GoodsCategoryStatusDisable && in.Status == int(valobj.GoodsCategoryStatusEnable) {
			err = errors.New("分类状态错误")
			return
		}

		in.Level = pData.Level + 1
	}
	if in.Level > constants.GoodsCategoryLevelMax {
		err = errors.New("分类层级不能超过三级")
		return
	}

	if in.Level == 1 {
		mobileExist, err := g.categoryRepo.NameExist(ctx, in.Name, in.Pid, 0)
		if err != nil {
			return nil, err
		}
		if mobileExist {
			return nil, errors.New("类目名称已存在")
		}
	}
	if in.Level == 2 {
		goodsCategoryDos, _ := g.categoryRepo.FindByPid(ctx, in.Pid)
		for _, goodsCategoryDo := range goodsCategoryDos {
			if goodsCategoryDo.Name == in.Name {
				return nil, errors.New("类目名称已存在")
			}
		}
	}
	if in.Level == 3 {
		goodsCategoryDos, _ := g.categoryRepo.FindByPid(ctx, in.Pid)
		for _, goodsCategoryDo := range goodsCategoryDos {
			if goodsCategoryDo.Name == in.Name {
				return nil, errors.New("类目名称已存在")
			}
		}
	}
	if in.Level == constants.GoodsCategoryLevelMax && in.Image == "" {
		err = errors.New("分类图片不能为空")
		return
	}
	if in.Level != constants.GoodsCategoryLevelMin && in.IndexShow.IsYes() {
		err = errors.New("非一级分类不能设置为首页展示")
		return
	}
	res, err = g.categoryRepo.Add(ctx, in)
	if err != nil {
		return
	}

	//删除缓存
	_ = g.categoryDs.DelCache(ctx)
	return
}

func (g *GoodsCategoryBiz) Update(ctx context.Context, in *bizbo.GoodsCategoryUpdateBo) (row int, err error) {
	d, _ := g.categoryRepo.FindById(ctx, in.Id)
	if d == nil {
		err = errors.New("数据不存在")
		return
	}
	extraInfo, err := g.goodsCategoryExtraRepo.FindByCode(ctx, d.Code)
	if extraInfo == nil {
		extraInfo = &bizdo.GoodsCategoryExtraDo{}
		extraInfo.Init(d.Code)
	}

	if in.IndexShow.IsYes() {
		if !d.IsMinLevel() {
			err = errors.New("非一级分类不能设置为首页展示")
			return
		}
	}
	extraInfo.IndexShow = in.IndexShow
	if in.Status != nil {
		extraInfo.Status = valobj.GoodsCategoryStatusObj(*in.Status)
	}
	if in.Recommend.IsValid() {
		extraInfo.Recommend = in.Recommend
	}
	if in.Sort != nil {
		extraInfo.Sort = *in.Sort
	}
	extraInfo, err = g.upsertExtra(ctx, extraInfo)
	if err != nil {
		return
	}

	if in.Status != nil && d.Status.GetInt() != *in.Status {
		status := valobj.GoodsCategoryStatusObj(*in.Status)
		if !d.IsMinLevel() && status.IsEnable() {
			categories := g.categoryDs.GetLine(ctx, d.Id)
			codes := make([]string, 0)
			for _, category := range categories {
				if category.Id != d.Id && category.Status.IsDisable() {
					codes = append(codes, category.Code)
				}
			}
			if len(codes) > 0 {
				err = g.upsertExtraStatusByCode(ctx, codes, status)
				if err != nil {
					return
				}
			}
		}
	}

	//修改子分类的状态
	if in.Status != nil && d.Status.GetInt() != *in.Status {
		if !d.IsMaxLevel() {
			ids := g.categoryDs.FindNodeId(ctx, d.Id)
			children, _ := g.categoryRepo.FindByIds(ctx, ids)
			codes := slice.Map(children, func(index int, item *bizdo.GoodsCategoryDo) string {
				return item.Code
			})
			if len(codes) > 0 {
				err = g.upsertExtraStatusByCode(ctx, codes, valobj.GoodsCategoryStatusObj(*in.Status))
				if err != nil {
					return
				}
			}
		}
	}

	//删除缓存
	_ = g.categoryDs.DelCache(ctx)
	return
}

func (g *GoodsCategoryBiz) upsertExtra(ctx context.Context, in *bizdo.GoodsCategoryExtraDo) (row *bizdo.GoodsCategoryExtraDo, err error) {
	now := helper.GetNow()
	if in.ID == 0 {
		in.CreateTime = now
		in.UpdateTime = now
		return g.goodsCategoryExtraRepo.Create(ctx, in)
	} else {
		in.UpdateTime = now
		_, err = g.goodsCategoryExtraRepo.Update(ctx, in)
		return in, err
	}
}

func (g *GoodsCategoryBiz) upsertExtraStatusByCode(ctx context.Context, codes []string, status valobj.GoodsCategoryStatusObj) error {
	exitsMap := make(map[string]struct{})
	now := helper.GetNow()
	extras, _ := g.goodsCategoryExtraRepo.GetByCode(ctx, codes...)
	for _, extra := range extras {
		exitsMap[extra.Code] = struct{}{}
		if extra.Status == status {
			continue
		}
		extra.Status = status
		extra.UpdateTime = now
		_, err := g.goodsCategoryExtraRepo.Update(ctx, extra)
		if err != nil {
			return err
		}
	}

	bulks := make([]*bizdo.GoodsCategoryExtraDo, 0)
	for _, code := range codes {
		if _, ok := exitsMap[code]; !ok {
			extra := &bizdo.GoodsCategoryExtraDo{}
			extra.Init(code)
			extra.CreateTime = now
			extra.UpdateTime = now
			bulks = append(bulks, extra)
		}
	}
	_, err := g.goodsCategoryExtraRepo.CreateBulk(ctx, bulks)
	return err
}

func (g *GoodsCategoryBiz) Query(ctx context.Context, in *bizbo.GoodsCategoryQueryBo) ([]*bizdo.GoodsCategoryDo, error) {
	return g.categoryDs.SearchByNoCacheTree(ctx, in), nil
}

func (g *GoodsCategoryBiz) Del(ctx context.Context, id int) (row int, err error) {
	d, _ := g.categoryRepo.FindById(ctx, id)
	if d == nil {
		return 0, errors.New("分类不存在")
	}
	if d.Level < constants.GoodsCategoryLevelMax {
		cd, _ := g.categoryRepo.FindByPid(ctx, d.Id)
		if len(cd) > 0 {
			return 0, errors.New("该分类下有子分类，不能删除")
		}
	}
	var (
		goodsListDos     *bizdo.GoodsListDo
		supplierGoodsDos *bizdo.SupplierGoodsDo
	)
	goodsListDos, err = g.goodsRepo.FindByCategoryID(ctx, id)
	if err != nil {
		return 0, err
	}
	if goodsListDos != nil {
		return 0, errors.New("无法删除，当前分类已关联生成商品，请删除商品后重试")
	}
	supplierGoodsDos, err = g.supplierGoodsRepo.FindByCategoryID(ctx, id)
	if err != nil {
		return 0, err
	}
	if supplierGoodsDos != nil {
		return 0, errors.New("无法删除，当前分类已关联供应商商品，请删除商品后重试")
	}
	productAuthorizeDos, err := g.productAuthorizeRepo.FindByCategoryID(ctx, id)
	if err != nil {
		return 0, err
	}
	if len(productAuthorizeDos) > 0 {
		return 0, errors.New("无法删除，当前分类已关联授权商品，请删除商品后重试")
	}
	if d.Level == constants.GoodsCategoryLevelMax {
		orderGoods, _ := g.orderGoodsRepo.FindByCategoryId(ctx, id)
		if orderGoods != nil {
			return 0, errors.New("无法删除，当前分类已产生订单")
		}
	}
	row, err = g.CategoryBrandRepo.DelByCategoryId(ctx, id)
	if err != nil {
		return 0, err
	}
	//删除缓存
	_ = g.categoryDs.DelCache(ctx)

	return g.categoryRepo.Del(ctx, id)
}
