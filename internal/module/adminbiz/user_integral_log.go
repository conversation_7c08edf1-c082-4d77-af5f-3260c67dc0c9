package adminbiz

import (
	do2 "cardMall/internal/biz/do"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/adminbiz/repository"
	h5Bo "cardMall/internal/module/h5biz/bo"
	h5Repo "cardMall/internal/module/h5biz/repository"
	"context"
	"errors"
	"fmt"
)

type UserIntegralLogBiz struct {
	integralLogRepo repository.UserIntegralLogRepo
	userRepo        repository.UserRepo
	h5UserRepo      h5Repo.UserRepo
	data            *data.Data
	trans           bizRepo.TransactionRepo
}

func NewUserIntegralLogBiz(integralLogRepo repository.UserIntegralLogRepo, userRepo repository.UserRepo, h5UserRepo h5Repo.UserRepo, data *data.Data, trans bizRepo.TransactionRepo) *UserIntegralLogBiz {
	return &UserIntegralLogBiz{integralLogRepo: integralLogRepo, userRepo: userRepo, h5UserRepo: h5UserRepo, data: data, trans: trans}
}

func (u *UserIntegralLogBiz) List(ctx context.Context, in *bo.UserIntegralListBo) (int, []*do.UserIntegralLogDo, error) {
	count, res, err := u.integralLogRepo.List(ctx, in)
	if err != nil {
		return 0, nil, err
	}

	userIds := make([]int, 0, len(res))
	for _, val := range res {
		userIds = append(userIds, val.UserId)
	}

	users, err := u.userRepo.GetUserById(ctx, userIds)
	if err != nil {
		return 0, nil, err
	}

	userMap := make(map[int]*do.UserListItem, len(users))
	for _, val := range users {
		userMap[val.ID] = val
	}

	for i, val := range res {
		if user, ok := userMap[val.UserId]; ok {
			res[i].NickName = user.NickName
			res[i].PhoneNumber = user.PhoneNumber
		}
	}

	return count, res, nil
}

func (u *UserIntegralLogBiz) Add(ctx context.Context, in *bo.UserIntegralAddBo) (int, error) {
	err := error(nil)
	row := &do.UserIntegralLogDo{}
	userInfo, _ := u.userRepo.GetOneUserById(ctx, in.UserId)
	if userInfo == nil {
		return 0, errors.New("用户不存在")
	}
	if in.Type == valobj.UserIntegralLogTypeReduce && userInfo.Integral < in.Integral {
		return 0, errors.New("用户积分不足")
	}
	err = u.trans.Exec(ctx, func(ctx context.Context) error {
		if in.Type == valobj.UserIntegralLogTypeIncr {
			in.IncrSource = valobj.UserIntegralLogIncrSourceSystem
		}
		row, err = u.integralLogRepo.Add(ctx, in)
		if err != nil {
			return err
		}
		if in.Type == valobj.UserIntegralLogTypeIncr {
			_, err = u.userRepo.IncrIntegral(ctx, in.UserId, in.Integral)
		} else if in.Type == valobj.UserIntegralLogTypeReduce {
			_, err = u.userRepo.ReduceIntegral(ctx, in.UserId, in.Integral)
		}
		return err
	})
	return row.Id, nil
}

func (u *UserIntegralLogBiz) Del(ctx context.Context, id int) (int, error) {
	return u.integralLogRepo.Del(ctx, []int{id})
}

func (u *UserIntegralLogBiz) ImportAdd(ctx context.Context, in *bo.UserIntegralImportBatchAddBo, conf *do2.IntegralConfigDo) error {
	err := error(nil)
	err = u.trans.Exec(ctx, func(ctx context.Context) error {
		//查询用户信息,没有则创建
		user, _ := u.h5UserRepo.FindByPhone(ctx, in.PhoneNumber)
		if user == nil {
			user, err = u.h5UserRepo.Create(ctx, &h5Bo.CreateUserBo{
				PhoneNumber: in.PhoneNumber,
				NickName:    "用户_" + in.PhoneNumber,
			})
			if err != nil {
				return errors.New("用户创建失败:" + err.Error())
			}
		}
		_, err = u.integralLogRepo.Add(ctx, &bo.UserIntegralAddBo{
			UserId:     user.Id,
			Remark:     in.Remark,
			Type:       valobj.UserIntegralLogTypeIncr,
			Integral:   in.Integral,
			Status:     valobj.UserIntegralLogStatusFinish,
			IncrSource: valobj.UserIntegralLogIncrSourceSystem,
			ExpireTime: conf.GetExpireTime(),
		})
		if err != nil {
			return err
		}
		_, err = u.userRepo.IncrIntegral(ctx, user.Id, in.Integral)
		return err
	})
	if err != nil {
		err = fmt.Errorf("用户手机号【%s】积分发放失败:%s", in.PhoneNumber, err.Error())
	}
	return err
}
