package adminbiz

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	bizRepository "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/pkg/jwt"
	"context"
	"encoding/json"
	"errors"
	"github.com/duke-git/lancet/v2/slice"
	"strconv"
)

type SysMenuBiz struct {
	repo              repository.SysMenuRepo
	transactionRepo   bizRepository.TransactionRepo
	sysRoleMenuRepo   repository.SysRoleMenuRepo
	sysRoleRepo       repository.SysRoleRepo
	sysOperateLogRepo repository.SysOperateLogRepo
	adminRepo         bizRepository.AdminRepo
	conf              *conf.Bootstrap
	data              *data.Data
	jwt               *jwt.JWT
}

func NewSysMenuBiz(repo repository.SysMenuRepo,
	transactionRepo bizRepository.TransactionRepo,
	sysRoleMenuRepo repository.SysRoleMenuRepo,
	sysRoleRepo repository.SysRoleRepo,
	sysOperateLogRepo repository.SysOperateLogRepo,
	adminRepo bizRepository.AdminRepo,
	data *data.Data,
	jwt *jwt.JWT,
	conf *conf.Bootstrap) *SysMenuBiz {
	return &SysMenuBiz{
		repo:              repo,
		conf:              conf,
		transactionRepo:   transactionRepo,
		sysRoleMenuRepo:   sysRoleMenuRepo,
		sysRoleRepo:       sysRoleRepo,
		sysOperateLogRepo: sysOperateLogRepo,
		adminRepo:         adminRepo,
		data:              data,
		jwt:               jwt,
	}
}
func (g *SysMenuBiz) GetSysMenuList(ctx context.Context) ([]*do.SysMenuDo, error) {

	sysMenuList, err := g.repo.SysMenuList(ctx, valobj.SysMenuTypeShop)
	if err != nil {
		return nil, err
	}
	return sysMenuList, nil
}
func (g *SysMenuBiz) SysMenuDetail(ctx context.Context, sysMenuId int) (*do.SysMenuDo, error) {
	sysMenuDo, err := g.repo.SysMenuById(ctx, sysMenuId)
	if err != nil {
		return nil, err
	}
	return sysMenuDo, nil
}
func (g *SysMenuBiz) AddSysMenu(ctx context.Context, in *bo.SysMenuBo) (int, error) {
	//if in.APIPath != "" {
	//	menuDo, err := g.repo.FindByApiPath(ctx, in.APIPath, 0)
	//	if err != nil {
	//		return 0, err
	//	}
	//	if menuDo != nil && menuDo.ShopId != 0 {
	//		return 0, errors.New("权限/菜单名已存在")
	//	}
	//}

	roleId, err := g.repo.AddSysMenu(ctx, &ent.SysMenu{
		MenuName:       in.MenuName,
		ParentID:       in.ParentID,
		MenuPath:       in.MenuPath,
		MenuIcon:       in.MenuIcon,
		MenuSort:       in.MenuSort,
		MenuType:       in.MenuType,
		APIPath:        in.APIPath,
		APIMethod:      in.APIMethod,
		FrontComponent: in.FrontComponent,
		FrontURL:       in.FrontURL,
		ProcessType:    in.ProcessType,
		SysMenuType:    valobj.SysMenuTypeShop,
	})
	if err != nil {
		return 0, err
	}
	return roleId, nil
}
func (g *SysMenuBiz) UpdateSysMenu(ctx context.Context, in *bo.SysMenuBo) (int, error) {

	//if in.APIPath != "" {
	//	menuDo, err := g.repo.FindByApiPath(ctx, in.APIPath, in.ShopId)
	//	if err != nil {
	//		return 0, err
	//	}
	//	if menuDo != nil && menuDo.ShopId != 0 {
	//		return 0, errors.New("权限/菜单名已存在")
	//	}
	//}
	dbMenuDo, err := g.repo.SysMenuById(ctx, in.Id)
	if err != nil {
		return 0, err
	}
	if dbMenuDo == nil || dbMenuDo.Id == 0 {
		return 0, errors.New("菜单不存在")
	}

	_, err = g.repo.UpdateSysMenu(ctx, &ent.SysMenu{
		ID:             in.Id,
		MenuName:       in.MenuName,
		ParentID:       in.ParentID,
		MenuPath:       in.MenuPath,
		MenuIcon:       in.MenuIcon,
		MenuSort:       in.MenuSort,
		MenuType:       in.MenuType,
		APIPath:        in.APIPath,
		APIMethod:      in.APIMethod,
		FrontComponent: in.FrontComponent,
		FrontURL:       in.FrontURL,
		ProcessType:    in.ProcessType})
	if err != nil {
		return 0, err
	}
	// api接口 删除掉缓存
	if dbMenuDo.ApiPath != "" {
		err = g.DeleteCacheByApiPaths(ctx, []string{dbMenuDo.ApiPath})
		if err != nil {
			return 0, err
		}
	}
	return 1, nil
}
func (g *SysMenuBiz) DeleteSysMenu(ctx context.Context, sysMenuId []int) (int, error) {
	var (
		err        error
		apiPaths   []string
		sysMenuDos []*do.SysMenuDo
	)
	err = g.transactionRepo.Exec(ctx, func(ctx context.Context) error {
		// 删除缓存
		sysMenuDos, err = g.repo.SysMenuListByIds(ctx, sysMenuId)
		if err != nil {
			return err
		}
		for _, menuDo := range sysMenuDos {
			if menuDo.ApiPath != "" {
				apiPaths = append(apiPaths, menuDo.ApiPath)
			}
		}
		if len(apiPaths) > 0 {
			err = g.DeleteCacheByApiPaths(ctx, apiPaths)
			if err != nil {
				return err
			}
		}
		// 删除菜单权限
		_, err = g.repo.DeleteSysMenu(ctx, sysMenuId)
		if err != nil {
			return err
		}
		// 删除角色权限
		_, err = g.sysRoleMenuRepo.DeleteSysRoleByMenuIds(ctx, sysMenuId)
		if err != nil {
			return err
		}
		return nil

	})
	if err != nil {
		return 0, err
	}

	return 1, nil
}
func (g *SysMenuBiz) GetSysMenuListByRoleId(ctx context.Context, roleId int) ([]*do.SysMenuDo, error) {
	sysMenuList, err := g.sysRoleMenuRepo.SysRoleMenuListByRoleId(ctx, roleId)
	if err != nil {
		return nil, err
	}
	menuIds := slice.Map(sysMenuList, func(_ int, item *do.SysRoleMenuDo) int {
		return item.MenuId
	})
	return g.repo.SysMenuListByIds(ctx, menuIds)
}
func (g *SysMenuBiz) DeleteCacheByApiPaths(ctx context.Context, apiPaths []string) error {
	for _, apiPath := range apiPaths {
		menuKey := constants.MenuCachePrefixTpl.GetKey(ctx, apiPath)
		g.data.Rdb.Del(ctx, menuKey).Val()
	}
	return nil
}
func (g *SysMenuBiz) GetCacheByApiPath(ctx context.Context, apiPath string) (*do.SysMenuDo, error) {
	var (
		sysMenuDo = &do.SysMenuDo{}
		menuKey   = constants.MenuCachePrefixTpl.GetKey(isolationcustomer.WithCustomerAndDisableShopCtx(ctx, 0), apiPath)
		err       error
	)
	menuExists := g.data.Rdb.Exists(ctx, menuKey).Val()
	if menuExists > 0 {
		val, _ := g.data.Rdb.Get(ctx, menuKey).Result()
		err = json.Unmarshal([]byte(val), sysMenuDo)
		return sysMenuDo, nil
	}
	sysMenuDo, err = g.repo.SysMenuByApiPath(ctx, apiPath)
	if err != nil {
		return nil, errors.New("查询数据失败")
	}
	if sysMenuDo == nil || sysMenuDo.Id == 0 {
		return nil, errors.New("权限不存在或者未配置")
	}
	bytes, _ := json.Marshal(sysMenuDo)
	_, err = g.data.Rdb.Set(ctx, menuKey, string(bytes), constants.MenuCachePrefixTpl.GetTTL()).Result()
	if err != nil {
		return nil, errors.New("设置缓存数据失败")
	}
	return sysMenuDo, nil
}

func (g *SysMenuBiz) GetUserRole(ctx context.Context, adminId int) (roleId int, err error) {
	var (
		userRoleCacheKey = constants.UserRoleCachePrefixTpl.GetKey(ctx, adminId)
	)
	val := g.data.Rdb.Exists(ctx, userRoleCacheKey).Val()
	if val > 0 {
		result, _ := g.data.Rdb.Get(ctx, userRoleCacheKey).Result()
		roleId, _ = strconv.Atoi(result)
	}
	if roleId > 0 {
		return
	}
	adminDo, err := g.adminRepo.GetUserById(ctx, adminId)
	if err != nil {
		return
	}
	err = g.RefreshUserRole(ctx, adminId)
	if err != nil {
		return
	}
	roleId = adminDo.RoleID
	return
}

func (g *SysMenuBiz) RefreshUserRole(ctx context.Context, adminId int) (err error) {
	var (
		userRoleCacheKey = constants.UserRoleCachePrefixTpl.GetKey(ctx, adminId)
	)
	_, err = g.data.Rdb.Del(ctx, userRoleCacheKey).Result()
	if err != nil {
		return err
	}
	adminDo, err := g.adminRepo.GetUserById(ctx, adminId)
	if err != nil {
		return
	}
	_, err = g.data.Rdb.Set(ctx, userRoleCacheKey, adminDo.RoleID, constants.UserRoleCachePrefixTpl.GetTTL()).Result()
	if err != nil {
		return err
	}
	return nil
}

func (g *SysMenuBiz) RefreshRoleAuthMenu(ctx context.Context, roleId int) (err error) {
	var (
		authKey = constants.RoleMenuCachePrefixTpl.GetKey(ctx, roleId)
	)
	_, err = g.data.Rdb.Del(ctx, authKey).Result()
	if err != nil {
		return err
	}
	sysMenuDos, err := g.GetSysMenuListByRoleId(ctx, roleId)
	if err != nil {
		return err
	}
	var args []interface{}
	for _, menuDo := range sysMenuDos {
		if menuDo.ApiPath != "" {
			args = append(args, menuDo.ApiPath)
			args = append(args, menuDo.ApiPath)
		}
	}
	if len(args) > 0 {
		_, err = g.data.Rdb.HMSet(ctx, authKey, args...).Result()
		if err != nil {
			return err
		}
		_, err = g.data.Rdb.Expire(ctx, authKey, constants.RoleMenuCachePrefixTpl.GetTTL()).Result()
		if err != nil {
			return err
		}
	}
	return nil
}
func (g *SysMenuBiz) Auth(ctx context.Context, adminId int, apiPath string) (result bool, err error) {

	roleId, err := g.GetUserRole(ctx, adminId)
	if err != nil {
		return
	}
	if roleId == 0 {
		err = errors.New("当前账户未配置角色")
		return
	}
	authKey := constants.RoleMenuCachePrefixTpl.GetKey(ctx, roleId)

	val := g.data.Rdb.HExists(ctx, authKey, apiPath).Val()
	if val {
		result = true
		return
	}

	err = g.RefreshRoleAuthMenu(ctx, roleId)
	if err != nil {
		return
	}
	return g.data.Rdb.HExists(ctx, authKey, apiPath).Result()
}
func (g *SysMenuBiz) AddOperateLog(ctx context.Context, in *do.SysOperateLogDo) (err error) {
	return g.sysOperateLogRepo.AddSysOperateLog(ctx, in)
}
