package adminbiz

import (
	"context"
	"errors"
	"strings"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data/ent"
	"cardMall/internal/pkg/isolationcustomer"
)

type SupplierBiz struct {
	supplierRepo      repository.SupplierRepo
	supplierGoodsRepo repository.SupplierGoodsRepo
	adminRepo         repository.AdminRepo
	sassCustomerRepo  repository.SaasCustomerRepo
}

func NewSupplierBiz(
	supplierRepo repository.SupplierRepo,
	supplierGoodsRepo repository.SupplierGoodsRepo,
	adminRepo repository.AdminRepo,
	sassCustomerRepo repository.SaasCustomerRepo,
) *SupplierBiz {
	return &SupplierBiz{
		supplierRepo:      supplierRepo,
		supplierGoodsRepo: supplierGoodsRepo,
		adminRepo:         adminRepo,
		sassCustomerRepo:  sassCustomerRepo,
	}
}

func (s *SupplierBiz) All(ctx context.Context, in *bo.SupplierQueryBo) ([]*do.SupplierDo, error) {
	d, _ := s.supplierRepo.All(ctx, isolationcustomer.GetCustomerIdZero(ctx), isolationcustomer.GetShopIdZero(ctx))
	if len(d) == 0 {
		return nil, nil
	}
	treeData := s.getTreeData(d)
	return s.searchByCacheTree(treeData, in), nil
}

func (s *SupplierBiz) GetCustomer(ctx context.Context, id int) (*do.SaasCustomerDo, error) {
	ctx = isolationcustomer.WithSassDbCtx(ctx)
	return s.sassCustomerRepo.Get(ctx, id)
}

func (s *SupplierBiz) Add(ctx context.Context, in *bo.SupplierAddBo) (int, error) {
	if in.Pid > 0 {
		d, _ := s.supplierRepo.Get(ctx, in.Pid)
		if d == nil {
			return 0, errors.New("父级供应商不存在")
		}
		if d.IsAdmin() {
			return 0, errors.New("当前父级供应商无法创建子级供应商")
		}
	}

	return s.supplierRepo.Add(ctx, in)
}

func (s *SupplierBiz) Update(ctx context.Context, in *bo.SupplierUpdateBo) (int, error) {
	d, _ := s.supplierRepo.Get(ctx, in.Id)
	if d == nil {
		return 0, errors.New("供应商不存在")
	}
	if d.IsAdmin() && in.Status == valobj.SupplierStatusDisable {
		return 0, errors.New("当前供应商无法被禁用")
	}
	return s.supplierRepo.Update(ctx, in)
}

func (s *SupplierBiz) GetByAdminId(ctx context.Context, adminId int) (*do.SupplierDo, error) {
	supplierDo, err := s.supplierRepo.GetByAdminId(ctx, adminId)
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, errors.New("未绑定供应商")
		}
		return nil, err
	}
	return supplierDo, nil
}

func (s *SupplierBiz) Del(ctx context.Context, id int) (int, error) {
	ids := []int{id}
	d, _ := s.supplierRepo.Get(ctx, id)
	if d == nil {
		return 0, errors.New("供应商不存在")
	}
	if d.IsAdmin() {
		return 0, errors.New("当前供应商无法删除")
	}
	if d.Pid == 0 {
		children, _ := s.supplierRepo.GetChildren(ctx, id)
		for _, v := range children {
			ids = append(ids, v.ID)
		}
	}
	exists, err := s.adminRepo.ExistSupplier(ctx, ids)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, errors.New("供应商存在登录账户，无法删除")
	}

	if exist, _ := s.supplierGoodsRepo.SupplierExist(ctx, ids); exist {
		return 0, errors.New("供应商存在商品，无法删除")
	}
	return s.supplierRepo.Delete(ctx, ids...)
}

// getTreeData 获取树形结构
func (s *SupplierBiz) getTreeData(suppliers []*do.SupplierDo) []*do.SupplierDo {
	rootSuppliers := make([]*do.SupplierDo, 0)

	for _, sup := range suppliers {
		if sup.Pid == 0 {
			rootSuppliers = append(rootSuppliers, sup)
		} else {
			parent := s.find(suppliers, sup.Pid)
			if parent != nil {
				parent.Children = append(parent.Children, sup)
			}
		}
	}
	return rootSuppliers
}

func (s *SupplierBiz) find(suppliers []*do.SupplierDo, id int) *do.SupplierDo {
	for _, supplier := range suppliers {
		if supplier.ID == id {
			return supplier
		}
	}
	return nil
}

// searchByCacheTree 根据条件查询筛选结构树
func (s *SupplierBiz) searchByCacheTree(suppliers []*do.SupplierDo, in *bo.SupplierQueryBo) []*do.SupplierDo {
	res := make([]*do.SupplierDo, 0)
	for _, sup := range suppliers {
		if s.searchFromTree(sup, in) != nil {
			res = append(res, sup)
		}
	}
	return res
}

func (s *SupplierBiz) searchFromTree(supplier *do.SupplierDo, in *bo.SupplierQueryBo) (res *do.SupplierDo) {
	if supplier == nil {
		return
	}
	if s.search(supplier, in) {
		return supplier
	}
	children := make([]*do.SupplierDo, 0)
	for _, v := range supplier.Children {
		c := s.searchFromTree(v, in)
		if c != nil {
			children = append(children, c)
		}
	}
	if len(children) > 0 {
		supplier.Children = children
		return supplier
	}
	return
}

func (s *SupplierBiz) search(supplier *do.SupplierDo, in *bo.SupplierQueryBo) bool {
	ok := true
	if in.Name != "" {
		ok = strings.Contains(supplier.Name, in.Name)
		if !ok {
			return false
		}
	}
	if in.Status.IsValid() && in.Status != supplier.Status {
		return false
	}
	if in.Pid > 0 && in.Pid != supplier.Pid {
		return false
	}

	return ok
}

func (s *SupplierBiz) FindById(ctx context.Context, id int) (*do.SupplierDo, error) {
	return s.supplierRepo.Get(ctx, id)
}
