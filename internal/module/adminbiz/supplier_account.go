package adminbiz

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"errors"
)

type SupplierAccountBiz struct {
	adminRepo    repository.AdminRepo
	supplierRepo repository.SupplierRepo
}

func NewSupplierAccountBiz(adminRepo repository.AdminRepo, supplierRepo repository.SupplierRepo) *SupplierAccountBiz {
	return &SupplierAccountBiz{adminRepo: adminRepo, supplierRepo: supplierRepo}
}

func (s *SupplierAccountBiz) List(ctx context.Context, in *bo.SupplierAccountListBo) (int, []*do.AdminDo, error) {
	reqBo := bo.NewAdminListBo(in)

	count, d, err := s.adminRepo.ListSupplier(ctx, reqBo)
	if err != nil || count == 0 {
		return 0, nil, err
	}

	supplierIds := make([]int, 0, len(d))
	for _, v := range d {
		supplierIds = append(supplierIds, v.SupplierID)
	}
	supplierIds = helper.SliceIntUnique(supplierIds)

	supplierMap, err := s.supplierRepo.GetAllMap(ctx, supplierIds)
	if err != nil {
		return 0, nil, err
	}

	for i, v := range d {
		if supplier, ok := supplierMap[v.SupplierID]; ok {
			d[i].Supplier = supplier
		}
	}

	return count, d, nil
}

func (s *SupplierAccountBiz) Add(ctx context.Context, in *bo.SupplierAccountAddBo) (int, error) {
	supplier, _ := s.supplierRepo.Get(ctx, in.SupplierId)
	if supplier == nil {
		return 0, errors.New("商城供应商不存在")
	}
	exists, err := s.adminRepo.AccountExist(isolationcustomer.WithDisableShopCtx(ctx), in.Account, 0)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, errors.New("商城供应商账号已存在")
	}

	mobileExist, err := s.adminRepo.MobileExist(ctx, in.PhoneNumber, 0, valobj.AdminTypeShopSupplier)
	if err != nil {
		return 0, err
	}
	if mobileExist {
		return 0, errors.New("商城供应商手机号已存在")
	}

	reqBo := bo.NewAddSupplierBo(in)
	//reqBo.Password, err = helper.GetPwdHash(in.Password)
	//if err != nil {
	//	return 0, err
	//}

	return s.adminRepo.AddSupplier(ctx, reqBo)
}

func (s *SupplierAccountBiz) Update(ctx context.Context, in *bo.SupplierAccountUpdateBo) (int, error) {
	reqBo := bo.NewUpdateSupplierBo(in)
	//if in.Password != "" {
	//	var err error
	//	reqBo.Password, err = helper.GetPwdHash(in.Password)
	//	if err != nil {
	//		return 0, err
	//	}
	//}
	exists, err := s.adminRepo.AccountExist(isolationcustomer.WithDisableShopCtx(ctx), in.Account, in.Id)
	if err != nil {
		return 0, err
	}
	if exists {
		return 0, apierr.ErrorParam("商城供应商账号已存在")
	}

	mobileExist, err := s.adminRepo.MobileExist(ctx, in.PhoneNumber, in.Id, valobj.AdminTypeShopSupplier)
	if err != nil {
		return 0, err
	}
	if mobileExist {
		return 0, errors.New("商城供应商手机号已存在")
	}

	return s.adminRepo.UpdateSupplier(ctx, reqBo)
}

func (s *SupplierAccountBiz) Del(ctx context.Context, id int) (int, error) {
	return s.adminRepo.DelSupplier(ctx, id)
}
