package adminbiz

import (
	bizBo "cardMall/internal/biz/bo"
	bizDo "cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/pkg"
	"cardMall/internal/pkg/helper"
	"context"
	"errors"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/log"
	"time"
)

type SysConfigBiz struct {
	sysConfigRepo bizRepo.SysConfigRepo
	oss           *pkg.AliyunOss
	hLog          *log.Helper
	data          *data.Data
	trans         bizRepo.TransactionRepo
	conf          *conf.Bootstrap
	sysConfigDs   *ds.SysConfigDs
}

func NewSysConfigBiz(
	sysConfigRepo bizRepo.SysConfigRepo,
	oss *pkg.AliyunOss,
	trans bizRepo.TransactionRepo,
	data *data.Data,
	conf *conf.Bootstrap,
	sysConfigDs *ds.SysConfigDs,
	hLog *log.Helper) *SysConfigBiz {
	c := &SysConfigBiz{
		sysConfigRepo: sysConfigRepo,
		oss:           oss,
		hLog:          hLog,
		trans:         trans,
		data:          data,
		conf:          conf,
		sysConfigDs:   sysConfigDs,
	}

	return c
}

// SysConfig 插入更新
func (o *SysConfigBiz) SysConfig(ctx context.Context, sysConfigBos []*bizBo.SysConfigBo) error {

	updateSysConfigBoMap := slice.KeyBy(sysConfigBos, func(item *bizBo.SysConfigBo) string {
		return item.ConfigKey
	})
	err := o.trans.Exec(ctx, func(ctx context.Context) error {

		for _, sysConfigBo := range updateSysConfigBoMap {

			if sysConfigBo.ConfigKey == valobj.SysConfigLoginType {

				updateConfigValueStr, _ := helper.StringToInt32(sysConfigBo.ConfigValue)
				updateConfigValue := valobj.ConfigLoginType(updateConfigValueStr)

				sysConfigDo, err := o.sysConfigRepo.FindByConfigKey(ctx, sysConfigBo.ConfigKey)
				if err != nil && !ent.IsNotFound(err) {
					return err
				}
				if sysConfigDo != nil {
					configValue := sysConfigDo.GetLoginTypeType()

					if configValue == valobj.ConfigLoginTypePhoneAndPassword ||
						configValue == valobj.ConfigLoginTypePhoneAndSms {

						if updateConfigValue != valobj.ConfigLoginTypePhoneAndPassword &&
							updateConfigValue != valobj.ConfigLoginTypePhoneAndSms {
							return errors.New("当前登录方式不可切换")
						}

					}
					if configValue == valobj.ConfigLoginTypeAccountAndPassword && updateConfigValue != valobj.ConfigLoginTypeAccountAndPassword {
						return errors.New("当前登录方式不可切换")
					}
					if configValue == valobj.ConfigLoginTypeWechat && updateConfigValue != valobj.ConfigLoginTypeWechat {
						return errors.New("当前登录方式不可切换")
					}
				}
				if updateConfigValue == valobj.ConfigLoginTypeWechat {
					bindPhoneConfigBo := updateSysConfigBoMap[valobj.SysConfigRegisterBindPhone]

					bindPhoneConfigValueStr, _ := helper.StringToInt32(bindPhoneConfigBo.ConfigValue)
					bindPhoneConfigValue := valobj.ConfigRegisterBindPhone(bindPhoneConfigValueStr)

					if bindPhoneConfigValue != valobj.ConfigRegisterBindPhoneYes {
						return errors.New("微信openID注册时必须强制绑定手机号")
					}

				}

			}

			err := o.sysConfigRepo.Upsert(ctx, &bizDo.SysConfigDo{
				//ID:             0,
				CreateUserID:   sysConfigBo.CreateUserID,
				CreateUserName: sysConfigBo.CreateUserName,
				UpdateUserID:   sysConfigBo.UpdateUserID,
				UpdateUserName: sysConfigBo.UpdateUserName,
				ConfigKey:      sysConfigBo.ConfigKey,
				ConfigValue:    sysConfigBo.ConfigValue,
				CreateTime:     int(time.Now().Unix()),
				UpdateTime:     int(time.Now().Unix()),
			})
			if err != nil {
				return err
			}

		}

		return nil
	})
	if err != nil {
		return err
	}

	return nil
}

// SysConfigList 获取配置
func (o *SysConfigBiz) SysConfigList(ctx context.Context, configKeys []string) ([]*bizDo.SysConfigDo, error) {
	configs, err := o.sysConfigRepo.FindByConfigKeys(ctx, configKeys...)
	if err != nil {
		return nil, err
	}
	return o.sysConfigDs.LoadDefault(ctx, configs, configKeys...), nil
}
func (o *SysConfigBiz) FindByConfigKey(ctx context.Context, configKey string) (*bizDo.SysConfigDo, error) {
	config, err := o.sysConfigRepo.FindByConfigKey(ctx, configKey)
	if err != nil {
		return nil, err
	}
	if config != nil {
		return config, nil
	}
	configs := o.sysConfigDs.LoadDefault(ctx, []*bizDo.SysConfigDo{}, configKey)
	if len(configs) > 0 {
		return configs[0], nil
	}
	return nil, nil
}
