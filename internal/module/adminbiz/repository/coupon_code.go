package repository

import (
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"context"
)

type CouponCodeRepo interface {
	Add(ctx context.Context, in *bo.CouponCodeAddBo) (int, error)

	Find(ctx context.Context, in *bo.CouponCodeFindBo) (*do.CouponCodeDo, error)

	Abolish(ctx context.Context, CouponId int) (int, error)

	DelByCouponId(ctx context.Context, couponId int) (int, error)

	List(ctx context.Context, in *bo.CouponCodeListBo) (int, []*do.CouponCodeDo, error)
}
