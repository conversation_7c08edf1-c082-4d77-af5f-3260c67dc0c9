package repository

import (
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"context"
)

type BaseGoodsRepo interface {
	Pull(ctx context.Context, in []*bo.BaseGoodsUpdateBo) error

	List(ctx context.Context, in *bo.BaseGoodsQueryBo) (int, []*do.BaseGoodsQueryDo, error)

	SetCardExpire(ctx context.Context, in *bo.BaseGoodsCardExpireBo) (int, error)

	UpdateStatus(ctx context.Context, in *bo.BaseGoodsUpdateStatusBo) (int, error)

	All(ctx context.Context, in *bo.BaseGoodsQueryBo) ([]*do.BaseGoodsQueryDo, error)

	DisableNotAuth(ctx context.Context, authProductId ...string) int

	UpdateChannelPrice(ctx context.Context, productId string, channelPrice float64) (int, error)
}
