package repository

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"context"
)

type PayMerchantRepo interface {
	Add(ctx context.Context, in *bo.PayMerchantAddBo) (int, error)

	Update(ctx context.Context, in *bo.PayMerchantUpdateBo) (int, error)

	Find(ctx context.Context, in *bo.PayMerchantQueryBo) (*do.PayMerchantDo, error)

	Del(ctx context.Context, id int) (int, error)

	DelCache(ctx context.Context, payType valobj.PayMerchantPayTypeObj) error

	Get(ctx context.Context, in *bo.PayMerchantQueryBo) ([]*do.PayMerchantDo, error)
}
