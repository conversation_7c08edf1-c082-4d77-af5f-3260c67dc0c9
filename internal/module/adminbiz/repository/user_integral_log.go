package repository

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"context"
)

type UserIntegralLogRepo interface {
	List(ctx context.Context, in *bo.UserIntegralListBo) (int, []*do.UserIntegralLogDo, error)

	Find(ctx context.Context, id int) (*do.UserIntegralLogDo, error)

	Add(ctx context.Context, in *bo.UserIntegralAddBo) (*do.UserIntegralLogDo, error)

	UpdateStatus(ctx context.Context, id int, status valobj.UserIntegralLogStatusObj) (int, error)

	Del(ctx context.Context, ids []int) (int, error)
}
