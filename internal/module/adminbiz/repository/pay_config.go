package repository

import (
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"context"
)

type PayConfigRepo interface {
	All(ctx context.Context, in *bo.PayConfigQueryBo) ([]*do.PayConfigDo, error)

	Add(ctx context.Context, in *bo.PayConfigChannelBo) (int, error)

	Update(ctx context.Context, in *bo.PayConfigUpdateBo) (int, error)

	Del(ctx context.Context, payMerchantId int) (int, error)

	DelCache(ctx context.Context) error
}
