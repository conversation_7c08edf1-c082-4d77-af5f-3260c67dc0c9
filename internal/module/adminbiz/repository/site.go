package repository

import (
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"context"
)

type SiteRepo interface {
	List(ctx context.Context, in *bo.SiteListBo) (int, []*do.SiteDo, error)

	Add(ctx context.Context, in *bo.SiteAddBo) (int, error)

	Del(ctx context.Context, id int) (int, error)

	Update(ctx context.Context, in *bo.SiteUpdateBo) (int, error)

	FindByUniqueStr(ctx context.Context, uniqueStr string) (*do.SiteDo, error)

	FindById(ctx context.Context, id int) (*do.SiteDo, error)

	GetMapByIds(ctx context.Context, id []int) (map[int]*do.SiteDo, error)

	All(ctx context.Context) ([]*do.SiteDo, error)
}
