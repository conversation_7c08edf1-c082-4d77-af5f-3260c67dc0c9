package adminbiz

import (
	"cardMall/internal/biz/ds"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"time"

	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"github.com/duke-git/lancet/v2/slice"
)

type ActivityBiz struct {
	activityRepo       repository.ActivityRepo
	goodsSkRepo        repository.GoodsSkuRepo
	transactionRepo    repository.TransactionRepo
	flashSaleTimeRepo  repository.FlashSaleTimeRepo
	flashSaleGoodsRepo repository.FlashSaleGoodsRepo
	payOrderRepo       repository.PayOrderRepo
	goodsDs            *ds.GoodsDs
}

func NewActivityBiz(
	activityRepo repository.ActivityRepo,
	goodsSkRepo repository.GoodsSkuRepo,
	transactionRepo repository.TransactionRepo,
	flashSaleTimeRepo repository.FlashSaleTimeRepo,
	flashSaleGoodsRepo repository.FlashSaleGoodsRepo,
	payOrderRepo repository.PayOrderRepo,
	goodsDs *ds.GoodsDs,
) *ActivityBiz {
	return &ActivityBiz{
		activityRepo:       activityRepo,
		goodsSkRepo:        goodsSkRepo,
		transactionRepo:    transactionRepo,
		flashSaleTimeRepo:  flashSaleTimeRepo,
		flashSaleGoodsRepo: flashSaleGoodsRepo,
		payOrderRepo:       payOrderRepo,
		goodsDs:            goodsDs,
	}
}

func (a *ActivityBiz) UpsertNormal(ctx context.Context, req *bo.ActivityAddReq) error {
	activityDo := &do.ActivityDo{
		ID:     req.Id,
		Name:   req.Name,
		Banner: req.Banner,
		Bg:     req.Bg,
		//GoodsIds:  req.GoodsIds,
		GoodsList: nil,
		StartTime: req.StartTime,
		EndTime:   req.EndTime,
		//ActivityStatus: req.Status,
		ActivityType: valobj.ActivityTypeNormal,
	}
	if len(req.GoodsGoodsSkuNos) > 0 {
		goodsSkDos, err := a.goodsSkRepo.GetBySkuNos(ctx, req.GoodsGoodsSkuNos)
		if err != nil {
			return err
		}
		if len(goodsSkDos) != len(req.GoodsGoodsSkuNos) {
			return apierr.ErrorParam("商品不存在")
		}
		//for _, goodsSkDo := range goodsSkDos {
		//	if !valobj.GoodsSkuStatusObj(goodsSkDo.ActivityStatus).IsOnline() {
		//		return apierr.ErrorParam("商品已下架")
		//	}
		//}
		activityDo.GoodsList = goodsSkDos
		activityDo.GoodsIds = slice.Map(goodsSkDos, func(_ int, item *do.GoodsSkuDo) int { return item.Id })
	}
	return a.transactionRepo.Exec(ctx, func(ctx context.Context) (err error) {
		if activityDo.ID > 0 {
			check := a.activityRepo.NameExists(ctx, activityDo.Name, activityDo.ID)
			if check {
				return apierr.ErrorParam("活动名称已存在")
			}
			oldActivityDo := a.activityRepo.Get(ctx, activityDo.ID)
			if oldActivityDo == nil {
				return apierr.ErrorDbNotFound("活动不存在")
			}
			if oldActivityDo.IsFinish() || oldActivityDo.ActivityStatus.IsStop() {
				return apierr.ErrorParam("活动已结算，不能修改")
			}
			if !oldActivityDo.ActivityStatus.IsDisable() {
				return apierr.ErrorParam("请先将活动设置为禁用")
			}
			a.activityRepo.Update(ctx, activityDo)
			return nil
		} else {
			check := a.activityRepo.NameExists(ctx, activityDo.Name)
			if check {
				return apierr.ErrorParam("活动名称已存在")
			}
			a.activityRepo.Create(ctx, activityDo)
		}
		return nil
	})
}

func (a *ActivityBiz) List(ctx context.Context, req *bo.ActivityListReq) ([]*do.ActivityDo, *bo.RespPageBo, error) {
	activityDos, page := a.activityRepo.SearchList(ctx, req)
	return activityDos, page, nil
}

func (a *ActivityBiz) OrderCount(ctx context.Context, activityIds []int) ([]*do.PayOrderActivityTotalDo, error) {
	if len(activityIds) == 0 {
		return nil, nil
	}
	return a.payOrderRepo.ActivityOrderTotal(ctx, activityIds)
}

func (a *ActivityBiz) GoodsCount(ctx context.Context, activity []*do.ActivityDo) ([]*do.ActivityGoodsCountDo, error) {
	if len(activity) == 0 {
		return nil, nil
	}
	var res []*do.ActivityGoodsCountDo
	activityGroup := slice.GroupWith(activity, func(item *do.ActivityDo) valobj.ActivityTypeObj { return item.ActivityType })
	for activityType, items := range activityGroup {
		activityIds := slice.Map(items, func(index int, item *do.ActivityDo) int {
			return item.ID
		})
		switch activityType {
		case valobj.ActivityTypeNormal:
			activities := a.activityRepo.NormalCount(ctx, activityIds)
			for _, item := range activities {
				res = append(res, &do.ActivityGoodsCountDo{
					ActivityID: item.ID,
					Count:      len(item.GoodsList),
				})
			}
		case valobj.ActivityTypeFlashSale:
			count, err := a.flashSaleGoodsRepo.GetCount(ctx, activityIds)
			if err != nil {
				return nil, err
			}
			res = append(res, count...)
		}
	}
	return res, nil
}

func (a *ActivityBiz) Detail(ctx context.Context, id int) (*do.ActivityDo, error) {
	activityDo := a.activityRepo.FindWithCommonById(ctx, id)
	if activityDo == nil {
		return nil, apierr.ErrorDbNotFound("活动不存在")
	}
	activityDo.GoodsList = a.goodsDs.GetStock(ctx, activityDo.GoodsList)

	return activityDo, nil
}

func (a *ActivityBiz) DetailFlashSale(ctx context.Context, id int) (*do.ActivityDo, error) {
	activityDo := a.activityRepo.Get(ctx, id)
	if activityDo == nil {
		return nil, apierr.ErrorDbNotFound("活动不存在")
	}
	if !activityDo.ActivityType.IsFlashSale() {
		return nil, apierr.ErrorParam("活动类型错误")
	}

	times, err := a.flashSaleTimeRepo.GetByActivityId(ctx, id)
	if err != nil {
		return nil, err
	}
	activityDo.FlashSaleTime = times

	skus, err := a.flashSaleGoodsRepo.GetByActivityId(ctx, id)
	if err != nil {
		return nil, err
	}
	skuNos := slice.Map(skus, func(_ int, item *do.FlashSaleGoodsDo) string { return item.SkuNo })
	activityDo.FlashSaleGoods = skus

	goodsSkus, err := a.goodsSkRepo.FindWithGoodsBySkuNos(ctx, skuNos)
	if err != nil {
		return nil, err
	}
	goodsSkus = a.goodsDs.GetStock(ctx, goodsSkus)

	goodsSkusMap := slice.KeyBy(goodsSkus, func(item *do.GoodsSkuDo) string { return item.SkuNo })
	for _, sku := range activityDo.FlashSaleGoods {
		sku.GoodsSku = goodsSkusMap[sku.SkuNo]
	}

	return activityDo, nil
}

func (a *ActivityBiz) UpdateStatus(ctx context.Context, req *bo.ActivityUpdateStatusReq) error {
	activityDo := a.activityRepo.Get(ctx, req.Id)
	if activityDo == nil {
		return apierr.ErrorDbNotFound("活动不存在")
	}
	if activityDo.IsFinish() {
		return apierr.ErrorParam("活动已结算，不能修改状态")
	}

	if activityDo.IsStop() {
		return apierr.ErrorParam("活动已结算，不能修改状态")
	}

	a.activityRepo.UpdateStatus(ctx, req)
	return nil
}

func (a *ActivityBiz) UpsertFlashSale(ctx context.Context, req *bo.ActivityAddReq) (err error) {
	goodsSku, _ := a.goodsSkRepo.GetBySkuNos(ctx, req.GoodsGoodsSkuNos)
	for _, sku := range goodsSku {
		if sku.IsDelete() {
			return apierr.ErrorParam("【%s】商品已删除", sku.GetSkuFullName())
		}
		if !sku.SettlementType.IsCash() {
			return apierr.ErrorParam("【%s】商品结算类型必须是现金类型，当前类型【%s】", sku.GetSkuFullName(), sku.SettlementType.String())
		}
	}
	now := time.Now()
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	shopId := isolationcustomer.GetShopIdZero(ctx)
	err = a.transactionRepo.Exec(ctx, func(ctx context.Context) error {
		if req.Id > 0 {
			check := a.activityRepo.NameExists(ctx, req.Name, req.Id)
			if check {
				return apierr.ErrorParam("活动名称已存在")
			}
			activity := a.activityRepo.Get(ctx, req.Id)
			if activity == nil {
				return apierr.ErrorDbNotFound("活动不存在")
			}
			if activity.IsFinish() || activity.ActivityStatus.IsStop() {
				return apierr.ErrorParam("活动已结算，不能修改")
			}
			if !activity.ActivityStatus.IsDisable() {
				return apierr.ErrorParam("请先将活动设置为禁用")
			}

			activity.Name = req.Name
			activity.Banner = req.Banner
			activity.Bg = req.Bg
			activity.StartTime = req.StartTime
			activity.EndTime = req.EndTime
			a.activityRepo.Update(ctx, activity)

			_, err = a.flashSaleGoodsRepo.DelByActivityId(ctx, req.Id)
			if err != nil {
				return err
			}
			_, err = a.flashSaleTimeRepo.DelByActivityId(ctx, req.Id)
			if err != nil {
				return err
			}

			goods := make([]*do.FlashSaleGoodsDo, 0, len(req.FlashSaleGoods))
			for _, good := range req.FlashSaleGoods {
				goods = append(goods, &do.FlashSaleGoodsDo{
					ActivityID: activity.ID,
					SkuNo:      good.SkuNo,
					TotalStock: good.Stock,
					Stock:      good.Stock,
					Price:      good.Price,
					LimitNum:   good.LimitNum,
					Sort:       good.Sort,
					CustomerID: customerId,
					ShopID:     shopId,
				})
			}
			_, err = a.flashSaleGoodsRepo.CreateBulk(ctx, goods)
			if err != nil {
				return err
			}

			times := make([]*do.FlashSaleTimeDo, 0, len(req.FlashSaleTime))
			for _, item := range req.FlashSaleTime {
				times = append(times, &do.FlashSaleTimeDo{
					ActivityID: activity.ID,
					Date:       item.Date,
					StartTime:  item.StartTime,
					EndTime:    item.EndTime,
					CustomerID: customerId,
					ShopID:     shopId,
				})
			}
			_, err = a.flashSaleTimeRepo.CreateBulk(ctx, times)
			return err
		} else {
			check := a.activityRepo.NameExists(ctx, req.Name)
			if check {
				return apierr.ErrorParam("活动名称已存在")
			}
			activity := a.activityRepo.Create(ctx, &do.ActivityDo{
				Name:         req.Name,
				Banner:       req.Banner,
				Bg:           req.Bg,
				StartTime:    req.StartTime,
				EndTime:      req.EndTime,
				CreatedAt:    now,
				UpdatedAt:    now,
				ActivityType: req.ActivityType,
			})
			if activity == nil || activity.ID == 0 {
				return apierr.ErrorDbNotFound("活动创建失败")
			}

			goods := make([]*do.FlashSaleGoodsDo, 0, len(req.FlashSaleGoods))
			for _, good := range req.FlashSaleGoods {
				goods = append(goods, &do.FlashSaleGoodsDo{
					ActivityID: activity.ID,
					SkuNo:      good.SkuNo,
					TotalStock: good.Stock,
					Stock:      good.Stock,
					Price:      good.Price,
					LimitNum:   good.LimitNum,
					Sort:       good.Sort,
					CustomerID: customerId,
					ShopID:     shopId,
				})
			}
			_, err = a.flashSaleGoodsRepo.CreateBulk(ctx, goods)
			if err != nil {
				return err
			}

			times := make([]*do.FlashSaleTimeDo, 0, len(req.FlashSaleTime))
			for _, item := range req.FlashSaleTime {
				times = append(times, &do.FlashSaleTimeDo{
					ActivityID: activity.ID,
					Date:       item.Date,
					StartTime:  item.StartTime,
					EndTime:    item.EndTime,
					CustomerID: customerId,
					ShopID:     shopId,
				})
			}
			_, err = a.flashSaleTimeRepo.CreateBulk(ctx, times)
			return err
		}
	})
	return err
}

func (a *ActivityBiz) Del(ctx context.Context, id int) error {
	activityDo := a.activityRepo.Get(ctx, id)
	if activityDo == nil {
		return apierr.ErrorDbNotFound("活动不存在")
	}
	if !activityDo.IsNotStart() {
		return apierr.ErrorParam("活动不是未开始状态，不能删除")
	}
	a.activityRepo.Delete(ctx, id)
	return nil
}

func (a *ActivityBiz) FindOne(ctx context.Context, id int) *do.ActivityDo {
	return a.activityRepo.Get(ctx, id)
}
