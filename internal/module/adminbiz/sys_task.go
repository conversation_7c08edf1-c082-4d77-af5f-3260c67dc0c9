package adminbiz

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"time"

	bizBo "cardMall/internal/biz/bo"
	bizDo "cardMall/internal/biz/do"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/data"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	adminrepo "cardMall/internal/module/adminbiz/repository"
	"cardMall/internal/pkg"
	"cardMall/internal/pkg/build"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util"
	"github.com/duke-git/lancet/v2/maputil"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/shopspring/decimal"
	"github.com/skip2/go-qrcode"
	"github.com/xuri/excelize/v2"
	"golang.org/x/crypto/bcrypt"
)

type SysTaskBiz struct {
	sysTaskRepo         bizRepo.SysTaskRepo
	sysTaskLogRepo      bizRepo.SysTaskLogRepo
	oss                 *pkg.AliyunOss
	hLog                *log.Helper
	data                *data.Data
	trans               bizRepo.TransactionRepo
	messageBus          bizRepo.EventBusRepo
	cardBatchCouponRepo bizRepo.CardBatchCouponRepo
	cardBatchGiftRepo   bizRepo.CardBatchGiftRepo
	cardBatchRepo       bizRepo.CardBatchRepo
	conf                *conf.Bootstrap
	UserRepo            adminrepo.UserRepo
	siteRepo            bizRepo.SiteRepo
	orderBiz            *OrderBiz
}

func NewSysTaskBiz(
	sysTaskRepo bizRepo.SysTaskRepo,
	sysTaskLogRepo bizRepo.SysTaskLogRepo,
	oss *pkg.AliyunOss,
	trans bizRepo.TransactionRepo,
	data *data.Data,
	conf *conf.Bootstrap,
	cardBatchCouponRepo bizRepo.CardBatchCouponRepo,
	cardBatchGiftRepo bizRepo.CardBatchGiftRepo,
	cardBatchRepo bizRepo.CardBatchRepo,
	UserRepo adminrepo.UserRepo,
	siteRepo bizRepo.SiteRepo,
	orderBiz *OrderBiz,
	bus bizRepo.EventBusRepo,
	hLog *log.Helper) *SysTaskBiz {
	c := &SysTaskBiz{
		sysTaskRepo:         sysTaskRepo,
		sysTaskLogRepo:      sysTaskLogRepo,
		oss:                 oss,
		hLog:                hLog,
		trans:               trans,
		data:                data,
		messageBus:          bus,
		conf:                conf,
		cardBatchCouponRepo: cardBatchCouponRepo,
		cardBatchGiftRepo:   cardBatchGiftRepo,
		cardBatchRepo:       cardBatchRepo,
		UserRepo:            UserRepo,
		orderBiz:            orderBiz,
		siteRepo:            siteRepo,
	}
	_ = bus.Subscribe("executeExportTaskAdmin", func(ctx context.Context, id int) {
		c.ExecuteExportTask(ctx, id)
	})
	_ = bus.Subscribe("executeImportTaskAdmin", func(ctx context.Context, id int) {
		c.ExecuteImportTask(ctx, id)
	})
	return c
}

// SysTaskList 任务-查询-分页
func (o *SysTaskBiz) SysTaskList(ctx context.Context, reqBo *bizBo.SysTaskSearchBo) ([]*bizDo.SysTaskDo, *bizBo.RespPageBo) {
	return o.sysTaskRepo.SearchList(ctx, reqBo)
}

// SysTaskLogList 任务日志-查询-分页
func (o *SysTaskBiz) SysTaskLogList(ctx context.Context, sysTaskID int) ([]*bizDo.SysTaskLogDo, error) {
	return o.sysTaskLogRepo.FindByTaskId(ctx, sysTaskID)
}
func (o *SysTaskBiz) GenerateTask(ctx context.Context, sysTaskExecuteBo *bizBo.SysTaskExecuteBo) (sysTaskDo *bizDo.SysTaskDo, err error) {

	err = o.trans.Exec(ctx, func(ctx context.Context) error {
		exportTaskDo := &bizDo.SysTaskDo{
			SysTaskName:   sysTaskExecuteBo.SysTaskName,
			SysTaskNumber: helper.GenExportTaskNo(),
			SysTaskType:   sysTaskExecuteBo.SysTaskType,
			SysTaskSource: sysTaskExecuteBo.SysTaskSource,
			Parameter:     sysTaskExecuteBo.Parameter,
			RelatedNo:     sysTaskExecuteBo.RelatedNo,
			DownloadURL:   "",
			Status:        valobj.SysTaskStatusNo,
			Remark:        sysTaskExecuteBo.Remark,
			FailReason:    "",
			UserID:        sysTaskExecuteBo.UserID,
			UserName:      sysTaskExecuteBo.UserName,
			CreateTime:    int(time.Now().Unix()),
			UpdateTime:    int(time.Now().Unix()),
		}
		sysTaskDo, err = o.sysTaskRepo.Create(ctx, exportTaskDo)
		if err != nil {
			return err
		}
		sysTaskLogDo := &bizDo.SysTaskLogDo{
			SysTaskID:  sysTaskDo.ID,
			Content:    "导出任务-创建",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		}
		_, err = o.sysTaskLogRepo.Create(ctx, sysTaskLogDo)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return
	}
	ctx2 := util.CopyValueCtx(ctx)
	if sysTaskExecuteBo.SysTaskType == valobj.SysTaskTypeExport {
		o.PublishExport(ctx2, sysTaskDo.ID)
	}
	if sysTaskExecuteBo.SysTaskType == valobj.SysTaskTypeImport {
		o.PublishImport(ctx2, sysTaskDo.ID)
	}
	return
}
func (s *SysTaskBiz) Publish(ctx context.Context, id int) {
	taskDo, err := s.sysTaskRepo.Get(ctx, id)
	if err != nil {
		log.Infof("SysTaskBiz.Publish fail id:%d err:{%v}", id, err)
		return
	}
	ctx2 := util.CopyValueCtx(ctx)
	if taskDo.SysTaskType == valobj.SysTaskTypeExport {
		s.PublishExport(ctx2, taskDo.ID)
	}
	if taskDo.SysTaskType == valobj.SysTaskTypeImport {
		s.PublishImport(ctx2, taskDo.ID)
	}
}

func (s *SysTaskBiz) PublishExport(ctx context.Context, id int) {
	s.messageBus.Publish("executeExportTaskAdmin", ctx, id)
}
func (s *SysTaskBiz) PublishImport(ctx context.Context, id int) {
	s.messageBus.Publish("executeImportTaskAdmin", ctx, id)
}
func (s *SysTaskBiz) ExecuteImportTask(ctx context.Context, id int) {
	start := time.Now()
	importTaskDo, err := s.sysTaskRepo.Get(ctx, id)
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}
	log.Infof("SysTaskBiz.ExecuteImportTask start ImportTaskNumber:%s ImportTaskName:%s", importTaskDo.SysTaskName, importTaskDo.SysTaskNumber)
	_, err = s.sysTaskRepo.UpdateStatus(ctx, id, valobj.SysTaskStatusInProgress)
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  importTaskDo.ID,
		Content:    "导入任务-开始执行",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}

	err = s.importTask(ctx, importTaskDo.SysTaskSource, importTaskDo.Parameter)
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
			ID:          importTaskDo.ID,
			DownloadURL: "",
			Status:      valobj.SysTaskStatusFail,
			FailReason:  err.Error(),
			UpdateTime:  int(time.Now().Unix()),
			FinishTime:  int(time.Now().Unix()),
		})
		_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
			SysTaskID:  importTaskDo.ID,
			Content:    "导入任务-执行失败",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})

		return
	}

	_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
		ID:          importTaskDo.ID,
		DownloadURL: "",
		Status:      valobj.SysTaskStatusDone,
		FailReason:  importTaskDo.FailReason,
		UpdateTime:  int(time.Now().Unix()),
		FinishTime:  int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  importTaskDo.ID,
		Content:    "导入任务-执行完毕",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.ExecuteImportTask fail id:%d err:{%v}", id, err)
		return
	}

	log.Infof("SysTaskBiz.ExecuteImportTask end ImportTaskNumber:%s ImportTaskName:%s 耗时:{%v}", importTaskDo.SysTaskName, importTaskDo.SysTaskNumber, time.Since(start))
	return

}
func (s *SysTaskBiz) ExecuteExportTask(ctx context.Context, id int) {
	start := time.Now()
	exportTaskDo, err := s.sysTaskRepo.Get(ctx, id)
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}
	log.Infof("SysTaskBiz.executeExportTask start ExportTaskNumber:%s ExportTaskName:%s", exportTaskDo.SysTaskName, exportTaskDo.SysTaskNumber)
	_, err = s.sysTaskRepo.UpdateStatus(ctx, id, valobj.SysTaskStatusInProgress)
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  exportTaskDo.ID,
		Content:    "导出任务-开始执行",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}

	fileName, buff, err := s.generateExcel(ctx, exportTaskDo.SysTaskSource, exportTaskDo.Parameter)
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
			ID:          exportTaskDo.ID,
			DownloadURL: "",
			Status:      valobj.SysTaskStatusFail,
			FailReason:  err.Error(),
			UpdateTime:  int(time.Now().Unix()),
			FinishTime:  int(time.Now().Unix()),
		})
		_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
			SysTaskID:  exportTaskDo.ID,
			Content:    "导出任务-执行失败",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})

		return
	}
	url, err := s.oss.Upload(isolationcustomer.WithCustomerAndShopCtx(ctx, exportTaskDo.CustomerID, exportTaskDo.ShopID), fileName, buff.Bytes())
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
			ID:          exportTaskDo.ID,
			DownloadURL: "",
			Status:      valobj.SysTaskStatusFail,
			FailReason:  err.Error(),
			UpdateTime:  int(time.Now().Unix()),
			FinishTime:  int(time.Now().Unix()),
		})
		_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
			SysTaskID:  exportTaskDo.ID,
			Content:    "导出任务-执行失败",
			CreateTime: int(time.Now().Unix()),
			UpdateTime: int(time.Now().Unix()),
		})
		return
	}
	_, err = s.sysTaskRepo.Update(ctx, &bizDo.SysTaskDo{
		ID:          exportTaskDo.ID,
		DownloadURL: url,
		Status:      valobj.SysTaskStatusDone,
		FailReason:  exportTaskDo.FailReason,
		UpdateTime:  int(time.Now().Unix()),
		FinishTime:  int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}
	_, err = s.sysTaskLogRepo.Create(ctx, &bizDo.SysTaskLogDo{
		SysTaskID:  exportTaskDo.ID,
		Content:    "导出任务-执行完毕",
		CreateTime: int(time.Now().Unix()),
		UpdateTime: int(time.Now().Unix()),
	})
	if err != nil {
		log.Infof("SysTaskBiz.executeExportTask fail id:%d err:{%v}", id, err)
		return
	}

	log.Infof("SysTaskBiz.executeExportTask end ExportTaskNumber:%s ExportTaskName:%s 耗时:{%v}", exportTaskDo.SysTaskName, exportTaskDo.SysTaskNumber, time.Since(start))
	return

}

func (s *SysTaskBiz) CardBatchCouponDownloadExcel(ctx context.Context, cardBatchNumber string) (fileName string, buff *bytes.Buffer, err error) {
	cardBatchDo, err := s.cardBatchRepo.GetByCardBatchNumber(ctx, cardBatchNumber)
	if err != nil {
		return
	}
	if cardBatchDo == nil {
		return
	}
	var (
		couponData []*bizDo.CardBatchCouponDo
		giftData   []*bizDo.CardBatchGiftDo
	)
	if cardBatchDo.CardBatchType == valobj.CardBatchTypeCoupon {
		couponData, err = s.cardBatchCouponRepo.FindByCardBatchNumber(ctx, cardBatchNumber)
	} else {
		giftData, err = s.cardBatchGiftRepo.FindByCardBatchNumber(ctx, cardBatchNumber)
	}
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return
	}
	_ = f.SetCellStr("sheet1", "A1", "礼包券码")
	var (
		cardBatch *bizDo.CardBatchDo
	)
	if len(couponData) > 0 || len(giftData) > 0 {
		if cardBatchDo.CardBatchType == valobj.CardBatchTypeCoupon {
			cardBatch = couponData[0].CardBatch
			var row = 2
			for _, val := range couponData {
				if val.CardBatch.Status != valobj.CardBatchStatusEnable {
					continue
				}
				if val.CardNumberType == valobj.CardBatchNumberTypeWhiteList {
					continue
				}
				strRwo := strconv.Itoa(row)
				cardCouponNumber := ""
				if val.CardNumberType == valobj.CardBatchNumberTypeCDKey {
					cardCouponNumber = val.CardCouponNumber
					_ = f.SetCellStr("sheet1", "A"+strRwo, cardCouponNumber)
				}
				if val.CardNumberType == valobj.CardBatchNumberTypeLink {
					site, _ := s.siteRepo.FindDefault(ctx)

					cardCouponNumber = site.GetUrl("subPackageB/gift/bind?code=" + val.CardCouponNumber)
					//cardCouponNumber = s.conf.Site.Domain + "subPackageB/gift/bind?code=" + val.CardCouponNumber
					_ = f.SetCellStr("sheet1", "A"+strRwo, cardCouponNumber)
				}
				if val.CardNumberType == valobj.CardBatchNumberTypeQrcode {
					//cardCouponNumber = s.conf.Site.Domain + "subPackageB/gift/bind?code=" + val.CardCouponNumber
					site, _ := s.siteRepo.FindDefault(ctx)
					cardCouponNumber = site.GetUrl("subPackageB/gift/bind?code=" + val.CardCouponNumber)
					content, _ := qrcode.Encode(cardCouponNumber, qrcode.Medium, 1000)

					f.SetRowHeight("sheet1", row, 100)
					f.AddPictureFromBytes("sheet1", "A"+strRwo, &excelize.Picture{
						Extension: ".jpg",
						File:      content,
						Format: &excelize.GraphicOptions{
							AutoFit: true,
							ScaleX:  2,
							ScaleY:  2,
						},
					})
				}
				row++
			}
		} else {
			cardBatch = giftData[0].CardBatch
			var row = 2
			for _, val := range giftData {
				if val.CardBatch.Status != valobj.CardBatchStatusEnable {
					continue
				}
				if val.CardNumberType == valobj.CardBatchNumberTypeWhiteList {
					continue
				}
				strRwo := strconv.Itoa(row)
				cardGiftNumber := ""
				if val.CardNumberType == valobj.CardBatchNumberTypeCDKey {
					cardGiftNumber = val.CardGiftNumber
					_ = f.SetCellStr("sheet1", "A"+strRwo, cardGiftNumber)
				}
				//if val.CardNumberType == valobj.CardBatchNumberTypeLink {
				//	cardCouponNumber = s.conf.Site.Domain + "subPackageB/gift/bind?code=" + val.CardGiftNumber
				//	_ = f.SetCellStr("sheet1", "A"+strRwo, cardCouponNumber)
				//}
				//if val.CardNumberType == valobj.CardBatchNumberTypeQrcode {
				//	cardCouponNumber = s.conf.Site.Domain + "subPackageB/gift/bind?code=" + val.CardGiftNumber
				//	content, _ := qrcode.Encode(cardCouponNumber, qrcode.Medium, 1000)
				//
				//	f.SetRowHeight("sheet1", row, 100)
				//	f.AddPictureFromBytes("sheet1", "A"+strRwo, &excelize.Picture{
				//		Extension: ".jpg",
				//		File:      content,
				//		Format: &excelize.GraphicOptions{
				//			AutoFit: true,
				//			ScaleX:  2,
				//			ScaleY:  2,
				//		},
				//	})
				//}
				row++
			}
		}
	}

	f.SetActiveSheet(index)
	fileName = cardBatch.CardBatchName + "_" + cardBatch.CardBatchNumber + "_" + helper.GetTimeDateInt64(time.Now().Unix()) + ".xlsx"
	buff, err = f.WriteToBuffer()
	if err != nil {
		return
	}
	return
}
func (s *SysTaskBiz) RealOrderExport(ctx context.Context, in *bo.ExportRealOrderListBo) (fileName string, buff *bytes.Buffer, err error) {
	data, err := s.orderBiz.ExportRealOrderList(ctx, in)
	if err != nil {
		return
	}
	realOrderSubListDoMap, realOrderMainListDoMap, realOrderGoodsListDoMap, err := s.getRealOrderDataList(ctx, data)
	if err != nil {
		return
	}
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return
	}

	_ = f.MergeCell("Sheet1", "A1", "A2")
	_ = f.MergeCell("Sheet1", "B1", "B2")
	_ = f.MergeCell("Sheet1", "C1", "C2")
	_ = f.MergeCell("Sheet1", "D1", "D2")

	_ = f.MergeCell("Sheet1", "N1", "N2")
	_ = f.MergeCell("Sheet1", "O1", "O2")
	_ = f.MergeCell("Sheet1", "P1", "P2")
	_ = f.MergeCell("Sheet1", "Q1", "Q2")
	_ = f.MergeCell("Sheet1", "R1", "R2")
	_ = f.MergeCell("Sheet1", "S1", "S2")
	_ = f.MergeCell("Sheet1", "T1", "T2")
	_ = f.MergeCell("Sheet1", "U1", "U2")
	_ = f.MergeCell("Sheet1", "V1", "V2")
	_ = f.MergeCell("Sheet1", "W1", "W2")
	_ = f.MergeCell("Sheet1", "X1", "X2")
	_ = f.MergeCell("Sheet1", "Y1", "Y2")
	_ = f.MergeCell("Sheet1", "Z1", "Z2")
	_ = f.MergeCell("Sheet1", "AA1", "AA2")
	_ = f.MergeCell("Sheet1", "AB1", "AB2")
	_ = f.MergeCell("Sheet1", "AC1", "AC2")
	_ = f.MergeCell("Sheet1", "AD1", "AD2")
	_ = f.MergeCell("Sheet1", "AE1", "AE2")
	_ = f.MergeCell("Sheet1", "AF1", "AF2")
	_ = f.MergeCell("Sheet1", "AG1", "AG2")
	_ = f.MergeCell("Sheet1", "AH1", "AH2")

	_ = f.MergeCell("Sheet1", "E1", "M1")
	style, err := f.NewStyle(&excelize.Style{

		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	_ = f.SetCellStr("Sheet1", "E1", "商品信息")
	_ = f.SetCellStyle("Sheet1", "E1", "E1", style)

	_ = f.SetCellStr("sheet1", "A1", "订单编号")
	_ = f.SetCellStr("sheet1", "B1", "子订单号")
	_ = f.SetCellStr("sheet1", "C1", "订单类型")
	_ = f.SetCellStr("sheet1", "D1", "站点名称")

	_ = f.SetCellStr("sheet1", "E2", "商品编号")
	_ = f.SetCellStr("sheet1", "F2", "商品名称")
	_ = f.SetCellStr("sheet1", "G2", "商品数量")
	//_ = f.SetCellStr("sheet1", "H2", "发货状态")
	_ = f.SetCellStr("sheet1", "H2", "供应单价")
	_ = f.SetCellStr("sheet1", "I2", "销售单价")
	_ = f.SetCellStr("sheet1", "J2", "销售积分")
	_ = f.SetCellStr("sheet1", "K2", "供应价合计")
	_ = f.SetCellStr("sheet1", "L2", "销售价合计")
	_ = f.SetCellStr("sheet1", "M2", "销售积分合计")
	_ = f.SetCellStr("sheet1", "N2", "售后单号")
	_ = f.SetCellStr("sheet1", "O2", "售后类型")
	_ = f.SetCellStr("sheet1", "P2", "快递单号")
	_ = f.SetCellStr("sheet1", "Q2", "快递公司")

	_ = f.SetCellStr("sheet1", "R1", "供应商")
	_ = f.SetCellStr("sheet1", "S1", "下单人昵称")
	_ = f.SetCellStr("sheet1", "T1", "下单人手机号")
	_ = f.SetCellStr("sheet1", "U1", "下单人账号")
	_ = f.SetCellStr("sheet1", "V1", "下单时间")
	_ = f.SetCellStr("sheet1", "W1", "订单总价")
	_ = f.SetCellStr("sheet1", "X1", "结算类型")
	_ = f.SetCellStr("sheet1", "Y1", "支付金额")
	_ = f.SetCellStr("sheet1", "Z1", "支付积分")
	_ = f.SetCellStr("sheet1", "AA1", "运费")
	_ = f.SetCellStr("sheet1", "AB1", "收货人")
	_ = f.SetCellStr("sheet1", "AC1", "收货人号码")
	_ = f.SetCellStr("sheet1", "AD1", "收货人地址")
	_ = f.SetCellStr("sheet1", "AE1", "优惠券名称")
	_ = f.SetCellStr("sheet1", "AF1", "优惠券金额")
	_ = f.SetCellStr("sheet1", "AG1", "优惠券券码")
	_ = f.SetCellStr("sheet1", "AH1", "礼券券码")

	_ = f.SetColWidth("Sheet1", "A", "D", 30)

	_ = f.SetColWidth("Sheet1", "N", "AH", 20)
	_ = f.SetColWidth("Sheet1", "E", "F", 20)

	_ = f.SetColWidth("Sheet1", "G", "N", 15)

	realOrderMainListDos := maputil.Values(realOrderMainListDoMap)
	sort.Slice(realOrderMainListDos, func(i, j int) bool {
		return realOrderMainListDos[i].CreateTime > realOrderMainListDos[j].CreateTime
	})
	var row = 3
	for _, orderMainListDo := range realOrderMainListDos {
		var (
			orderUser  *do.UserListItem
			couponInfo *do.CouponInfo
			title      string
			couponCode string
		)
		orderUser, err = s.UserRepo.GetOneUserById(ctx, orderMainListDo.UserID)
		if err != nil {
			return
		}
		if orderMainListDo.CouponCodeId != 0 {
			couponInfo, couponCode, err = s.orderBiz.CouponInfo(ctx, orderMainListDo.CouponCodeId)
			if err != nil {
				return
			}
			title = couponInfo.Title
		}
		realOrderSubListDos := realOrderSubListDoMap[orderMainListDo.PayOrderNumber]

		sort.Slice(realOrderSubListDos, func(i, j int) bool {
			return realOrderSubListDos[i].CreateTime > realOrderSubListDos[j].CreateTime
		})
		for _, realOrderSubListDo := range realOrderSubListDos {
			var (
				supplier            *bizDo.SupplierDo
				orderAfterSaleDos   []*bizDo.OrderAfterSaleDo
				realOrderDeliverDos []*bizDo.RealOrderDeliverDo
			)

			//supplier, err = s.orderBiz.GetSupplier(ctx, realOrderSubListDo.SupplierID)
			//if err != nil {
			//	return
			//}
			if realOrderSubListDo.SupplierID < isolationcustomer.OfficialSupplierIdMax {
				supplier = &bizDo.SupplierDo{ID: 1, Name: "官方供应商"}
			} else {

				supplier, err = s.orderBiz.GetSupplier(isolationcustomer.WithDisableShopCtx(ctx), realOrderSubListDo.SupplierID)
				if err != nil {
					return
				}
				if supplier.ShopID == 0 {
					supplier = &bizDo.SupplierDo{ID: 2, Name: "企业供应商"}
				}
			}

			// 售后详情
			orderAfterSaleDos, err = s.orderBiz.AfterSaleList(ctx, realOrderSubListDo.OrderNumber)
			if err != nil {
				return
			}
			afterSaleNos := slice.Map(orderAfterSaleDos, func(index int, item *bizDo.OrderAfterSaleDo) string {
				return item.AfterSaleNo
			})
			afterSaleTypes := slice.Map(orderAfterSaleDos, func(index int, item *bizDo.OrderAfterSaleDo) string {
				return item.Type.String()
			})
			// 订单物流
			realOrderDeliverDos, err = s.orderBiz.RealOrderDeliverOne(ctx, realOrderSubListDo.OrderNumber)
			if err != nil {
				return
			}
			logisticsNos := slice.Map(realOrderDeliverDos, func(index int, item *bizDo.RealOrderDeliverDo) string {
				return item.LogisticsNo
			})
			kdNames := slice.Map(realOrderDeliverDos, func(index int, item *bizDo.RealOrderDeliverDo) string {
				return item.KdName
			})
			realOrderGoodsListDos := realOrderGoodsListDoMap[realOrderSubListDo.OrderNumber]
			for rowIndex, realOrderGoodsListDo := range realOrderGoodsListDos {
				strRwo := strconv.Itoa(row)
				_ = f.SetCellStr("sheet1", "A"+strRwo, realOrderSubListDo.PayOrderNumber)
				_ = f.SetCellStr("sheet1", "B"+strRwo, realOrderSubListDo.OrderNumber)
				_ = f.SetCellStr("sheet1", "C"+strRwo, "实物订单")
				_ = f.SetCellStr("sheet1", "D"+strRwo, orderMainListDo.SiteName)
				_ = f.SetCellStr("sheet1", "E"+strRwo, realOrderGoodsListDo.SkuNo)
				_ = f.SetCellStr("sheet1", "F"+strRwo, build.GenGoodsNameWithSKU(realOrderGoodsListDo.GoodsName, realOrderGoodsListDo.GoodsSkuName))
				_ = f.SetCellStr("sheet1", "G"+strRwo, fmt.Sprintf("%d", realOrderGoodsListDo.Quantity))
				//_ = f.SetCellStr("sheet1", "H"+strRwo, "")
				_ = f.SetCellStr("sheet1", "H"+strRwo, realOrderGoodsListDo.SupplierPrice.String())
				_ = f.SetCellStr("sheet1", "I"+strRwo, realOrderGoodsListDo.SalePrice.String())
				_ = f.SetCellStr("sheet1", "J"+strRwo, fmt.Sprintf("%d", realOrderGoodsListDo.SaleIntegral))
				_ = f.SetCellStr("sheet1", "K"+strRwo, decimal.NewFromInt(int64(realOrderGoodsListDo.Quantity)).Mul(realOrderGoodsListDo.SupplierPrice).String())
				_ = f.SetCellStr("sheet1", "L"+strRwo, decimal.NewFromInt(int64(realOrderGoodsListDo.Quantity)).Mul(realOrderGoodsListDo.SalePrice).String())
				_ = f.SetCellStr("sheet1", "M"+strRwo, fmt.Sprintf("%d", realOrderGoodsListDo.Quantity*realOrderGoodsListDo.SaleIntegral))
				_ = f.SetCellStr("sheet1", "N"+strRwo, strings.Join(afterSaleNos, ";"))
				_ = f.SetCellStr("sheet1", "O"+strRwo, strings.Join(afterSaleTypes, ";"))
				_ = f.SetCellStr("sheet1", "P"+strRwo, strings.Join(logisticsNos, ";"))
				_ = f.SetCellStr("sheet1", "Q"+strRwo, strings.Join(kdNames, ";"))

				_ = f.SetCellStr("sheet1", "R"+strRwo, supplier.Name)
				_ = f.SetCellStr("sheet1", "S"+strRwo, orderUser.NickName)
				_ = f.SetCellStr("sheet1", "T"+strRwo, orderUser.PhoneNumber)
				_ = f.SetCellStr("sheet1", "U"+strRwo, orderUser.Username)
				_ = f.SetCellStr("sheet1", "V"+strRwo, helper.GetTimeDateInt64(realOrderSubListDo.CreateTime))

				_ = f.SetCellStr("sheet1", "W"+strRwo, realOrderSubListDo.TotalAmount.String())
				if len(realOrderGoodsListDos) > 1 && rowIndex == 0 {
					_ = f.MergeCell("Sheet1", "W"+strRwo, "W"+fmt.Sprintf("%d", row+len(realOrderGoodsListDos)-1))
				}

				_ = f.SetCellStr("sheet1", "X"+strRwo, realOrderSubListDo.SettlementType.String())
				if len(realOrderGoodsListDos) > 1 && rowIndex == 0 {
					_ = f.MergeCell("Sheet1", "X"+strRwo, "X"+fmt.Sprintf("%d", row+len(realOrderGoodsListDos)-1))
				}

				_ = f.SetCellStr("sheet1", "Y"+strRwo, realOrderSubListDo.PayAmount.String())
				if len(realOrderGoodsListDos) > 1 && rowIndex == 0 {
					_ = f.MergeCell("Sheet1", "Y"+strRwo, "Y"+fmt.Sprintf("%d", row+len(realOrderGoodsListDos)-1))
				}

				_ = f.SetCellStr("sheet1", "Z"+strRwo, fmt.Sprintf("%d", realOrderSubListDo.PayIntegral))
				if len(realOrderGoodsListDos) > 1 && rowIndex == 0 {
					_ = f.MergeCell("Sheet1", "Z"+strRwo, "Z"+fmt.Sprintf("%d", row+len(realOrderGoodsListDos)-1))
				}

				_ = f.SetCellStr("sheet1", "AA"+strRwo, realOrderSubListDo.FreightFee.String())
				if len(realOrderGoodsListDos) > 1 && rowIndex == 0 {
					_ = f.MergeCell("Sheet1", "AA"+strRwo, "AA"+fmt.Sprintf("%d", row+len(realOrderGoodsListDos)-1))
				}
				_ = f.SetCellStr("sheet1", "AB"+strRwo, realOrderSubListDo.UserAddressName)
				_ = f.SetCellStr("sheet1", "AC"+strRwo, realOrderSubListDo.PhoneNumber)
				_ = f.SetCellStr("sheet1", "AD"+strRwo, realOrderSubListDo.Area+realOrderSubListDo.Detail)
				_ = f.SetCellStr("sheet1", "AE"+strRwo, title)
				_ = f.SetCellStr("sheet1", "AF"+strRwo, realOrderGoodsListDo.CouponDiscountAmount.String())
				_ = f.SetCellStr("sheet1", "AG"+strRwo, couponCode)
				_ = f.SetCellStr("sheet1", "AH"+strRwo, orderMainListDo.CardCouponNumber)
				row++
			}
		}
	}
	f.SetActiveSheet(index)
	fileName = "order_" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	buff, err = f.WriteToBuffer()
	if err != nil {
		return
	}
	return

}

func (s *SysTaskBiz) VirtualOrderExport(ctx context.Context, in *bo.OrderListBo) (fileName string, buff *bytes.Buffer, err error) {
	orderListDos, err := s.orderBiz.Export(ctx, in)
	if err != nil {
		return
	}
	if err != nil {
		return
	}
	f := excelize.NewFile()
	defer func() {
		if err = f.Close(); err != nil {
			//a.hLog.Error("excelize.close失败:" + err.Error())
		}
	}()
	index, err := f.NewSheet("sheet1")
	if err != nil {
		return
	}

	_ = f.MergeCell("Sheet1", "A1", "A2")
	_ = f.MergeCell("Sheet1", "B1", "B2")
	_ = f.MergeCell("Sheet1", "C1", "C2")
	_ = f.MergeCell("Sheet1", "D1", "D2")

	_ = f.MergeCell("Sheet1", "N1", "N2")
	_ = f.MergeCell("Sheet1", "O1", "O2")
	_ = f.MergeCell("Sheet1", "P1", "P2")
	_ = f.MergeCell("Sheet1", "Q1", "Q2")
	_ = f.MergeCell("Sheet1", "R1", "R2")
	_ = f.MergeCell("Sheet1", "S1", "S2")
	_ = f.MergeCell("Sheet1", "T1", "T2")
	_ = f.MergeCell("Sheet1", "U1", "U2")
	_ = f.MergeCell("Sheet1", "V1", "V2")
	_ = f.MergeCell("Sheet1", "W1", "W2")
	_ = f.MergeCell("Sheet1", "X1", "X2")
	_ = f.MergeCell("Sheet1", "Y1", "Y2")
	_ = f.MergeCell("Sheet1", "Z1", "Z2")
	_ = f.MergeCell("Sheet1", "AA1", "AA2")
	_ = f.MergeCell("Sheet1", "AB1", "AB2")
	_ = f.MergeCell("Sheet1", "AC1", "AC2")
	_ = f.MergeCell("Sheet1", "AD1", "AD2")
	_ = f.MergeCell("Sheet1", "AE1", "AE2")
	_ = f.MergeCell("Sheet1", "AF1", "AF2")
	_ = f.MergeCell("Sheet1", "AG1", "AG2")
	_ = f.MergeCell("Sheet1", "AH1", "AH2")

	_ = f.MergeCell("Sheet1", "E1", "M1")
	style, err := f.NewStyle(&excelize.Style{

		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
	})
	_ = f.SetCellStr("Sheet1", "E1", "商品信息")
	_ = f.SetCellStyle("Sheet1", "E1", "E1", style)

	_ = f.SetCellStr("sheet1", "A1", "订单编号")
	_ = f.SetCellStr("sheet1", "B1", "子订单号")
	_ = f.SetCellStr("sheet1", "C1", "订单类型")
	_ = f.SetCellStr("sheet1", "D1", "站点名称")

	_ = f.SetCellStr("sheet1", "E2", "商品编号")
	_ = f.SetCellStr("sheet1", "F2", "商品名称")
	_ = f.SetCellStr("sheet1", "G2", "商品数量")
	//_ = f.SetCellStr("sheet1", "H2", "发货状态")
	_ = f.SetCellStr("sheet1", "H2", "供应单价")
	_ = f.SetCellStr("sheet1", "I2", "销售单价")
	_ = f.SetCellStr("sheet1", "J2", "销售积分")
	_ = f.SetCellStr("sheet1", "K2", "供应价合计")
	_ = f.SetCellStr("sheet1", "L2", "销售价合计")
	_ = f.SetCellStr("sheet1", "M2", "销售积分合计")
	_ = f.SetCellStr("sheet1", "N2", "售后单号")
	_ = f.SetCellStr("sheet1", "O2", "售后类型")
	_ = f.SetCellStr("sheet1", "P2", "快递单号")
	_ = f.SetCellStr("sheet1", "Q2", "快递公司")

	_ = f.SetCellStr("sheet1", "R1", "供应商")
	_ = f.SetCellStr("sheet1", "S1", "下单人昵称")
	_ = f.SetCellStr("sheet1", "T1", "下单人手机号")
	_ = f.SetCellStr("sheet1", "U1", "下单人账号")
	_ = f.SetCellStr("sheet1", "V1", "下单时间")
	_ = f.SetCellStr("sheet1", "W1", "订单总价")
	_ = f.SetCellStr("sheet1", "X1", "结算类型")
	_ = f.SetCellStr("sheet1", "Y1", "支付金额")
	_ = f.SetCellStr("sheet1", "Z1", "支付积分")
	_ = f.SetCellStr("sheet1", "AA1", "运费")
	_ = f.SetCellStr("sheet1", "AB1", "收货人")
	_ = f.SetCellStr("sheet1", "AC1", "收货人号码")
	_ = f.SetCellStr("sheet1", "AD1", "收货人地址")
	_ = f.SetCellStr("sheet1", "AE1", "优惠券名称")
	_ = f.SetCellStr("sheet1", "AF1", "优惠券金额")
	_ = f.SetCellStr("sheet1", "AG1", "优惠券券码")
	_ = f.SetCellStr("sheet1", "AH1", "礼券券码")

	_ = f.SetColWidth("Sheet1", "A", "D", 30)

	_ = f.SetColWidth("Sheet1", "N", "AH", 20)
	_ = f.SetColWidth("Sheet1", "E", "F", 20)

	_ = f.SetColWidth("Sheet1", "G", "N", 15)

	var row = 3
	for _, orderListDo := range orderListDos {
		var (
			orderUser  *do.UserListItem
			couponInfo *do.CouponInfo
			title      string
			couponCode string
		)
		orderUser, err = s.UserRepo.GetOneUserById(ctx, orderListDo.UserId)
		if err != nil {
			return
		}
		if orderListDo.CouponCodeId != 0 {
			couponInfo, couponCode, err = s.orderBiz.CouponInfo(ctx, orderListDo.CouponCodeId)
			if err != nil {
				return
			}
			title = couponInfo.Title
		}

		var (
			supplier *bizDo.SupplierDo
		)

		if orderListDo.SupplierID < isolationcustomer.OfficialSupplierIdMax {
			supplier = &bizDo.SupplierDo{ID: 1, Name: "官方供应商"}
		} else {

			supplier, err = s.orderBiz.GetSupplier(isolationcustomer.WithDisableShopCtx(ctx), orderListDo.SupplierID)
			if err != nil {
				return
			}
			if supplier.ShopID == 0 {
				supplier = &bizDo.SupplierDo{ID: 2, Name: "企业供应商"}
			}
		}

		strRwo := strconv.Itoa(row)
		_ = f.SetCellStr("sheet1", "A"+strRwo, orderListDo.PayOrderNumber)
		_ = f.SetCellStr("sheet1", "B"+strRwo, orderListDo.OrderNumber)
		_ = f.SetCellStr("sheet1", "C"+strRwo, "虚拟订单")
		_ = f.SetCellStr("sheet1", "D"+strRwo, orderListDo.SiteName)
		_ = f.SetCellStr("sheet1", "E"+strRwo, orderListDo.SkuNo)
		_ = f.SetCellStr("sheet1", "F"+strRwo, build.GenGoodsNameWithSKU(orderListDo.GoodsName, orderListDo.GoodsSkuName))
		_ = f.SetCellStr("sheet1", "G"+strRwo, fmt.Sprintf("%d", orderListDo.Quantity))
		//_ = f.SetCellStr("sheet1", "H"+strRwo, "")
		_ = f.SetCellStr("sheet1", "H"+strRwo, helper.Float64ToString(orderListDo.SupplierPrice, 2))
		_ = f.SetCellStr("sheet1", "I"+strRwo, helper.Float64ToString(orderListDo.SalePrice, 2))
		_ = f.SetCellStr("sheet1", "J"+strRwo, fmt.Sprintf("%d", orderListDo.SaleIntegral))
		_ = f.SetCellStr("sheet1", "K"+strRwo, decimal.NewFromInt(int64(orderListDo.Quantity)).Mul(decimal.NewFromFloat(orderListDo.SupplierPrice)).String())
		_ = f.SetCellStr("sheet1", "L"+strRwo, decimal.NewFromInt(int64(orderListDo.Quantity)).Mul(decimal.NewFromFloat(orderListDo.SalePrice)).String())
		_ = f.SetCellStr("sheet1", "M"+strRwo, fmt.Sprintf("%d", orderListDo.Quantity*orderListDo.SaleIntegral))
		_ = f.SetCellStr("sheet1", "N"+strRwo, "")
		_ = f.SetCellStr("sheet1", "O"+strRwo, "")
		_ = f.SetCellStr("sheet1", "P"+strRwo, "")
		_ = f.SetCellStr("sheet1", "Q"+strRwo, "")

		_ = f.SetCellStr("sheet1", "R"+strRwo, supplier.Name)
		_ = f.SetCellStr("sheet1", "S"+strRwo, orderUser.NickName)
		_ = f.SetCellStr("sheet1", "T"+strRwo, orderUser.PhoneNumber)
		_ = f.SetCellStr("sheet1", "U"+strRwo, orderUser.Username)
		_ = f.SetCellStr("sheet1", "V"+strRwo, helper.GetTimeDate(orderListDo.CreateTime))

		_ = f.SetCellStr("sheet1", "W"+strRwo, helper.Float64ToString(orderListDo.TotalAmount, 2))

		_ = f.SetCellStr("sheet1", "X"+strRwo, orderListDo.SettlementType.String())

		_ = f.SetCellStr("sheet1", "Y"+strRwo, helper.Float64ToString(orderListDo.PayAmount, 2))

		_ = f.SetCellStr("sheet1", "Z"+strRwo, fmt.Sprintf("%d", orderListDo.PayIntegral))

		_ = f.SetCellStr("sheet1", "AA"+strRwo, "")

		_ = f.SetCellStr("sheet1", "AB"+strRwo, "")
		_ = f.SetCellStr("sheet1", "AC"+strRwo, "")
		_ = f.SetCellStr("sheet1", "AD"+strRwo, "")
		_ = f.SetCellStr("sheet1", "AE"+strRwo, title)
		_ = f.SetCellStr("sheet1", "AF"+strRwo, helper.Float64ToString(orderListDo.CouponDiscountAmount, 2))
		_ = f.SetCellStr("sheet1", "AG"+strRwo, couponCode)
		_ = f.SetCellStr("sheet1", "AH"+strRwo, orderListDo.CardCouponNumber)
		row++
	}
	f.SetActiveSheet(index)
	fileName = "order_" + strconv.Itoa(int(time.Now().Unix())) + ".xlsx"
	buff, err = f.WriteToBuffer()
	if err != nil {
		return
	}
	return

}

func (o *SysTaskBiz) getRealOrderDataList(ctx context.Context, realOrderListDos []*do.RealOrderListDo) (map[string][]*do.RealOrderListDo, map[string]*do.RealOrderMainListDo, map[string][]*do.RealOrderGoodsListDo, error) {

	var (
		payOrderNumbers         = make([]string, 0, len(realOrderListDos))
		orderNumbers            = make([]string, 0, len(realOrderListDos))
		realOrderSubListDoMap   = make(map[string][]*do.RealOrderListDo)
		realOrderMainListDoMap  = make(map[string]*do.RealOrderMainListDo)
		realOrderGoodsListDoMap = make(map[string][]*do.RealOrderGoodsListDo)
	)
	// 子订单
	for _, val := range realOrderListDos {
		realOrderSubListDoMap[val.PayOrderNumber] = append(realOrderSubListDoMap[val.PayOrderNumber], val)
		payOrderNumbers = append(payOrderNumbers, val.PayOrderNumber)
		orderNumbers = append(orderNumbers, val.OrderNumber)
	}
	// 主订单
	orderMainList, err := o.orderBiz.RealOrderMainList(ctx, &bo.RealOrderMainListBo{
		PayOrderNumbers: payOrderNumbers,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	for _, val := range orderMainList {
		realOrderMainListDoMap[val.PayOrderNumber] = val
	}
	// 订单商品
	realOrderGoodsList, err := o.orderBiz.RealOrderGoodsList(ctx, &bo.RealOrderGoodsListBo{
		OrderNumbers: orderNumbers,
	})
	if err != nil {
		return nil, nil, nil, err
	}
	for _, val := range realOrderGoodsList {
		realOrderGoodsListDoMap[val.OrderNumber] = append(realOrderGoodsListDoMap[val.OrderNumber], val)
	}
	return realOrderSubListDoMap, realOrderMainListDoMap, realOrderGoodsListDoMap, nil
}

func (s *SysTaskBiz) importAccount(ctx context.Context, rows [][]string) error {

	var (
		usernameMap = make(map[string]string)
		phoneMap    = make(map[string]string)
		userAddBos  = make([]*bo.UserAddBo, 0)
		rowCount    = len(rows)
	)
	for i := 1; i < rowCount; i++ {
		if len(rows[i]) < 3 {
			return errors.New(fmt.Sprintf("第%d行数据错误", i))
		}
		username := strings.Trim(rows[i][0], " ")
		password := strings.Trim(rows[i][1], " ")
		phoneNumber := strings.Trim(rows[i][2], " ")

		if username == "" {
			return errors.New(fmt.Sprintf("第%d行账号为空", i))
		}
		if val := len(username); val < 10 || val > 20 {
			return errors.New(fmt.Sprintf("第%d行账号格式错误,账号长度大于等于10小于等于20", i))
		}
		if helper.HasChineseCharacters(username) {
			return errors.New(fmt.Sprintf("第%d行账号格式错误,账号不能包含中文字符", i))
		}
		var (
			userListItem *do.UserListItem
		)
		userListItem, err := s.UserRepo.FindByUsername(ctx, username)
		if err != nil {
			return errors.New(fmt.Sprintf("第%d行账号格式错误", i))
		}
		if userListItem != nil {
			return errors.New(fmt.Sprintf("第%d行账号已存在", i))
		}
		userListItem, err = s.UserRepo.FindByPhoneNumber(ctx, phoneNumber)
		if err != nil {
			return errors.New(fmt.Sprintf("第%d行手机号格式错误", i))
		}
		if userListItem != nil {
			return errors.New(fmt.Sprintf("第%d行手机号已存在", i))
		}
		if !helper.IsAccountPhone(phoneNumber) {
			return errors.New(fmt.Sprintf("第%d行手机号格式错误", i))
		}

		if _, ok := usernameMap[username]; ok {
			return errors.New(fmt.Sprintf("第%d行账号重复", i))
		}
		if _, ok := phoneMap[phoneNumber]; ok {
			return errors.New(fmt.Sprintf("第%d行手机号重复", i))
		}

		if !helper.ValidatePassword(password) {
			return errors.New(fmt.Sprintf("第%d行密码不符合规则,长度大于8,需要包含大写字母小写字母数字,数字不能连续", i))
		}
		hashedPassword, passwordErr := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
		if passwordErr != nil {
			return errors.New(fmt.Sprintf("第%d行密码不符合规则", i))
		}
		phoneMap[phoneNumber] = phoneNumber
		usernameMap[username] = username

		userAddBos = append(userAddBos, &bo.UserAddBo{
			Username:    username,
			NickName:    "用户_" + username,
			Password:    string(hashedPassword),
			PhoneNumber: phoneNumber,
		})
	}
	if len(userAddBos) > 0 {
		s.ImportAccount(ctx, userAddBos)
	}
	return nil
}

func (l *SysTaskBiz) ImportAccount(ctx context.Context, userAddBos []*bo.UserAddBo) {
	err := l.trans.Exec(ctx, func(ctx context.Context) error {

		for _, userAddBo := range userAddBos {
			_, err := l.UserRepo.CreateV2(ctx, userAddBo)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return
	}
}

func (s *SysTaskBiz) generateExcel(ctx context.Context, sysTaskSource valobj.SysTaskSource, parameter string) (fileName string, buff *bytes.Buffer, err error) {

	switch sysTaskSource {
	case valobj.SysTaskSourceCardBatchCouponExport:
		return s.CardBatchCouponDownloadExcel(ctx, parameter)
	case valobj.SysTaskSourceRealOrderExport:
		in := &bo.ExportRealOrderListBo{}
		err = json.Unmarshal([]byte(parameter), in)
		if err != nil {
			return
		}
		return s.RealOrderExport(ctx, in)
	case valobj.SysTaskSourceVirtualOrderExport:
		in := &bo.OrderListBo{}
		err = json.Unmarshal([]byte(parameter), in)
		if err != nil {
			return
		}
		return s.VirtualOrderExport(ctx, in)
	default:
		err = fmt.Errorf("SysTaskBiz.generateExcel - fail sysTaskSource(%v), db's data error, parameter(%s)", sysTaskSource, parameter)
		return
	}
}
func (s *SysTaskBiz) importTask(ctx context.Context, sysTaskSource valobj.SysTaskSource, parameter string) (err error) {

	switch sysTaskSource {
	case valobj.SysTaskSourceImportAccount:

		split := strings.Split(parameter, ";")
		rows := make([][]string, len(split))
		for i, s2 := range split {
			rows[i] = strings.Split(s2, ",")
		}
		return s.importAccount(ctx, rows)
	default:
		err = fmt.Errorf("SysTaskBiz.importTask - fail sysTaskSource(%v), db's data error, parameter(%s)", sysTaskSource, parameter)
		return
	}
}
