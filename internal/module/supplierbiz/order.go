package supplierbiz

import (
	"cardMall/api/apierr"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/ds"
	bizrepository "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/data/ent"
	appBo "cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/server/middleware/authtools"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"github.com/shopspring/decimal"
)

type OrderBiz struct {
	orderRepo                 bizrepository.OrderRepo
	orderDeliverGoodsRepo     bizrepository.OrderDeliverGoodsRepo
	orderDeliverRepo          bizrepository.OrderDeliverRepo
	orderUserAddressRepo      bizrepository.OrderUserAddressRepo
	trans                     bizrepository.TransactionRepo
	orderOperatorLogRepo      bizrepository.OrderOperatorLogRepo
	orderAfterSaleRepo        bizrepository.OrderAfterSaleRepo
	goodsSkuRepo              bizrepository.GoodsSkuRepo
	orderGoodsRepo            bizrepository.OrderGoodsRepo
	brandRepo                 bizrepository.GoodsBrandRepo
	siteRepo                  bizrepository.SiteRepo
	supplierRepo              bizrepository.SupplierRepo
	bizPayOrderRepo           bizrepository.PayOrderRepo
	cardBatchCouponRepo       bizrepository.CardBatchCouponRepo
	cardCouponOperatorLogRepo bizrepository.CardCouponOperatorLogRepo
	supplierGoodsSkuRepo      bizrepository.SupplierGoodsSkuRepo
	supplierGoodsRepo         bizrepository.SupplierGoodsRepo

	payOrderRepo    repository.PayOrderRepo
	appOrderRepo    repository.OrderRepo
	deliverBaseBiz  *DeliverBaseBiz
	orderBaseBiz    *OrderBaseBiz
	refundDs        *ds.RefundDs
	goodsDs         *ds.GoodsDs
	goodsCategoryDs *ds.GoodsCategoryDs
	log             *log.Helper
	idGenerator     *idgenerator.Generator
	conf            *conf.Bootstrap
}

func NewOrderBiz(orderRepo bizrepository.OrderRepo, orderDeliverGoodsRepo bizrepository.OrderDeliverGoodsRepo, orderDeliverRepo bizrepository.OrderDeliverRepo, orderUserAddressRepo bizrepository.OrderUserAddressRepo, trans bizrepository.TransactionRepo, orderOperatorLogRepo bizrepository.OrderOperatorLogRepo, orderAfterSaleRepo bizrepository.OrderAfterSaleRepo, goodsSkuRepo bizrepository.GoodsSkuRepo, orderGoodsRepo bizrepository.OrderGoodsRepo, brandRepo bizrepository.GoodsBrandRepo, siteRepo bizrepository.SiteRepo, supplierRepo bizrepository.SupplierRepo, bizPayOrderRepo bizrepository.PayOrderRepo, cardBatchCouponRepo bizrepository.CardBatchCouponRepo, cardCouponOperatorLogRepo bizrepository.CardCouponOperatorLogRepo, supplierGoodsSkuRepo bizrepository.SupplierGoodsSkuRepo, supplierGoodsRepo bizrepository.SupplierGoodsRepo, payOrderRepo repository.PayOrderRepo, appOrderRepo repository.OrderRepo, deliverBaseBiz *DeliverBaseBiz, orderBaseBiz *OrderBaseBiz, refundDs *ds.RefundDs, goodsDs *ds.GoodsDs, goodsCategoryDs *ds.GoodsCategoryDs, log *log.Helper, idGenerator *idgenerator.Generator, conf *conf.Bootstrap) *OrderBiz {
	return &OrderBiz{orderRepo: orderRepo, orderDeliverGoodsRepo: orderDeliverGoodsRepo, orderDeliverRepo: orderDeliverRepo, orderUserAddressRepo: orderUserAddressRepo, trans: trans, orderOperatorLogRepo: orderOperatorLogRepo, orderAfterSaleRepo: orderAfterSaleRepo, goodsSkuRepo: goodsSkuRepo, orderGoodsRepo: orderGoodsRepo, brandRepo: brandRepo, siteRepo: siteRepo, supplierRepo: supplierRepo, bizPayOrderRepo: bizPayOrderRepo, cardBatchCouponRepo: cardBatchCouponRepo, cardCouponOperatorLogRepo: cardCouponOperatorLogRepo, supplierGoodsSkuRepo: supplierGoodsSkuRepo, supplierGoodsRepo: supplierGoodsRepo, payOrderRepo: payOrderRepo, appOrderRepo: appOrderRepo, deliverBaseBiz: deliverBaseBiz, orderBaseBiz: orderBaseBiz, refundDs: refundDs, goodsDs: goodsDs, goodsCategoryDs: goodsCategoryDs, log: log, idGenerator: idGenerator, conf: conf}
}

// SearchList 弃用
// deprecated
func (o *OrderBiz) SearchList(ctx context.Context, reqBo *commonbo.OrderSearchBo) ([]*do.OrderDo, *commonbo.RespPageBo) {
	return o.orderRepo.SearchList(ctx, reqBo)
}

func (o *OrderBiz) SearchDeliverList(ctx context.Context, reqBo *commonbo.OrderDeliverSearchBo) ([]*do.OrderDeliverDo, *commonbo.RespPageBo, error) {
	dos, pageInfo := o.orderDeliverRepo.SearchList(ctx, reqBo)

	orderNumberSlice := slice.Map(dos, func(_ int, item *do.OrderDeliverDo) string {
		return item.OrderNumber
	})

	addrDos, err := o.orderUserAddressRepo.FindByOrderNumber(ctx, orderNumberSlice...)
	if err != nil {
		return dos, pageInfo, err
	}
	addrMap := slice.KeyBy(addrDos, func(item *do.OrderUserAddressDo) string {
		return item.OrderNumber
	})

	for _, deliverDo := range dos {
		if addr, ok := addrMap[deliverDo.OrderNumber]; ok {
			deliverDo.OrderUserAddress = addr
		}
	}

	return dos, pageInfo, nil
}

// OrderList .
func (o *OrderBiz) OrderList(ctx context.Context, reqBo *commonbo.OrderSearchBo) ([]*do.OrderDo, *commonbo.RespPageBo, error) {
	dos, pageInfo := o.orderRepo.SearchList(ctx, reqBo)
	if len(dos) == 0 {
		return dos, pageInfo, nil
	}
	// 合并发货数据
	err := o.orderBaseBiz.FixDeliverQuantity(ctx, dos...)
	if err != nil {
		return nil, pageInfo, err
	}
	// 合并售后数据
	err = o.deliverBaseBiz.FixAfterSaleData(ctx, dos...)
	if err != nil {
		return nil, pageInfo, err
	}
	if reqBo.Edges != nil && reqBo.Edges.WithSite {
		if err = o.orderBaseBiz.FixSiteData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}
	if reqBo.Edges != nil && reqBo.Edges.WithSupplier {
		if err = o.orderBaseBiz.FixSupplierData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}
	if reqBo.Edges != nil && reqBo.Edges.WithPayOrder {
		if err = o.orderBaseBiz.FixPayOrderData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}

	return dos, pageInfo, nil
}

// OrderDetail .
func (o *OrderBiz) OrderDetail(ctx context.Context, orderNumber string) (*do.OrderDo, error) {
	var (
		orderDo *do.OrderDo
		err     error
	)
	isSaasSupplier := false
	if authtools.GetLoginInfo(ctx) != nil {
		isSaasSupplier = authtools.GetLoginInfo(ctx).AdminType.IsSaasSupplier()
	}
	if isSaasSupplier {
		orderDo, err = o.orderBaseBiz.GetSaasOrderDetail(ctx, orderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true, WithUserAddress: false, WithOperatorLog: true})
	} else {
		orderDo, err = o.orderRepo.GetWithEdges(ctx, orderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true, WithUserAddress: false, WithOperatorLog: true})
	}
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, apierr.ErrorDbNotFound("订单不存在")
		}
		return nil, err
	}
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(orderDo.ShopID))
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID)
	// 合并发货数据
	err = o.orderBaseBiz.FixDeliverQuantity(ctx, orderDo)
	if err != nil {
		return nil, err
	}
	// 合并售后数据
	err = o.deliverBaseBiz.FixAfterSaleData(ctx, orderDo)
	if err != nil {
		return nil, err
	}

	return orderDo, nil
}

// DeliverEntity 订单发货
func (o *OrderBiz) DeliverEntity(ctx context.Context, reqBo *bo.DeliverEntityBo) error {
	var (
		orderDo *do.OrderDo
		err     error
	)
	if reqBo.IsSaasSupplier() {
		orderDo, err = o.orderBaseBiz.GetSaasOrderDetail(ctx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true, WithUserAddress: true})
	} else {
		orderDo, err = o.orderRepo.GetWithEdges(ctx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true, WithUserAddress: true})
	}
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("订单不存在")
		}
		return err
	}

	return o.deliverBaseBiz.DeliverEntity(ctx, reqBo, orderDo)
}

// DelayDeliverRemark 延迟发货备注
func (o *OrderBiz) DelayDeliverRemark(ctx context.Context, reqBo *bo.DelayDeliverRemarkBo) error {
	orderDo, err := o.orderBaseBiz.GetSaasOrderDetail(ctx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{})
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("订单不存在")
		}
		return err
	}
	if !orderDo.IsWaitForDeliver() {
		return apierr.ErrorNotAllow("订单不是待发货状态")
	}
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(orderDo.ShopID))
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID)

	return o.orderRepo.SetDelayDeliverRemark(ctx, reqBo)
}

// Confirm .
func (o *OrderBiz) Confirm(ctx context.Context, reqBo *bo.OrderConfirmBo) error {
	queryOrderCtx := ctx
	if reqBo.AdminType.IsSaasSupplier() {
		queryOrderCtx = isolationcustomer.WithSassDbCtx(ctx)
	}
	orderDo, err := o.orderRepo.GetWithEdges(queryOrderCtx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{})
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("订单不存在")
		}
		return err
	}
	if !orderDo.Status.IsToBeConfirmed() {
		return apierr.ErrorNotAllow("确认失败，当前状态不可确认")
	}

	ctx = isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID)
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(orderDo.ShopID))

	return o.trans.Exec(ctx, func(ctx context.Context) error {
		_, tranErr := o.orderOperatorLogRepo.Add(ctx, &commonbo.OrderOperatorLogAddBo{
			OrderId:          orderDo.ID,
			OrderNumber:      orderDo.OrderNumber,
			OrderStatus:      valobj.OrderStatusAwaitingShip,
			Content:          valobj.OrderStatusAwaitingShip.GetOperatorContent(),
			OperatorUserType: valobj.OrderOperatorLogUserTypeSupplier,
			OperatorUserId:   reqBo.GetOperaId(),
			OperatorUserName: reqBo.GetOperaName(),
		})
		if tranErr != nil {
			return tranErr
		}
		return o.orderRepo.Confirm(ctx, reqBo.OrderNumber)
	})
}

// Reject .
func (o *OrderBiz) Reject(ctx context.Context, reqBo *bo.OrderRejectBo) error {
	var (
		orderDo *do.OrderDo
		err     error
	)
	if reqBo.IsSaasSupplier() {
		orderDo, err = o.orderBaseBiz.GetSaasOrderDetail(ctx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true})
	} else {
		orderDo, err = o.orderRepo.GetWithEdges(ctx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true})
	}
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorNotAllow("订单不存在")
		}
		return err
	}
	if orderDo.SupplierID == int(o.conf.Hyt.SupplierId) {
		if !orderDo.Status.IsWaitingShip() {
			return apierr.ErrorNotAllow("订单当前状态【%s】，不可进行驳回", orderDo.Status.GetName())
		}
	} else {
		if !orderDo.Status.IsToBeConfirmed() {
			return apierr.ErrorNotAllow("订单当前状态【%s】，不可进行驳回", orderDo.Status.GetName())
		}
	}

	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(orderDo.ShopID))
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID)

	goodsAmount := decimal.Zero
	for _, tmpGoodsDo := range orderDo.OrderGoods {
		goodsAmount = goodsAmount.Add(decimal.NewFromFloat(tmpGoodsDo.ChannelPrice).Mul(decimal.NewFromInt(int64(tmpGoodsDo.Quantity))))
	}
	goodsAmount = goodsAmount.Mul(decimal.NewFromInt(10000))

	fn := func(ctx context.Context) error {
		_, tranErr := o.orderOperatorLogRepo.Add(ctx, &commonbo.OrderOperatorLogAddBo{
			OrderId:          orderDo.ID,
			OrderNumber:      orderDo.OrderNumber,
			OrderStatus:      valobj.OrderStatusReject,
			Content:          valobj.OrderStatusReject.GetOperatorContent() + ":" + reqBo.Remark,
			OperatorUserType: valobj.OrderOperatorLogUserTypeSupplier,
			OperatorUserId:   reqBo.GetOperaId(),
			OperatorUserName: reqBo.GetOperaName(),
		})
		if tranErr != nil {
			return tranErr
		}

		if orderDo.ExtType.IsCardBatchCoupon() {
			if err = o.RefundCardBatchCoupon(ctx, reqBo, orderDo); err != nil {
				return err
			}
		} else {
			tranErr = o.refundDs.Refund(ctx, &commonbo.RefundBo{
				OrderId:      orderDo.ID,
				RefundAmount: 0,
				RefundReason: fmt.Sprintf("订单驳回，原因：%s", reqBo.Remark),
				StockOperator: &commonbo.RefundStockOperatorBo{
					OperatorId:   reqBo.GetOperaId(),
					OperatorName: reqBo.GetOperaName(),
					OperatorType: valobj.ModifyGoodsStockTypeOrderRefund,
				},
			}, valobj.OrderStatusReject)
			if tranErr != nil {
				return tranErr
			}
		}
		return tranErr
	}

	if goodsAmount.IsPositive() && orderDo.RequestId != "" {
		err = o.refundDs.TccRefund(ctx, orderDo, orderDo.OrderNumber, int(goodsAmount.IntPart()), func(ctx context.Context, s string) error {
			return fn(ctx)
		})
		if err != nil {
			return err
		}
	} else {
		err = o.trans.Exec(ctx, func(ctx context.Context) error {
			return fn(ctx)
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// RefundCardBatchCoupon 退款卡券订单
func (o *OrderBiz) RefundCardBatchCoupon(ctx context.Context, reqBo *bo.OrderRejectBo, orderDoArgs ...*do.OrderDo) error {
	var orderDo *do.OrderDo
	var err error
	if len(orderDoArgs) == 0 {
		orderDo, err = o.orderRepo.GetWithEdges(ctx, reqBo.OrderNumber, &commonbo.OrderWithEdgeBo{WithGoods: true})
		if err != nil {
			return err
		}
	} else {
		orderDo = orderDoArgs[0]
	}

	payOrder, err := o.bizPayOrderRepo.FindByOrderNumber(ctx, orderDo.PayOrderNumber)
	if err != nil {
		return err
	}
	if payOrder == nil {
		return apierr.ErrorNotAllow("未找到支付单")
	}
	couponDo, err := o.cardBatchCouponRepo.GetByCardCouponNumber(ctx, payOrder.CardCouponNumber)
	if err != nil {
		return err
	}
	payOrderDos, err := o.bizPayOrderRepo.FindByCardCouponNumber(ctx, payOrder.CardCouponNumber)
	if err != nil {
		return err
	}
	payOrderNumbers := slice.Map(payOrderDos, func(index int, item *do.PayOrderDo) string { return item.OrderNumber })
	otherPayOrderNumbers := slice.Difference(payOrderNumbers, []string{payOrder.OrderNumber})
	orderDos, err := o.orderRepo.FindByPayOrderNumber(ctx, otherPayOrderNumbers)
	if err != nil {
		return err
	}
	// 过滤主订单
	orderDos = slice.Filter(orderDos, func(index int, item *do.OrderDo) bool { return !item.IsMainOrder() })

	skuNos := make([]string, 0)
	for _, tmpOrderDo := range orderDos {
		for _, tmpOrderGoodsDo := range tmpOrderDo.OrderGoods {
			skuNos = append(skuNos, tmpOrderGoodsDo.SkuNo)
		}
	}
	skuNosJsonByte, _ := json.Marshal(skuNos)

	currentUsedNum := 0
	for _, tmpOrderGoodsDo := range orderDo.OrderGoods {
		currentUsedNum += tmpOrderGoodsDo.Quantity
	}

	// 返还库存
	for _, g := range orderDo.OrderGoods {
		skuInfo, _ := o.goodsSkuRepo.FindBySkuNo(ctx, g.SkuNo)
		if skuInfo == nil {
			return apierr.ErrorDbNotFound("商品不存在:%s", g.SkuNo)
		}
		supCtx := isolationcustomer.WithCustomerAndDisableShopCtx(context.Background(), skuInfo.SupplierCustomerId)
		supSkuInfo, _ := o.supplierGoodsSkuRepo.FindBySkuNo(supCtx, skuInfo.SkuNo)
		err = o.goodsDs.AddStockNew(supCtx, supSkuInfo, g.Quantity, &ds.SupplierGoodsSkuStockLogParams{
			StockLogType: valobj.ModifyGoodsStockTypeOrderRefund,
			UserId:       reqBo.GetOperaId(),
			UserName:     reqBo.GetOperaName(),
		})
		if err != nil {
			o.log.Errorf("返还库存失败,skuNo:%s,quantity:%d,err:%s", g.SkuNo, g.Quantity, err.Error())
			return errors.New("系统繁忙，请稍后再试")
		}
	}

	// 修改使用数量
	err = o.cardBatchCouponRepo.RejectOrderCardCouponUseNum(ctx, couponDo.ID, &commonbo.RejectOrderCardBatchCouponUseInfoBo{
		UseNum:    currentUsedNum,
		UseSkuNos: string(skuNosJsonByte),
	})
	if err != nil {
		return err
	}

	text := couponDo.GetLogRejectContent(currentUsedNum)
	if reqBo.GetOperaId() == 0 {
		text = couponDo.GetLogContentRefund(currentUsedNum)
	}

	// 记录券码操作日志
	_, err = o.cardCouponOperatorLogRepo.Create(ctx, &do.CardCouponOperatorLogDo{
		ID:                0,
		CardBatchCouponID: couponDo.ID,
		CardCouponNumber:  couponDo.CardCouponNumber,
		UserID:            reqBo.GetOperaId(),
		UserName:          reqBo.GetOperaName(),
		Content:           text,
		CreateTime:        helper.GetNow(),
		UpdateTime:        helper.GetNow(),
	})
	if err != nil {
		return err
	}

	// 修改子订单状态为已退款,保存累计退款金额、累计退款积分
	_, err = o.appOrderRepo.RefundSuccess(ctx, &appBo.OrderRefundSuccessBo{
		OrderNumber:                orderDo.OrderNumber,
		RefundAmount:               0,
		RefundGoodsAmount:          0, // 此订单为全退，退款商品的金额等于支付金额
		RefundIntegral:             0,
		RefundCouponDiscountAmount: 0,
		RefundRemark:               reqBo.Remark,
		IsRefundAll:                true,
	})
	if err != nil {
		return err
	}

	// 修改支付单订单状态
	payRefundSuccessBo := &appBo.PayOrderRefundSuccessBo{
		OrderId:        payOrder.ID,
		RefundAmount:   0,
		RefundIntegral: 0,
		OrderStatus:    valobj.PayOrderStatusRefundedAll,
	}
	_, err = o.payOrderRepo.RefundSuccess(ctx, payRefundSuccessBo)

	return nil
}

// InvalidDeliver .
func (o *OrderBiz) InvalidDeliver(ctx context.Context, reqBo *bo.InvalidDeliverBo) error {
	var (
		err       error
		deliverDo *do.OrderDeliverDo
	)
	if reqBo.AdminLoginInfoBo.AdminType.IsSaasSupplier() {
		deliverDo, err = o.orderDeliverRepo.GetV2(isolationcustomer.WithSassDbCtx(ctx), reqBo.DeliverId, reqBo.OrderNo)
	} else {
		deliverDo, err = o.orderDeliverRepo.Get(ctx, reqBo.DeliverId)
	}
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("发货单不存在")
		}
		return err
	}
	if deliverDo.Enable.IsDisable() {
		return nil
	}
	if deliverDo.LogisticsOpCode.IsSign() {
		return apierr.ErrorNotAllow("已签收，不可作废")
	}
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(deliverDo.ShopID))
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, deliverDo.CustomerID)

	orderDo, err := o.orderRepo.GetWithEdges(ctx, deliverDo.OrderNumber, &commonbo.OrderWithEdgeBo{})
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("订单不存在")
		}
		return err
	}
	if orderDo.IsReceived() {
		return apierr.ErrorNotAllow("订单已收货，不可作废")
	}
	if orderDo.FinishTime > 0 {
		return apierr.ErrorNotAllow("订单已完成，不可作废")
	}

	exist, err := o.orderDeliverRepo.ExistDeliver(ctx, deliverDo.ID, deliverDo.OrderNumber)
	if err != nil {
		return err
	}
	return o.trans.Exec(ctx, func(ctx context.Context) error {

		if err = o.orderDeliverRepo.InvalidDeliver(ctx, deliverDo.ID); err != nil {
			return err
		}

		if exist {
			err = o.orderRepo.SetEntityOrderWaitDeliverPartial(ctx, deliverDo.OrderNumber)
		} else {
			err = o.orderRepo.SetEntityOrderWaitDeliver(ctx, deliverDo.OrderNumber)
		}
		if err != nil {
			return err
		}

		_, err = o.orderOperatorLogRepo.Add(ctx, &commonbo.OrderOperatorLogAddBo{
			OrderId:          orderDo.ID,
			OrderNumber:      orderDo.OrderNumber,
			OrderStatus:      valobj.OrderStatusAwaitingShip,
			Content:          fmt.Sprintf("作废发货单-%d", deliverDo.ID),
			OperatorUserType: valobj.OrderOperatorLogUserTypeSupplier,
			OperatorUserId:   reqBo.GetOperaId(),
			OperatorUserName: reqBo.GetOperaName(),
		})
		return err
	})

}
func (o *OrderBiz) AfterSaleOrderCreate(ctx context.Context, afterSaleId int) (string, error) {
	// 查询售后单
	itemDo, err := o.orderAfterSaleRepo.GetWithEdges(ctx, afterSaleId, &commonbo.OrderAfterSaleEdgesBo{
		WithOrderAfterSaleDeliver: false,
		WithOrderAfterSaleGoods:   true,
		WithOrderAfterSaleLog:     false,
	})
	if err != nil {
		if ent.IsNotFound(err) {
			return "", errors.New("售后单不存在")
		}
		return "", err
	}
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(itemDo.ShopID))
	// 组装子订单以及子订单商品信息
	subOrderCreateBo, orderGoodsList, err := o.getSubOrder(ctx, itemDo)
	if err != nil {
		return "", err
	}
	// 组装主订单信息
	mainOrderCreateBo, err := o.getMainOrder(ctx, subOrderCreateBo.PayOrderNumber)
	if err != nil {
		return "", err
	}
	// 组装支付单
	payOrderCreateBo, err := o.getPayOrder(ctx, subOrderCreateBo.PayOrderNumber)
	if err != nil {
		return "", err
	}
	address, err := o.getOrderUserAddress(ctx, itemDo.OrderNumber, subOrderCreateBo.PayOrderNumber)
	if err != nil {
		return "", err
	}
	// 支付单换新
	payOrderNumber := helper.GetPayOrderNumber(o.idGenerator)
	payOrderCreateBo.OrderNumber = payOrderNumber
	payOrderCreateBo.TotalAmount = subOrderCreateBo.TotalAmount
	payOrderCreateBo.SettlementType = valobj.PayOrderSettlementTypeNone
	payOrderCreateBo.TotalPayAmount = 0
	payOrderCreateBo.Num = 1
	// 主订单换新
	mainOrderCreateBo.PayOrderNumber = payOrderNumber
	mainOrderCreateBo.OrderNumber = payOrderNumber
	mainOrderCreateBo.TotalAmount = subOrderCreateBo.TotalAmount
	mainOrderCreateBo.Num = subOrderCreateBo.Num
	mainOrderCreateBo.PayAmount = 0
	mainOrderCreateBo.ExtType = valobj.OrderExtTypeExchangeOrder
	mainOrderCreateBo.OriginalOrderNo = subOrderCreateBo.OriginalOrderNo
	// 子订单换新
	subOrderCreateBo.PayOrderNumber = payOrderNumber
	ctx = isolationcustomer.WithShopIdCtx(ctx, helper.AnyToPtr(mainOrderCreateBo.ShopId))
	ctx = isolationcustomer.WithCustomerIdCtx(ctx, mainOrderCreateBo.CustomerId)
	execFn := func(ctx context.Context) error {
		var (
			orderId     int
			mainOrderId int
		)
		_, err = o.payOrderRepo.Create(ctx, payOrderCreateBo)
		if err != nil {
			return err
		}
		mainOrderId, err = o.orderRepo.Create(ctx, mainOrderCreateBo)
		if err != nil {
			return err
		}
		orderId, err = o.orderRepo.Create(ctx, subOrderCreateBo)
		if err != nil {
			return err
		}
		for _, orderGoods := range orderGoodsList {
			orderGoods.OrderId = orderId
			_, err = o.orderGoodsRepo.Create(ctx, orderGoods)
			if err != nil {
				return err
			}
		}
		_, err = o.orderOperatorLogRepo.Add(ctx, &commonbo.OrderOperatorLogAddBo{
			OrderId:          orderId,
			OrderNumber:      subOrderCreateBo.OrderNumber,
			OrderStatus:      valobj.OrderStatusAfterSaleFinish,
			Content:          valobj.OrderStatusAfterSaleFinish.GetOperatorContent() + "_自动创建订单",
			OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
			OperatorUserId:   0,
			OperatorUserName: "系统",
		})

		for _, tmpDo := range itemDo.OrderAfterSaleGoods {
			if tmpDo.Type.IsExchange() {
				skuInfo, _ := o.goodsSkuRepo.FindBySkuNo(ctx, tmpDo.SkuNo)
				supplierCtx := isolationcustomer.WithCustomerAndDisableShopCtx(context.Background(), skuInfo.SupplierCustomerId)
				supSkuInfo, _ := o.supplierGoodsSkuRepo.FindBySkuNo(supplierCtx, tmpDo.SkuNo)
				err = o.goodsDs.ReduceStockNew(supplierCtx, supSkuInfo, tmpDo.GoodsNum, &ds.SupplierGoodsSkuStockLogParams{
					StockLogType: valobj.ModifyGoodsStockTypeOrder,
					UserId:       authtools.GetLoginUserId(ctx),
					UserName:     authtools.GetLoginUserName(ctx),
				})
				if err != nil {
					return err
				}
			}
		}

		if err != nil {
			return err
		}
		for index, addressDo := range address {
			addressDo.OrderID = orderId
			addressDo.OrderNumber = subOrderCreateBo.OrderNumber
			if index == 0 {
				addressDo.OrderID = mainOrderId
				addressDo.OrderNumber = mainOrderCreateBo.OrderNumber
			}
			_, err = o.orderUserAddressRepo.Create(ctx, addressDo)
			if err != nil {
				return err
			}
		}
		return nil
	}

	if err = execFn(ctx); err != nil {
		return "", err
	}

	return subOrderCreateBo.OrderNumber, nil
}

func (o *OrderBiz) getOrderUserAddress(ctx context.Context, orderNumber string, payOrderNumber string) ([]*do.OrderUserAddressDo, error) {
	userAddressDos, err := o.orderUserAddressRepo.FindByOrderNumber(ctx, orderNumber, payOrderNumber)
	if err != nil {
		return nil, err
	}
	orderUserAddressDos := make([]*do.OrderUserAddressDo, 0)
	for _, addressDo := range userAddressDos {
		d := &do.OrderUserAddressDo{}
		err = copier.Copy(d, addressDo)
		if err != nil {
			return nil, err
		}
		orderUserAddressDos = append(orderUserAddressDos, d)
	}
	return orderUserAddressDos, nil
}

func (o *OrderBiz) getPayOrder(ctx context.Context, orderNumber string) (*commonbo.PayOrderCreateBo, error) {

	payOrderDo, err := o.payOrderRepo.FindByOrderNumber(ctx, orderNumber)
	if err != nil {
		return nil, err
	}
	payOrderCreateBo := &commonbo.PayOrderCreateBo{}
	err = copier.Copy(payOrderCreateBo, payOrderDo)
	if err != nil {
		return nil, err
	}
	// 支付单
	//payOrderCreateBo.MiniOrderNo = payOrderNumber
	payOrderCreateBo.Status = valobj.PayOrderStatusPaid
	payOrderCreateBo.TotalPayAmount = 0
	//payOrderCreateBo.TotalAmount = totalAmount
	payOrderCreateBo.TotalPayIntegral = 0
	payOrderCreateBo.CouponDiscountAmount = 0
	payOrderCreateBo.IntegralDiscountAmount = 0
	payOrderCreateBo.CouponId = 0
	payOrderCreateBo.CouponCodeId = 0
	return payOrderCreateBo, nil
}
func (o *OrderBiz) getMainOrder(ctx context.Context, orderNumber string) (*commonbo.OrderCreateBo, error) {
	// 查询主订单
	mainOrderDo, err := o.orderRepo.GetWithEdges(ctx, orderNumber, &commonbo.OrderWithEdgeBo{
		WithGoods:       false,
		WithUserAddress: false,
		WithOperatorLog: false,
	})
	if err != nil {
		return nil, err
	}
	mainOrderCreateBo := &commonbo.OrderCreateBo{}
	err = copier.Copy(mainOrderCreateBo, mainOrderDo)
	if err != nil {
		return nil, err
	}
	// 主订单
	//mainOrderCreateBo.PayOrderNumber = payOrderNumber
	//mainOrderCreateBo.MiniOrderNo = payOrderNumber
	mainOrderCreateBo.Status = valobj.OrderStatusPaySuccess
	//mainOrderCreateBo.TotalAmount = totalAmount
	mainOrderCreateBo.PayIntegral = 0
	mainOrderCreateBo.CouponDiscountAmount = 0
	mainOrderCreateBo.IntegralDiscountAmount = 0
	mainOrderCreateBo.PayAmount = 0
	//mainOrderCreateBo.Num = exchangeGoodsDo.GoodsNum
	return mainOrderCreateBo, nil
}
func (o *OrderBiz) getSubOrder(ctx context.Context, itemDo *do.OrderAfterSaleDo) (*commonbo.OrderCreateBo, []*commonbo.OrderGoodsCreateBo, error) {

	// 查询子订单
	subOrderDo, err := o.orderRepo.GetWithEdges(ctx, itemDo.OrderNumber, &commonbo.OrderWithEdgeBo{
		WithGoods:       true,
		WithUserAddress: false,
		WithOperatorLog: false,
	})
	if err != nil {
		return nil, nil, err
	}

	subOrderCreateBo := &commonbo.OrderCreateBo{}
	err = copier.Copy(subOrderCreateBo, subOrderDo)
	if err != nil {
		return nil, nil, err
	}
	totalAmount := decimal.Zero
	//payAmount := decimal.Zero
	goodsNum := 0
	// 子订单
	//subOrderCreateBo.PayOrderNumber = payOrderNumber
	subOrderCreateBo.OrderNumber = helper.GetOrderNumber(o.idGenerator)
	subOrderCreateBo.Status = valobj.OrderStatusAwaitingShip
	subOrderCreateBo.PayIntegral = 0
	subOrderCreateBo.CouponDiscountAmount = 0
	subOrderCreateBo.IntegralDiscountAmount = 0
	subOrderCreateBo.ExtType = valobj.OrderExtTypeExchangeOrder
	subOrderCreateBo.OriginalOrderNo = subOrderDo.OrderNumber
	if subOrderDo.OriginalOrderNo != "" {
		subOrderCreateBo.OriginalOrderNo = subOrderDo.OriginalOrderNo
	}

	// 申请得商品
	returnGoodsDos := itemDo.GetSaleGoodsByType(valobj.AfterSaleGoodsTypeApply)
	if len(returnGoodsDos) == 0 {
		return nil, nil, errors.New("售后单置换的商品不存在")
	}
	exchangeGoodsDos := itemDo.GetSaleGoodsByType(valobj.AfterSaleGoodsTypeExchange)
	if len(exchangeGoodsDos) == 0 {
		return nil, nil, errors.New("售后单置换的商品不存在")
	}
	exchangeGoodsDoMaps := slice.KeyBy(exchangeGoodsDos, func(item *do.OrderAfterSaleGoodsDo) int {
		return item.ExchangeID
	})

	orderDeliverExpireTime := valobj.SupplierGoodeDeliverTimelineNotSet.GetExpireTime()
	orderGoodsList := make([]*commonbo.OrderGoodsCreateBo, 0)
	for _, returnGoodsDo := range returnGoodsDos {
		if exchangeGoodsDo, ok := exchangeGoodsDoMaps[returnGoodsDo.ID]; ok {
			var (
				goodsSkuInfo *do.GoodsSkuDo
				brandInfo    *do.GoodsBrandDo
				categoryName = ""
			)
			// 获取原订单商品信息
			orderGoodsDo := subOrderDo.GetOrderGoodsBySkuNO(returnGoodsDo.SkuNo)
			if orderGoodsDo == nil {
				return nil, nil, errors.New("订单商品不存在")
			}
			// 获取要换货得商品信息
			goodsSkuInfo, err = o.goodsSkuRepo.FindBySkuNo(ctx, exchangeGoodsDo.SkuNo)
			if err != nil {
				return nil, nil, err
			}
			newCtx := isolationcustomer.WithCustomerAndDisableShopCtx(context.Background(), goodsSkuInfo.SupplierCustomerId)
			supplierSkuDo, err := o.supplierGoodsSkuRepo.FindBySkuNo(newCtx, goodsSkuInfo.SkuNo)
			if err != nil {
				return nil, nil, err
			}
			if supplierSkuDo == nil {
				return nil, nil, apierr.ErrorException("换货商品不存在")
			}
			supplierGoodsDo, err := o.supplierGoodsRepo.Get(newCtx, supplierSkuDo.GoodsID)
			if err != nil {
				return nil, nil, err
			}
			if supplierGoodsDo.DeliverTimeline.GetExpireTime() < orderDeliverExpireTime {
				orderDeliverExpireTime = supplierGoodsDo.DeliverTimeline.GetExpireTime()
			}

			brandInfo, err = o.brandRepo.GetOneByID(ctx, goodsSkuInfo.Goods.BrandID)
			if err != nil {
				return nil, nil, err
			}
			categoryName, err = o.goodsCategoryDs.GetTreeLineJoin(ctx, goodsSkuInfo.Goods.CategoryID, "/")
			if err != nil {
				return nil, nil, err
			}
			orderGoods := &commonbo.OrderGoodsCreateBo{}
			err = copier.Copy(orderGoods, orderGoodsDo)
			if err != nil {
				return nil, nil, err
			}

			subOrderCreateBo.DeliverExpireTime = orderDeliverExpireTime

			// 订单商品信息
			orderGoods.OrderNumber = subOrderCreateBo.OrderNumber
			orderGoods.OrderId = 0
			orderGoods.GoodsId = goodsSkuInfo.Goods.ID
			orderGoods.GoodsName = goodsSkuInfo.GetGoodsName()
			orderGoods.GoodsSkuId = goodsSkuInfo.Id
			orderGoods.GoodsSkuName = goodsSkuInfo.Name
			orderGoods.SkuNo = goodsSkuInfo.SkuNo

			goodsImage := goodsSkuInfo.Image
			if goodsImage == "" {
				goodsImage = goodsSkuInfo.Goods.Image
			}
			orderGoods.GoodsImage = goodsImage
			orderGoods.CategoryId = goodsSkuInfo.Goods.CategoryID
			orderGoods.Quantity = exchangeGoodsDo.GoodsNum
			orderGoods.SalePrice = goodsSkuInfo.SalePrice
			//orderGoods.OriginPrice = goodsSkuInfo.SupplierPrice
			//orderGoods.ChannelPrice = goodsSkuInfo.ChannelPrice
			orderGoods.OriginPrice = supplierSkuDo.SupplierPrice
			orderGoods.ChannelPrice = supplierSkuDo.ChannelPrice

			orderGoods.SupplierPrice = goodsSkuInfo.SupplierPrice
			orderGoods.RealSupplierPrice = supplierSkuDo.SupplierPrice
			orderGoods.BrandName = brandInfo.Name
			orderGoods.CategoryName = categoryName
			orderGoods.ProductId = ""
			orderGoods.CouponDiscountAmount = 0
			orderGoods.PayAmount = 0
			orderGoods.IntegralDiscountAmount = 0
			orderGoods.PayIntegral = 0
			orderGoods.ActualDiscountIntegral = 0

			orderGoods.OriginalSkuNo = orderGoodsDo.SkuNo
			if orderGoodsDo.OriginalSkuNo != "" {
				orderGoods.OriginalSkuNo = orderGoodsDo.OriginalSkuNo
			}
			orderGoodsList = append(orderGoodsList, orderGoods)

			totalAmount = totalAmount.Add(decimal.NewFromFloat(goodsSkuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(exchangeGoodsDo.GoodsNum))))
			//payAmount = payAmount.Add(decimal.NewFromFloat(returnGoodsDo.RefundAmount))
			goodsNum += exchangeGoodsDo.GoodsNum
		}
	}
	subOrderCreateBo.Num = goodsNum
	subOrderCreateBo.PayAmount = 0
	subOrderCreateBo.TotalAmount = totalAmount.InexactFloat64()
	return subOrderCreateBo, orderGoodsList, nil

}
