package supplierbiz

//
//import (
//	"cardMall/internal/biz/ds"
//	"cardMall/internal/conf"
//	"cardMall/internal/data"
//	"cardMall/internal/data/adminrepositoryimpl"
//	"cardMall/internal/data/apprepositoryimpl"
//	"cardMall/internal/data/repositoryimpl"
//	"cardMall/internal/data/rpcimpl"
//	log2 "cardMall/internal/pkg/log"
//	pkgLog "cardMall/internal/pkg/log"
//	"context"
//	"flag"
//	"github.com/go-kratos/kratos/v2/config"
//	"github.com/go-kratos/kratos/v2/config/file"
//	"github.com/go-kratos/kratos/v2/log"
//	"github.com/go-kratos/kratos/v2/middleware/tracing"
//	"os"
//	"testing"
//)
//
//var (
//	flagconf2           string
//	supplierbizOrderBiz *OrderBiz
//)
//
//func init() {
//	flag.StringVar(&flagconf2, "conf", "./configs", "config path, eg: -conf config.yaml")
//}
//func init2() {
//	logger := log.With(log.NewStdLogger(os.Stdout),
//		"ts", log.DefaultTimestamp,
//		"caller", log.DefaultCaller,
//		//"service.id", id,
//		//"service.name", Name,
//		//"service.version", Version,
//		"trace.id", tracing.TraceID(),
//		"span.id", tracing.SpanID(),
//		//"request.id", RequestId(),
//	)
//
//	c := config.New(
//		config.WithSource(
//			file.NewSource(flagconf2),
//		),
//	)
//	defer c.Close()
//
//	if err := c.Load(); err != nil {
//		panic(err)
//	}
//
//	var bc conf.Bootstrap
//	if err := c.Scan(&bc); err != nil {
//		panic(err)
//	}
//
//	logHelper := log.NewHelper(logger)
//	FileLogger := pkgLog.NewFileLogger(nil, nil, nil, nil, nil, nil)
//
//	initApp(bc.Data, logHelper, &bc, FileLogger)
//}
//
//func TestOrderBiz_AfterSaleOrderCreate(t *testing.T) {
//	init2()
//	supplierbizOrderBiz.AfterSaleOrderCreate(context.Background(), 226)
//}
//func initApp(confData *conf.Data, logHelper *log.Helper, bootstrap *conf.Bootstrap, fileLogger *log2.FileLogger) {
//	dataData, _, err := data.NewData(confData, logHelper)
//	if err != nil {
//		return
//	}
//
//	transactionRepo := data.NewTransaction(dataData, logHelper)
//
//	goodsCategoryRepo := repositoryimpl.NewGoodsCategoryRepoImpl(dataData)
//	goodsCategoryBrandRepo := repositoryimpl.NewGoodsCategoryBrandRepoImpl(dataData)
//	repositoryGoodsCategoryBrandRepo := apprepositoryimpl.NewGoodsCategoryBrandRepoImpl(dataData)
//	repositoryGoodsCategoryRepo := apprepositoryimpl.NewGoodsCategoryRepoImpl(dataData)
//	goodsBrandRepo := apprepositoryimpl.NewGoodsBrandRepoImpl(dataData)
//	goodsCategoryDs := ds.NewGoodsCategoryDs(goodsCategoryRepo, goodsCategoryBrandRepo, repositoryGoodsCategoryBrandRepo, repositoryGoodsCategoryRepo, goodsBrandRepo, dataData)
//	repositoryGoodsBrandRepo := repositoryimpl.NewGoodsBrandRepoImpl(dataData)
//	goodsRepo := repositoryimpl.NewGoodsRepoImpl(dataData)
//
//	userRepo := adminrepositoryimpl.NewUserRepoImpl(dataData)
//
//	userIntegralLogRepo := adminrepositoryimpl.NewUserIntegralLogRepoImpl(dataData)
//
//	goodsSkuRepo := repositoryimpl.NewGoodsSkuRepoImpl(dataData)
//	supplierGoodsSkuRepo := repositoryimpl.NewSupplierGoodsSkuRepoImpl(dataData)
//
//	repositoryCouponCodeRepo := apprepositoryimpl.NewCouponCodeRepoImpl(dataData)
//	repositoryCouponRepo := apprepositoryimpl.NewCouponRepoImpl(dataData)
//	orderGoodsRepo := repositoryimpl.NewOrderGoodsRepoImpl(dataData)
//	repositoryGoodsRepo := apprepositoryimpl.NewGoodsRepoImpl(dataData)
//	orderDeliverRepo := repositoryimpl.NewOrderDeliverRepoImpl(dataData)
//	orderOperatorLogRepo := repositoryimpl.NewOrderOperatorLogImpl(dataData)
//	orderLogisticsRepo := apprepositoryimpl.NewOrderLogisticsRepoImpl(dataData)
//	repositoryOrderAfterSaleRepo := repositoryimpl.NewOrderAfterSaleRepoImpl(dataData)
//
//	repositoryOrderRepo := apprepositoryimpl.NewOrderRepoImpl(dataData)
//	repositoryOrderGoodsRepo := apprepositoryimpl.NewOrderGoodsRepoImpl(dataData)
//	repositoryPayOrderRepo := apprepositoryimpl.NewPayOrderRepoImpl(dataData)
//	orderUserAddressRepo := repositoryimpl.NewOrderUserAddressRepoImpl(dataData)
//	orderDs := ds.NewOrderDs(repositoryPayOrderRepo, repositoryOrderRepo, goodsRepo, goodsSkuRepo, orderGoodsRepo, orderUserAddressRepo, orderOperatorLogRepo)
//	couponDs := ds.NewCouponDs(repositoryCouponRepo, repositoryCouponCodeRepo)
//	integralDs := ds.NewIntegralDs(userIntegralLogRepo, userRepo)
//	supplierGoodsStockLogRepo := repositoryimpl.NewSupplierGoodsStockLogRepoImpl(dataData)
//	goodsDs := ds.NewGoodsDs(supplierGoodsSkuRepo, supplierGoodsStockLogRepo)
//	payRepo := rpcimpl.NewPayRepoImpl(dataData, bootstrap, fileLogger)
//	orderRefundLogRepo := apprepositoryimpl.NewOrderRefundLogRepoImpl(dataData)
//	payMerchantRepo := apprepositoryimpl.NewPayMerchantRepoImpl(dataData)
//	payConfigRepo := apprepositoryimpl.NewPayConfigRepoImpl(dataData)
//
//	orderRepo2 := repositoryimpl.NewOrderRepoImpl(dataData)
//	payDs := ds.NewPayDs(payRepo, repositoryPayOrderRepo, orderRefundLogRepo, payMerchantRepo, payConfigRepo, bootstrap, fileLogger)
//	refundDs := ds.NewRefundDs(repositoryOrderRepo, repositoryOrderGoodsRepo, repositoryGoodsRepo, repositoryPayOrderRepo, orderRepo2, orderDs, couponDs, integralDs, goodsDs, payDs, logHelper)
//	orderAfterSaleGoodsRepo := repositoryimpl.NewOrderAfterSaleGoodsRepoImpl(dataData)
//	kdRepo := repositoryimpl.NewKdRepoImpl(dataData)
//	orderDeliverGoodsRepo := repositoryimpl.NewOrderDeliverGoodsRepoImpl(dataData)
//	repositoryOrderLogisticsRepo := repositoryimpl.NewOrderLogisticsRepoImpl(dataData)
//	goodsSkuV2Repo := repositoryimpl.NewGoodsSkuV2RepoImpl(dataData)
//	caiNiaoRepo := rpcimpl.NewCaiNiaoRepoImpl(bootstrap, fileLogger)
//	repositoryOrderDeliverRepo := apprepositoryimpl.NewOrderDeliverRepoImpl(dataData)
//	logisticsDs := ds.NewLogisticsDs(caiNiaoRepo, orderDeliverRepo, repositoryOrderLogisticsRepo, orderLogisticsRepo, repositoryOrderDeliverRepo, repositoryOrderRepo)
//	deliverBaseBiz := NewDeliverBaseBiz(logHelper, orderRepo2, orderDeliverRepo, kdRepo, supplierGoodsSkuRepo, orderDeliverGoodsRepo, repositoryOrderLogisticsRepo, transactionRepo, goodsSkuV2Repo, orderOperatorLogRepo, orderAfterSaleGoodsRepo, logisticsDs)
//
//	supplierbizOrderBiz = NewOrderBiz(orderRepo2, orderDeliverGoodsRepo, orderDeliverRepo, orderUserAddressRepo, transactionRepo, orderOperatorLogRepo, deliverBaseBiz, refundDs, goodsSkuRepo, repositoryPayOrderRepo, orderGoodsRepo, repositoryGoodsBrandRepo, goodsCategoryDs, repositoryOrderAfterSaleRepo, logHelper)
//
//}
