package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
)

type AfterSaleAuditCommonBo struct {
	Reason string
	*AfterSaleOperaBo
}

// Validate 验证
func (a AfterSaleAuditCommonBo) Validate(mustReason bool) error {
	if mustReason && a.Reason == "" {
		return apierr.ErrorParam("请填写操作备注")
	}
	if helper.Utf8StrLength(a.Reason) > constants.AfterSaleAuditRemarkMaxLength {
		return apierr.ErrorParam("操作备注不能超过%d个字符", constants.AfterSaleAuditRemarkMaxLength)
	}
	return nil
}
