package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
)

type OrderReturnAddressSaveBo struct {
	Id           int    // ID
	ContactName  string // 联系人
	ContactPhone string // 手机号
	Address      string // 快递地址
	CreateTime   string // 创建时间
	Title        string // 退款地址名称
	SupplierId   int

	// SupplierId int // 供应商ID

	*AfterSaleOperaBo
}

// Validate 校验
func (o *OrderReturnAddressSaveBo) Validate() error {
	if helper.Utf8StrLength(o.Address) > constants.AfterSaleAddressMaxLen {
		return apierr.ErrorParam("退货地址不能超过%d个字符", constants.AfterSaleAddressMaxLen)
	}
	if o.ContactName == "" {
		return apierr.ErrorParam("联系人不能为空")
	}
	if !helper.IsMobileFormat(o.ContactPhone) {
		return apierr.ErrorParam("手机号格式不正确")
	}
	if o.Title == "" {
		return apierr.ErrorParam("退货地址名称不能为空")
	}
	if o.Address == "" {
		return apierr.ErrorParam("退货地址不能为空")
	}
	if helper.Utf8StrLength(o.Title) > constants.AfterSaleTitleMaxLen {
		return apierr.ErrorParam("退货地址名称不能超过%d个字符", constants.AfterSaleTitleMaxLen)
	}
	return nil
}
