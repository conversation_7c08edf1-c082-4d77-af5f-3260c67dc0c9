package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"github.com/duke-git/lancet/v2/slice"
)

type SaveTransportBo struct {
	Id           int                            // 运费模版id，修改时必传
	Name         string                         // 运费模版名称
	Description  string                         // 运费模版描述
	KdId         int                            // 快递公司ID
	PricingMode  valobj.TransportPricingModeObj // 计费模式
	DefaultNum   float64                        // 默认数量
	DefaultPrice float64                        // 默认运费
	AddNum       float64                        // 增加数量
	AddPrice     float64                        // 增加运费
	Items        []*TransportItem               // 运费模板详情
}

// GetDefaultPrice 获取默认运费
func (s *SaveTransportBo) GetDefaultPrice() float64 {
	if s.DefaultPrice < 0 {
		return 0
	}
	return s.DefaultPrice
}

func (s *SaveTransportBo) GetDefaultNum() float64 {
	if s.DefaultNum < 0 {
		return 0
	}
	return s.DefaultNum
}

// GetAddPrice 获取增加运费
func (s *SaveTransportBo) GetAddPrice() float64 {
	if s.AddPrice < 0 {
		return 0
	}
	return s.AddPrice
}

// GetAddNum 获取增加数量
func (s *SaveTransportBo) GetAddNum() float64 {
	if s.AddNum < 0 {
		return 0
	}
	return s.AddNum
}

// Validate 校验
func (s *SaveTransportBo) Validate() error {
	if s.Name == "" {
		return apierr.ErrorParam("运费模版名称不能为空")
	}
	if helper.Utf8StrLength(s.Name) > constants.TransportNameMaxLen {
		return apierr.ErrorParam("运费模版名称不能超过%d个字符", constants.TransportNameMaxLen)
	}
	if helper.Utf8StrLength(s.Description) > constants.TransportDescriptionMaxLen {
		return apierr.ErrorParam("运费模版描述不能超过%d个字符", constants.TransportDescriptionMaxLen)
	}
	if s.KdId == 0 {
		return apierr.ErrorParam("快递公司ID不能为空")
	}
	if s.AddPrice >= constants.TransportMaxAddPrice {
		return apierr.ErrorParam("增加运费不能超过%f元", constants.TransportMaxAddPrice)
	}
	if s.AddPrice < 0 {
		return apierr.ErrorParam("增加运费不能小于0")
	}
	if s.DefaultNum < 0 {
		return apierr.ErrorParam("默认数量不能小于0")
	}
	if s.AddNum < 0 {
		return apierr.ErrorParam("增加数量不能小于0")
	}

	if s.PricingMode == 0 {
		return apierr.ErrorParam("计费模式不能为空")
	}
	if !s.PricingMode.Exists() {
		return apierr.ErrorParam("计费模式不存在")
	}
	if !s.PricingMode.IsEnable() {
		return apierr.ErrorParam("计费模式未启用")
	}
	if s.PricingMode.IsFree() {
		if len(s.Items) != 0 {
			return apierr.ErrorParam("免运费时，设置自定义运费无效")
		}
	}

	cityIds := make([]int, 0)
	for _, item := range s.Items {
		if len(item.Items) == 0 {
			return apierr.ErrorParam("自定义运费模版城市不能为空")
		}
		for _, v := range item.Items {
			if slice.Contain(cityIds, v.CityId) {
				return apierr.ErrorParam("城市不能重复设置规则")
			}
			cityIds = append(cityIds, v.CityId)
		}
		if err := item.Validate(); err != nil {
			return err
		}
	}

	return nil
}

type TransportItem struct {
	DefaultNum   float64              // 默认数量
	DefaultPrice float64              // 默认运费
	AddNum       float64              // 增加数量
	AddPrice     float64              // 增加运费
	Items        []*TransportCityItem // 运费模板详情
}

// Validate 校验
func (t *TransportItem) Validate() error {
	if t.AddPrice >= constants.TransportMaxAddPrice {
		return apierr.ErrorParam("增加运费不能超过%f元", constants.TransportMaxAddPrice)
	}
	if t.AddPrice < 0 {
		return apierr.ErrorParam("增加运费不能小于0")
	}
	if t.DefaultNum < 0 {
		return apierr.ErrorParam("默认数量不能小于0")
	}
	if t.AddNum < 0 {
		return apierr.ErrorParam("增加数量不能小于0")
	}
	return nil
}

// GetDefaultPrice 获取默认运费
func (t *TransportItem) GetDefaultPrice() float64 {
	if t.DefaultPrice < 0 {
		return 0
	}
	return t.DefaultPrice
}

func (t *TransportItem) GetDefaultNum() float64 {
	if t.DefaultNum < 0 {
		return 0
	}
	return t.DefaultNum
}

// GetAddPrice 获取增加运费
func (t *TransportItem) GetAddPrice() float64 {
	if t.AddPrice < 0 {
		return 0
	}
	return t.AddPrice
}

// GetAddNum 获取增加数量
func (t *TransportItem) GetAddNum() float64 {
	if t.AddNum < 0 {
		return 0
	}
	return t.AddNum
}

// GetCityIds 获取城市id
func (t *TransportItem) GetCityIds() []int {
	cityIds := make([]int, 0)
	for _, item := range t.Items {
		cityIds = append(cityIds, item.CityId)
	}
	return cityIds
}

// GetCityIdsStr 获取城市id
func (t *TransportItem) GetCityIdsStr() string {
	cityIds := make([]int, 0)
	for _, item := range t.Items {
		cityIds = append(cityIds, item.CityId)
	}
	return slice.Join(t.GetCityIds(), ",")
}

type TransportCityItem struct {
	CityId   int    // 省份
	CityName string // 省份
}
