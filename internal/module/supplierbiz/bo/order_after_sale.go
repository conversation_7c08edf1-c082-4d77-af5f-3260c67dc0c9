package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"encoding/json"
)

type AfterSaleApproveBo struct {
	*AfterSaleOperaBo

	Reason       string
	ContactName  string // 联系人【退货/换货时必填】
	ContactPhone string // 手机号【退货/换货时必填】
	Address      string // 物流单号【退货/换货时必填】
}

// ValidateAddress 验证address
func (a AfterSaleApproveBo) ValidateAddress() error {
	if a.OperaName != "系统" {
		if a.ContactName == "" || a.ContactPhone == "" || a.Address == "" {
			return apierr.ErrorParam("请填写退货/换货地址信息")
		}
	}
	return nil
}

// Validate 验证
func (a AfterSaleApproveBo) Validate() error {
	//if a.Reason == "" {
	//	return apierr.ErrorParam("请填写操作备注")
	//}
	if helper.Utf8StrLength(a.Reason) > constants.AfterSaleAuditRemarkMaxLength {
		return apierr.ErrorParam("操作备注不能超过%d个字符", constants.AfterSaleAuditRemarkMaxLength)
	}
	return nil
}

type AfterSaleRefundBo struct {
	*AfterSaleOperaBo
}

type AfterSaleSetReceiveAddressBo struct {
	*AfterSaleOperaBo
	ContactName  string
	ContactPhone string
	Address      string
}

type AfterSaleRefuseBo struct {
	Reason string
	*AfterSaleOperaBo
}

// Validate 验证
func (a AfterSaleRefuseBo) Validate() error {
	if a.Reason == "" {
		return apierr.ErrorParam("请填写操作备注")
	}
	if helper.Utf8StrLength(a.Reason) > constants.AfterSaleAuditRemarkMaxLength {
		return apierr.ErrorParam("操作备注不能超过%d个字符", constants.AfterSaleAuditRemarkMaxLength)
	}
	return nil
}

type AfterSaleConfirmReceiveBo struct {
	*AfterSaleOperaBo
}

type AfterSaleDeliverBo struct {
	ContactName  string   // 联系人
	ContactPhone string   // 手机号
	ExpressNo    string   // 物流单号
	KdName       string   // 物流公司
	KdCode       string   // 物流code
	Images       []string // 图片
	Reason       string   // 操作备注
	*AfterSaleOperaBo
}

// GetImagesJson 获取图片json
func (a AfterSaleDeliverBo) GetImagesJson() string {
	data, _ := json.Marshal(a.Images)
	return string(data)
}

type CreateExchangeOrderBo struct {
	*AfterSaleOperaBo
}

type AfterSaleOperaBo struct {
	Id      int
	OrderNo string
	*bo.AdminLoginInfoBo
}

func (a AfterSaleOperaBo) CheckOrderNo() error {
	if a.OrderNo == "" {
		return apierr.ErrorParam("订单号不能为空")
	}
	return nil
}

func (a AfterSaleOperaBo) GetOrderNo() string {
	return a.OrderNo
}

func (a AfterSaleOperaBo) GetId() int {
	return a.Id
}

func (a AfterSaleOperaBo) GetSupplierId() int {
	return a.GetLoginSupplierId()
}

func (a AfterSaleOperaBo) GetAdminId() int {
	return a.OperaId
}

func (a AfterSaleOperaBo) GetAdminName() string {
	return a.OperaName
}
func (a AfterSaleOperaBo) GetAdminType() valobj.AdminTypeObj {
	return a.AdminType
}

type AfterSalePermission interface {
	GetId() int
	GetOrderNo() string
	GetSupplierId() int
	GetAdminType() valobj.AdminTypeObj
}
