package supplierbiz

import (
	"cardMall/api/apierr"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	bizrepository "cardMall/internal/biz/repository"
	"cardMall/internal/data/ent"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

type OrderBaseBiz struct {
	orderRepo                 bizrepository.OrderRepo
	orderDeliverGoodsRepo     bizrepository.OrderDeliverGoodsRepo
	siteRepo                  bizrepository.SiteRepo
	orderGoodsRepo            bizrepository.OrderGoodsRepo
	orderUserAddressRepo      bizrepository.OrderUserAddressRepo
	supplierRepo              bizrepository.SupplierRepo
	orderCardGiftRepo         bizrepository.OrderCardGiftRepo
	bizPayOrderRepo           bizrepository.PayOrderRepo
	orderOperatorLogRepo      bizrepository.OrderOperatorLogRepo
	orderAfterSaleDeliverRepo bizrepository.OrderAfterSaleDeliverRepo
	orderAfterSaleGoodsRepo   bizrepository.OrderAfterSaleGoodsRepo
	orderAfterSaleLogRepo     bizrepository.OrderAfterSaleLogRepo
}

func NewOrderBaseBiz(orderRepo bizrepository.OrderRepo, orderDeliverGoodsRepo bizrepository.OrderDeliverGoodsRepo, siteRepo bizrepository.SiteRepo, orderGoodsRepo bizrepository.OrderGoodsRepo, orderUserAddressRepo bizrepository.OrderUserAddressRepo, supplierRepo bizrepository.SupplierRepo, orderCardGiftRepo bizrepository.OrderCardGiftRepo, bizPayOrderRepo bizrepository.PayOrderRepo, orderOperatorLogRepo bizrepository.OrderOperatorLogRepo, orderAfterSaleDeliverRepo bizrepository.OrderAfterSaleDeliverRepo, orderAfterSaleGoodsRepo bizrepository.OrderAfterSaleGoodsRepo, orderAfterSaleLogRepo bizrepository.OrderAfterSaleLogRepo) *OrderBaseBiz {
	return &OrderBaseBiz{orderRepo: orderRepo, orderDeliverGoodsRepo: orderDeliverGoodsRepo, siteRepo: siteRepo, orderGoodsRepo: orderGoodsRepo, orderUserAddressRepo: orderUserAddressRepo, supplierRepo: supplierRepo, orderCardGiftRepo: orderCardGiftRepo, bizPayOrderRepo: bizPayOrderRepo, orderOperatorLogRepo: orderOperatorLogRepo, orderAfterSaleDeliverRepo: orderAfterSaleDeliverRepo, orderAfterSaleGoodsRepo: orderAfterSaleGoodsRepo, orderAfterSaleLogRepo: orderAfterSaleLogRepo}
}

//	订单发货数量获取并写入dos
//
// 将订单SKU已经发货的数据写入 do.OrderDo.OrderGoods.DeliverQuantity
func (o *OrderBaseBiz) FixDeliverQuantity(ctx context.Context, dos ...*do.OrderDo) error {
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) int {
		return item.ID
	})

	deliverGoodsDos, err := o.orderDeliverGoodsRepo.FindByOrderId(ctx, ids...)
	if err != nil {
		return err
	}

	deliverMap := make(map[string]int)
	for _, item := range deliverGoodsDos {
		mapKey := item.GetDeliverUniqueKey()
		v, ok := deliverMap[mapKey]
		if ok {
			deliverMap[mapKey] = v + item.Quantity
		} else {
			deliverMap[mapKey] = item.Quantity
		}
	}

	for _, orderDo := range dos {
		for _, orderGoodsDo := range orderDo.OrderGoods {
			v, ok := deliverMap[orderGoodsDo.GetDeliverUniqueKey()]
			if !ok {
				continue
			}
			if v > orderGoodsDo.Quantity {
				orderGoodsDo.DeliverQuantity = orderGoodsDo.Quantity
				deliverMap[orderGoodsDo.GetDeliverUniqueKey()] = v - orderGoodsDo.Quantity
			} else {
				orderGoodsDo.DeliverQuantity = deliverMap[orderGoodsDo.GetDeliverUniqueKey()]
			}
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixPayOrderData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) string { return item.PayOrderNumber })
	ids = slice.Unique(ids)

	tmpDos, err := o.bizPayOrderRepo.ListByOrderNumber(ctx, ids)
	if err != nil {
		return err
	}
	dosMap := slice.KeyBy(tmpDos, func(item *do.PayOrderDo) string { return item.OrderNumber })
	for _, item := range dos {
		if _, ok := dosMap[item.PayOrderNumber]; ok {
			item.PayOrderInfo = dosMap[item.PayOrderNumber]
		}
	}
	return nil
}
func (o *OrderBaseBiz) FixCardGiftData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) string { return item.OrderNumber })
	ids = slice.Unique(ids)

	tmpDos, err := o.orderCardGiftRepo.FindByOrderNumbers(ctx, ids)
	if err != nil {
		return err
	}
	dosMap := slice.GroupWith(tmpDos, func(item *do.OrderCardGiftDo) string { return item.OrderNumber })

	for _, item := range dos {
		if _, ok := dosMap[item.OrderNumber]; ok {
			item.OrderCardGiftInfo = dosMap[item.OrderNumber]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixSupplierData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) int { return item.SupplierID })
	ids = slice.Unique(ids)

	tmpDos, err := o.supplierRepo.Find(ctx, isolationcustomer.GetCustomerIdZero(ctx), ids...)
	if err != nil {
		return err
	}
	dosMap := slice.KeyBy(tmpDos, func(item *do.SupplierDo) int { return item.ID })
	for _, item := range dos {
		if _, ok := dosMap[item.SupplierID]; ok {
			item.SupplierInfo = dosMap[item.SupplierID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixSiteData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) int { return item.GetSiteId() })
	ids = slice.Unique(ids)

	siteDos, err := o.siteRepo.Find(ctx, ids...)
	if err != nil {
		return err
	}
	siteMap := slice.KeyBy(siteDos, func(item *do.SiteDo) int { return item.ID })
	for _, item := range dos {
		if _, ok := siteMap[item.GetSiteId()]; ok {
			item.SetSiteData(siteMap[item.GetSiteId()])
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderGoods(ctx context.Context, dos ...*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) int { return item.ID })
	ids = slice.Unique(ids)

	orderGoodsDos, err := o.orderGoodsRepo.GetByOrderIds(ctx, ids)
	if err != nil {
		return err
	}
	groupMap := slice.GroupWith(orderGoodsDos, func(item *do.OrderGoodsDo) int { return item.OrderID })
	for _, item := range dos {
		if _, ok := groupMap[item.ID]; ok {
			item.OrderGoods = groupMap[item.ID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderUserAddress(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) int { return item.ID })
	ids = slice.Unique(ids)

	orderUserAddressDos, err := o.orderUserAddressRepo.FindByOrderIds(ctx, ids...)
	if err != nil {
		return err
	}
	dosMap := slice.KeyBy(orderUserAddressDos, func(item *do.OrderUserAddressDo) int { return item.OrderID })
	for _, item := range dos {
		if _, ok := dosMap[item.ID]; ok {
			item.OrderUserAddress = dosMap[item.ID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderUserAddressByDeliverOrder(ctx context.Context, dos []*do.OrderDeliverDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDeliverDo) int { return item.OrderID })
	ids = slice.Unique(ids)

	orderUserAddressDos, err := o.orderUserAddressRepo.FindByOrderIds(ctx, ids...)
	if err != nil {
		return err
	}
	dosMap := slice.KeyBy(orderUserAddressDos, func(item *do.OrderUserAddressDo) int { return item.OrderID })
	for _, item := range dos {
		if _, ok := dosMap[item.OrderID]; ok {
			item.OrderUserAddress = dosMap[item.OrderID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderOperatorLog(ctx context.Context, dos ...*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDo) int { return item.ID })
	ids = slice.Unique(ids)

	orderOperatorLogDos, err := o.orderOperatorLogRepo.FindByOrderIds(ctx, ids)
	if err != nil {
		return err
	}
	dosMap := slice.GroupWith(orderOperatorLogDos, func(item *do.OrderOperatorLogDo) int { return item.OrderID })
	for _, item := range dos {
		if _, ok := dosMap[item.ID]; ok {
			item.OrderOperatorLog = dosMap[item.ID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) GetSaasOrderDetail(ctx context.Context, orderNumber string, edgeBo *commonbo.OrderWithEdgeBo) (*do.OrderDo, error) {
	orderDo, err := o.orderRepo.GetWithEdges(isolationcustomer.WithSassDbCtx(ctx), orderNumber, &commonbo.OrderWithEdgeBo{})
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, apierr.ErrorDbNotFound("订单不存在")
		}
		return nil, err
	}

	if edgeBo != nil {
		if edgeBo.WithGoods {
			if err = o.FixOrderGoods(isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID), orderDo); err != nil {
				return nil, err
			}
		}
		if edgeBo.WithUserAddress {
			if err = o.FixOrderUserAddress(isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID), []*do.OrderDo{orderDo}); err != nil {
				return nil, err
			}
		}
		if edgeBo.WithOperatorLog {
			if err = o.FixOrderOperatorLog(isolationcustomer.WithCustomerIdCtx(ctx, orderDo.CustomerID), orderDo); err != nil {
				return nil, err
			}
		}
	}
	return orderDo, nil
}

func (o *OrderBaseBiz) FixOrderDeliverGoods(ctx context.Context, dos []*do.OrderDeliverDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderDeliverDo) int { return item.ID })
	ids = slice.Unique(ids)

	groupDos, err := o.orderDeliverGoodsRepo.FindByOrderDeliverIds(ctx, ids)
	if err != nil {
		return err
	}
	groupMap := slice.GroupWith(groupDos, func(item *do.OrderDeliverGoodsDo) int { return item.OrderDeliverID })

	for _, item := range dos {
		if _, ok := groupMap[item.ID]; ok {
			item.OrderDeliverGoods = groupMap[item.ID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixSupplier(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderAfterSaleDo) int { return item.SupplierID })
	ids = slice.Unique(ids)

	supplierDos, err := o.supplierRepo.Find(ctx, isolationcustomer.GetCustomerIdZero(ctx), ids...)
	if err != nil {
		return err
	}
	supplierDosMap := slice.KeyBy(supplierDos, func(item *do.SupplierDo) int { return item.ID })
	for _, item := range dos {
		if s, ok := supplierDosMap[item.SupplierID]; ok {

			if s.ShopID != 0 {
				item.SupplierInfo = s
			} else {
				item.SupplierInfo = &do.SupplierDo{ID: 2, Name: "企业供应商"}
			}

		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderAfterSaleDeliver(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderAfterSaleDo) int { return item.ID })
	ids = slice.Unique(ids)

	deliverDos, err := o.orderAfterSaleDeliverRepo.FindByPids(ctx, ids...)
	if err != nil {
		return err
	}
	deliverDosMap := slice.KeyBy(deliverDos, func(item *do.OrderAfterSaleDeliverDo) int { return item.Pid })
	for _, item := range dos {
		if _, ok := deliverDosMap[item.ID]; ok {
			item.OrderAfterSaleDeliver = deliverDosMap[item.ID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderAfterSaleGoods(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderAfterSaleDo) int { return item.ID })
	ids = slice.Unique(ids)

	goodsDos, err := o.orderAfterSaleGoodsRepo.FindByPids(ctx, ids...)
	if err != nil {
		return err
	}
	goodsDosMap := slice.GroupWith(goodsDos, func(item *do.OrderAfterSaleGoodsDo) int { return item.Pid })
	for _, item := range dos {
		if _, ok := goodsDosMap[item.ID]; ok {
			item.OrderAfterSaleGoods = goodsDosMap[item.ID]
		}
	}
	return nil
}

func (o *OrderBaseBiz) FixOrderAfterSaleLog(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	ids := slice.Map(dos, func(_ int, item *do.OrderAfterSaleDo) int { return item.ID })
	ids = slice.Unique(ids)

	logDos, err := o.orderAfterSaleLogRepo.FindByPids(ctx, ids...)
	if err != nil {
		return err
	}
	logDosMap := slice.GroupWith(logDos, func(item *do.OrderAfterSaleLogDo) int { return item.Pid })
	for _, item := range dos {
		if _, ok := logDosMap[item.ID]; ok {
			item.OrderAfterSaleLog = logDosMap[item.ID]
		}
	}
	return nil
}
