package supplierbiz

import (
	"context"
	"time"

	"cardMall/api/apierr"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data/ent"
	"cardMall/internal/module/supplierbiz/bo"
	"github.com/duke-git/lancet/v2/slice"
)

type TransportBiz struct {
	trans                     repository.TransactionRepo
	supplierTransportRepo     repository.SupplierTransportRepo
	supplierTransportItemRepo repository.SupplierTransportItemRepo
	supplierTransportCityRepo repository.SupplierTransportCityRepo
	kdRepo                    repository.KdRepo
	areaRepo                  repository.AreaRepo
	supplierGoodsRepo         repository.SupplierGoodsRepo
}

func NewTransportBiz(trans repository.TransactionRepo, supplierTransportRepo repository.SupplierTransportRepo, supplierTransportItemRepo repository.SupplierTransportItemRepo, supplierTransportCityRepo repository.SupplierTransportCityRepo, kdRepo repository.KdRepo, areaRepo repository.AreaRepo, supplierGoodsRepo repository.SupplierGoodsRepo) *TransportBiz {
	return &TransportBiz{trans: trans, supplierTransportRepo: supplierTransportRepo, supplierTransportItemRepo: supplierTransportItemRepo, supplierTransportCityRepo: supplierTransportCityRepo, kdRepo: kdRepo, areaRepo: areaRepo, supplierGoodsRepo: supplierGoodsRepo}
}

// Delete 删除物流模板
func (t *TransportBiz) Delete(ctx context.Context, supplierId, id int) error {
	transportDo, err := t.supplierTransportRepo.Get(ctx, id)
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("物流模板不存在")
		}
		return err
	}
	if !transportDo.CheckSupplierId(supplierId) {
		return apierr.ErrorNotAllow("没有权限删除该模版")
	}
	exists, err := t.supplierGoodsRepo.TransportIdExist(ctx, id)
	if err != nil {
		return err
	}
	if exists {
		return apierr.ErrorNotAllow("删除失败，物流模板正在使用中！")
	}
	return t.trans.Exec(ctx, func(ctx context.Context) error {
		if _, err = t.supplierTransportRepo.Delete(ctx, id); err != nil {
			return err
		}
		_, err = t.supplierTransportItemRepo.DeleteByTransportID(ctx, id)
		return err
	})
}

// Get 获取物流模板
func (t *TransportBiz) Get(ctx context.Context, supplierId, id int) (*do.SupplierTransportDo, error) {
	transportDo, err := t.supplierTransportRepo.GetWithEdges(ctx, id, &commonbo.SupplierTransportEdgesBo{
		WithSupplierTransportItem: true,
		WithSupplierTransportCity: true,
	})
	if err != nil {
		if ent.IsNotFound(err) {
			return nil, apierr.ErrorDbNotFound("物流模板不存在")
		}
		return nil, err
	}
	if !transportDo.CheckSupplierId(supplierId) {
		return nil, apierr.ErrorNotAllow("没有权限")
	}

	return transportDo, nil
}

// List 获取物流模板列表
func (t *TransportBiz) List(ctx context.Context, reqBo *commonbo.SupplierTransportSearchBo) ([]*do.SupplierTransportDo, *commonbo.RespPageBo) {
	return t.supplierTransportRepo.SearchList(ctx, reqBo)
}

// Save 保存物流模板
func (t *TransportBiz) Save(ctx context.Context, supplierID int, reqBo *bo.SaveTransportBo) error {
	kdDo, err := t.kdRepo.Get(ctx, reqBo.KdId)
	if err != nil {
		if ent.IsNotFound(err) {
			return apierr.ErrorDbNotFound("快递公司ID不存在")
		}
		return err
	}
	if !kdDo.IsEnable() {
		return apierr.ErrorNotAllow("快递公司已禁用")
	}

	areaDos, err := t.areaRepo.ListProvince(ctx)
	if err != nil {
		return err
	}
	areaMap := slice.KeyBy(areaDos, func(item *do.AreaDo) int {
		return item.ID
	})

	if reqBo.Id > 0 {
		var oldDo *do.SupplierTransportDo
		if oldDo, err = t.supplierTransportRepo.Get(ctx, reqBo.Id); err != nil {
			if ent.IsNotFound(err) {
				return apierr.ErrorDbNotFound("物流模板不存在")
			}
			return err
		}
		if !oldDo.CheckSupplierId(supplierID) {
			return apierr.ErrorNotAllow("没有权限")
		}
	}

	now := int(time.Now().Unix())
	transDo := &do.SupplierTransportDo{
		ID:           reqBo.Id,
		Name:         reqBo.Name,
		Description:  reqBo.Description,
		KdID:         kdDo.ID,
		KdName:       kdDo.Name,
		KdCode:       kdDo.Code,
		PricingMode:  reqBo.PricingMode,
		SupplierID:   supplierID,
		DefaultNum:   reqBo.GetDefaultNum(),
		DefaultPrice: reqBo.GetDefaultPrice(),
		AddNum:       reqBo.GetAddNum(),
		AddPrice:     reqBo.GetAddPrice(),
		CreateTime:   now,
		UpdateTime:   now,
	}

	transItemDos := make([]*do.SupplierTransportItemDo, 0, len(reqBo.Items))
	for _, item := range reqBo.Items {
		cityNameSlice := make([]string, 0)
		for _, id := range item.GetCityIds() {
			areaDo, ok := areaMap[id]
			if !ok {
				return apierr.ErrorNotAllow("城市ID不存在")
			}
			cityNameSlice = append(cityNameSlice, areaDo.Name)
		}

		cityDos := make([]*do.SupplierTransportCityDo, 0)
		for _, id := range item.GetCityIds() {
			areaDo, ok := areaMap[id]
			if ok {
				cityDos = append(cityDos, &do.SupplierTransportCityDo{
					ID:          0,
					ItemID:      0,
					TransportID: 0,
					CityID:      id,
					CityName:    areaDo.Shortname,
				})
			}
		}
		transItemDos = append(transItemDos, &do.SupplierTransportItemDo{
			ID:                    0,
			TransportID:           reqBo.Id,
			DefaultNum:            item.GetDefaultNum(),
			DefaultPrice:          item.GetDefaultPrice(),
			AddNum:                item.GetAddNum(),
			AddPrice:              item.GetAddPrice(),
			CreateTime:            now,
			UpdateTime:            now,
			SupplierTransportCity: cityDos,
		})
	}

	return t.trans.Exec(ctx, func(ctx context.Context) error {
		if reqBo.Id > 0 {
			// 更新
			_, err = t.supplierTransportRepo.UpdateV2(ctx, supplierID, transDo)
			if err != nil {
				return err
			}
			// 删除以往的模板
			if _, err = t.supplierTransportItemRepo.DeleteByTransportID(ctx, reqBo.Id); err != nil {
				return err
			}
			if _, err = t.supplierTransportCityRepo.DeleteByTransportID(ctx, reqBo.Id); err != nil {
				return err
			}
		} else {
			transDo.ID, err = t.supplierTransportRepo.Create(ctx, transDo)
			if err != nil {
				return err
			}
		}
		for _, itemDo := range transItemDos {
			if itemDo.ID, err = t.supplierTransportItemRepo.Create(ctx, transDo.ID, itemDo); err != nil {
				return err
			}
			if _, err = t.supplierTransportCityRepo.CreateBulk(ctx, transDo.ID, itemDo.ID, itemDo.SupplierTransportCity); err != nil {
				return err
			}
		}
		return nil
	})
}

// GetTransportMap 获取物流
func (t *TransportBiz) GetTransportMap(ctx context.Context, names []string) map[string]*do.SupplierTransportDo {
	transportDos := t.supplierTransportRepo.FindByNamesByLoginInfo(ctx, names)
	transportMap := make(map[string]*do.SupplierTransportDo, len(transportDos))
	for _, transportDo := range transportDos {
		transportMap[transportDo.Name] = transportDo
	}
	return transportMap
}
