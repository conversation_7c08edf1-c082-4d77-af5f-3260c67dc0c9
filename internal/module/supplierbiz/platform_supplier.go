package supplierbiz

import (
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	bizrepository "cardMall/internal/biz/repository"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/log"
)

type PlatformSupplierBiz struct {
	orderRepo            bizrepository.OrderRepo
	orderDeliverRepo     bizrepository.OrderDeliverRepo
	orderGoodsRepo       bizrepository.OrderGoodsRepo
	orderUserAddressRepo bizrepository.OrderUserAddressRepo
	trans                bizrepository.TransactionRepo
	orderAfterSaleRepo   bizrepository.OrderAfterSaleRepo
	deliverBaseBiz       *DeliverBaseBiz
	orderBaseBiz         *OrderBaseBiz
	log                  *log.Helper
}

func NewPlatformSupplierBiz(orderRepo bizrepository.OrderRepo, orderGoodsRepo bizrepository.OrderGoodsRepo, orderDeliverRepo bizrepository.OrderDeliverRepo, orderUserAddressRepo bizrepository.OrderUserAddressRepo, trans bizrepository.TransactionRepo, orderAfterSaleRepo bizrepository.OrderAfterSaleRepo, deliverBaseBiz *DeliverBaseBiz, orderBaseBiz *OrderBaseBiz, log *log.Helper) *PlatformSupplierBiz {
	return &PlatformSupplierBiz{orderRepo: orderRepo, orderGoodsRepo: orderGoodsRepo, orderDeliverRepo: orderDeliverRepo, orderUserAddressRepo: orderUserAddressRepo, trans: trans, orderAfterSaleRepo: orderAfterSaleRepo, deliverBaseBiz: deliverBaseBiz, orderBaseBiz: orderBaseBiz, log: log}
}

func (o *PlatformSupplierBiz) OrderList(ctx context.Context, reqBo *commonbo.OrderSearchBo) ([]*do.OrderDo, *commonbo.RespPageBo, error) {
	// 从saas端获取聚合数据
	dos, pageInfo := o.orderRepo.SearchList(isolationcustomer.WithSassDbCtx(ctx), reqBo)
	if len(dos) == 0 {
		return dos, pageInfo, nil
	}
	// 合并站点数据, 不能使用聚合数据去查询，id 关联不准确
	if reqBo.Edges != nil {
		if reqBo.Edges.WithGoods {
			err := o.FixOrderGoods(ctx, dos)
			if err != nil {
				return nil, pageInfo, err
			}
		}
		if reqBo.Edges.WithUserAddress {
			err := o.FixOrderUserAddress(ctx, dos)
			if err != nil {
				return nil, pageInfo, err
			}
		}
	}
	// 合并发货数据
	err := o.FixDeliverQuantity(ctx, dos...)
	if err != nil {
		return nil, pageInfo, err
	}
	// 合并售后数据
	err = o.deliverBaseBiz.FixAfterSaleData(ctx, dos...)
	if err != nil {
		return nil, pageInfo, err
	}
	if reqBo.Edges != nil && reqBo.Edges.WithSite {
		if err = o.FixSiteData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}
	if reqBo.Edges != nil && reqBo.Edges.WithSupplier {
		if err = o.FixSupplierData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}
	if reqBo.Edges != nil && reqBo.Edges.WithPayOrder {
		if err = o.FixPayOrderData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}
	if reqBo.Edges != nil && reqBo.Edges.WithCardGift {
		if err = o.FixCardGiftData(ctx, dos); err != nil {
			return nil, nil, err
		}
	}

	return dos, pageInfo, nil
}

func (o *PlatformSupplierBiz) OrderDeliverSearchList(ctx context.Context, reqBo *commonbo.OrderDeliverSearchBo) ([]*do.OrderDeliverDo, *commonbo.RespPageBo, error) {
	dos, pageInfo := o.orderDeliverRepo.SearchList(isolationcustomer.WithSassDbCtx(ctx), reqBo)

	if reqBo.Edges != nil && reqBo.Edges.WithDeliverGoods {
		_ = o.FixOrderDeliverGoods(ctx, dos)
	}

	_ = o.FixOrderUserAddressByDeliverOrder(ctx, dos)
	return dos, pageInfo, nil
}
func (o *PlatformSupplierBiz) AfterSaleOrderGoods(ctx context.Context, orderAfterSaleDos []*do.OrderAfterSaleDo) error {
	if len(orderAfterSaleDos) == 0 {
		return nil
	}
	newCtx := isolationcustomer.WithCustomerIdCtx(ctx, orderAfterSaleDos[0].CustomerID)
	newCtx = isolationcustomer.WithDisableShopCtx(newCtx)
	for _, orderAfterSaleDo := range orderAfterSaleDos {
		for _, orderAfterSaleGoods := range orderAfterSaleDo.OrderAfterSaleGoods {
			orderGoodsDo, err := o.orderGoodsRepo.FindByOrderNumberAndSkuNo(newCtx, orderAfterSaleGoods.OrderNumber, orderAfterSaleGoods.SkuNo)
			if err != nil {
				return err
			}
			if orderGoodsDo == nil {
				continue
			}
			orderAfterSaleGoods.RealSupplierPrice = orderGoodsDo.RealSupplierPrice
		}
	}
	return nil
}
func (o *PlatformSupplierBiz) AfterSaleSearchList(ctx context.Context, reqBo *commonbo.OrderAfterSaleSearchBo) ([]*do.OrderAfterSaleDo, *commonbo.RespPageBo) {
	dos, pageInfo := o.orderAfterSaleRepo.SearchList(isolationcustomer.WithSassDbCtx(ctx), reqBo)
	if len(dos) == 0 {
		return dos, pageInfo
	}
	if reqBo.Edges != nil {
		if reqBo.Edges.WithSupplier {
			_ = o.FixSupplier(ctx, dos)
		}
		if reqBo.Edges.WithOrderAfterSaleDeliver {
			_ = o.FixOrderAfterSaleDeliver(ctx, dos)
		}
		if reqBo.Edges.WithOrderAfterSaleGoods {
			_ = o.FixOrderAfterSaleDeliverGoods(ctx, dos)
		}
		if reqBo.Edges.WithOrderAfterSaleLog {
			_ = o.FixOrderAfterSaleLog(ctx, dos)
		}
	}
	err := o.AfterSaleOrderGoods(ctx, dos)
	if err != nil {
		return dos, pageInfo
	}

	return dos, pageInfo
}

func (o *PlatformSupplierBiz) FixDeliverQuantity(ctx context.Context, dos ...*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	for _, tmpDos := range groupDos {
		err := o.orderBaseBiz.FixDeliverQuantity(ctx, tmpDos...)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixSiteData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	for _, tmpDos := range groupDos {
		err := o.orderBaseBiz.FixSiteData(ctx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixSupplierData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	for _, tmpDos := range groupDos {
		err := o.orderBaseBiz.FixSupplierData(ctx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixCardGiftData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixCardGiftData(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixPayOrderData(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixPayOrderData(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixOrderGoods(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderGoods(newCtx, tmpDos...)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixOrderUserAddress(ctx context.Context, dos []*do.OrderDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderUserAddress(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixOrderUserAddressByDeliverOrder(ctx context.Context, dos []*do.OrderDeliverDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDeliverDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderUserAddressByDeliverOrder(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixOrderDeliverGoods(ctx context.Context, dos []*do.OrderDeliverDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderDeliverDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderDeliverGoods(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixSupplier(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	saasDos := slice.GroupWith(dos, func(item *do.OrderAfterSaleDo) int {
		if item.SupplierID < isolationcustomer.OfficialSupplierIdMax {
			return 0
		} else {
			return 1
		}
	})
	if saasDos[0] != nil && len(saasDos[0]) != 0 {
		newCtx := isolationcustomer.WithSupplierAndDisableShopCtx(ctx, saasDos[0][0].SupplierID)
		err := o.orderBaseBiz.FixSupplier(newCtx, saasDos[0])
		if err != nil {
			return err
		}
	}

	if saasDos[1] != nil && len(saasDos[1]) != 0 {
		groupDos := slice.GroupWith(saasDos[1], func(item *do.OrderAfterSaleDo) int { return item.CustomerID })
		ctx = isolationcustomer.WithDisableShopCtx(ctx)

		for _, tmpDos := range groupDos {
			newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
			err := o.orderBaseBiz.FixSupplier(newCtx, tmpDos)
			if err != nil {
				return err
			}
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixOrderAfterSaleDeliver(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderAfterSaleDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderAfterSaleDeliver(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}
func (o *PlatformSupplierBiz) FixOrderAfterSaleDeliverGoods(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderAfterSaleDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderAfterSaleGoods(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}

func (o *PlatformSupplierBiz) FixOrderAfterSaleLog(ctx context.Context, dos []*do.OrderAfterSaleDo) error {
	if len(dos) == 0 {
		return nil
	}
	groupDos := slice.GroupWith(dos, func(item *do.OrderAfterSaleDo) int { return item.CustomerID })
	ctx = isolationcustomer.WithDisableShopCtx(ctx)

	for _, tmpDos := range groupDos {
		newCtx := isolationcustomer.WithCustomerIdCtx(ctx, tmpDos[0].CustomerID)
		err := o.orderBaseBiz.FixOrderAfterSaleLog(newCtx, tmpDos)
		if err != nil {
			return err
		}
	}
	return nil
}
