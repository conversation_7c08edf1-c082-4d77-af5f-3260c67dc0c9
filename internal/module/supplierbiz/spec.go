package supplierbiz

import (
	"cardMall/api/apierr"
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data/ent"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/helper"
	"context"
	"time"
)

type SpecBiz struct {
	supplierGoodsSpecRepo     repository.SupplierGoodsSpecRepo
	supplierGoodsSpecItemRepo repository.SupplierGoodsSpecItemRepo
	trans                     repository.TransactionRepo
}

func NewSpecBiz(supplierGoodsSpecRepo repository.SupplierGoodsSpecRepo, supplierGoodsSpecItemRepo repository.SupplierGoodsSpecItemRepo, trans repository.TransactionRepo) *SpecBiz {
	return &SpecBiz{supplierGoodsSpecRepo: supplierGoodsSpecRepo, supplierGoodsSpecItemRepo: supplierGoodsSpecItemRepo, trans: trans}
}

// CreateGoodsSpec 保存商品规格
func (s *SpecBiz) CreateGoodsSpec(ctx context.Context, supplierID int, reqBo *bo.CreateGoodsSpecBo) (*bo.CreateGoodsSpecBo, error) {
	now := int(time.Now().Unix())
	for i, specData := range reqBo.SpecData {
		if specData.NeedSkipSave() {
			continue
		}
		specDo, err := s.supplierGoodsSpecRepo.GetByName(ctx, supplierID, specData.Name)
		if err != nil {
			if !ent.IsNotFound(err) {
				return nil, err
			}
			specDo, err = s.supplierGoodsSpecRepo.Create(ctx, &do.SupplierGoodsSpecDo{
				SupplierID: supplierID,
				Name:       specData.Name,
				Remark:     "",
				IsTpl:      valobj.SupplierGoodsSpecTplNo,
				Sort:       0,
				CreateTime: now,
				UpdateTime: now,
			})
			if err != nil {
				return nil, err
			}
		}

		// 更新spec id
		reqBo.SpecData[i].Id = specDo.ID

		for j, specItem := range specData.Items {
			if specItem.NeedSkipSave() {
				continue
			}

			var itemDo *do.SupplierGoodsSpecItemDo
			itemDo, err = s.supplierGoodsSpecItemRepo.GetByName(ctx, specDo.ID, specItem.Name)
			if err != nil {
				if !ent.IsNotFound(err) {
					return nil, err
				}
				itemDo, err = s.supplierGoodsSpecItemRepo.Create(ctx, &do.SupplierGoodsSpecItemDo{
					ID:         0,
					SpecID:     specDo.ID,
					Name:       specItem.Name,
					SpecName:   specDo.Name,
					Sort:       0,
					IsTpl:      valobj.SupplierGoodsSpecTplNo,
					CreateTime: now,
					UpdateTime: now,
				})
				if err != nil {
					return nil, err
				}
			}
			// 更新spec item id
			reqBo.SpecData[i].Items[j].Id = itemDo.ID
			reqBo.SpecData[i].Items[j].SpecId = specDo.ID
			reqBo.SpecData[i].Items[j].SpecName = specDo.Name
		}
	}

	return reqBo, nil
}

// SaveSpecTpl 保存商品规格模板
func (s *SpecBiz) SaveSpecTpl(ctx context.Context, supplierID int, reqBo *bo.SaveSpecTplBo) error {
	var (
		err   error
		tplDo *do.SupplierGoodsSpecDo
	)
	now := helper.GetNow()
	if reqBo.Id > 0 {
		tplDo, err = s.supplierGoodsSpecRepo.GetTpl(ctx, supplierID, reqBo.Id)
		if err != nil {
			if ent.IsNotFound(err) {
				return apierr.ErrorDbNotFound("规格模板不存在")
			}
			return err
		}
		if err = s.supplierGoodsSpecRepo.UpdateTpl(ctx, supplierID, tplDo.ID, reqBo.Name); err != nil {
			return err
		}
	} else {
		tplDo, err = s.supplierGoodsSpecRepo.GetTplByName(ctx, supplierID, reqBo.Name)
		if err != nil && !ent.IsNotFound(err) {
			return err
		}
		if tplDo != nil {
			return apierr.ErrorNotAllow("规格模板已存在")
		}

		tplDo, err = s.supplierGoodsSpecRepo.Create(ctx, &do.SupplierGoodsSpecDo{
			ID:         0,
			SupplierID: supplierID,
			Name:       reqBo.Name,
			Remark:     "",
			IsTpl:      valobj.SupplierGoodsSpecTplYes,
			Sort:       0,
			CreateTime: now,
			UpdateTime: now,
		})
	}

	itemDos := make([]*do.SupplierGoodsSpecItemDo, 0)
	for _, itemName := range reqBo.SpecValue {
		itemDos = append(itemDos, &do.SupplierGoodsSpecItemDo{
			ID:         0,
			SpecID:     tplDo.ID,
			Name:       itemName,
			SpecName:   tplDo.Name,
			Sort:       0,
			IsTpl:      valobj.SupplierGoodsSpecTplYes,
			CreateTime: now,
			UpdateTime: now,
		})
	}
	return s.trans.Exec(ctx, func(ctx context.Context) error {
		// 删除旧模版
		if err = s.supplierGoodsSpecItemRepo.DeleteTplBySpecId(ctx, tplDo.ID); err != nil {
			return err
		}

		// 创建新模版
		return s.supplierGoodsSpecItemRepo.CreateBulkTpl(ctx, itemDos)
	})
}

// DelSpecTpl 删除商品规格模板
func (s *SpecBiz) DelSpecTpl(ctx context.Context, supplierID int, specId int) error {
	return s.trans.Exec(ctx, func(ctx context.Context) error {
		// 删除规格
		if err := s.supplierGoodsSpecRepo.DelTpl(ctx, supplierID, specId); err != nil {
			return err
		}
		// 删除规格项
		return s.supplierGoodsSpecItemRepo.DeleteTplBySpecId(ctx, specId)
	})
}

func (s *SpecBiz) SearchList(ctx context.Context, reqBo *bo2.SupplierGoodsSpecSearchBo) (dos []*do.SupplierGoodsSpecDo, respPage *bo2.RespPageBo) {
	return s.supplierGoodsSpecRepo.SearchList(ctx, reqBo)
}
