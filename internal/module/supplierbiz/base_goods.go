package supplierbiz

import (
	"context"

	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	bizrepository "cardMall/internal/biz/repository"
	"cardMall/internal/conf"
	"github.com/go-kratos/kratos/v2/log"
)

type BaseGoodsBiz struct {
	baseGoodsRepo bizrepository.BaseGoodsRepo
	log           *log.Helper
	conf          *conf.Bootstrap
}

func NewBaseGoodsBiz(baseGoodsRepo bizrepository.BaseGoodsRepo, log *log.Helper, conf *conf.Bootstrap) *BaseGoodsBiz {
	return &BaseGoodsBiz{baseGoodsRepo: baseGoodsRepo, log: log, conf: conf}
}

// SearchList 查询商品列表
func (b *BaseGoodsBiz) SearchList(ctx context.Context, reqBo *commonbo.BaseGoodsSearchBo) ([]*do.BaseGoodsDo, *commonbo.RespPageBo) {
	return b.baseGoodsRepo.SearchList(ctx, reqBo)
}

// GetRelationGoodsMap 获取关联商品map
func (b *BaseGoodsBiz) GetRelationGoodsMap(ctx context.Context, names []string) map[string]*do.BaseGoodsDo {
	goodsDos := b.baseGoodsRepo.FindByProductIdsByLoginInfo(ctx, names)
	goodsMap := make(map[string]*do.BaseGoodsDo)
	for _, goodsDo := range goodsDos {
		goodsMap[goodsDo.ProductID+"--"+goodsDo.ProductName] = goodsDo
	}
	return goodsMap
}
