package supplierbiz

import (
	"context"

	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"github.com/go-kratos/kratos/v2/log"
)

type AreaBaseBiz struct {
	areaRepo repository.AreaRepo
	log      *log.Helper
}

func NewAreaBaseBiz(areaRepo repository.AreaRepo, log *log.Helper) *AreaBaseBiz {
	return &AreaBaseBiz{areaRepo: areaRepo, log: log}
}

// GetAllProvinceId 获取所有省份ID
func (a *AreaBaseBiz) GetAllProvinceId(ctx context.Context) ([]int, error) {
	return a.areaRepo.GetAllProvinceId(ctx)
}

// ListProvince 获取所有省份
func (a *AreaBaseBiz) ListProvince(ctx context.Context) ([]*do.AreaDo, error) {
	return a.areaRepo.ListProvince(ctx)
}

// FindAreaByIds 获取所有省份
func (a *AreaBaseBiz) FindAreaByIds(ctx context.Context, ids []int) ([]*do.AreaDo, error) {
	return a.areaRepo.FindByIds(ctx, ids)
}

// GetAreaMap 获取所有省份
func (a *AreaBaseBiz) GetAreaMap(ctx context.Context, names []string) map[string]*do.AreaDo {
	dos := a.areaRepo.FindByNamesByLoginInfo(ctx, names)
	areaMap := make(map[string]*do.AreaDo)
	for _, areaDo := range dos {
		areaMap[areaDo.Name] = areaDo
	}
	return areaMap
}
