package supplierbiz

import (
	"cardMall/api/apierr"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/excel"
	"cardMall/internal/biz/excel/exceldo"
	bizrepository "cardMall/internal/biz/repository"
	"cardMall/internal/constants"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"fmt"
	"github.com/duke-git/lancet/v2/slice"
	"github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"strings"
)

type DeliverBiz struct {
	orderRepo            bizrepository.OrderRepo
	orderDeliverRepo     bizrepository.OrderDeliverRepo
	kdRepo               bizrepository.KdRepo
	supplierGoodsSkuRepo bizrepository.SupplierGoodsSkuRepo
	trans                bizrepository.TransactionRepo
	ossExcel             excel.OssExcel
	deliverBaseBiz       *DeliverBaseBiz
	log                  *log.Helper
}

func NewDeliverBiz(orderRepo bizrepository.OrderRepo, orderDeliverRepo bizrepository.OrderDeliverRepo, kdRepo bizrepository.KdRepo, supplierGoodsSkuRepo bizrepository.SupplierGoodsSkuRepo, trans bizrepository.TransactionRepo, ossExcel excel.OssExcel, deliverBaseBiz *DeliverBaseBiz, log *log.Helper) *DeliverBiz {
	return &DeliverBiz{orderRepo: orderRepo, orderDeliverRepo: orderDeliverRepo, kdRepo: kdRepo, supplierGoodsSkuRepo: supplierGoodsSkuRepo, trans: trans, ossExcel: ossExcel, deliverBaseBiz: deliverBaseBiz, log: log}
}

// parseExcel 解析excel
func (d *DeliverBiz) parseExcel(ctx context.Context, excelUrl string) ([]*exceldo.ExcelItem, error) {
	isPrivate := true
	if strings.HasPrefix(excelUrl, "http://127.0.0.1") {
		isPrivate = false
	}
	excelItems, err := d.ossExcel.ReadBatchDeliverExcel(ctx, excelUrl, isPrivate)

	if err != nil {
		return nil, err
	}

	// 去掉表格的表头
	if len(excelItems) > constants.MaxDeliverEntityExcelNum {
		return nil, apierr.ErrorNotAllow("批量发货数量不能超过%d条", constants.MaxDeliverEntityExcelNum)
	}

	errInfo := make([]*bo.ExcelErr, 0)

	for excelIndex, excelItem := range excelItems {
		if excelItem.HasError() {
			errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, excelItem.ErrMsg)
		}
	}
	d.FixExcelErrMsg(errInfo, excelItems)
	return excelItems, nil
}

// BatchDeliver 批量发货
func (d *DeliverBiz) BatchDeliver(ctx context.Context, reqBo *bo.BatchDeliverBo) (*bo.BatchDeliverResponse, error) {
	excelItems, err := d.BatchDeliverPreview(ctx, reqBo)
	if err != nil {
		return nil, err
	}
	deliverMap := d.ExcelItemsToDeliverEntityBo(excelItems, reqBo.AdminLoginInfoBo, false)

	deliverErrInfo := make([]*bo.ExcelDeliverErr, 0)

	rsp := &bo.BatchDeliverResponse{
		ErrorExcelUrl:     "",
		ExcelSuccessNum:   0,
		DeliverSuccessNum: 0,
		ExcelFailNum:      0,
		DeliverFailNum:    0,
	}
	for _, item := range excelItems {
		if item.HasError() {
			rsp.ExcelFailNum++
		}
	}

	for _, entityBo := range deliverMap {
		if len(entityBo.Goods) == 0 {
			rsp.DeliverFailNum++
			rsp.ExcelFailNum += len(entityBo.ExcelIndex)
			continue
		}
		entityBo.AdminLoginInfoBo = reqBo.AdminLoginInfoBo

		newCtx := isolationcustomer.WithCustomerAndShopCtx(ctx, entityBo.CustomerId, entityBo.ShopId)
		if deliverErr := d.deliverBaseBiz.DeliverEntity(newCtx, entityBo); deliverErr != nil {
			deliverErrInfo = append(deliverErrInfo, &bo.ExcelDeliverErr{ExcelIndex: entityBo.ExcelIndex, ErrMsg: deliverErr})
			for _, index := range entityBo.ExcelIndex {
				excelItems[index].SetErr(deliverErr, index)
			}
			rsp.DeliverFailNum++
			rsp.ExcelFailNum += len(entityBo.ExcelIndex)
		} else {
			rsp.DeliverSuccessNum++
			rsp.ExcelSuccessNum += len(entityBo.ExcelIndex)
		}
	}

	errExcelItems := make([]*exceldo.ExcelItem, 0)
	for _, excelItem := range excelItems {
		if excelItem.HasError() {
			errExcelItems = append(errExcelItems, excelItem)
		}
	}

	// 将错误数据的上传至oss
	if len(errExcelItems) > 0 {
		rsp.ErrorExcelUrl, err = d.ossExcel.UploadDeliverErrorExcel(ctx, errExcelItems)
		if err != nil {
			return nil, err
		}
	}

	return rsp, nil
}

// BatchDeliverPreview 批量发货预览
func (d *DeliverBiz) BatchDeliverPreview(ctx context.Context, reqBo *bo.BatchDeliverBo) ([]*exceldo.ExcelItem, error) {
	excelItems, err := d.parseExcel(ctx, reqBo.ExcelUrl)

	if err != nil {
		return nil, err
	}

	if err = d.FixShopInfo(ctx, excelItems); err != nil {
		return nil, err
	}

	// 验证快递和商品信息
	if err = d.ValidateExcelGoodsAndKdBySaasSupplier(ctx, excelItems); err != nil {
		return nil, err
	}

	// 预检查发货数量
	if err = d.CheckDeliverDataBySaas(ctx, excelItems, reqBo.AdminLoginInfoBo); err != nil {
		return nil, err
	}
	return excelItems, nil
}

func (d *DeliverBiz) FixShopInfo(ctx context.Context, excelItems []*exceldo.ExcelItem) error {
	orderNos := slice.Map(excelItems, func(index int, item *exceldo.ExcelItem) string { return item.OrderNumber })
	orderDos, err := d.orderRepo.FindWithEdges(isolationcustomer.WithSassDbCtx(ctx), &commonbo.OrderWithEdgeBo{}, orderNos...)
	if err != nil {
		return err
	}
	orderMap := slice.KeyBy(orderDos, func(item *do.OrderDo) string { return item.OrderNumber })
	for excelIndex, excelItem := range excelItems {
		if orderDo, ok := orderMap[excelItem.OrderNumber]; ok {
			excelItem.ShopId = orderDo.ShopID
			excelItem.CustomerId = orderDo.CustomerID
		} else {
			excelItem.SetErrString("订单号不存在", excelIndex)
		}
	}
	return nil
}

func (d *DeliverBiz) ValidateExcelGoodsAndKdBySaasSupplier(ctx context.Context, excelItems []*exceldo.ExcelItem) error {
	if len(excelItems) == 0 {
		return nil
	}
	excelGroup := slice.GroupWith(excelItems, func(item *exceldo.ExcelItem) int { return item.CustomerId })
	for _, tmpItems := range excelGroup {
		if err := d.ValidateExcelGoodsAndKd(ctx, tmpItems); err != nil {
			return err
		}
	}
	return nil
}

// ValidateExcelGoodsAndKd 验证excel 快递和商品信息
func (d *DeliverBiz) ValidateExcelGoodsAndKd(ctx context.Context, excelItems []*exceldo.ExcelItem) error {
	errInfo := make([]*bo.ExcelErr, 0)

	kdNameSlice := make([]string, 0)
	skuNos := make([]string, 0)
	for _, excelItem := range excelItems {
		kdNameSlice = append(kdNameSlice, excelItem.KdName)
		skuNos = append(skuNos, excelItem.SkuNo)
	}
	kdNameSlice = slice.Unique(kdNameSlice)
	skuNos = slice.Unique(skuNos)

	kdDos, err := d.kdRepo.FindByNames(isolationcustomer.WithCustomerAndDisableShopCtx(ctx, excelItems[0].CustomerId), kdNameSlice...)
	if err != nil {
		return err
	}
	kdMap := slice.KeyBy(kdDos, func(item *do.KdDo) string {
		return item.Name
	})

	skuDos, err := d.supplierGoodsSkuRepo.FindWithGoodsBySkuNo(ctx, skuNos...)
	if err != nil {
		return err
	}
	skuMap := slice.KeyBy(skuDos, func(item *do.SupplierGoodsSkuDo) string {
		return item.SkuNo
	})

	for excelIndex, excelItem := range excelItems {
		if _, ok := kdMap[excelItem.KdName]; !ok {
			excelItem.SetErrString("快递公司不存在", excelIndex)
			errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, "快递公司不存在")
		} else {
			// 补充kdCode
			excelItem.KdCode = kdMap[excelItem.KdName].Code
		}

		if _, ok := skuMap[excelItem.SkuNo]; !ok {
			// 如果已经有错误，则跳过
			excelItem.SetErrString("商品不存在", excelIndex)
			errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, "商品不存在")
		} else {
			if !skuMap[excelItem.SkuNo].CanDeliver() {
				// 如果已经有错误，则跳过
				excelItem.SetErrString("商品已下架", excelIndex)
				errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, "商品已下架")
			}
		}
	}
	// 将相同订单的错误信息设置到其他索引上
	d.FixExcelErrMsg(errInfo, excelItems)
	return nil
}

func (d *DeliverBiz) CheckDeliverDataBySaas(ctx context.Context, excelItems []*exceldo.ExcelItem, loginInfoBo *commonbo.AdminLoginInfoBo) error {
	if len(excelItems) == 0 {
		return nil
	}
	itemsGroup := slice.GroupWith(excelItems, func(item *exceldo.ExcelItem) int { return item.CustomerId })
	for _, tmpItems := range itemsGroup {
		if err := d.CheckDeliverData(ctx, tmpItems, loginInfoBo); err != nil {
			return err
		}
	}
	return nil
}

// CheckDeliverData 预检查发货情况
func (d *DeliverBiz) CheckDeliverData(ctx context.Context, excelItems []*exceldo.ExcelItem, loginInfoBo *commonbo.AdminLoginInfoBo) error {
	errInfo := make([]*bo.ExcelErr, 0)
	orderNumberSlice := make([]string, 0)
	for _, item := range excelItems {
		if !item.HasError() {
			orderNumberSlice = append(orderNumberSlice, item.OrderNumber)
		}
	}

	newCtx := isolationcustomer.WithCustomerAndDisableShopCtx(ctx, excelItems[0].CustomerId)
	orderDos, err := d.orderRepo.FindWithEdges(newCtx, &commonbo.OrderWithEdgeBo{WithGoods: true}, orderNumberSlice...)
	if err != nil {
		return err
	}
	orderMap := slice.KeyBy(orderDos, func(item *do.OrderDo) string {
		return item.OrderNumber
	})

	for excelIndex, excelItem := range excelItems {
		// 订单数据不存在
		if orderDo, ok := orderMap[excelItem.OrderNumber]; !ok {
			excelItem.SetErrString("订单号不存在", excelIndex)
			errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, "订单号不存在")
		} else {
			if !orderDo.OrderType.IsEntity() {
				msg := fmt.Sprintf("订单类型[%s]不能进行发货操作", orderDo.OrderType.GetName())
				excelItem.SetErrString(msg, excelIndex)
				errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, msg)
				continue
			}

			if !orderDo.CheckSupplierId(loginInfoBo.GetLoginSupplierId()) {
				msg := "不能操作其他供应商订单"
				excelItem.SetErrString(msg, excelIndex)
				errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, msg)
				continue
			}

			if !orderDo.CanDeliver() {
				msg := fmt.Sprintf("当前订单状态[%s]不能进行发货操作", orderDo.Status.GetName())
				excelItem.SetErrString(msg, excelIndex)
				errInfo = d.AppendErrInfo(errInfo, excelItem, excelIndex, msg)
				continue
			}
		}
	}
	d.FixExcelErrMsg(errInfo, excelItems)

	allAlreadyDeliverDos, err := d.orderDeliverRepo.FindWithGoodsByOrderNumber(newCtx, orderNumberSlice...)
	if err != nil {
		return err
	}
	allAlreadyDeliverMap := make(map[string][]*do.OrderDeliverDo)
	for _, item := range allAlreadyDeliverDos {
		if _, ok := allAlreadyDeliverMap[item.OrderNumber]; !ok {
			allAlreadyDeliverMap[item.OrderNumber] = make([]*do.OrderDeliverDo, 0)
		}
		allAlreadyDeliverMap[item.OrderNumber] = append(allAlreadyDeliverMap[item.OrderNumber], item)
	}
	deliverMap := d.ExcelItemsToDeliverEntityBo(excelItems, loginInfoBo, true)

	for orderNumber, deliverItem := range deliverMap {
		deliverData, err := d.deliverBaseBiz.FormatDeliverData(orderMap[orderNumber].OrderGoods, allAlreadyDeliverMap[orderNumber], deliverItem.Goods)
		if err != nil {
			for _, excelIndex := range deliverItem.ExcelIndex {
				excelItems[excelIndex].SetErr(err, excelIndex)
				errInfo = d.AppendErrInfo(errInfo, excelItems[excelIndex], excelIndex, errors.FromError(err).GetMessage())
			}
		}

		// 检查发货是否超量，全部发货批量发货数据不准确不能使用
		_, err = d.deliverBaseBiz.CheckDeliverData(deliverData)
		if err != nil {
			for _, excelIndex := range deliverItem.ExcelIndex {
				excelItems[excelIndex].SetErr(err, excelIndex)
				errInfo = d.AppendErrInfo(errInfo, excelItems[excelIndex], excelIndex, errors.FromError(err).GetMessage())
			}
		}

	}
	d.FixExcelErrMsg(errInfo, excelItems)
	return nil
}

// AppendErrInfo .
func (d *DeliverBiz) AppendErrInfo(errInfo []*bo.ExcelErr, excelItem *exceldo.ExcelItem, excelIndex int, errMsg string) []*bo.ExcelErr {
	if !slice.ContainBy(errInfo, func(item *bo.ExcelErr) bool { return item.ErrKey == excelItem.OrderErrKey() }) {
		errInfo = append(errInfo, &bo.ExcelErr{
			ExcelIndex: excelIndex,
			ErrMsg:     errMsg,
			ErrKey:     excelItem.OrderErrKey(),
		})
	}
	if !slice.ContainBy(errInfo, func(item *bo.ExcelErr) bool { return item.ErrKey == excelItem.ExpressErrKey() }) {
		errInfo = append(errInfo, &bo.ExcelErr{
			ExcelIndex: excelIndex,
			ErrMsg:     errMsg,
			ErrKey:     excelItem.ExpressErrKey(),
		})
	}

	return errInfo
}

// FixExcelErrMsg 将相同订单号或者发货单号的错误信息设置到其他索引上
func (d *DeliverBiz) FixExcelErrMsg(errInfo []*bo.ExcelErr, excelItems []*exceldo.ExcelItem) {
	if len(errInfo) > 0 {
		errMap := slice.KeyBy(errInfo, func(item *bo.ExcelErr) string {
			return item.ErrKey
		})
		for _, excelItem := range excelItems {
			if !excelItem.HasError() {
				if v, ok := errMap[excelItem.OrderErrKey()]; ok {
					excelItem.SetErrString(v.ErrMsg, v.ExcelIndex)
				}
				if v, ok := errMap[excelItem.ExpressErrKey()]; ok {
					excelItem.SetErrString(v.ErrMsg, v.ExcelIndex)
				}
			}
		}
	}
}

// ExcelItemsToDeliverEntityBo excelItem没有错误的数据转成DeliverEntityBo
func (d *DeliverBiz) ExcelItemsToDeliverEntityBo(excelItems []*exceldo.ExcelItem, loginInfo *commonbo.AdminLoginInfoBo, mapByOrderNumber bool) map[string]*bo.DeliverEntityBo {
	tmpMap := make(map[string]*bo.DeliverEntityBo)
	for excelIndex, excelItem := range excelItems {
		if excelItem.HasError() {
			continue
		}
		key := excelItem.OrderNumber
		if !mapByOrderNumber {
			key = excelItem.GetMapKey()
		}
		if _, ok := tmpMap[key]; !ok {
			tmpMap[key] = &bo.DeliverEntityBo{
				Goods: []*bo.DeliverEntityGoods{
					{Num: excelItem.Num, OrderQuantity: 0, NotNeedDeliverQuantity: 0, AlreadyDeliverQuantity: 0, NotDeliverQuantity: 0, SkuNo: excelItem.SkuNo},
				},
				KdCode:           excelItem.KdCode,
				KdName:           excelItem.KdName,
				ExpressNo:        excelItem.ExpressNo,
				OrderNumber:      excelItem.OrderNumber,
				CustomerId:       excelItem.CustomerId,
				ShopId:           excelItem.ShopId,
				ExcelIndex:       []int{excelIndex},
				AdminLoginInfoBo: loginInfo,
			}
		} else {
			tmpMap[key].Goods = append(tmpMap[key].Goods, &bo.DeliverEntityGoods{Num: excelItem.Num, OrderQuantity: 0, NotNeedDeliverQuantity: 0, AlreadyDeliverQuantity: 0, NotDeliverQuantity: 0, SkuNo: excelItem.SkuNo})
			tmpMap[key].ExcelIndex = append(tmpMap[key].ExcelIndex, excelIndex)
		}
	}

	return tmpMap
}
