package constants

import "time"

const AdminVerifyCodeLen = 6

const AdminVerifyCodeExpire = 300
const AdminDefaultId = 1

//const (
//	//AdminSmsCheckPrefix  string = "card:mall:admin:check:%s:%s"
//	//AdminAuthCheckPrefix string = "card:mall:admin:auth:%d"
//)
//
////func GetAdminSMSCheckCacheKey(phone string, verifyId string) string {
////	return fmt.Sprintf(AdminSmsCheckPrefix, phone, verifyId)
////}
////func GetAdminAuthCacheKey(adminId int) string {
////	return fmt.Sprintf(AdminAuthCheckPrefix, adminId)
////}

// AdminSmsLoginMobileMustExists 后台登录时，账号是否必须存在
const AdminSmsLoginMobileMustExists = true

// AdminLoginNeedsSmsVerifyInterval 后台登录时，手机号是否需要验证
const AdminLoginNeedsSmsVerifyInterval = 7 * 24 * time.Hour
