package page

import (
	bizbo "cardMall/internal/biz/bo"
	bizdo "cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"fmt"
	"testing"
)

func TestNewPageSlice(t *testing.T) {
	in := &bizbo.SupplierGoodsSkuBo{
		SearchType: 3,
	}

	ps := NewPageSlice(2, 1, func(i, j *bizdo.SupplierGoodsSkuDo) bool {
		if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSaleVolume {
			return i.SalesVolume > j.SalesVolume
		} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeNew {
			return i.CreateTime > j.CreateTime
		} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSalePrice {
			return i.SupplierPrice < j.SupplierPrice
		} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeProfit {
			return i.Profit > j.Profit
		}
		return true
	})
	ps.AddItem(&bizdo.SupplierGoodsSkuDo{ID: 1, SupplierPrice: 1})
	ps.AddItem(&bizdo.SupplierGoodsSkuDo{ID: 2, SupplierPrice: 2})
	ps.AddItem(&bizdo.SupplierGoodsSkuDo{ID: 3, SupplierPrice: 3})
	ps.AddItem(&bizdo.SupplierGoodsSkuDo{ID: 4, SupplierPrice: 4})
	ps.Sort()
	paginatedItems := ps.Paginate()

	for _, item := range paginatedItems {
		fmt.Println(item.ID)
	}
}
