package tools

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"github.com/duke-git/lancet/v2/slice"
)

func IsPlatformCustomer(id int) bool {
	return slice.Contain(constants.PlatformCustomerIds, id)
}

func GetCustomerAminType(id int) valobj.AdminTypeObj {
	return helper.TernaryAny(IsPlatformCustomer(id), valobj.AdminTypeSaasAdmin, valobj.AdminTypeCustomer)
}
