package rpc

import (
	"cardMall/internal/module/appbiz/bo"
	"context"
)

type AppletRepo interface {
	// GetAccessToken 获取小程序access_token
	GetAccessToken(ctx context.Context, appId, appSecret string) (string, error)

	// GetAppletUrl 获取小程序外部打开链接
	GetAppletUrl(ctx context.Context, in *bo.AppletUrlBo) (string, error)

	// GetUserPhoneNumber 获取小程序用户手机号
	GetUserPhoneNumber(ctx context.Context, appId, appSecret, code string) (string, error)

	GetAppletUrlV2(ctx context.Context, in *bo.AppletUrlBo) (string, error)
}
