package calvalobj

import (
	"strconv"
)

type QianZhuOrderStatusObj int

type QianZhuKFCOrderStatusObj int

type QianZhuNotifyTypeObj int

type QianZhuOrderSourceObj int

const (
	// QianZhuOrderStatusUnknown -1:未知
	QianZhuOrderStatusUnknown QianZhuOrderStatusObj = -1
	// QianZhuOrderStatusUnPaid 0:待付款
	QianZhuOrderStatusUnPaid QianZhuOrderStatusObj = 0
	// QianZhuOrderStatusPending 5:待出票
	QianZhuOrderStatusPending QianZhuOrderStatusObj = 5
	// QianZhuOrderStatusPended 10:已出票
	QianZhuOrderStatusPended QianZhuOrderStatusObj = 10
	// QianZhuOrderStatusFinished 15:交易成功
	QianZhuOrderStatusFinished QianZhuOrderStatusObj = 15
	// QianZhuOrderStatusCancel -5:已取消
	QianZhuOrderStatusCancel QianZhuOrderStatusObj = -5
)

const (
	QianZhuNotifyTypeRefund QianZhuNotifyTypeObj = iota - 1
	QianZhuNotifyTypeUnknown
	QianZhuNotifyTypePaid
	QianZhuNotifyTypePended
	QianZhuNotifyTypeFinished
	QianZhuNotifyTypePendedPart
	QianZhuNotifyTypeDeliverChange
)

const (
	QianZhuKFCOrderStatusCanceled QianZhuKFCOrderStatusObj = -5
	QianZhuKFCOrderStatusUnKnown  QianZhuKFCOrderStatusObj = -1
	QianZhuKFCOrderStatusUnPaid   QianZhuKFCOrderStatusObj = 0
	QianZhuKFCOrderStatusPending  QianZhuKFCOrderStatusObj = 5
	QianZhuKFCOrderStatusFinish   QianZhuKFCOrderStatusObj = 15
)

const (
	QianZhuOrderSourceCinema QianZhuOrderSourceObj = iota + 1
	QianZhuOrderSourceKFC
	QianZhuOrderSourceStarBucks
)

var QianZhuOrderStatusMap = map[QianZhuOrderStatusObj]string{
	QianZhuOrderStatusUnknown:  "未知",
	QianZhuOrderStatusUnPaid:   "待付款",
	QianZhuOrderStatusPending:  "待出票",
	QianZhuOrderStatusPended:   "已出票",
	QianZhuOrderStatusFinished: "交易成功",
	QianZhuOrderStatusCancel:   "已取消",
}

func (q QianZhuOrderStatusObj) GetText() string {
	if t, ok := QianZhuOrderStatusMap[q]; ok {
		return t
	}
	return strconv.Itoa(int(q))
}

type QianZhuStarBucksOrderStatusObj int

const (
	QianZhuStarBucksOrderStatusUnpaid   QianZhuStarBucksOrderStatusObj = 0   // 待付款
	QianZhuStarBucksOrderStatusPaid     QianZhuStarBucksOrderStatusObj = 5   // 已支付
	QianZhuStarBucksOrderStatusPending  QianZhuStarBucksOrderStatusObj = 10  // 出单中
	QianZhuStarBucksOrderStatusPended   QianZhuStarBucksOrderStatusObj = 15  // 已出单
	QianZhuStarBucksOrderStatusDeliver  QianZhuStarBucksOrderStatusObj = 20  // 配送中
	QianZhuStarBucksOrderStatusFinished QianZhuStarBucksOrderStatusObj = 25  // 已完成
	QianZhuStarBucksOrderStatusCanceled QianZhuStarBucksOrderStatusObj = -5  // 已取消
	QianZhuStarBucksOrderStatusFail     QianZhuStarBucksOrderStatusObj = -10 // 失败
)

func (q QianZhuStarBucksOrderStatusObj) IsFinished() bool {
	return q == QianZhuStarBucksOrderStatusFinished
}
