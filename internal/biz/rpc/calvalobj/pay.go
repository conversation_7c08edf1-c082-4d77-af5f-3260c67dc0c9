package calvalobj

type PayChannelType int32

const (
	// PayChannelTypeUnknown 未知支付渠道
	PayChannelTypeUnknown PayChannelType = iota
	// PayChannelTypeAlipay 支付宝支付
	PayChannelTypeAlipay
	// PayChannelTypeWechat 微信支付
	PayChannelTypeWechat
	// PayChannelTypeYsf 云闪付支付
	PayChannelTypeYsf
)

// String 支付渠道类型
func (t PayChannelType) String() string {
	switch t {
	case PayChannelTypeUnknown:
		return "未知"
	case PayChannelTypeAlipay:
		return "支付宝"
	case PayChannelTypeWechat:
		return "微信"
	case PayChannelTypeYsf:
		return "云闪付"
	default:
		return "未定义"
	}
}

// Value 支付渠道类型
func (t PayChannelType) Value() int32 {
	return int32(t)
}

type PayModeType int32

const (
	// PayModeTypeUnknown 未知支付方式
	PayModeTypeUnknown PayModeType = iota
	// PayModeTypeH5 h5
	PayModeTypeH5
	// PayModeTypeQrcode web二维码
	PayModeTypeQrcode
	// PayModeTypeApp app
	PayModeTypeApp
	// PayModeTypeJsapi js_api
	PayModeTypeJsapi
	// PayModeTypeApplet 小程序
	PayModeTypeApplet
)

func (t PayModeType) String() string {
	switch t {
	case PayModeTypeUnknown:
		return "未知"
	case PayModeTypeH5:
		return "h5"
	case PayModeTypeQrcode:
		return "web二维码"
	case PayModeTypeApp:
		return "app"
	case PayModeTypeJsapi:
		return "js_api"
	case PayModeTypeApplet:
		return "小程序"
	default:
		return "未定义"
	}
}

func (t PayModeType) Value() int32 {
	return int32(t)
}

type RefundStatus int32

const (
	// RefundStatusUnknown 未知退款状态
	RefundStatusUnknown RefundStatus = iota
	// RefundStatusRefunding 退款中
	RefundStatusRefunding
	// RefundStatusSuccess 退款成功
	RefundStatusSuccess
	// RefundStatusFail 退款失败
	RefundStatusFail
)

func (t RefundStatus) String() string {
	switch t {
	case RefundStatusUnknown:
		return "未知"
	case RefundStatusRefunding:
		return "退款中"
	case RefundStatusSuccess:
		return "退款成功"
	case RefundStatusFail:
		return "退款失败"
	default:
		return "未定义"
	}
}

func (t RefundStatus) Value() int32 {
	return int32(t)
}

func (t RefundStatus) IsFail() bool {
	return t == RefundStatusFail
}

func (t RefundStatus) IsSuccess() bool {
	return t == RefundStatusSuccess
}

type PayStatus int32

const (
	// PayStatusUnknown 未知支付状态
	PayStatusUnknown PayStatus = iota
	// PayStatusPaying 支付中
	PayStatusPaying
	// PayStatusSuccess 支付成功
	PayStatusSuccess
	// PayStatusFail 支付失败
	PayStatusFail
	// PayStatusCancel 取消支付
	PayStatusCancel
	// PayStatusRefund 已经退款
	PayStatusRefund
)

func (t PayStatus) String() string {
	switch t {
	case PayStatusUnknown:
		return "未知"
	case PayStatusPaying:
		return "支付中"
	case PayStatusSuccess:
		return "支付成功"
	case PayStatusFail:
		return "支付失败"
	case PayStatusCancel:
		return "取消支付"
	case PayStatusRefund:
		return "已经退款"
	default:
		return "未定义"
	}
}

func (t PayStatus) Value() int32 {
	return int32(t)
}

func (t PayStatus) Success() bool {
	return t == PayStatusSuccess
}

type CallbackMethod int32

const (
	// CallbackMethodUnknown 未知回调方法
	CallbackMethodUnknown CallbackMethod = iota
	// CallbackMethodPaySuccess 支付成功回调
	CallbackMethodPaySuccess
	// CallbackMethodRefundSuccess 退款成功回调
	CallbackMethodRefundSuccess
)

func (t CallbackMethod) String() string {
	switch t {
	case CallbackMethodUnknown:
		return "未知"
	case CallbackMethodPaySuccess:
		return "支付成功"
	case CallbackMethodRefundSuccess:
		return "退款成功"
	default:
		return "未定义"
	}
}

func (t CallbackMethod) Value() int32 {
	return int32(t)
}

type PayCenterStatus int32

const (
	// PayCenterStatusUnknown 未知 状态
	PayCenterStatusUnknown PayCenterStatus = iota
	// PayCenterStatusEnable 启用
	PayCenterStatusEnable
	// PayCenterStatusDisable 禁用
	PayCenterStatusDisable
)

func (t PayCenterStatus) String() string {
	switch t {
	case PayCenterStatusUnknown:
		return "未知"
	case PayCenterStatusEnable:
		return "启用"
	case PayCenterStatusDisable:
		return "禁用"
	default:
		return "未定义"
	}
}

func (t PayCenterStatus) Value() int32 {
	return int32(t)
}

type NotifyType int32

const NotifyTypePay = 1
const NotifyTypeRefund = 2

func (n NotifyType) String() string {
	switch n {
	case NotifyTypePay:
		return "支付"
	case NotifyTypeRefund:
		return "退款"
	default:
		return "未定义"
	}
}

func (n NotifyType) IsPay() bool {
	return n == NotifyTypePay
}

func (n NotifyType) IsRefund() bool {
	return n == NotifyTypeRefund
}
