package acldo

import (
	"cardMall/internal/biz/rpc/calvalobj"
)

type RechargeNotifyDo struct {
	OutTradeNo      string
	Status          calvalobj.RechargeOrderStatusObj
	RechargeAccount string
	CardCode        string
}

type RechargeNotifyV2Do struct {
	TradeStatus     string                  `json:"tradeStatus"`
	OrderNo         string                  `json:"orderNo"`
	TradeStateDesc  string                  `json:"tradeStateDesc"`
	Cards           []*RechargeNotifyCardDo `json:"cards"`
	MchId           int                     `json:"mchId"`
	OutTradeNo      string                  `json:"outTradeNo"`
	RechargeAccount string                  `json:"rechargeAccount"`
	UnitPrice       float64                 `json:"unitPrice"`
}

// ConvertV1Status 转换成v1的status
func (r *RechargeNotifyV2Do) ConvertV1Status() calvalobj.RechargeOrderStatusObj {
	switch r.TradeStatus {
	case "SUCCESS":
		return calvalobj.RechargeOrderStatusSuccess
	case "FAIL":
		return calvalobj.RechargeOrderStatusFail
	default:
		return calvalobj.RechargeOrderStatusException
	}
}

type RechargeNotifyCardDo struct {
	No       string `json:"no"`
	Pwd      string `json:"pwd"`
	Deadline string `json:"deadline"`
	CardType int    `json:"cardType"`
	Url      string `json:"url"`
}

type RechargeDo struct {
}
