package acldo

import "io"

// BaseError 错误返回
type BaseError struct {
	Code     int         `json:"code"`
	Message  string      `json:"message"`
	Reason   string      `json:"reason"`
	Metadata interface{} `json:"metadata"`
}

// UploadReq 文件上传请求
type UploadReq struct {
	System   string
	Business string
	UserID   int
	Filename string
	File     io.ReadSeeker
}

// UploadResp 文件上传响应
type UploadResp struct {
	BaseError
	Url        string `json:"url"`        // 文件地址
	PreviewUrl string `json:"previewUrl"` // 预览地址
}
