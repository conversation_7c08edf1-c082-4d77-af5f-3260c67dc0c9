package acldo

type QzMovieOrdersByUpdateResp struct {
	Total     int32
	PageSize  int32
	PageIndex int32
	PageCount int32
	Data      []*MovieOrdersByUpdateRespOrder
}

type MovieOrdersByUpdateRespOrder struct {
	CreateTime      string
	UserId          uint32
	UserName        string
	OrderNo         string
	MarketUnitPrice float32
	Status          int32
	StatusDesc      string
	UnitPrice       float32
	TotalPrice      float32
	PaymentTime     string
	Amount          float32
	SeatsDesc       string
	SeatsCount      int32
	CinemaId        int32
	CinemaAddr      string
	CinemaName      string
	FilmId          int32
	FilmName        string
	Pic             string
	UserMobile      string
	ShowTime        string
	ShowEndTime     string
	CompleteTime    string
	CancelTime      string
	CancelType      int32
	DrawTime        string
}
