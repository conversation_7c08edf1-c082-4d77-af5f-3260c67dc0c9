package acldo

import "encoding/json"

type AlipayMiniOrderCreateRsp struct {
	AlipayOpenMiniRsp
	OrderId             string `json:"order_id"`
	CustomerDisplayText string `json:"customer_display_text"`
}

type AlipayMiniOrderCreateDo struct {
	Rsp *AlipayMiniOrderCreateRsp `json:"alipay_open_mini_order_create_response"`
}

func (a *AlipayMiniOrderCreateDo) IsSuccess() bool {
	return a.Rsp.IsSuccess()
}

func (a *AlipayMiniOrderCreateDo) GetSubMsg() string {
	return a.Rsp.GetSubMsg()
}

func (a *AlipayMiniOrderCreateDo) GetCode() string {
	return a.Rsp.GetSubMsg()
}

func (a *AlipayMiniOrderCreateDo) GetOrderId() string {
	return a.Rsp.OrderId
}

func (a *AlipayMiniOrderCreateDo) ToJson() string {
	r, _ := json.Marshal(a)
	return string(r)
}
