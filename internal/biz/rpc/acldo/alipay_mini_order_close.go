package acldo

import "encoding/json"

type AlipayMiniOrderCloseRsp struct {
	AlipayOpenMiniRsp
}

type AlipayMiniOrderCloseDo struct {
	Rsp *AlipayMiniOrderCloseRsp `json:"alipay_open_mini_order_close_response"`
}

func (a *AlipayMiniOrderCloseDo) IsSuccess() bool {
	return a.Rsp.IsSuccess()
}

func (a *AlipayMiniOrderCloseDo) GetSubMsg() string {
	return a.Rsp.GetSubMsg()
}

func (a *AlipayMiniOrderCloseDo) GetCode() string {
	return a.Rsp.GetSubMsg()
}

func (a *AlipayMiniOrderCloseDo) ToJson() string {
	r, _ := json.Marshal(a)
	return string(r)
}
