package acldo

import (
	"cardMall/internal/biz/valobj"
	"github.com/shopspring/decimal"
)

type DaoGaoShuShuLoginAclDo struct {
	DaoGaoShuShuExceptionRspAclDo
	Url string `json:"url"`
}

func (d *DaoGaoShuShuLoginAclDo) GetUrl() string {
	return d.Url
}

type DaoGaoShuShuPayAclDo struct {
	DaoGaoShuShuExceptionRspAclDo
	Success bool   `json:"success"`
	Message string `json:"message"`
	ErrCode string `json:"errCode"`
}

func (d *DaoGaoShuShuPayAclDo) IsSuccess() bool {
	return d.DaoGaoShuShuExceptionRspAclDo.IsRspSuccess() && d.Success
}

func (d *DaoGaoShuShuPayAclDo) GetMessage() string {
	return d.Message
}

func (d *DaoGaoShuShuPayAclDo) GetErrCode() string {
	return d.ErrCode
}

func (d *DaoGaoShuShuPayAclDo) IsResellerBalanceErr() bool {
	return !d.DaoGaoShuShuExceptionRspAclDo.IsRspSuccess() && d.DaoGaoShuShuExceptionRspAclDo.NeedRefund()
}

type DaoGaoShuShuQueryAclDo struct {
	DaoGaoShuShuExceptionRspAclDo
	OrderNo       string                                 `json:"orderNo"`
	Uid           string                                 `json:"uid"`
	TotalAmount   float64                                `json:"originalAmount"`
	PayAmount     float64                                `json:"totalAmount"`
	Title         string                                 `json:"title"`
	PayStatus     bool                                   `json:"payStatus"`
	PayStatusText string                                 `json:"payStatusText"`
	PayTime       string                                 `json:"payTime"`
	Status        valobj.DanGaoShuShuOrderQueryStatusObj `json:"status"`
	StatusText    string                                 `json:"statusText"`
	CreateTime    string                                 `json:"createTime"`
	OriginalInfo  string                                 `json:"originalInfo"`
	DetailUrl     string                                 `json:"detailUrl"`
	Goods         []*DaoGaoShuShuQueryGoodsAclDo         `json:"goods"`
	OrderType     valobj.DanGaoShuShuOrderTypeObj        `json:"orderType"`
}

func (d *DaoGaoShuShuQueryAclDo) GetOrderDetailUrl() string {
	return d.DetailUrl
}

func (d *DaoGaoShuShuQueryAclDo) IsCancel() bool {
	return d.Status.IsCancel()
}

type DaoGaoShuShuQueryGoodsAclDo struct {
	DaoGaoShuShuExceptionRspAclDo
	Name        string  `json:"name"`
	Image       string  `json:"image"`
	Price       float64 `json:"price"`
	Num         int     `json:"num"`
	OriginPrice float64 `json:"originalPrice"`
}

func (d *DaoGaoShuShuQueryGoodsAclDo) GetTotalAmount() float64 {
	return decimal.NewFromFloat(d.Price).Mul(decimal.NewFromInt(int64(d.Num))).InexactFloat64()
}

type DaoGaoShuShuExceptionRspAclDo struct {
	Code    int    `json:"code"`
	Reason  string `json:"reason"`
	Message string `json:"message"`
}

func (d *DaoGaoShuShuExceptionRspAclDo) GetErrCode() int {
	return d.Code
}
func (d *DaoGaoShuShuExceptionRspAclDo) GetErrReason() string {
	return d.Reason
}

func (d *DaoGaoShuShuExceptionRspAclDo) GetErrMessage() string {
	return d.Message
}

func (d *DaoGaoShuShuExceptionRspAclDo) IsRspSuccess() bool {
	return d == nil || d.Code == 0
}

func (d *DaoGaoShuShuExceptionRspAclDo) NeedRefund() bool {
	return d.IsClosed() || d.IsBalanceErr()
}

func (d *DaoGaoShuShuExceptionRspAclDo) IsBalanceErr() bool {
	return d.GetErrReason() == "BALANCE_NOT_ENOUGH"
}

func (d *DaoGaoShuShuExceptionRspAclDo) IsClosed() bool {
	return d.GetErrReason() == "TRADE_CLOSED"
}
