package acldo

import (
	"cardMall/internal/biz/rpc/calvalobj"
)

type PayNotifyResponseDo struct {
	Method calvalobj.NotifyType    `json:"method"`
	Order  *PayNotifyResponseOrder `json:"order"`
}

type PayNotifyResponseOrder struct {
	// 订单ID
	Id int32 `json:"ShopId"`
	//支付中心订单号
	No string `json:"no"`
	//我方订单号
	OutTradeNo string `json:"outTradeNo"`
	// 支付金额(单位分)
	Amount int64 `json:"amount"`
	// 状态
	Status calvalobj.PayStatus
	// 自定义产品信息
	CustomProduct *PayCenterProduct `json:"customProduct"`
}

type RefundNotifyResponseDo struct {
	Method calvalobj.NotifyType        `json:"method"`
	Order  *RefundNotifyResponseRefund `json:"order"`
}

type RefundNotifyResponseRefund struct {
	// 订单ID
	OrderId int32 `json:"orderId"`

	OutTradeNo string `json:"OrderNo"`
	// 退款金额(单位分)
	Amount int64 `json:"amount"`
	// 退款原因
	Reason string `json:"reason"`
	// 退款状态
	Status calvalobj.RefundStatus
	// 退款时间
	RefundedAt int
	// 退款ID
	RefundId string
}

// PayCenterProduct 自定义商品详情
type PayCenterProduct struct {
	// 商品ID
	ID int32 `json:"id"`
	// 商品名称
	Name string `json:"name"`
	// 商品价格
	Price int64 `json:"price"`
	// 商品说明信息
	Remark string `json:"remark"`
	// 商品属性
	Attr string `json:"attr"`
	// 商品状态
	Status calvalobj.PayStatus `json:"status"`
	// 商品创建时间 unix时间戳
	CreatedAt int64 `json:"createdAt"`
	// 商品更新时间 unix时间戳
	UpdatedAt int64 `json:"updatedAt"`
	// 商户ID
	MerchantID int32 `json:"merchantId"`
	// 企业ID
	CustomerId uint32 `json:"customerId"`
	// 商城ID
	ShopId uint32 `json:"shopId"`
}
