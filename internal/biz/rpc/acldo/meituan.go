package acldo

import (
	"cardMall/internal/biz/rpc/calvalobj"
	"encoding/json"
	"strconv"
)

// MeiTuanRsp 响应公共参数
type MeiTuanRsp struct {
	TraceId string `json:"traceId"`
	Status  int    `json:"status"`
	Msg     string `json:"msg"`
	Data    string `json:"data"`
}

type MeiTuanOrderDetailDo struct {
	OrderStatus     calvalobj.MeiTuanOrderStatusObj          `json:"orderStatus"`
	LogisticsStatus calvalobj.MeiTuanOrderLogisticsStatusObj `json:"logisticsStatus"`
	GoodsList       []*MeiTuanOrderDetailGoods               `json:"foodList"`
}

type MeiTuanOrderDetailGoods struct {
	GoodsName  string `json:"foodName"`
	GoodsPrice string `json:"foodPrice"`
	Quantity   int    `json:"foodCount"`
}

type MeiTuanOrderDetailGoodsJson struct {
	GoodsName  string  `json:"goods_name"`
	GoodsPrice float64 `json:"goods_price"`
	Quantity   int     `json:"quantity"`
}

func (m *MeiTuanOrderDetailDo) ToJson() string {
	d := make([]*MeiTuanOrderDetailGoodsJson, 0, len(m.GoodsList))
	for _, val := range m.GoodsList {
		goodsPrice, err := strconv.ParseFloat(val.GoodsPrice, 64)
		if err != nil {
			panic(err)
		}
		goods := &MeiTuanOrderDetailGoodsJson{
			GoodsName:  val.GoodsName,
			GoodsPrice: goodsPrice,
			Quantity:   val.Quantity,
		}
		d = append(d, goods)
	}
	res, err := json.Marshal(d)
	if err != nil {
		panic(err)
	}
	return string(res)
}

func (m *MeiTuanOrderDetailDo) IsFinish() bool {
	return m.LogisticsStatus == calvalobj.MeiTuanOrderLogisticsStatusFinish && m.OrderStatus == calvalobj.MeiTuanOrderStatusFinish
}

func (m *MeiTuanOrderDetailDo) IsCancel() bool {
	return m.LogisticsStatus == calvalobj.MeiTuanOrderLogisticsStatusCancel || m.OrderStatus == calvalobj.MeiTuanOrderStatusCancel
}
