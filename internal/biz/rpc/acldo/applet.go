package acldo

type AppletAccessTokenRsp struct {
	AccessToken string `json:"access_token"`
	ExpiresIn   int    `json:"expires_in"`
}

type AppletSchemeRsp struct {
	ErrorCode int    `json:"errcode"`
	ErrMsg    string `json:"errmsg"`
	OpenLink  string `json:"openlink"`
}

type AppletUserPhoneNumberRsp struct {
	ErrorCode int                    `json:"errcode"`
	ErrMsg    string                 `json:"errmsg"`
	Data      *AppletUserPhoneNumber `json:"phone_info"`
}

type AppletUserPhoneNumber struct {
	PhoneNumber     string `json:"phoneNumber"`
	PurePhoneNumber string `json:"purePhoneNumber"`
	CountryCode     string `json:"countryCode"`
}
