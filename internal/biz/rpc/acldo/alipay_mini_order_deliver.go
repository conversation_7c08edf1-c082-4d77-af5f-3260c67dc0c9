package acldo

import "encoding/json"

type AlipayMiniOrderDeliverRsp struct {
	AlipayOpenMiniRsp
}

type AlipayMiniOrderDeliverDo struct {
	Rsp *AlipayMiniOrderDeliverRsp `json:"alipay_open_mini_order_delivery_send_response"`
}

func (a *AlipayMiniOrderDeliverDo) IsSuccess() bool {
	return a.Rsp.IsSuccess()
}

func (a *AlipayMiniOrderDeliverDo) GetSubMsg() string {
	return a.Rsp.GetSubMsg()
}

func (a *AlipayMiniOrderDeliverDo) GetCode() string {
	return a.Rsp.GetSubMsg()
}

func (a *AlipayMiniOrderDeliverDo) ToJson() string {
	r, _ := json.Marshal(a)
	return string(r)
}
