package acldo

import (
	"cardMall/internal/biz/valobj"
	"encoding/json"
	"strconv"
)

type ResellerDo struct {
	Id        int                      `json:"id"`
	Name      string                   `json:"name"`
	SecretKey string                   `json:"secretKey"`
	Status    valobj.ResellerStatusObj `json:"status"`
	Balance   float64                  `json:"balance"`
}

func (r *ResellerDo) Json() string {
	s, _ := json.Marshal(r)
	return string(s)
}

func (r *ResellerDo) Init(s string) error {
	return json.Unmarshal([]byte(s), &r)
}

func (r *ResellerDo) IsNil() bool {
	return r == nil || r.Id == 0
}

func (r *ResellerDo) IsEnable() bool {
	return r.Status.IsEnable()
}

func (r *ResellerDo) GetIdToString() string {
	return strconv.Itoa(r.Id)
}
