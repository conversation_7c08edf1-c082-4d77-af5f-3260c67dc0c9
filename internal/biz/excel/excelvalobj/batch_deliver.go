package excelvalobj

type BatchDeliverExcelMap map[string]string

var batchDeliverExcelMap BatchDeliverExcelMap = map[string]string{
	"orderNumber": "订单号",
	"skuId":       "SKU ID",
	"originalNum": "数量",
	// "kdCode":        "快递编码",
	"kdName":    "快递公司",
	"expressNo": "物流单号",
	"errMsg":    "错误原因",
}

// map 是无序的，所以需要一个排序的keys
var batchDeliverExcelMapSortKeys = []string{
	"orderNumber",
	"skuId",
	"originalNum",
	// "kdCode",
	"kdName",
	"expressNo",
	"errMsg",
}

// GetBatchDeliverExcelMap 获取BatchDeliverExcelMap
func GetBatchDeliverExcelMap(keys ...string) BatchDeliverExcelMap {
	if len(keys) == 0 {
		return batchDeliverExcelMap
	}
	resMap := make(BatchDeliverExcelMap)
	for _, k := range keys {
		if _, ok := batchDeliverExcelMap[k]; ok {
			resMap[k] = batchDeliverExcelMap[k]
		}
	}
	return resMap
}

// GetHeader 获取排序后的values
func (p BatchDeliverExcelMap) GetHeader() []string {
	res := make([]string, 0, len(p))
	for _, v := range batchDeliverExcelMapSortKeys {
		if _, ok := p[v]; ok {
			res = append(res, p[v])
		}
	}
	return res
}

// GetKeys 获取排序后的keys
func (p BatchDeliverExcelMap) GetKeys() []string {
	res := make([]string, 0, len(p))
	for _, v := range batchDeliverExcelMapSortKeys {
		if _, ok := p[v]; ok {
			res = append(res, v)
		}
	}
	return res
}
