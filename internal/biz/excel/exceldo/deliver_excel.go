package exceldo

import (
	"cardMall/api/apierr"
	"fmt"
	"github.com/go-kratos/kratos/v2/errors"
	"strconv"
)

// ExcelItem 单条excel数据
type ExcelItem struct {
	SkuNo          string `json:"skuNo"`          //
	SkuId          string `json:"skuId"`          //
	Num            int    `json:"num"`            // 数量
	OriginalNum    string `json:"originalNum"`    // 原始数量
	OrderNumber    string `json:"orderNumber"`    // 客户订单号
	KdCode         string `json:"kdCode"`         // 指定发货快递
	KdName         string `json:"kdName"`         // 快递公司名称
	ExpressNo      string `json:"expressNo"`      // 快递单号
	HasErr         bool   `json:"hasErr"`         // 是否有错误
	ErrSourceIndex int    `json:"errSourceIndex"` // 错误来着哪一行
	ErrMsg         string `json:"errMsg"`         // 错误信息，只显示第一条

	CustomerId int
	ShopId     int
}

// SetNumByString 设置数量
func (e *ExcelItem) SetNumByString(numStr string, idx int) {
	e.OriginalNum = numStr
	if e.HasError() {
		return
	}
	num, err := strconv.Atoi(numStr)
	if err != nil {
		e.SetErrString("发货数量解析错误", idx)
	} else {
		if num < 0 {
			e.SetErrString("发货数量不能小于0", idx)
		} else {
			e.Num = num
		}
	}
}

// SetErr 设置错误信息
func (e *ExcelItem) SetErr(err error, idx int) {
	if !e.HasError() {
		e.HasErr = true
		e.ErrSourceIndex = idx
		e.ErrMsg = errors.FromError(err).GetMessage()
	}
}

// SetErrString 设置错误信息
func (e *ExcelItem) SetErrString(errMsg string, idx int) {
	if !e.HasError() {
		e.HasErr = true
		e.ErrSourceIndex = idx
		e.ErrMsg = errMsg
	}
}

// HasError 是否有错误
func (e *ExcelItem) HasError() bool {
	return e.ErrMsg != "" && e.HasErr
}

// GetShowErrMsg 获取显示的错误信息
func (e *ExcelItem) GetShowErrMsg(idx int) string {
	if !e.HasError() {
		return ""
	}
	if e.ErrSourceIndex == idx {
		return e.ErrMsg
	}
	return fmt.Sprintf("错误来自第%d行:%s", e.ErrSourceIndex+1, e.ErrMsg)
}

// GetMapKey 获取map key
func (e *ExcelItem) GetMapKey() string {
	return e.OrderNumber + "-" + e.ExpressNo
}

// ExpressErrKey 快递错误key
func (e *ExcelItem) ExpressErrKey() string {
	return "ExpressErr:" + e.ExpressNo
}

// OrderErrKey 订单错误key
func (e *ExcelItem) OrderErrKey() string {
	return "OrderErr:" + e.OrderNumber
}

// Validate 校验
func (e *ExcelItem) Validate() error {
	if e.SkuNo == "" {
		return apierr.ErrorParam("skuId不能为空")
	}
	if e.OrderNumber == "" {
		return apierr.ErrorParam("订单号不能为空")
	}
	if e.Num == 0 {
		return apierr.ErrorParam("发货数量错误")
	}
	if e.KdName == "" {
		return apierr.ErrorParam("快递公司不能为空")
	}
	if e.ExpressNo == "" {
		return apierr.ErrorParam("快递单号不能为空")
	}
	return nil
}
