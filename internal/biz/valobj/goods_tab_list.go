package valobj

//go:generate stringer -type=GoodsTabList -linecomment -trimfuncname=GoodsTabList -inputcamelcase
type GoodsTabList int

// tab 1-全部商品 2-售卖中 3-已下架
// 商品审核 10-全部 11-待审核 12-审核通过 13-审核驳回 14-已撤销
// 20-商品草稿
const (
	GoodsTabListAll         GoodsTabList = 1
	GoodsTabListOnSale      GoodsTabList = 2
	GoodsTabListOffShelf    GoodsTabList = 3
	GoodsTabListAuditAll    GoodsTabList = 10
	GoodsTabListWaitAudit   GoodsTabList = 11
	GoodsTabListAuditPass   GoodsTabList = 12
	GoodsTabListAuditFail   GoodsTabList = 13
	GoodsTabListAuditCancel GoodsTabList = 14
	GoodsTabListDraft       GoodsTabList = 20
)
