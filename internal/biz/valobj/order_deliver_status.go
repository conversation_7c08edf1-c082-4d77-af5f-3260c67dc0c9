package valobj

type OrderDeliverEnableObj int

// 是否启用
const (
	OrderDeliverDisable OrderDeliverEnableObj = iota
	OrderDeliverEnable
)

// IsEnable .
func (o OrderDeliverEnableObj) IsEnable() bool {
	return o == OrderDeliverEnable
}

// IsDisable .
func (o OrderDeliverEnableObj) IsDisable() bool {
	return o == OrderDeliverDisable
}

var orderDeliverEnableMap = map[OrderDeliverEnableObj]string{
	OrderDeliverDisable: "作废",
	OrderDeliverEnable:  "正常",
}

// GetName .
func (o OrderDeliverEnableObj) GetName() string {
	v, ok := orderDeliverEnableMap[o]
	if !ok {
		return "未知"
	}
	return v
}

// ToInt .
func (o OrderDeliverEnableObj) ToInt() int {
	return int(o)
}
