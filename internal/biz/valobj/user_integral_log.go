package valobj

type UserIntegralLogTypeObj int

type UserIntegralLogStatusObj int

type UserIntegralLogIncrSourceObj int

type UserIntegralLogExpireTaskStatus int

const UserIntegralLogTypeIncr = 1
const UserIntegralLogTypeReduce = 2

const UserIntegralLogStatusInProgress = 1
const UserIntegralLogStatusFinish = 2
const UserIntegralLogStatusCanceled = 3

// IntegralRate 积分抵扣比例 IntegralRate积分=1元
const IntegralRate = 100

const (
	UserIntegralLogIncrSourceUnknown UserIntegralLogIncrSourceObj = iota
	UserIntegralLogIncrSourceBuyGoods
	UserIntegralLogIncrSourceSystem
)
const (
	UserIntegralLogExpireTaskStatusUnknown UserIntegralLogExpireTaskStatus = iota
	UserIntegralLogExpireTaskStatusPending
	UserIntegralLogExpireTaskStatusDone
)

var UserIntegralLogTypeMap = map[UserIntegralLogTypeObj]string{
	UserIntegralLogTypeIncr:   "增加",
	UserIntegralLogTypeReduce: "减少",
}

func (u UserIntegralLogTypeObj) GetName() string {
	s, ok := UserIntegralLogTypeMap[u]
	if !ok {
		return ""
	}
	return s
}

func (u UserIntegralLogTypeObj) IsValid() bool {
	_, ok := UserIntegralLogTypeMap[u]
	return ok
}
