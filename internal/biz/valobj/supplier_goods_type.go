package valobj

type SupplierGoodsTypeObj int

const (
	SupplierGoodsTypeVirtual SupplierGoodsTypeObj = 1
	SupplierGoodsTypeEntity  SupplierGoodsTypeObj = 2
)

var supplierGoodsTypeMap = map[SupplierGoodsTypeObj]string{
	SupplierGoodsTypeEntity:  "实体",
	SupplierGoodsTypeVirtual: "虚拟",
}

// ToInt 转换为 SupplierGoodsTypeObj
func (g SupplierGoodsTypeObj) ToInt() int {
	return int(g)
}

// IsExists 判断是否存在
func (g SupplierGoodsTypeObj) IsExists() bool {
	_, ok := supplierGoodsTypeMap[g]
	return ok
}

// IsEntity 判断是否实体商品
func (g SupplierGoodsTypeObj) IsEntity() bool {
	return g == SupplierGoodsTypeEntity
}

// IsVirtual 判断是否虚拟商品
func (g SupplierGoodsTypeObj) IsVirtual() bool {
	return g == SupplierGoodsTypeVirtual
}
