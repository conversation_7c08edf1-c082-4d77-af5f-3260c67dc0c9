package valobj

import (
	"cardMall/internal/pkg/hyt/hytvo"
	"fmt"
)

// AfterSaleType 1-仅退款 2-退款退货 3-换货
//
//go:generate stringer -type=AfterSaleType -linecomment -trimfuncname=AfterSaleType -inputcamelcase
type AfterSaleType int

const (
	AfterSaleTypeRefund       AfterSaleType = 1 // 仅退款
	AfterSaleTypeRefundReturn AfterSaleType = 2 // 退款退货
	AfterSaleTypeExchange     AfterSaleType = 3 // 换货
	AfterSaleTypeReissue      AfterSaleType = 4 // 补发
)

// OrderStatus 可以申请售后的订单状态
func (a AfterSaleType) OrderStatus() []OrderStatusObj {
	switch a {
	case AfterSaleTypeRefund:
		return []OrderStatusObj{OrderStatusAwaitingShip, OrderStatusShipping, OrderStatusShipped, OrderStatusFinish, OrderStatusRefundPart}
	case AfterSaleTypeRefundReturn, AfterSaleTypeExchange, AfterSaleTypeReissue:
		return []OrderStatusObj{OrderStatusFinish, OrderStatusRefundPart}
	default:
		return []OrderStatusObj{}
	}
}

func (a AfterSaleType) ToHytAfterSaleType() (hytvo.AfterSaleTypeObj, error) {
	switch a {
	case AfterSaleTypeRefund:
		return hytvo.AfterSaleTypeRefundNoReturn, nil
	case AfterSaleTypeRefundReturn:
		return hytvo.AfterSaleTypeReturnRefund, nil
	case AfterSaleTypeExchange:
		return hytvo.AfterSaleTypeExchangeSamePro, nil
	case AfterSaleTypeReissue:
		return hytvo.AfterSaleTypeReissue, nil
	default:
		return hytvo.AfterSaleTypeObj(0), fmt.Errorf("未知售后类型")
	}
}

// NeedBuyerDeliver 是否需要买家物流
func (a AfterSaleType) NeedBuyerDeliver() bool {
	return a.IsRefundReturn() || a.IsExchange()
}

// IsAfterSaleExchange 是否需要买家物流
func (a AfterSaleType) IsAfterSaleExchange() bool {
	return a.IsExchange() || a.IsReissue()
}

// IsAfterSaleRefund 是否需要买家物流
func (a AfterSaleType) IsAfterSaleRefund() bool {
	return a.IsRefund() || a.IsRefundReturn()
}

// GetApprovedStatus 获取售后申请通过后的状态
func (a AfterSaleType) GetApprovedStatus() AfterSaleStatus {
	switch a {
	case AfterSaleTypeRefund:
		return AfterSaleStatusWaitRefund
	case AfterSaleTypeRefundReturn:
		return AfterSaleStatusWaitBuyerDeliver
	case AfterSaleTypeExchange:
		// 等待买家发货
		return AfterSaleStatusWaitBuyerDeliver
	case AfterSaleTypeReissue:
		// 不需要买家发货,直接走到待换货状态
		// 补发，不需要用户发货
		return AfterSaleStatusWaitExchange
	}
	return AfterSaleStatus(0)
}
