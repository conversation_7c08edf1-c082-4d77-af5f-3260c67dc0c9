package valobj

import "time"

// OrderDefaultShipTime 订单默认三天内发货
const OrderDefaultShipTime = 86400 * 2

// OrderShipTodayTimeLimit 当如发货时间限制，超过此时间则发货时间会从第二天开始计算
const OrderShipTodayTimeLimit = 21

func GetOrderTodayShipTime(now time.Time) time.Time {
	day := now.Day()
	if now.Hour() >= OrderShipTodayTimeLimit {
		day += 1
	}
	endTime := time.Date(now.Year(), now.Month(), day, 23, 59, 59, 0, now.Location())
	return endTime
}
