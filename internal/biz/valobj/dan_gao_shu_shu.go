package valobj

import "strconv"

// DanGaoShuShuChangeTypeObj 变更类型
type DanGaoShuShuChangeTypeObj int

const (
	DanGaoShuShuChangeTypeDeliver DanGaoShuShuChangeTypeObj = 101 //配送状态
)

func (d DanGaoShuShuChangeTypeObj) IsDeliver() bool {
	return d == DanGaoShuShuChangeTypeDeliver
}

// DanGaoShuShuChangeStatusObj 配送状态
type DanGaoShuShuChangeStatusObj int

const (
	DanGaoShuShuChangeStatusReceived DanGaoShuShuChangeStatusObj = 40 //已收货
)

func (d DanGaoShuShuChangeStatusObj) IsReceived() bool {
	return d == DanGaoShuShuChangeStatusReceived
}

func (d DanGaoShuShuChangeStatusObj) String() string {
	return strconv.Itoa(int(d))
}

type DanGaoShuShuOrderQueryStatusObj int

const (
	DanGaoShuShuOrderQueryStatusCancel DanGaoShuShuOrderQueryStatusObj = 9
)

func (d DanGaoShuShuOrderQueryStatusObj) IsCancel() bool {
	return d == DanGaoShuShuOrderQueryStatusCancel
}

type DanGaoShuShuOrderTypeObj int

const (
	DanGaoShuShuOrderTypeNormal DanGaoShuShuOrderTypeObj = 1 //蛋糕
	DanGaoShuShuOrderTypeCard                            = 2 //电子卡
)

func (d DanGaoShuShuOrderTypeObj) IsNormal() bool {
	return d == DanGaoShuShuOrderTypeNormal
}

func (d DanGaoShuShuOrderTypeObj) IsCard() bool {
	return d == DanGaoShuShuOrderTypeCard
}
