package valobj

// AdminStatusObj 管理员类型
//
//go:generate stringer -type=AdminStatusObj -linecomment -trimfuncname=AdminStatus -inputcamelcase
type AdminStatusObj int

const (
	AdminStatusEnable  AdminStatusObj = iota + 1 // 启用
	AdminStatusDisable                           // 禁用
)

func (a AdminStatusObj) IsValid() bool {
	switch a {
	case AdminStatusEnable, AdminStatusDisable:
		return true
	}
	return false
}

// AdminTypeObj 管理员类型
//
//go:generate stringer -type=AdminTypeObj -linecomment -trimfuncname=AdminType -inputcamelcase
type AdminTypeObj int

// Saas供应商也属于企业供应商
const (
	AdminTypeShopAdmin           AdminTypeObj = iota + 1 // 商城管理员
	AdminTypeShopSupplier                                // 商城供应商
	AdminTypeCustomer                                    // 企业管理员
	AdminTypeCustomerSupplier                            // 企业供应商
	AdminTypeSaasAdmin                                   // 平台管理员
	AdminTypeSaasSupplier                                // 平台供应商
	AdminTypeCustomerToShopAdmin AdminTypeObj = 100      // 企业管理员转商城管理员
	AdminTypeSaasToCustomerAdmin AdminTypeObj = 101      // 平台管理员转企业管理员
	AdminTypeSaasToShopAdmin     AdminTypeObj = 102      // 平台管理员转商城管理员
	AdminTypeCustomerToSupplier  AdminTypeObj = 103      // 企业管理员转供应商
	AdminTypeShopToSupplier      AdminTypeObj = 104      // 商城管理员转供应商
)

func (a AdminTypeObj) IsSupplier() bool {
	return a.IsShopSupplier() || a.IsCustomerSupplier() || a.IsSaasSupplier() || a.IsCustomerToSupplier() || a.IsShopToSupplier()
}

var frontMap = map[AdminTypeObj]string{
	AdminTypeShopAdmin:        "/shop",
	AdminTypeShopSupplier:     "/supplier",
	AdminTypeCustomer:         "/business",
	AdminTypeCustomerSupplier: "/supplier",
	AdminTypeSaasAdmin:        "/platform",
	AdminTypeSaasSupplier:     "/supplier",
}

func (a AdminTypeObj) GetFrontUrlSuffix() string {
	return frontMap[a]
}

var adminTypeTransfer = map[AdminTypeObj]struct{}{
	AdminTypeCustomerToShopAdmin: struct{}{},
	AdminTypeSaasToCustomerAdmin: struct{}{},
	AdminTypeSaasToShopAdmin:     struct{}{},
	AdminTypeCustomerToSupplier:  struct{}{},
	AdminTypeShopToSupplier:      struct{}{},
}

func (a AdminTypeObj) IsTransfer() bool {
	_, ok := adminTypeTransfer[a]
	return ok
}

func (a AdminTypeObj) IsCustomerTransfer() bool {
	return a.IsCustomerToSupplier() || a.IsCustomerToShopAdmin()
}

func (a AdminTypeObj) IsTransferToSupplier() bool {
	return a.IsCustomerToSupplier() || a.IsShopToSupplier()
}

func (a AdminTypeObj) IsSaasTransfer() bool {
	return a.IsSaasToCustomerAdmin() || a.IsSaasToShopAdmin()
}

// AdminIsRootObj 是否是超管，1=否，2=是
//
//go:generate stringer -type=AdminIsRootObj -linecomment -trimfuncname=AdminIsRoot -inputcamelcase
type AdminIsRootObj int

const (
	AdminIsRootNo  AdminIsRootObj = iota + 1 // 否
	AdminIsRootYes                           // 是
)
