package valobj

import (
	"encoding/json"

	"cardMall/internal/pkg/convert"
)

const (
	SysConfigLoginMode         string = "login_mode"
	SysConfigLoginType         string = "login_type"
	SysConfigRegisterBindPhone string = "register_bind_phone"
	SysConfigShopScene         string = "shop_scene"
	SysConfigLoginPlatform     string = "login_platform"
	SysConfigShopLogo          string = "shop_logo"
	SysConfigShopColor         string = "shop_color"

	//服务条款与服务政策、常见问题、商务合作电话、售后电话、客服电话
	SysConfigService              string = "shop_service"
	SysConfigQA                   string = "shop_qa"
	SysConfigPartnerPhone         string = "shop_partner_phone"
	SysConfigAfterSalePhone       string = "shop_after_sale_phone"
	SysConfigCustomerSupportPhone string = "shop_customer_support_phone"

	SysConfigGoodsShowType          string = "goods_show_type"
	SysConfigShopShowBalance        string = "shop_show_balance"
	SysConfigShopThirdPayUrl        string = "shop_openapi_third_pay_url"
	SysConfigShopShopSecret         string = "shop_openapi_secret"
	SysConfigShopNotifyUrl          string = "shop_openapi_notify_url"
	SysConfigShopClientCategoryMode string = "shop_client_category_mode"

	SysConfigShopPorcelainStatus string = "shop_porcelain_status"
)

// 登录模式 1.游客模式 2.强制登录模式
//
//go:generate stringer -type=ConfigLoginMode -linecomment -trimfuncname=ConfigLoginMode -inputcamelcase
type ConfigLoginMode int

const (
	ConfigLoginModeTourist ConfigLoginMode = 1 // 游客模式
	ConfigLoginModeMust    ConfigLoginMode = 2 // 强制登录模式
)

// 登录方式 1.手机号+密码 2.手机号+验证码 3.账号+密码 4.微信OpenID静默登录
//
//go:generate stringer -type=ConfigLoginType -linecomment -trimfuncname=ConfigLoginType -inputcamelcase
type ConfigLoginType int

const (
	ConfigLoginTypePhoneAndPassword   ConfigLoginType = 1 // 手机号+密码
	ConfigLoginTypePhoneAndSms        ConfigLoginType = 2 // 手机号+验证码
	ConfigLoginTypeAccountAndPassword ConfigLoginType = 3 // 账号+密码
	ConfigLoginTypeWechat             ConfigLoginType = 4 // 微信OpenID静默登录
)

func (c ConfigLoginType) IsMustRegisterBindPhone() bool {
	return c.IsWechat()
}

func (c ConfigLoginType) IsCanRegisterBindPhone() bool {
	return c.IsWechat() || c.IsAccountAndPassword()
}

func (c ConfigLoginType) GetUpdateDisable() map[ConfigLoginType]struct{} {
	return map[ConfigLoginType]struct{}{
		ConfigLoginTypeAccountAndPassword: struct{}{},
		ConfigLoginTypeWechat:             struct{}{},
	}
}

func (c ConfigLoginType) IsUpdateDisable() bool {
	_, ok := c.GetUpdateDisable()[c]
	return ok
}

// 注册是否强制绑定手机号 1 否 2 是
//
//go:generate stringer -type=ConfigRegisterBindPhone -linecomment -trimfuncname=ConfigRegisterBindPhone -inputcamelcase
type ConfigRegisterBindPhone int

const (
	ConfigRegisterBindPhoneNo  ConfigRegisterBindPhone = 1 // 否
	ConfigRegisterBindPhoneYes ConfigRegisterBindPhone = 2 // 是
)

// SysConfigShopSceneObj 经营场景 1 虚拟权益 2 实物 3 福利 4 分销 5 积分 6 电影票 7 肯德基 8 美团外卖 7 蛋糕叔叔
//
//go:generate stringer -type=SysConfigShopSceneObj -linecomment -trimfuncname=SysConfigShopScene -inputcamelcase
type SysConfigShopSceneObj int

const (
	SysConfigShopSceneVir          SysConfigShopSceneObj = iota + 1 // 虚拟权益
	SysConfigShopSceneEntity                                        // 实物
	SysConfigShopSceneCard                                          // 福利
	SysConfigShopSceneDistribution                                  // 分销
	SysConfigShopSceneIntegral                                      // 积分
	SysConfigShopSceneCinema                                        // 电影票
	SysConfigShopSceneKFC                                           // KFC
	SysConfigShopSceneMeiTuan                                       // 美团外卖
	SysConfigShopSceneDGSS                                          // 蛋糕叔叔
	SysConfigShopSceneCoupon                                        // 优惠券
	SysConfigShopSceneStarBucks                                     // 星巴克
)

type SysConfigShopSceneSlice []SysConfigShopSceneObj

func (s SysConfigShopSceneSlice) IsValid() bool {
	for _, v := range s {
		if !v.Exists() {
			return false
		}
	}
	return true
}

func (s SysConfigShopSceneSlice) IsVirEnable() bool {
	for _, v := range s {
		if v.IsVir() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsEntityEnable() bool {
	for _, v := range s {
		if v.IsEntity() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsCardEnable() bool {
	for _, v := range s {
		if v.IsCard() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsDistributionEnable() bool {
	for _, v := range s {
		if v.IsDistribution() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsIntegralEnable() bool {
	for _, v := range s {
		if v.IsIntegral() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsKfcEnable() bool {
	for _, v := range s {
		if v.IsKFC() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsMeiTuanEnable() bool {
	for _, v := range s {
		if v.IsMeiTuan() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsCinemaEnable() bool {
	for _, v := range s {
		if v.IsCinema() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsDGSSEnable() bool {
	for _, v := range s {
		if v.IsDGSS() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsCouponEnable() bool {
	for _, v := range s {
		if v.IsCoupon() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) IsStarBucksEnable() bool {
	for _, v := range s {
		if v.IsStarBucks() {
			return true
		}
	}
	return false
}

func (s SysConfigShopSceneSlice) ToString() (string, error) {
	b, err := json.Marshal(s)
	return string(b), err
}

// ToOpenMap 转换为open map
func (s SysConfigShopSceneSlice) ToOpenMap() map[string]string {
	return map[string]string{
		"shop_scene_open_dgss":         convert.Bool2Str(s.IsDGSSEnable()),
		"shop_scene_open_cinema":       convert.Bool2Str(s.IsCinemaEnable()),
		"shop_scene_open_meituan":      convert.Bool2Str(s.IsMeiTuanEnable()),
		"shop_scene_open_kfc":          convert.Bool2Str(s.IsKfcEnable()),
		"shop_scene_open_distribution": convert.Bool2Str(s.IsDistributionEnable()),
		"shop_scene_open_integral":     convert.Bool2Str(s.IsIntegralEnable()),
		"shop_scene_open_card":         convert.Bool2Str(s.IsCardEnable()),
		"shop_scene_open_entity":       convert.Bool2Str(s.IsEntityEnable()),
		"shop_scene_open_virtual":      convert.Bool2Str(s.IsVirEnable()),
		"shop_scene_open_coupon":       convert.Bool2Str(s.IsCouponEnable()),
	}
}

func (s SysConfigShopSceneSlice) GetKey() string {
	return SysConfigShopScene
}

// SysConfigLoginPlatformObj 登录平台 1 H5界面 2 微信公众号 3 微信小程序 4 支付宝小程序
//
//go:generate stringer -type=SysConfigLoginPlatformObj -linecomment -trimfuncname=SysConfigLoginPlatform -inputcamelcase
type SysConfigLoginPlatformObj int

const (
	SysConfigLoginPlatformH5           SysConfigLoginPlatformObj = iota + 1 // H5界面
	SysConfigLoginPlatformWxOfficial                                        // 微信公众号
	SysConfigLoginPlatformWxApplet                                          // 微信小程序
	SysConfigLoginPlatformAlipayApplet                                      // 支付宝小程序
)

type SysConfigLoginPlatformSlice []SysConfigLoginPlatformObj

func (s SysConfigLoginPlatformSlice) IsValid() bool {
	for _, v := range s {
		if !v.Exists() {
			return false
		}
	}
	return true
}

func (s SysConfigLoginPlatformSlice) HasOpenIdLoginPlatform() bool {
	for _, v := range s {
		if v.IsWxApplet() || v.IsAlipayApplet() || v.IsWxOfficial() {
			return true
		}
	}
	return false
}

func (s SysConfigLoginPlatformSlice) IsH5Enable() bool {
	for _, v := range s {
		if v.IsH5() {
			return true
		}
	}
	return false
}

func (s SysConfigLoginPlatformSlice) IsWechatOfficialAccountEnable() bool {
	for _, v := range s {
		if v.IsWxOfficial() {
			return true
		}
	}
	return false
}

func (s SysConfigLoginPlatformSlice) IsWechatAppletEnable() bool {
	for _, v := range s {
		if v.IsWxApplet() {
			return true
		}
	}
	return false
}

func (s SysConfigLoginPlatformSlice) IsAlipayAppletEnable() bool {
	for _, v := range s {
		if v.IsAlipayApplet() {
			return true
		}
	}
	return false
}

func (s SysConfigLoginPlatformSlice) IsAppletEnable() bool {
	return s.IsAlipayAppletEnable() || s.IsWechatAppletEnable()
}

func (s SysConfigLoginPlatformSlice) ToString() (string, error) {
	b, err := json.Marshal(s)
	return string(b), err
}

func (s SysConfigLoginPlatformSlice) GetKey() string {
	return SysConfigLoginPlatform
}

// SysConfigGoodsShowTypeObj C端商品展示方式 0 默认 1 SPU维度 2 SKU维度
//
//go:generate stringer -type=SysConfigGoodsShowTypeObj -linecomment -trimfuncname=SysConfigGoodsShowType -inputcamelcase
type SysConfigGoodsShowTypeObj int

const (
	SysConfigGoodsShowTypeDefault SysConfigGoodsShowTypeObj = iota // 默认
	SysConfigGoodsShowTypeSpu                                      // SPU维度
	SysConfigGoodsShowTypeSku                                      // SKU维度
)

// SysConfigShopShowBalanceObj 是否透传企业余额授信 1 否，2 是
//
//go:generate stringer -type=SysConfigShopShowBalanceObj -linecomment -trimfuncname=SysConfigShopShowBalance -inputcamelcase
type SysConfigShopShowBalanceObj int

const (
	SysConfigShopShowBalanceNo  SysConfigShopShowBalanceObj = iota + 1 // 否
	SysConfigShopShowBalanceYes                                        // 是
)

// SysConfigShopClientCategoryModeObj 客户端分类模式 1 默认 2 自定义
//
//go:generate stringer -type=SysConfigShopClientCategoryModeObj -linecomment -trimfuncname=SysConfigShopClientCategoryMode -inputcamelcase
type SysConfigShopClientCategoryModeObj int

const (
	SysConfigShopClientCategoryModeDefault SysConfigShopClientCategoryModeObj = iota + 1 // 默认
	SysConfigShopClientCategoryModeCustom                                                // 自定义
)
