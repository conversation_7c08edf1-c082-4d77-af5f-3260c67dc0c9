package valobj

import "cardMall/internal/pkg/helper"

type BaseGoodsStatusObj int

type BaseGoodsTypeObj int

const (
	BaseGoodsStatusDisable = iota
	BaseGoodsStatusEnable
)

const (
	BaseGoodsTypeUnknown  = 0
	BaseGoodsTypeRecharge = 1
	BaseGoodsTypeCard     = 2
)

var BaseGoodsStatusMap = map[BaseGoodsStatusObj]string{
	BaseGoodsStatusEnable:  "启用",
	BaseGoodsStatusDisable: "禁用",
}

func (d BaseGoodsStatusObj) GetName() string {
	s, ok := BaseGoodsStatusMap[d]
	if !ok {
		return "未知"
	}
	return s
}

var BaseGoodsTypeMap = map[BaseGoodsTypeObj]string{
	BaseGoodsTypeUnknown:  "未知",
	BaseGoodsTypeRecharge: "直充",
	BaseGoodsTypeCard:     "卡密",
}

func (d BaseGoodsTypeObj) GetName() string {
	s, ok := BaseGoodsTypeMap[d]
	if !ok {
		return "未知"
	}
	return s
}

func (d BaseGoodsTypeObj) IsCard() bool {
	return d == BaseGoodsTypeCard
}

func (d BaseGoodsTypeObj) IsRecharge() bool {
	return d == BaseGoodsTypeRecharge
}

func (d BaseGoodsTypeObj) IsUnknown() bool {
	return d == BaseGoodsTypeUnknown
}

func (d BaseGoodsStatusObj) Enable() bool {
	return d == BaseGoodsStatusEnable
}

// BaseGoodsAccountTypeObj 虚拟商品充值账号类型 按二进制位表示，0 其他 1 手机号  2 邮箱 4 QQ号  8  微信
//
//go:generate stringer -type=BaseGoodsAccountTypeObj -linecomment -trimfuncname=BaseGoodsAccountType -inputcamelcase
type BaseGoodsAccountTypeObj int

const (
	BaseGoodsAccountTypeOther  BaseGoodsAccountTypeObj = 0 // 其他
	BaseGoodsAccountTypePhone  BaseGoodsAccountTypeObj = 1 // 手机号
	BaseGoodsAccountTypeEmail  BaseGoodsAccountTypeObj = 2 // 邮箱
	BaseGoodsAccountTypeQQ     BaseGoodsAccountTypeObj = 4 // QQ号
	BaseGoodsAccountTypeWechat BaseGoodsAccountTypeObj = 8 // 微信号
)

func (m BaseGoodsAccountTypeObj) Validate(account string) bool {
	switch m {
	case BaseGoodsAccountTypeOther:
		return true
	case BaseGoodsAccountTypePhone:
		return helper.IsAccountPhone(account)
	case BaseGoodsAccountTypeEmail:
		return helper.IsAccountEmail(account)
	case BaseGoodsAccountTypeQQ:
		return helper.IsAccountQQ(account)
	case BaseGoodsAccountTypeWechat:
		return helper.IsAccountWechat(account)
	default:
		return true
	}
}
