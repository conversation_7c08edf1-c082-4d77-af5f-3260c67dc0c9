package valobj

type GoodsBrandStatusObj int

type GoodsBrandRecommendObj int

type GoodsBrandTypeObj int

const (
	GoodsBrandStatusDisable GoodsBrandStatusObj = iota
	GoodsBrandStatusEnable
)

const (
	GoodsBrandRecommendNo  GoodsBrandRecommendObj = 0
	GoodsBrandRecommendYes GoodsBrandRecommendObj = 1
)

var GoodsBrandStatusMap = map[GoodsBrandStatusObj]string{
	GoodsBrandStatusEnable:  "启用",
	GoodsBrandStatusDisable: "禁用",
}

func (d GoodsBrandStatusObj) GetName() string {
	s, ok := GoodsBrandStatusMap[d]
	if !ok {
		return "未知"
	}
	return s
}

func (d GoodsBrandStatusObj) IsEnable() bool {
	return d == GoodsBrandStatusEnable
}

// GetInt 获取int值
func (d GoodsBrandStatusObj) GetInt() int {
	return int(d)
}

// GetDefault 获取默认值
func (d GoodsBrandStatusObj) GetDefault() GoodsBrandStatusObj {
	return GoodsBrandStatusEnable
}

func (d GoodsBrandStatusObj) IsValid() bool {
	_, ok := GoodsBrandStatusMap[d]

	return ok
}

// GetDefault 获取默认值
func (d GoodsBrandRecommendObj) GetDefault() GoodsBrandRecommendObj {
	return GoodsBrandRecommendNo
}

// GetInt
func (d GoodsBrandRecommendObj) GetInt() int {
	return int(d)
}
