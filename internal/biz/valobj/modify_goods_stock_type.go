package valobj

import "github.com/duke-git/lancet/v2/slice"

type ModifyGoodsStockType int

// 操作类型 1-增加库存 2-减少库存 3-下单消耗
const (
	ModifyGoodsStockTypeAdd         ModifyGoodsStockType = 1
	ModifyGoodsStockTypeSub         ModifyGoodsStockType = 2
	ModifyGoodsStockTypeOrder       ModifyGoodsStockType = 3
	ModifyGoodsStockTypeOrderCancel ModifyGoodsStockType = 4
	ModifyGoodsStockTypeOrderRefund ModifyGoodsStockType = 5
)

var modifyGoodsStockTypeMap = map[ModifyGoodsStockType]string{
	ModifyGoodsStockTypeAdd:         "增加库存",
	ModifyGoodsStockTypeSub:         "减少库存",
	ModifyGoodsStockTypeOrder:       "订单消耗",
	ModifyGoodsStockTypeOrderCancel: "待支付订单取消",
	ModifyGoodsStockTypeOrderRefund: "订单退款",
}

// Exists ..
func (m ModifyGoodsStockType) Exists() bool {
	_, ok := modifyGoodsStockTypeMap[m]
	return ok
}

func (m ModifyGoodsStockType) GetName() string {
	if !m.Exists() {
		return ""
	}
	return modifyGoodsStockTypeMap[m]
}

// 减少库存
var subStockMap = []ModifyGoodsStockType{
	ModifyGoodsStockTypeSub,
	ModifyGoodsStockTypeOrder,
}

// IsSub 减少库存
func (m ModifyGoodsStockType) IsSub() bool {
	return slice.Contain(subStockMap, m)
}
