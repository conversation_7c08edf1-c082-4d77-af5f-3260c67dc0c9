package valobj

type SupplierAccountStatusObj int

const (
	SupplierAccountStatusEnable  SupplierAccountStatusObj = 1
	SupplierAccountStatusDisable SupplierAccountStatusObj = 2
)

var supplierAccountStatusMap = map[SupplierAccountStatusObj]string{
	SupplierAccountStatusEnable: "启用",
}

func (s SupplierAccountStatusObj) GetName() string {
	name, isExist := supplierAccountStatusMap[s]
	if isExist {
		return name
	}
	return "未知"
}

// ToInt 转换为 SupplierAccountStatusObj
func (s SupplierAccountStatusObj) ToInt() int {
	return int(s)
}

func (s SupplierAccountStatusObj) IsValid() bool {
	return s == SupplierAccountStatusEnable || s == SupplierAccountStatusDisable
}

// Disable 禁用
func (s SupplierAccountStatusObj) Disable() bool {
	return s == SupplierAccountStatusDisable
}

// Enable 启用
func (s SupplierAccountStatusObj) Enable() bool {
	return s == SupplierAccountStatusEnable
}
