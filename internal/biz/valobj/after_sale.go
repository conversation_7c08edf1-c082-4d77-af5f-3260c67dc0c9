package valobj

// AfterSaleDeliverType 操作用户类型 1-用户 2-商家 3-供应商
//
//go:generate stringer -type=AfterSaleDeliverType -linecomment -trimfuncname=AfterSaleDeliverType -inputcamelcase
type AfterSaleDeliverType int

const (
	AfterSaleDeliverTypeBuyer    AfterSaleDeliverType = 1 // 用户
	AfterSaleDeliverTypeMerchant AfterSaleDeliverType = 2 // 商家
	AfterSaleDeliverTypeSupplier AfterSaleDeliverType = 3 // 供应商
)

func (a AfterSaleDeliverType) IsSeller() bool {
	return a == AfterSaleDeliverTypeMerchant || a == AfterSaleDeliverTypeSupplier
}

// AfterSaleRelType 1-货易通
//
//go:generate stringer -type=AfterSaleRelType -linecomment -trimfuncname=AfterSaleRelType -inputcamelcase
type AfterSaleRelType int

const (
	AfterSaleRelTypeHyt AfterSaleRelType = 1 // 货易通
)
