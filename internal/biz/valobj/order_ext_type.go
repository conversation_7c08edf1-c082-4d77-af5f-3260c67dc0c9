package valobj

// OrderExtTypeObj 订单扩展类型
//
//go:generate stringer -type=OrderExtTypeObj -linecomment -trimfuncname=OrderExtType -inputcamelcase
type OrderExtTypeObj int

const (
	OrderExtTypeDefault           OrderExtTypeObj = iota //
	OrderExtTypeExchangeOrder                            // 换货
	OrderExtTypeCardBatchCoupon                          // 礼包兑换订单
	OrderExtTypeGoodsIntegralCash                        // 积分+钱购的订单
	OrderExtTypeGoodsIntegral                            // 积分兑换的订单
)

// CanChangeAddress 是否可以修改地址
func (o OrderExtTypeObj) CanChangeAddress() bool {
	return o.IsDefault() || o.IsCardBatchCoupon() || o.IsGoodsIntegral() || o.IsGoodsIntegralCash()
}

// FreightFeeIsFire 运费是否为空
func (o OrderExtTypeObj) FreightFeeIsFire() bool {
	return o.IsCardBatchCoupon() || o.IsGoodsIntegralCash()
}

// CanRefund 是否可以申请售后退款
func (o OrderExtTypeObj) CanRefund() bool {
	return o.IsDefault() || o.IsGoodsIntegral() || o.IsGoodsIntegralCash()
}
