package valobj

type CouponEffectTimeTypeObj int

type CouponStatusObj int

type CouponIsAbolishObj int

type CouponProductLimitObj int

type CouponCollectionTypeObj int

const CouponEffectTimeTypeTimeSlot = 1
const CouponEffectTimeTypeAfterCollection = 2

const CouponStatusNotStart = 1
const CouponStatusStarting = 2
const CouponStatusFinish = 3

const CouponIsAbolishNo = 0
const CouponIsAbolishYes = 1

const CouponProductLimitNone = 1

const CouponProductLimitAppoint = 2

const (
	CouponCollectionTypeUnknown CouponCollectionTypeObj = iota
	CouponCollectionTypeUser
	CouponCollectionTypeSystem
)

var CouponEffectTimeTypeMap = map[CouponEffectTimeTypeObj]string{
	CouponEffectTimeTypeTimeSlot:        "时间段",
	CouponEffectTimeTypeAfterCollection: "领取后xx天有效",
}

func (c CouponEffectTimeTypeObj) GetName() string {
	s, ok := CouponEffectTimeTypeMap[c]
	if !ok {
		return "未知"
	}
	return s
}

var CouponStatusMap = map[CouponStatusObj]string{
	CouponStatusNotStart: "未开始",
	CouponStatusStarting: "进行中",
	CouponStatusFinish:   "已结束",
}

func (c CouponStatusObj) GetName() string {
	s, ok := CouponStatusMap[c]
	if !ok {
		return "未知"
	}
	return s
}

var CouponIsAbolishMap = map[CouponIsAbolishObj]string{
	CouponIsAbolishNo:  "否",
	CouponIsAbolishYes: "是",
}

func (c CouponIsAbolishObj) GetName() string {
	s, ok := CouponIsAbolishMap[c]
	if !ok {
		return "未知"
	}
	return s
}

var CouponProductLimitMap = map[CouponProductLimitObj]string{
	CouponProductLimitNone:    "不限",
	CouponProductLimitAppoint: "指定",
}

func (c CouponProductLimitObj) GetName() string {
	s, ok := CouponProductLimitMap[c]
	if !ok {
		return "未知"
	}
	return s
}
