package valobj

type OrderLogisticsFromObj int

type OrderLogisticsOpCodeObj string

const (
	OrderLogisticsFromSub OrderLogisticsFromObj = iota + 1
	OrderLogisticsFromNotify
	OrderLogisticsFromQuery
)

const (
	// OrderLogisticsOpCodeWarehouseAccept 仓库已接单--订单提交至仓库，仓库接单
	OrderLogisticsOpCodeWarehouseAccept OrderLogisticsOpCodeObj = "WAREHOUSE_ACCEPT"

	// OrderLogisticsOpCodeWarehouseProcess 仓库处理中--仓库进行拣货或打包
	OrderLogisticsOpCodeWarehouseProcess OrderLogisticsOpCodeObj = "WAREHOUSE_PROCESS"

	// OrderLogisticsOpCodeWarehouseConfirmed 已出库-- 仓库发出包裹，等待快递员揽收
	OrderLogisticsOpCodeWarehouseConfirmed OrderLogisticsOpCodeObj = "WAREHOUSE_CONFIRMED"

	// OrderLogisticsOpCodeConsign 已发货--商家发出包裹，等待快递员揽收
	OrderLogisticsOpCodeConsign OrderLogisticsOpCodeObj = "CONSIGN"

	// OrderLogisticsOpCodeLHHO 干线运输中--火车、飞机、船舶、卡车等运输中，通常在国际场景
	OrderLogisticsOpCodeLHHO OrderLogisticsOpCodeObj = "LH_HO"

	// OrderLogisticsOpCodeCCHO 清关中--快件抵达海关监管作业场所后，从向海关进行电子申报开始，至海关完成审单、征税、查验、放行等监管流程后，待快件公司提离等
	OrderLogisticsOpCodeCCHO OrderLogisticsOpCodeObj = "CC_HO"

	// OrderLogisticsOpCodeAccept 已揽件--物流网点收件或快递员揽收包裹
	OrderLogisticsOpCodeAccept OrderLogisticsOpCodeObj = "ACCEPT"

	// OrderLogisticsOpCodeTransport 运输中-包裹在途，网点发出包裹，入分拨、出分拨、中转
	OrderLogisticsOpCodeTransport OrderLogisticsOpCodeObj = "TRANSPORT"

	// OrderLogisticsOpCodeDelivering 派送中--网点快递员进行派件
	OrderLogisticsOpCodeDelivering OrderLogisticsOpCodeObj = "DELIVERING"

	// OrderLogisticsOpCodeSTADelivering 驿站派送中--包裹放入驿站，由驿站进行派送
	OrderLogisticsOpCodeSTADelivering OrderLogisticsOpCodeObj = "STA_DELIVERING"

	// OrderLogisticsOpCodeFailed 包裹异常
	OrderLogisticsOpCodeFailed OrderLogisticsOpCodeObj = "FAILED"

	// OrderLogisticsOpCodeAgentSign 待提货
	OrderLogisticsOpCodeAgentSign OrderLogisticsOpCodeObj = "AGENT_SIGN"

	// OrderLogisticsOpCodeSign 已签收--包裹正常签收、退签、代签等
	OrderLogisticsOpCodeSign OrderLogisticsOpCodeObj = "SIGN"

	// OrderLogisticsOpCodeReject 已拒收
	OrderLogisticsOpCodeReject OrderLogisticsOpCodeObj = "REJECT"

	// OrderLogisticsOpCodeReverseReturn	退货返--包裹拦截，包裹开始退回
	OrderLogisticsOpCodeReverseReturn OrderLogisticsOpCodeObj = "REVERSE_RETURN"

	//OrderLogisticsOpCodeOrderTransfer	已转单--包裹转单
	OrderLogisticsOpCodeOrderTransfer OrderLogisticsOpCodeObj = "ORDER_TRANSER"

	//OrderLogisticsOpCodeUnknown	其它
	OrderLogisticsOpCodeUnknown OrderLogisticsOpCodeObj = "INVALID"
)

// OrderLogisticsOpCodeMap 订单物流状态
var OrderLogisticsOpCodeMap = map[OrderLogisticsOpCodeObj]string{
	OrderLogisticsOpCodeWarehouseAccept:    "仓库已接单",
	OrderLogisticsOpCodeWarehouseProcess:   "仓库处理中",
	OrderLogisticsOpCodeWarehouseConfirmed: "已出库",
	OrderLogisticsOpCodeConsign:            "已发货",
	OrderLogisticsOpCodeLHHO:               "干线运输中",
	OrderLogisticsOpCodeCCHO:               "清关中",
	OrderLogisticsOpCodeAccept:             "已揽件",
	OrderLogisticsOpCodeTransport:          "运输中",
	OrderLogisticsOpCodeDelivering:         "派送中",
	OrderLogisticsOpCodeSTADelivering:      "驿站派送中",
	OrderLogisticsOpCodeFailed:             "包裹异常",
	OrderLogisticsOpCodeAgentSign:          "待提货",
	OrderLogisticsOpCodeSign:               "已签收",
	OrderLogisticsOpCodeReject:             "已拒收",
	OrderLogisticsOpCodeReverseReturn:      "退货返",
	OrderLogisticsOpCodeOrderTransfer:      "已转单",
	OrderLogisticsOpCodeUnknown:            "其它",
}

func (o OrderLogisticsOpCodeObj) GetTxt() string {
	return OrderLogisticsOpCodeMap[o]
}

func (o OrderLogisticsOpCodeObj) IsSign() bool {
	return o == OrderLogisticsOpCodeSign
}
