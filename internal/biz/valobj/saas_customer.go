package valobj

type SaasCustomerVersion string

const (
	SaasCustomerVersionUnknown  SaasCustomerVersion = ""           //
	SaasCustomerVersionStandard SaasCustomerVersion = "v-standard" // 标准版
)

var saasCustomerVersionMap = map[SaasCustomerVersion]string{
	SaasCustomerVersionStandard: "标准版",
}

func (s SaasCustomerVersion) Exists() bool {
	_, ok := saasCustomerVersionMap[s]
	return ok
}

func (s SaasCustomerVersion) GetName() string {
	if !s.Exists() {
		return ""
	}
	return saasCustomerVersionMap[s]
}

func (s SaasCustomerVersion) ToString() string {
	return string(s)
}

//go:generate stringer -type=SaasCustomerStatus -linecomment -trimfuncname=SaasCustomerStatus -inputcamelcase
type SaasCustomerStatus int

// 1-未购买 2-正常 3-临期 4-过期
const (
	SaasCustomerStatusUnknown      SaasCustomerStatus = 0 //
	SaasCustomerStatusNotBuy       SaasCustomerStatus = 1 // 未购买
	SaasCustomerStatusNormal       SaasCustomerStatus = 2 // 正常
	SaasCustomerStatusExpiringSoon SaasCustomerStatus = 3 // 临期
	SaasCustomerStatusExpired      SaasCustomerStatus = 4 // 过期
)
