package valobj

// 任务状态1=未开始，2=执行中，3=已完成，4=已失败
//
//go:generate stringer -type=SysTaskStatus -linecomment -trimfuncname=SysTaskStatus -inputcamelcase
type SysTaskStatus int

const (
	SysTaskStatusNo         SysTaskStatus = 1 // 未开始
	SysTaskStatusInProgress SysTaskStatus = 2 // 执行中
	SysTaskStatusDone       SysTaskStatus = 3 // 已完成
	SysTaskStatusFail       SysTaskStatus = 4 // 已失败
)

// 任务类型1=导出
//
//go:generate stringer -type=SysTaskType -linecomment -trimfuncname=SysTaskType -inputcamelcase
type SysTaskType int

const (
	SysTaskTypeExport SysTaskType = 1 // 导出
	SysTaskTypeImport SysTaskType = 2 // 导入
)

// 任务来源1=礼包券码下载
//
//go:generate stringer -type=SysTaskSource -linecomment -trimfuncname=SysTaskSource -inputcamelcase
type SysTaskSource int

const (
	SysTaskSourceCardBatchCouponExport SysTaskSource = 1 // 下载礼包券码
	SysTaskSourceImportAccount         SysTaskSource = 2 // 导入用户
	SysTaskSourceRealOrderExport       SysTaskSource = 3 // 实物订单导出
	SysTaskSourceVirtualOrderExport    SysTaskSource = 4 // 虚拟订单导出
	SysTaskSourceThirdOrderExport      SysTaskSource = 5 // 场景订单导出
	SysTaskSourceGoodsSkuSelect        SysTaskSource = 6 // 商品选品入库
)

func (s SysTaskSource) SMSValidateSource() []SysTaskSource {
	return []SysTaskSource{
		SysTaskSourceCardBatchCouponExport,
		SysTaskSourceRealOrderExport,
		SysTaskSourceVirtualOrderExport,
		SysTaskSourceThirdOrderExport,
	}
}
