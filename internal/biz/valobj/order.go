package valobj

type OrderStatusObj int

type OrderTypeObj int

type OrderClientStatusObj int

type OrderAfterSaleStatusObj int

// OrderAfterSaleTime 订单默认售后处理时间
const OrderAfterSaleTime = 86400

// OrderReceivedTimeOut 待收货订单默认自动确认收货时间
const OrderReceivedTimeOut = 86400 * 7

const (
	OrderStatusUnPaid          OrderStatusObj = 0
	OrderStatusAwaitingShip    OrderStatusObj = 1
	OrderStatusShipping        OrderStatusObj = 2
	OrderStatusShipped         OrderStatusObj = 3
	OrderStatusRechargeFail    OrderStatusObj = 4
	OrderStatusRefunding       OrderStatusObj = 5 // 待废弃，准备独立出来
	OrderStatusPartRefunding   OrderStatusObj = 6 // 待废弃，准备独立出来
	OrderStatusRefundPart      OrderStatusObj = 7 // 待废弃，准备独立出来
	OrderStatusRefunded        OrderStatusObj = 8 // 待废弃，准备独立出来
	OrderStatusFinish          OrderStatusObj = 9
	OrderStatusAfterSale       OrderStatusObj = 10 // 待废弃，准备独立出来
	OrderStatusAfterSaleFinish OrderStatusObj = 11 // 待废弃，准备独立出来
	OrderStatusCanceled        OrderStatusObj = 12
	OrderStatusPayCreate       OrderStatusObj = 13
	OrderStatusPayCanceled     OrderStatusObj = 14
	OrderStatusPaySuccess      OrderStatusObj = 15
	OrderStatusToBeConfirmed   OrderStatusObj = 20
	OrderStatusReject          OrderStatusObj = 21
	OrderStatusClose           OrderStatusObj = 22
)

const (
	OrderTypeUnknown OrderTypeObj = iota
	OrderTypeRecharge
	OrderTypeRechargeCard
	OrderTypeMeiTuan
	OrderTypeQianZhuCinema
	OrderTypeQianZhuKFC
	OrderTypeEntity
	OrderTypeDGSS
	OrderTypeQianZhuStarBucks
)

const (
	OrderClientStatusUnknown OrderClientStatusObj = iota
	OrderClientStatusUnPaid
	OrderClientStatusAwaitingShip
	OrderClientStatusFinish
	OrderClientStatusCanceled
	OrderClientStatusAfterSale
)

func (o OrderTypeObj) GetInt() int {
	return int(o)
}

var OrderStatusMap = map[OrderStatusObj]string{
	OrderStatusUnPaid:          "待支付",
	OrderStatusAwaitingShip:    "待发货",
	OrderStatusShipping:        "发货中",
	OrderStatusShipped:         "已发货",
	OrderStatusRechargeFail:    "充值失败",
	OrderStatusRefunding:       "退款中",
	OrderStatusPartRefunding:   "部分退款中",
	OrderStatusRefundPart:      "部分退款",
	OrderStatusRefunded:        "已退款",
	OrderStatusFinish:          "已完成",
	OrderStatusAfterSale:       "售后中",
	OrderStatusAfterSaleFinish: "售后完成",
	OrderStatusCanceled:        "已取消",
	OrderStatusPayCreate:       "待支付",
	OrderStatusPayCanceled:     "已取消",
	OrderStatusPaySuccess:      "已支付",
	OrderStatusToBeConfirmed:   "待确认",
	OrderStatusReject:          "已驳回",
	OrderStatusClose:           "交易关闭",
}

var OrderStatusClientTransMap = map[OrderStatusObj]string{
	OrderStatusUnPaid:          "待支付",
	OrderStatusAwaitingShip:    "待发货",
	OrderStatusShipping:        "发货中",
	OrderStatusShipped:         "已发货",
	OrderStatusRechargeFail:    "充值失败",
	OrderStatusRefunding:       "退款中",
	OrderStatusPartRefunding:   "部分退款中",
	OrderStatusRefundPart:      "部分退款",
	OrderStatusRefunded:        "已退款",
	OrderStatusFinish:          "已完成",
	OrderStatusAfterSale:       "售后中",
	OrderStatusAfterSaleFinish: "售后完成",
	OrderStatusCanceled:        "已取消",
	OrderStatusPayCreate:       "待支付",
	OrderStatusPayCanceled:     "已取消",
	OrderStatusPaySuccess:      "已支付",
	OrderStatusToBeConfirmed:   "待确认",
	OrderStatusReject:          "供应商取消",
	OrderStatusClose:           "交易关闭",
}

var OrderStatusOperatorTransMap = map[OrderStatusObj]string{
	OrderStatusUnPaid:          "用户创建订单",
	OrderStatusAwaitingShip:    "订单确认成功",
	OrderStatusShipping:        "开始发货",
	OrderStatusShipped:         "已发货",
	OrderStatusRechargeFail:    "虚拟单充值失败",
	OrderStatusRefunding:       "订单退款中",
	OrderStatusPartRefunding:   "订单部分退款中",
	OrderStatusRefundPart:      "订单部分退款",
	OrderStatusRefunded:        "订单已全额退款",
	OrderStatusFinish:          "订单已完成",
	OrderStatusAfterSale:       "用户发起售后",
	OrderStatusAfterSaleFinish: "订单售后完成",
	OrderStatusCanceled:        "订单已取消",
	OrderStatusPayCreate:       "待支付",
	OrderStatusPayCanceled:     "已取消",
	OrderStatusPaySuccess:      "已支付",
	OrderStatusToBeConfirmed:   "订单已支付，等待供应商确认",
	OrderStatusReject:          "订单驳回",
	OrderStatusClose:           "交易关闭",
}

var OrderTypeMap = map[OrderTypeObj]string{
	OrderTypeRecharge:         "直充",
	OrderTypeRechargeCard:     "卡密",
	OrderTypeMeiTuan:          "美团",
	OrderTypeQianZhuCinema:    "千猪电影票",
	OrderTypeQianZhuKFC:       "千猪肯德基",
	OrderTypeEntity:           "实物",
	OrderTypeDGSS:             "蛋糕叔叔",
	OrderTypeQianZhuStarBucks: "星巴克",
}

func (r OrderStatusObj) GetName() string {
	s, ok := OrderStatusMap[r]
	if !ok {
		return "未知"
	}
	return s
}

func (r OrderStatusObj) GetClientName() string {
	s, ok := OrderStatusClientTransMap[r]
	if !ok {
		return "未知"
	}
	return s
}

// CanDeliver 是否可以发货
func (r OrderStatusObj) CanDeliver() bool {
	if r == OrderStatusAwaitingShip || r == OrderStatusShipping {
		return true
	}
	return false
}

func (r OrderStatusObj) GetOperatorContent() string {
	s, ok := OrderStatusOperatorTransMap[r]
	if !ok {
		return "未知"
	}
	return s
}

func (r OrderStatusObj) GetInt() int {
	return int(r)
}

func (r OrderStatusObj) IsValid() bool {
	_, ok := OrderStatusMap[r]
	return ok
}

func (r OrderStatusObj) IsShipped() bool {
	return r == OrderStatusShipped
}

func (r OrderStatusObj) IsWaitingShip() bool {
	return r == OrderStatusAwaitingShip || r == OrderStatusShipping || r == OrderStatusToBeConfirmed
}

// CanChangeAddress 是否可以修改地址
func (r OrderStatusObj) CanChangeAddress() bool {
	if r == OrderStatusAwaitingShip || r == OrderStatusShipping || r == OrderStatusToBeConfirmed || r == OrderStatusPayCreate {
		return true
	}
	return false
}

func (r OrderStatusObj) IsAfterSale() bool {
	return r == OrderStatusAfterSale
}

func (r OrderStatusObj) IsFinish() bool {
	return r == OrderStatusFinish
}

func (r OrderStatusObj) CanRefund() bool {
	if r == OrderStatusRechargeFail || r == OrderStatusRefundPart || r == OrderStatusToBeConfirmed {
		return true
	}
	return false
}

// IsReject .
func (r OrderStatusObj) IsReject() bool {
	return r == OrderStatusReject
}

// IsToBeConfirmed .
func (r OrderStatusObj) IsToBeConfirmed() bool {
	return r == OrderStatusToBeConfirmed
}

func (r OrderStatusObj) IsPayOrder() bool {
	if r == OrderStatusPayCreate || r == OrderStatusPayCanceled {
		return true
	}
	return false
}

// CanApplyAfterSale .
func (r OrderStatusObj) CanApplyAfterSale() bool {
	return r == OrderStatusRefundPart || r.IsFinish()
}

func (r OrderStatusObj) IsRefund() bool {
	return r == OrderStatusRefundPart || r == OrderStatusRefunded
}

// CanApplyRefundAll 是否可申请全部退款
// 未完成，且已支付的 订单状态
// 1. 实物订单 且 已支付，但是未完成
func (r OrderStatusObj) CanApplyRefundAll() bool {
	return r == OrderStatusAwaitingShip || r == OrderStatusShipping || r == OrderStatusShipped
}

// GetNotCompleteOrderStatus 非终态的订单状态
func GetNotCompleteOrderStatus() []OrderStatusObj {
	return []OrderStatusObj{
		OrderStatusUnPaid,        //"待支付",
		OrderStatusAwaitingShip,  //"待发货",
		OrderStatusShipping,      //"发货中",
		OrderStatusShipped,       //"已发货",
		OrderStatusRechargeFail,  //"充值失败",
		OrderStatusRefunding,     //"退款中",
		OrderStatusPartRefunding, //"部分退款中",
		//OrderStatusRefundPart,    //"部分退款",
		//OrderStatusRefunded,      //"已退款",
		//OrderStatusFinish,          //"已完成",
		OrderStatusAfterSale, //"售后中",
		//OrderStatusAfterSaleFinish, //"售后完成",
		//OrderStatusCanceled,        //"已取消",
		//OrderStatusPayCreate, //"待支付",
		//OrderStatusPayCanceled,     //"已取消",
		//OrderStatusPaySuccess,    //"已支付",
		OrderStatusToBeConfirmed, //"待确认",
		//OrderStatusReject,          //"已驳回",
		//OrderStatusClose,           //"交易关闭",
	}
}

func (o OrderTypeObj) GetName() string {
	s, ok := OrderTypeMap[o]
	if !ok {
		return "未知"
	}
	return s
}

func (o OrderTypeObj) IsValid() bool {
	_, ok := OrderTypeMap[o]
	return ok
}

func (o OrderTypeObj) IsRecharge() bool {
	return o == OrderTypeRecharge || o == OrderTypeRechargeCard
}

func (o OrderTypeObj) IsEntity() bool {
	return o == OrderTypeEntity
}

func (o OrderTypeObj) IsOwnOrder() bool {
	return o.IsEntity() || o.IsRecharge()
}

func (o OrderTypeObj) IsDGSS() bool {
	return o == OrderTypeDGSS
}
func (o OrderTypeObj) IsQianZhuCinema() bool {
	return o == OrderTypeQianZhuCinema
}

func (o OrderTypeObj) IsQianZhuStarBucks() bool {
	return o == OrderTypeQianZhuStarBucks
}

func (o OrderTypeObj) GetSceneOrderType() []OrderTypeObj {
	return []OrderTypeObj{OrderTypeMeiTuan, OrderTypeDGSS, OrderTypeQianZhuCinema, OrderTypeQianZhuStarBucks, OrderTypeQianZhuKFC}
}

func (o OrderClientStatusObj) GetOrderStatus() []OrderStatusObj {
	switch o {
	case OrderClientStatusUnPaid:
		return []OrderStatusObj{OrderStatusPayCreate}
	case OrderClientStatusAwaitingShip:
		return []OrderStatusObj{OrderStatusAwaitingShip, OrderStatusToBeConfirmed, OrderStatusShipping, OrderStatusShipped}
	case OrderClientStatusFinish:
		return []OrderStatusObj{OrderStatusFinish}
	case OrderClientStatusCanceled:
		return []OrderStatusObj{OrderStatusPayCanceled, OrderStatusReject}
	//case OrderClientStatusAfterSale:
	//	return []OrderStatusObj{OrderStatusAfterSale, OrderStatusAfterSaleFinish}
	default:
		return []OrderStatusObj{}
	}
}

// OrderClientCreateSourceObj 订单创建来源
type OrderClientCreateSourceObj int

const (
	OrderClientCreateSourceUnknown OrderClientCreateSourceObj = iota
	// OrderClientCreateSourceCart 购物车下单
	OrderClientCreateSourceCart
	// OrderClientCreateSourceGoodsDetail 商品详情下单
	OrderClientCreateSourceGoodsDetail
	// OrderClientCreateSourceOrder 再次下单
	OrderClientCreateSourceOrder
)

var OrderClientCreateSourceMap = map[OrderClientCreateSourceObj]string{
	OrderClientCreateSourceCart:        "购物车下单",
	OrderClientCreateSourceGoodsDetail: "商品详情下单",
	OrderClientCreateSourceOrder:       "再次购买",
}

func (o OrderClientCreateSourceObj) IsCart() bool {
	return o == OrderClientCreateSourceCart
}

func (o OrderClientCreateSourceObj) IsGoodsDetail() bool {
	return o == OrderClientCreateSourceGoodsDetail
}

func (o OrderClientCreateSourceObj) IsOrder() bool {
	return o == OrderClientCreateSourceOrder
}

func (o OrderClientCreateSourceObj) IsValid() bool {
	_, ok := OrderClientCreateSourceMap[o]
	return ok
}

const (
	OrderAfterSaleStatusNone OrderAfterSaleStatusObj = iota
	OrderAfterSaleStatusHanding
	OrderAfterSaleStatusFinish
	OrderAfterSaleStatusClose
)

var OrderAfterSaleStatusMap = map[OrderAfterSaleStatusObj]string{
	OrderAfterSaleStatusNone:    "未申请",
	OrderAfterSaleStatusHanding: "处理中",
	OrderAfterSaleStatusFinish:  "已完成",
	OrderAfterSaleStatusClose:   "已关闭",
}

func (o OrderAfterSaleStatusObj) GetName() string {
	s, ok := OrderAfterSaleStatusMap[o]
	if !ok {
		return "未知"
	}
	return s
}
