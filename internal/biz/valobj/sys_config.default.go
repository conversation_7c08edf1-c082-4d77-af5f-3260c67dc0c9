package valobj

import "strconv"

// SysConfigDefaultObj 默认值，当数据库没有这个配置时，会使用这里的默认值返回
type SysConfigDefaultObj map[string]string

var SysConfigDefaultObjMap SysConfigDefaultObj = map[string]string{
	SysConfigGoodsShowType:          strconv.Itoa(SysConfigGoodsShowTypeDefault.GetValue()),
	SysConfigShopShowBalance:        strconv.Itoa(SysConfigShopShowBalanceNo.GetValue()),
	SysConfigShopClientCategoryMode: strconv.Itoa(SysConfigShopClientCategoryModeDefault.GetValue()),
}

func GetSysConfigDefaultVal(key string) string {
	return SysConfigDefaultObjMap[key]
}

func GetSysConfigDefaultObj() SysConfigDefaultObj {
	return SysConfigDefaultObjMap
}

func (s SysConfigDefaultObj) Get(key string) string {
	return s[key]
}
