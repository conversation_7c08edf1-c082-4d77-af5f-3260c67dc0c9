package valobj

type SupplierGoodsSkuSearchTypeObj int

// SupplierGoodsSkuSelectedTypeObj 选品调价时的调价方式
type SupplierGoodsSkuSelectedTypeObj int

// SupplierGoodsSkuSelectedPriceTypeObj 商品选品时售价调整类型
type SupplierGoodsSkuSelectedPriceTypeObj int

// SupplierGoodsSkuSelectedChangeTypeObj 选品调价方式:上调/下调
type SupplierGoodsSkuSelectedChangeTypeObj int

// SupplierGoodsSkuSelectedChangeUnitObj 调价单位:1=百分比(%),2=金额(分)
type SupplierGoodsSkuSelectedChangeUnitObj int

const (
	SupplierGoodsSkuSearchTypeSaleVolume SupplierGoodsSkuSearchTypeObj = iota + 1
	SupplierGoodsSkuSearchTypeNew
	SupplierGoodsSkuSearchTypeSalePrice
	SupplierGoodsSkuSearchTypeProfit
)

const (
	SupplierGoodsSkuSelectedPriceTypeUnknown SupplierGoodsSkuSelectedPriceTypeObj = iota
	//SupplierGoodsSkuSelectedPriceTypeSupplierPrice 供应价
	SupplierGoodsSkuSelectedPriceTypeSupplierPrice
	SupplierGoodsSkuSelectedPriceTypeSalePrice
	SupplierGoodsSkuSelectedPriceTypeMarketPrice
	SupplierGoodsSkuSelectedPriceTypeCustomPrice
	SupplierGoodsSkuSelectedPriceTypeCustomPriceIntegral
	SupplierGoodsSkuSelectedPriceTypeCustomIntegral
)

func (s SupplierGoodsSkuSelectedPriceTypeObj) ToFormula() string {
	switch s {
	case SupplierGoodsSkuSelectedPriceTypeSupplierPrice:
		return "supplier_price"
	case SupplierGoodsSkuSelectedPriceTypeSalePrice:
		return "sale_price"
	case SupplierGoodsSkuSelectedPriceTypeMarketPrice:
		return "market_price"
	case SupplierGoodsSkuSelectedPriceTypeCustomPrice:
		return "customer_price"
	case SupplierGoodsSkuSelectedPriceTypeCustomPriceIntegral:
		return "customer_price + customer_integral"
	case SupplierGoodsSkuSelectedPriceTypeCustomIntegral:
		return "customer_integral"
	default:
		return ""
	}
}

const (
	SupplierGoodsSkuSelectedTypeUnknown SupplierGoodsSkuSelectedTypeObj = iota
	SupplierGoodsSkuSelectedTypeChange
	SupplierGoodsSkuSelectedTypeEq
)

const (
	SupplierGoodsSkuSelectedChangeTypeUnknown SupplierGoodsSkuSelectedChangeTypeObj = iota
	SupplierGoodsSkuSelectedChangeTypeRaise
	SupplierGoodsSkuSelectedChangeTypeLower
)

func (s SupplierGoodsSkuSelectedChangeTypeObj) ToFormula() string {
	switch s {
	case SupplierGoodsSkuSelectedChangeTypeRaise:
		return "+"
	case SupplierGoodsSkuSelectedChangeTypeLower:
		return "-"
	default:
		return ""
	}
}

const (
	SupplierGoodsSkuSelectedChangeUnitUnknown SupplierGoodsSkuSelectedChangeUnitObj = iota
	SupplierGoodsSkuSelectedChangeUnitPercent
	SupplierGoodsSkuSelectedChangeUnitYuan
)

func (s SupplierGoodsSkuSelectedChangeUnitObj) ToFormula() string {
	switch s {
	case SupplierGoodsSkuSelectedChangeUnitPercent:
		return "%"
	case SupplierGoodsSkuSelectedChangeUnitYuan:
		return "1"
	default:
		return ""
	}
}

func (s SupplierGoodsSkuSelectedTypeObj) IsValid() bool {
	return s == SupplierGoodsSkuSelectedTypeChange || s == SupplierGoodsSkuSelectedTypeEq
}

func (s SupplierGoodsSkuSelectedTypeObj) GetPriceType() []SupplierGoodsSkuSelectedPriceTypeObj {
	if s == SupplierGoodsSkuSelectedTypeChange {
		return []SupplierGoodsSkuSelectedPriceTypeObj{
			SupplierGoodsSkuSelectedPriceTypeSupplierPrice,
			SupplierGoodsSkuSelectedPriceTypeSalePrice,
			SupplierGoodsSkuSelectedPriceTypeMarketPrice,
		}
	} else if s == SupplierGoodsSkuSelectedTypeEq {
		return []SupplierGoodsSkuSelectedPriceTypeObj{
			SupplierGoodsSkuSelectedPriceTypeSupplierPrice,
			SupplierGoodsSkuSelectedPriceTypeMarketPrice,
			SupplierGoodsSkuSelectedPriceTypeCustomPrice,
		}
	} else {
		return []SupplierGoodsSkuSelectedPriceTypeObj{}
	}
}

func (s SupplierGoodsSkuSelectedChangeTypeObj) IsValid() bool {
	return s == SupplierGoodsSkuSelectedChangeTypeRaise || s == SupplierGoodsSkuSelectedChangeTypeLower
}

func (s SupplierGoodsSkuSelectedChangeUnitObj) IsValid() bool {
	return s == SupplierGoodsSkuSelectedChangeUnitPercent || s == SupplierGoodsSkuSelectedChangeUnitYuan
}
