package valobj

type OrderOperatorLogUserTypeObj int

const (
	OrderOperatorLogUserTypeSupplier OrderOperatorLogUserTypeObj = iota + 1 // 供应商
	OrderOperatorLogUserTypeSystem                                          // 系统
	OrderOperatorLogUserTypeAdmin                                           // 管理员
	OrderOperatorLogUserTypeUser                                            // 用户
)

var OrderOperatorLogUserTypeMap = map[OrderOperatorLogUserTypeObj]string{
	OrderOperatorLogUserTypeSupplier: "供应商",
	OrderOperatorLogUserTypeSystem:   "系统",
	OrderOperatorLogUserTypeAdmin:    "管理员",
	OrderOperatorLogUserTypeUser:     "用户",
}

func (o OrderOperatorLogUserTypeObj) String() string {
	return OrderOperatorLogUserTypeMap[o]
}
