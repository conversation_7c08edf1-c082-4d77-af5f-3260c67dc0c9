package valobj

// CardGiftOperatorLogTypeObj 礼品卡日志类型  20=礼品卡商品使用，21=礼品卡商品退款 22=礼品卡运费使用  23=礼品卡运费退款
//
//go:generate stringer -type=CardGiftOperatorLogTypeObj -linecomment -trimfuncname=CardGiftOperatorLogType -inputcamelcase
type CardGiftOperatorLogTypeObj int

const (
	CardGiftOperatorLogTypeUse              CardGiftOperatorLogTypeObj = 20 // 礼品卡商品使用
	CardGiftOperatorLogTypeRefund           CardGiftOperatorLogTypeObj = 21 // 礼品卡商品退款
	CardGiftOperatorLogTypeFreightFeeUse    CardGiftOperatorLogTypeObj = 22 // 礼品卡运费使用
	CardGiftOperatorLogTypeFreightFeeRefund CardGiftOperatorLogTypeObj = 23 // 礼品卡运费退款
)
