//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"fmt"
	"github.com/shopspring/decimal"
	"strings"
)

type ProductAuthorizeDo struct {
	ID                 int                           // 任务id
	ProductType        valobj.SupplierGoodsTypeObj   // 商品类型:1=虚拟，2=实物
	SupplierID         int                           // 供应商ID
	SupplierGoodsID    int                           // 商品ID
	SupplierGoodsName  string                        // 商品名称 冗余
	SupplierGoodsSkuNo string                        // sku编码
	SupplierCustomerID int                           // 供应商客户ID
	Status             valobj.ProductAuthorizeStatus // 授权状态 1-已授权 2-取消授权
	AuthorizePrice     float64                       // 授权价格
	MarketPrice        float64                       // 市场价
	SrcSupplierPrice   float64                       // 源供应价
	CreateTime         int                           // 创建时间
	UpdateTime         int                           // 修改时间
	AuthorizeTime      int                           // 授权时间
	CustomerID         int                           // 客户id
	ShopID             int                           // 所属商城id，客户级为0
	// 所属分类
	CategoryID int
	// 品牌名称
	BrandID int
	// 源_所属分类
	SrcCategoryID int `json:"src_category_id,omitempty"`
	// 源_品牌
	SrcBrandID int `json:"src_brand_id,omitempty"`

	CustomerShopDo *CustomerShopDo

	GoodsBrandDo       *GoodsBrandDo
	GoodsCategoryDo    *GoodsCategoryDo
	SupplierDo         *SupplierDo
	SupplierGoodsSkuDo *SupplierGoodsSkuDo

	OfficialAuthorizePrice float64 // 最新官方授权价格

	AuthorizePriceFormula string // 授权价计算公式

	//重新计算授权价时有用
	NewMarketPrice      float64
	NewSrcSupplierPrice float64
	NewCustomerPrice    float64
}

func (p *ProductAuthorizeDo) GetAuthorizePriceInt() int {
	return int(decimal.NewFromFloat(p.AuthorizePrice).Mul(decimal.NewFromInt(10000)).IntPart())
}

func (p *ProductAuthorizeDo) RefreshAuthorizePriceByFormula() (float64, error) {
	if p.AuthorizePriceFormula == "" {
		return p.AuthorizePrice, nil
	}

	formula := strings.ReplaceAll(p.AuthorizePriceFormula, valobj.ProductAuthorizePriceChangeTypeSupplier.ToFormula(), fmt.Sprintf("%.4f", p.NewSrcSupplierPrice))
	formula = strings.ReplaceAll(formula, valobj.ProductAuthorizePriceChangeTypeMarket.ToFormula(), fmt.Sprintf("%.4f", p.NewMarketPrice))
	formula = strings.ReplaceAll(formula, valobj.ProductAuthorizePriceChangeTypeCustom.ToFormula(), fmt.Sprintf("%.4f", p.NewCustomerPrice))
	out, err := helper.Cel(formula)
	if err != nil {
		return 0, apierr.ErrorParam("【%s】授权价格计算失败，【%s】", p.SupplierGoodsSkuDo, formula)
	}
	//转为float64
	res, ok := out.Value().(float64)
	if !ok {
		return 0, apierr.ErrorParam("【%s】授权价格计算失败，结果类型为:%v", p.SupplierGoodsSkuDo, out.Type())
	}
	res = decimal.RequireFromString(fmt.Sprintf("%.8f", res)).RoundUp(4).InexactFloat64()
	if res <= 0 {
		return 0, apierr.ErrorParam("【%s】授权价格计算失败，【%s】结果小于等于0", p.SupplierGoodsSkuDo, formula)
	}
	return res, nil
}

func (p *ProductAuthorizeDo) PriceRefresh() *ProductAuthorizeDo {
	p.MarketPrice = p.NewMarketPrice
	p.SrcSupplierPrice = p.NewSrcSupplierPrice
	return p
}

func (p *ProductAuthorizeDo) SetNewUpstreamPrice(supplierPrice, marketPrice, customerPrice float64) *ProductAuthorizeDo {
	p.NewSrcSupplierPrice = supplierPrice
	p.NewMarketPrice = marketPrice
	p.NewCustomerPrice = customerPrice
	return p
}

// CalculateAuthPrice 计算授权价入口方法
func (p *ProductAuthorizeDo) CalculateAuthPrice(marketPrice, supplierPrice, customerPrice float64, formula string) (float64, error) {
	p.SetNewUpstreamPrice(supplierPrice, marketPrice, customerPrice)
	if formula != "" {
		p.AuthorizePriceFormula = formula
	}
	newAuthPrice, err := p.RefreshAuthorizePriceByFormula()
	if err != nil {
		return 0, err
	}
	p.AuthorizePrice = newAuthPrice
	//把新的价格设置为市场价和源供应价
	p.PriceRefresh()

	return newAuthPrice, err
}
