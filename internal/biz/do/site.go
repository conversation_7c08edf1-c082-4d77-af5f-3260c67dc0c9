//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
	"fmt"
	"strings"
)

type SiteDo struct {
	ID         int
	Name       string               // 站点名称
	Domain     string               // 站点地址
	UniqueStr  string               // 站点唯一标识
	Status     valobj.SiteStatusObj // 状态:1=禁用，2=启用
	CreateTime int                  // 创建时间
	UpdateTime int                  // 修改时间
	DeleteTime int                  // 删除时间
	CustomerId int
	ShopId     int
	IsDefault  valobj.SiteIsDefaultObj
}

func (s *SiteDo) IsDefaultSite() bool {
	return s.IsDefault.IsYes()
}

func (s *SiteDo) IsEnable() bool {
	if s == nil {
		return false
	}
	return s.Status == valobj.SiteStatusEnable
}

func (s *SiteDo) GetUrl(path string) string {
	return fmt.Sprintf(
		"%s#/%s",
		s.Domain,
		strings.TrimLeft(path, "/"),
	)
}
