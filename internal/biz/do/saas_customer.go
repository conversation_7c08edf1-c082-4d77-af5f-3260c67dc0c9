//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
)

type SaasCustomerDo struct {
	ID          int                        // 主键
	Name        string                     // 用户名
	ResellerID  int                        // 蓝色兄弟分销商ID
	IsLicense   valobj.BooleanObj          // 是否授权 该字段未使用
	AdminMobile string                     // 管理员手机号
	InitConfig  string                     // 初始化配置
	Remark      string                     // 备注
	CreateTime  int                        // 创建时间
	UpdateTime  int                        // 创建时间
	ExpiredTime int                        // 过期时间
	Version     valobj.SaasCustomerVersion // Version holds the value of the "version" field.

	CompanySupplierIds     []int32
	CompanySupplierEnable  bool
	ShoppingSupplierIds    []int32
	ShoppingSupplierEnable bool
}

// IsAuthorized 授权过，不代表没有过期
func (s *SaasCustomerDo) IsAuthorized() bool {
	if s == nil {
		return true
	}
	return s.ExpiredTime != 0
}

func (s *SaasCustomerDo) IsExpired() bool {
	if s == nil {
		return true
	}
	return s.ExpiredTime < helper.GetNow()
}

// GetExpiredDays 获取客户剩余天数
func (s *SaasCustomerDo) GetExpiredDays() int {
	if s == nil {
		return 0
	}
	if s.IsExpired() {
		return 0
	}
	return (s.ExpiredTime - helper.GetNow()) / 86400
}
