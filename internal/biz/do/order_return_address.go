//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

type OrderReturnAddressDo struct {
	ID           int
	SupplierID   int    // 供应商ID
	ContactName  string // 联系人
	ContactPhone string // 联系电话
	Address      string // 收货地址【用户需要寄商品到的地址】
	Title        string // 地址标题
	CreateTime   int    // 创建时间
	UpdateTime   int    // 更新时间
}

// CheckSupplierID 检查供应商ID
func (o *OrderReturnAddressDo) CheckSupplierID(supplierID int) bool {
	return o.SupplierID == supplierID
}
