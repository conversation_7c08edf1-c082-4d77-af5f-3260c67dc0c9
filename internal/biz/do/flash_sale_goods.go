//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

type FlashSaleGoodsDo struct {
	ID         int
	ActivityID int     // 活动ID
	SkuNo      string  // sku编码
	TotalStock int     // 活动总库存
	Stock      int     // 当前活动库存
	Price      float64 // 活动价
	LimitNum   int     // 限购数量
	Sort       int     // 排序，越大越靠前
	CustomerID int     // CustomerID holds the value of the "customer_id" field.
	ShopID     int     // ShopID holds the value of the "shop_id" field.
	GoodsSku   *GoodsSkuDo
}
