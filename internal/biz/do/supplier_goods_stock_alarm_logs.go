//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import "cardMall/internal/biz/valobj"

type SupplierGoodsStockAlarmLogsDo struct {
	ID         int
	Type       valobj.SupplierGoodsAlarmType   // 来源类型：1-库存可用数量预警,
	Status     valobj.SupplierGoodsAlarmStatus // 来源类型：1-有效，2-失败
	CreateTime int                             // 创建时间
	SupplierID int                             // 供应商id
	SkuID      int                             // 批次id
	SkuNo      string                          // SkuNo holds the value of the "sku_no" field.
	Content    string                          // 短信预警内容
	CustomerID int                             // 企业ID
	ShopID     int                             // 商城ID
}
