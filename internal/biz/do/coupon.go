//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
)

type CouponDo struct {
	ID                  int
	Title               string                         // 标题
	Quantity            int                            // 优惠券数量
	Remark              string                         // 使用说明
	CollectionStartTime int                            // 领取起始时间
	CollectionEndTime   int                            // 领取结束时间
	EffectTimeType      int                            // 生效时间类型：1=时间段，2=领取后xx天有效
	EffectStartTime     int                            // 生效起始时间
	EffectEndTime       int                            // 生效结束时间
	EffectStartDay      int                            // 领取后生效起始天数
	EffectEndDay        int                            // 领取后生效结束天数
	LimitAmount         float64                        // 使用限制:满xx可使用
	DiscountAmount      float64                        // 优惠金额
	CollectionQuantity  int                            // 限领数量，0=不限
	CollectionType      valobj.CouponCollectionTypeObj // 领取方式:1=用户领取,指定发放
	ProductLimit        int                            // 可用商品:1=不限，2=指定商品
	CollectionNum       int                            // 已领取数量
	UsedNum             int                            // 已使用
	AbolishNum          int                            // 已作废
	SurplusNum          int                            // 待领取
	CreateTime          int                            // 创建时间
	UpdateTime          int                            // 修改时间
	IsAbolish           int                            // 是否已作废:0=否，1=是
	CustomerID          int                            // 企业ID
	ShopID              int                            // 商城ID
}
