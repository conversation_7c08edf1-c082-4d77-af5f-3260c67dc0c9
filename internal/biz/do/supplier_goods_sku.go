//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"fmt"
	"strconv"
	"strings"

	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
)

type SupplierGoodsSkuDo struct {
	ID               int                           `json:"id"`               // sku ID
	GoodsID          int                           `json:"goodsId"`          // 商品ID
	SkuNo            string                        `json:"skuNo"`            // skuNo
	Name             string                        `json:"name"`             // sku 名称 = 商品名+规格名称
	Image            string                        `json:"image"`            // 规格图片商品图片
	SpecItemIds      string                        `json:"specItemIds"`      // 规格详细数据
	MarketPrice      float64                       `json:"marketPrice"`      // 市场价
	SalePrice        float64                       `json:"salePrice"`        // 建议零售价
	SupplierPrice    float64                       `json:"supplierPrice"`    // 供应价
	ChannelPrice     float64                       `json:"channelPrice"`     // 上游结算价
	FreePostagePrice float64                       `json:"freePostagePrice"` // 包邮包税价
	Barcode          string                        `json:"barcode"`          // 条形码【原厂编码】
	SupplierBarcode  string                        `json:"supplierBarcode"`  // 商家编码
	ProductID        string                        `json:"productId"`        // 映射商品 【虚拟商品映射使用】
	ProductType      valobj.VirtualProductTypeObj  `json:"productType"`      // 商品类型
	ProductName      string                        `json:"productName"`      // 虚拟商品名称
	Stock            int                           `json:"stock"`            // 库存
	StockAlarmNum    int                           `json:"stockAlarmNum"`    // 库存预警 为0时未开启库存预警
	Status           valobj.SupplierGoodsStatusObj `json:"status"`           // 状态:0=下架,1=上架
	Sort             int                           `json:"sort"`             // 排序
	SalesVolume      int                           `json:"salesVolume"`      // 销量
	Profit           float64                       `json:"profit"`           // 利润=售价-供应价
	ProfitRate       float64                       `json:"profitRate"`       // 利润率=(售价-供应价)/售价 13% => 0.13
	CreateTime       int                           `json:"createTime"`       // 创建时间
	UpdateTime       int                           `json:"updateTime"`       // 修改时间
	DeleteTime       int                           `json:"deleteTime"`       // 删除时间 软删除 0=未删除 时间戳=已删除
	CustomerID       int                           `json:"customerId"`       // 企业ID
	ShopID           int                           `json:"shopId"`           // 商城ID

	// Edges holds the relations/edges for other nodes in the graph.
	Goods *SupplierGoodsDo `json:"goods"`

	SupplierGoodsSpecItemDos []*SupplierGoodsSpecItemDo

	GoodsBrandDo    *GoodsBrandDo
	GoodsCategoryDo *GoodsCategoryDo
	SupplierDo      *SupplierDo
}

func (s *SupplierGoodsSkuDo) IsYiHuoTongGoods() bool {
	if s.ProductType.IsHuoYiTong() {
		return true
	}
	return false
}

// GetSpecItemDoMap 获取GetSpecItemDoMap
func (s *SupplierGoodsSkuDo) GetSpecItemDoMap() (result map[int]*SupplierGoodsSpecItemDo) {
	if len(s.SupplierGoodsSpecItemDos) == 0 {
		return nil
	}
	result = make(map[int]*SupplierGoodsSpecItemDo)
	for _, v := range s.SupplierGoodsSpecItemDos {
		result[v.ID] = v
	}
	return
}

// GenSkuNo 生成skuNo
func (s *SupplierGoodsSkuDo) GenSkuNo(goodsIds ...int) string {
	goodsId := s.GoodsID
	if len(goodsIds) > 0 && goodsIds[0] > 0 {
		goodsId = goodsIds[0]
	}
	return helper.MD5(fmt.Sprintf("%d-%s", goodsId, strings.ReplaceAll(s.SpecItemIds, ",", "-")))
}

func (s *SupplierGoodsSkuDo) GetSkuUniqueStr(goodsIds ...int) string {
	goodsId := s.GoodsID
	if len(goodsIds) > 0 && goodsIds[0] > 0 {
		goodsId = goodsIds[0]
	}
	// 企业ID-商城ID-商品ID-规格
	return fmt.Sprintf("%d-%d-%d-%s", s.CustomerID, s.ShopID, goodsId, strings.ReplaceAll(s.SpecItemIds, ",", "-"))
}

func (s *SupplierGoodsSkuDo) GetSkuSha384Str(goodsIds ...int) string {
	return helper.Sha384(s.GetSkuUniqueStr(goodsIds...))
}

// StatusToInt 状态转换成int
func (s *SupplierGoodsSkuDo) StatusToInt() int {
	return s.Status.ToInt()
}

// CanDeliver 能发发货
func (s *SupplierGoodsSkuDo) CanDeliver() bool {
	if !s.Status.IsOnline() {
		return false
	}
	if s.IsDelete() {
		return false
	}
	return true
}

func (s *SupplierGoodsSkuDo) IsDelete() bool {
	return s.DeleteTime > 0
}

// GetGoodsName 获取商品名称
func (s *SupplierGoodsSkuDo) GetGoodsName() string {
	if s.Goods != nil {
		return s.Goods.Name
	}
	return ""
}

// GetSkuFullName 获取规格名称
func (s *SupplierGoodsSkuDo) GetSkuFullName() string {
	if s.Goods != nil {
		return s.Goods.Name + "/" + s.Name
	}
	return s.Name
}

func (s *SupplierGoodsSkuDo) GetSpecItemIds() []int {
	specItemIds := make([]int, 0)
	if s.SpecItemIds == "" {
		return specItemIds
	}
	arr := strings.Split(s.SpecItemIds, ",")
	for _, v := range arr {
		specItemId, _ := strconv.Atoi(v)
		specItemIds = append(specItemIds, specItemId)
	}
	return specItemIds
}

// NoEdit 是否有修改
func (s *SupplierGoodsSkuDo) NoEdit(goodsDo *SupplierGoodsSkuDo) bool {
	if s.ID != goodsDo.ID {
		return false
	}
	if s.GoodsID != goodsDo.GoodsID {
		return false
	}
	if s.Name != goodsDo.Name {
		return false
	}
	if s.Image != goodsDo.Image {
		return false
	}
	if s.SpecItemIds != goodsDo.SpecItemIds {
		return false
	}
	if s.MarketPrice != goodsDo.MarketPrice {
		return false
	}
	if s.SalePrice != goodsDo.SalePrice {
		return false
	}
	if s.SupplierPrice != goodsDo.SupplierPrice {
		return false
	}
	if s.ChannelPrice != goodsDo.ChannelPrice {
		return false
	}
	if s.FreePostagePrice != goodsDo.FreePostagePrice {
		return false
	}
	if s.Barcode != goodsDo.Barcode {
		return false
	}
	if s.SupplierBarcode != goodsDo.SupplierBarcode {
		return false
	}
	if s.ProductID != goodsDo.ProductID {
		return false
	}
	if s.Stock != goodsDo.Stock {
		return false
	}
	if s.StockAlarmNum != goodsDo.StockAlarmNum {
		return false
	}
	if s.Status != goodsDo.Status {
		return false
	}
	//if s.Sort != goodsDo.Sort {
	//	return false
	//}
	//if s.SalesVolume != goodsDo.SalesVolume {
	//	return false
	//}
	if s.Profit != goodsDo.Profit {
		return false
	}
	if s.ProfitRate != goodsDo.ProfitRate {
		return false
	}
	//if s.CreateTime != goodsDo.CreateTime {
	//	return false
	//}
	//if s.UpdateTime != goodsDo.UpdateTime {
	//	return false
	//}
	//if s.DeleteTime != goodsDo.DeleteTime {
	//	return false
	//}
	return true
}

// GetSelectedSalePrice 供应商商品选品入库时，获取售价的基础价格
func (s *SupplierGoodsSkuDo) GetSelectedSalePrice(priceType valobj.SupplierGoodsSkuSelectedPriceTypeObj, supplierPrice, salePrice, marketPrice float64) float64 {
	switch priceType {
	case valobj.SupplierGoodsSkuSelectedPriceTypeSupplierPrice:
		return supplierPrice
	case valobj.SupplierGoodsSkuSelectedPriceTypeSalePrice:
		return salePrice
	case valobj.SupplierGoodsSkuSelectedPriceTypeMarketPrice:
		return marketPrice
	default:
		return 0
	}
}

func (s *SupplierGoodsSkuDo) Enable() bool {
	return s.Status.IsOnline() && !s.IsDelete()
}

// GetTransportNum 获取运费计算数
func (s *SupplierGoodsSkuDo) GetTransportNum(pricingMode valobj.TransportPricingModeObj, nums ...int) (float64, error) {
	// 购买数量
	orderNum := 1
	if len(nums) > 0 {
		orderNum = nums[0]
	}
	// // 计费规则(ENUM):1-免运费;2-按件数;3-按重量;4-按体积;
	switch pricingMode {
	case valobj.TransportPricingModeFree, valobj.TransportPricingModePiece:
		return float64(orderNum), nil
	case valobj.TransportPricingModeWeight:
		return 0, apierr.ErrorNotAllow("暂不支持按重量计费")
	case valobj.TransportPricingModeVolume:
		return 0, apierr.ErrorNotAllow("暂不支持按体积计费")
	default:
		return 0, apierr.ErrorNotAllow("未知的运费计费规则")
	}
}
