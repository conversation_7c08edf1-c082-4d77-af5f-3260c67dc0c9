//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"encoding/json"
	"fmt"

	"github.com/duke-git/lancet/v2/slice"
)

type CardBatchCouponDo struct {
	ID                  int                                 // ID
	CardCouponNumber    string                              // 券码/兑换码
	CardBatchID         int                                 // 礼包批次id
	CardBatchNumber     string                              // 礼包批次号
	CardBatchName       string                              // 礼包批次名称
	ExchangeGoodsRepeat valobj.CardBatchExchangeGoodsRepeat // 是否可重复领取商品 1.单个商品仅可领取1个 2.单个商品可领取多个
	ExchangeGoodsNum    int                                 // 可领取商品数量
	CardNumberType      valobj.CardBatchNumberType          // 兑换码样式 1.卡密 2.链接 3.二维码 4.白名单
	UseExpireStart      int                                 // 到期时间开始
	UseExpireEnd        int                                 // 到期时间结束
	UserID              int                                 // 会员ID
	UserName            string                              // 会员名称
	BindStatus          valobj.CardBatchCouponBindStatus    // 绑定状态 1.未绑定 2已绑定
	BindTime            int                                 // 绑定时间
	CardCouponUseNum    int                                 // 会员兑换次数/使用次数
	UseSkuNos           string                              // 使用过的sku_no【json】
	Status              valobj.CardBatchCouponStatus        // 卡券状态 1 待使用 2 已使用 3 已作废
	CreateTime          int                                 // 创建时间
	UpdateTime          int                                 // 修改时间

	// Edges holds the relations/edges for other nodes in the graph.
	CardBatch             *CardBatchDo
	CardBatchGoods        []*CardBatchGoodsDo
	CardCouponOperatorLog []*CardCouponOperatorLogDo
}

// GetLogContent 获取日志内容
func (c *CardBatchCouponDo) GetLogContent(currentUsedNum int) string {
	return fmt.Sprintf("领取礼包,礼包编号:%s,礼包名称:%s,领取数量:%d", c.CardCouponNumber, c.CardBatch.CardBatchName, currentUsedNum)
}

// GetLogRejectContent 获取日志内容
func (c *CardBatchCouponDo) GetLogRejectContent(currentUsedNum int) string {
	return fmt.Sprintf("订单驳回,礼包编号:%s,礼包名称:%s,领取数量:%d", c.CardCouponNumber, c.CardBatch.CardBatchName, currentUsedNum)
}

func (c *CardBatchCouponDo) GetLogContentRefund(currentUsedNum int) string {
	return fmt.Sprintf("充值失败自动返还,礼包编号:%s,礼包名称:%s,领取数量:%d", c.CardCouponNumber, c.CardBatch.CardBatchName, currentUsedNum)
}

// GetUsedSkuNos 获取已领取商品列表
func (c *CardBatchCouponDo) GetUsedSkuNos() []string {
	var usedSkuNos []string
	err := json.Unmarshal([]byte(c.UseSkuNos), &usedSkuNos)
	if err != nil {
		return []string{}
	}
	return usedSkuNos
}

// MergeUsedSkuNosToString 合并已领取商品列表
func (c *CardBatchCouponDo) MergeUsedSkuNosToString(skuNos []string) string {
	if len(skuNos) == 0 {
		return c.UseSkuNos
	}
	usedSkuNos := slice.Unique(append(skuNos, c.GetUsedSkuNos()...))
	usedSkuNosJson, _ := json.Marshal(usedSkuNos)
	return string(usedSkuNosJson)
}

// CheckUser 检查用户是否可以领取礼包
func (c *CardBatchCouponDo) CheckUser(userId int) error {
	if c.UserID == 0 {
		return apierr.ErrorNotAllow("该礼包暂未绑定")
	}
	if c.UserID != userId {
		return apierr.ErrorNotAllow("不能使用其他账号领取礼包")
	}
	return nil
}

// CheckEnable 检查礼包是否可用
func (c *CardBatchCouponDo) CheckEnable() error {
	if c.Status == valobj.CardBatchCouponStatusCancel {
		return apierr.ErrorNotAllow("礼包%s", c.Status.String())
	}
	if c.CardCouponUseNum >= c.ExchangeGoodsNum {
		return apierr.ErrorNotAllow("礼包已领取完")
	}
	now := helper.GetNow()
	if now < c.UseExpireStart {
		return apierr.ErrorNotAllow("请在%s-%s期间使用礼包", helper.GetTimeDate(c.UseExpireStart, helper.DateTimeFormat), helper.GetTimeDate(c.UseExpireEnd, helper.DateTimeFormat))
	}
	if now > c.UseExpireEnd {
		return apierr.ErrorNotAllow("礼包已过期")
	}
	return nil
}

type CardBatchUsedInterface interface {
	GetSkuNo() string
	GetNum() int
}

func (c *CardBatchCouponDo) CheckExchangeRule(useData []CardBatchUsedInterface, getSkuNameFunc func(string) string) error {
	skuUseMap := make(map[string]int)
	for _, item := range useData {
		if _, ok := skuUseMap[item.GetSkuNo()]; !ok {
			skuUseMap[item.GetSkuNo()] = 0
		}
		skuUseMap[item.GetSkuNo()] += item.GetNum()
	}

	usedSkuNos := c.GetUsedSkuNos()
	if c.ExchangeGoodsRepeat.IsDisable() {
		for skuNo, v := range skuUseMap {
			if v > 1 || slice.Contain(usedSkuNos, skuNo) {
				return apierr.ErrorNotAllow("商品%s限制兑换一次", getSkuNameFunc(skuNo))
			}

		}
	}
	return nil
}

// GetUnUseNum 获取未使用的数量
func (c *CardBatchCouponDo) GetUnUseNum() int {
	return c.ExchangeGoodsNum - c.CardCouponUseNum
}

type BatchCouponNumResult struct {
	CardBatchCouponID int `json:"card_batch_coupon_id,omitempty"`
	// 剩余次数
	SurplusNum int `json:"surplus_num,omitempty"`
	//作废次数
	CancelNum int `json:"cancel_num,omitempty"`
	//过期次数
	ExpireNum int `json:"expire_num,omitempty"`
}
