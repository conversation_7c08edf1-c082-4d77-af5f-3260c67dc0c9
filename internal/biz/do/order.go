//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"encoding/json"
	"fmt"

	"github.com/shopspring/decimal"
)

type OrderDo struct {
	ID                         int
	PayOrderNumber             string                         // 支付单号(主订单号)
	OrderNumber                string                         // 订单号
	RechargeNo                 string                         // 上游充值单号
	OrderType                  valobj.OrderTypeObj            // 订单类型:1=直充,2=卡密,3=美团
	UserID                     int                            // 用户ID
	BrandID                    int                            // 品牌ID
	BrandName                  string                         // 品牌
	GoodsID                    int                            // 商品ID
	ProductID                  int                            // 映射商品ID
	TotalAmount                float64                        // 子订单总金额
	PayAmount                  float64                        // 子订单支付金额
	PayIntegral                int                            // 子订单使用积分
	Status                     valobj.OrderStatusObj          // 订单状态:1=待充值，2=充值中,3=充值完成,4=退款中,5=已退款,6=已收货,7=售后中,8=售后完成
	GoodsName                  string                         // 商品名称
	GoodsImage                 string                         // 商品图
	SalePrice                  float64                        // 售价
	OriginPrice                float64                        // 原价
	ChannelPrice               float64                        // 上游结算价
	Account                    string                         // 充值账号
	Num                        int                            // 购买数量
	RefundTime                 int                            // 退款时间
	RefundAmount               float64                        // 累计退款金额
	RefundGoodsAmount          float64                        // 累计退款金额
	FinishTime                 int                            // 完成时间
	RefundRemark               string                         // 退款备注
	CardCode                   string                         // 卡密
	CardExpireTime             int                            // 卡密有效期
	CreateTime                 int                            // 创建时间
	UpdateTime                 int                            // 修改时间
	ExtraInfo                  string                         // 订单额外信息
	SiteID                     int                            // 订单来源站点
	SupplierID                 int                            // 订单来源站点
	DelayDeliverRemark         string                         // 订单来源站点
	DeliverStatus              valobj.OrderDeliverStatusObj   // 发货状态 0-未发货 1-部分发货 2-全部发货
	ReceiveStatus              valobj.OrderReceiveStatusObj   // 订单来源站点
	DeliverTime                int                            // 发货时间
	AfterSaleStatus            valobj.OrderAfterSaleStatusObj // 售后状态 0=未申请 1=处理中 2=已处理 3=已关闭
	AfterSaleAll               valobj.BooleanObj              // 售后是否全部
	ExtType                    valobj.OrderExtTypeObj         // 订单额外类型 0-默认 1-换货订单
	OriginalOrderNo            string                         // 源订单号
	FreightFee                 float64                        // 运费
	RefundFreightFee           float64                        // 已退款运费
	CouponDiscountAmount       float64                        // 优惠券抵扣金额
	RefundCouponDiscountAmount float64                        // 优惠券抵扣金额，不一定已经退了
	CustomerID                 int
	ShopID                     int
	RequestId                  string
	DeliverExpireTime          int
	// 关联数据
	OrderGoods        []*OrderGoodsDo
	OrderUserAddress  *OrderUserAddressDo
	OrderOperatorLog  []*OrderOperatorLogDo
	SiteInfo          *SiteDo
	SupplierInfo      *SupplierDo
	PayOrderInfo      *PayOrderDo
	OrderCardGiftInfo []*OrderCardGiftDo
	User              *UserDo

	CustomerShopDo *CustomerShopDo

	// 扩展字段
	TipMsg *OrderTipDo

	CouponCodeDo     *CouponCodeDo
	CouponDo         *CouponDo
	OrderAfterSaleDo *OrderAfterSaleDo
	SiteDo           *SiteDo

	SaleIntegral int
	GoodsSkuName string

	CanAfterSaleRefund    bool
	CanAfterSaleExchange  bool
	AfterSaleIsDealing    bool
	CanAfterSaleRefundAll bool
}

func (o *OrderDo) GetIsTimeout() bool {
	if o.Status.IsWaitingShip() && (o.DeliverExpireTime > 0 && o.DeliverExpireTime < helper.GetNow()) {
		return true
	}
	return false
}

func (o *OrderDo) GetDeliverStatusText() string {
	switch o.OrderType {
	case valobj.OrderTypeEntity:
		return o.DeliverStatus.GetName()
	default:
		return ""
	}
}

func (o *OrderDo) GetCardGiftFreightFee() float64 {
	if o.OrderCardGiftInfo != nil && len(o.OrderCardGiftInfo) > 0 {
		freightFee := decimal.Zero
		for _, v := range o.OrderCardGiftInfo {
			if v.Type.IsFreightFee() {
				freightFee = freightFee.Add(decimal.NewFromFloat(v.GetFreightFeeRefundAmount()))
			}
		}
		return freightFee.InexactFloat64()
	}
	return 0
}

// UsedCoupon 已使用优惠券
func (o *OrderDo) UsedCoupon() bool {
	return o.CouponDiscountAmount > 0
}

// CouponIsRefund 已使用优惠券
func (o *OrderDo) CouponIsRefund() bool {
	return o.RefundCouponDiscountAmount >= o.CouponDiscountAmount
}

// IsMainOrder 是否为主订单
func (o *OrderDo) IsMainOrder() bool {
	return o.OrderNumber == o.PayOrderNumber
}

// GetOrderNo 获取订单号
func (o *OrderDo) GetOrderNo() string {
	return o.OrderNumber
}

// IsOriginalOrder 是否为原订单
func (o *OrderDo) IsOriginalOrder() bool {
	return o.ExtType == valobj.OrderExtTypeDefault
}

// CanApplyAfterSaleRefund 是否为原订单
func (o *OrderDo) CanApplyAfterSaleRefund() bool {
	return o.ExtType.CanRefund()
}

// IsExchangeOrder 是否为换货订单
func (o *OrderDo) IsExchangeOrder() bool {
	return o.ExtType.IsExchangeOrder()
}

// GetOriginalOrderNo 获取源订单号
func (o *OrderDo) GetOriginalOrderNo() string {
	if o.IsExchangeOrder() {
		return o.OriginalOrderNo
	}
	return o.OrderNumber
}

// CanApplyRefundAll 是否可申请全部退款
// 1. 实物订单 且 已支付，但是未完成
func (o *OrderDo) CanApplyRefundAll() bool {
	if !o.OrderType.IsEntity() {
		return false
	}
	return o.Status.CanApplyRefundAll() && o.CanApplyAfterSaleRefund()
}

// CanApplyAfterSale .
func (o *OrderDo) CanApplyAfterSale() bool {
	return o.Status.CanApplyAfterSale() && o.OrderType.IsEntity()
}

// GetCanAfterSaleExchange 是否可申请换货
func (o *OrderDo) GetCanAfterSaleExchange() bool {
	return o.CanAfterSaleExchange && o.OrderType.IsEntity()
}

// GetCanAfterSaleRefund 是否可申请退款
func (o *OrderDo) GetCanAfterSaleRefund() bool {
	return o.CanAfterSaleRefund && o.OrderType.IsEntity()
}

// CanChangeAddress 是否可修改地址
func (o *OrderDo) CanChangeAddress() bool {
	return o.OrderType.IsEntity() && o.ExtType.CanChangeAddress() && o.Status.CanChangeAddress()
}

// GetOrderNumberJsonString 订单号json字符串
func (o *OrderDo) GetOrderNumberJsonString() string {
	orderNos := []string{o.OrderNumber}
	a, _ := json.Marshal(orderNos)
	return string(a)
}

func (o *OrderDo) GetNotRefundAmount() decimal.Decimal {
	return decimal.NewFromFloat(o.PayAmount).Sub(decimal.NewFromFloat(o.RefundAmount))
}

// HasDeliverTime 是否发货时间超时
func (o *OrderDo) HasDeliverTime() bool {
	return o.DeliverTime > 0
}

// GetOrderUserAddress .
func (o *OrderDo) GetOrderUserAddress() *OrderUserAddressDo {
	if o == nil || o.OrderUserAddress == nil {
		return &OrderUserAddressDo{}
	}
	return o.OrderUserAddress
}

// IsWaitForDeliver 待发货
func (o *OrderDo) IsWaitForDeliver() bool {
	return (o.DeliverStatus == valobj.OrderDeliverStatusWaiting || o.DeliverStatus == valobj.OrderDeliverStatusPartial) && o.Status == valobj.OrderStatusAwaitingShip
}

// IsReceived 已收货
func (o *OrderDo) IsReceived() bool {
	//if o.ReceiveStatus.IsReceive() {
	//	return true
	//}
	return o.Status.IsFinish() || o.Status.IsAfterSale()
}

// CanDeliver 订单可发货
func (o *OrderDo) CanDeliver() bool {
	return o.DeliverStatus.CanDeliver() && o.Status.CanDeliver()
}

// CheckSupplierId 检查供应商ID
func (o *OrderDo) CheckSupplierId(supplierId int) bool {
	return o.SupplierID == supplierId
}

// CheckUserId 检查用户ID
func (o *OrderDo) CheckUserId(userId int) bool {
	return o.UserID == userId
}

func (o *OrderDo) GetOrderGoodsBySkuNO(skuNo string) *OrderGoodsDo {
	if len(o.OrderGoods) == 0 {
		return nil
	}
	for _, orderGood := range o.OrderGoods {
		if orderGood.SkuNo == skuNo {
			return orderGood
		}
	}
	return nil
}

// IsUsedIntegral 使用了积分
func (o *OrderDo) IsUsedIntegral() bool {
	return o.PayIntegral > 0
}

// CheckAfterSaleExpired 检查售后日期是否过期
func (o *OrderDo) CheckAfterSaleExpired() error {
	if o.AfterSaleExpired() {
		return apierr.ErrorNotAllow("该订单已过售后有效期,请联系客服")
	}
	return nil
}

// AfterSaleExpired 是否售后过期
func (o *OrderDo) AfterSaleExpired() bool {
	return o.FinishTime > 0 && o.FinishTime+constants.OrderAfterSaleExpireTime < helper.GetNow()
}

// GetSkuQuantityMap 获取商品数量
func (o *OrderDo) GetSkuQuantityMap() map[string]int {
	skuMap := make(map[string]int)
	for _, orderGood := range o.OrderGoods {
		skuMap[orderGood.SkuNo] = orderGood.Quantity
	}
	return skuMap
}

// GetOriginalSkuNoMap 获取商品Map
func (o *OrderDo) GetOriginalSkuNoMap() map[string]*OrderGoodsDo {
	skuMap := make(map[string]*OrderGoodsDo)
	for _, orderGood := range o.OrderGoods {
		skuMap[orderGood.GetOriginalSkuNo()] = orderGood
	}
	return skuMap
}

// GetOrderGoodsSkuNos 获取商品SKU编号
func (o *OrderDo) GetOrderGoodsSkuNos() []string {
	skuNos := make([]string, 0)
	for _, orderGood := range o.OrderGoods {
		skuNos = append(skuNos, orderGood.SkuNo)
	}
	return skuNos
}

// ShowLogistics 订单列表是否显示最新物流信息
func (o *OrderDo) ShowLogistics() bool {
	return o.Status.IsShipped() && o.OrderType == valobj.OrderTypeEntity
}

// LoadLogisticsTipMsg 组装订单列表发货信息
func (o *OrderDo) LoadLogisticsTipMsg(l *OrderLogisticsDo) {
	if l != nil {
		o.TipMsg = &OrderTipDo{
			Status: l.OpCode.GetTxt(),
			Msg:    l.OpMessage,
		}
	}
}

// ShowWaitingShipTime 订单列表是否显示发货时限
func (o *OrderDo) ShowWaitingShipTime() bool {
	return o.Status.IsWaitingShip() && o.OrderType == valobj.OrderTypeEntity
}

// ShowAfterSaleTime 订单列表是否显示售后处理时限
func (o *OrderDo) ShowAfterSaleTime() bool {
	return o.AfterSaleStatus != valobj.OrderAfterSaleStatusNone
}

// LoadWaitingShipTipMsg 组装订单列表发货信息
func (o *OrderDo) LoadWaitingShipTipMsg() {
	shipTimeStr := helper.GetTimeDate(o.DeliverExpireTime, "1月2日 15:04")
	o.TipMsg = &OrderTipDo{
		Status: o.Status.GetName(),
		Msg:    fmt.Sprintf("<div><span style=\"color:#FF5656\">%s</span> 前发货</div>", shipTimeStr),
	}
}

// LoadAfterSaleTipMsg 组装订单列表售后信息
func (o *OrderDo) LoadAfterSaleTipMsg(title string, msg string) {
	o.TipMsg = &OrderTipDo{
		Status: title,
		Msg:    msg,
	}
}

// CanReceive 订单是否可以收货
func (o *OrderDo) CanReceive() bool {
	return o.Status.IsShipped() && o.ReceiveStatus.IsReceiveAll()
}

// StatusText 订单状态文字
func (o *OrderDo) StatusText() string {
	if o.Status.IsShipped() && o.DeliverStatus.IsPartial() {
		return o.DeliverStatus.GetName()
	}
	return o.Status.GetClientName()
}

// FreightFeeIsFire 运费是否
func (o *OrderDo) FreightFeeIsFire() bool {
	return o.ExtType.FreightFeeIsFire()
}

func (o *OrderDo) GetSiteId() int {
	return o.SiteID
}

func (o *OrderDo) SetSiteData(site *SiteDo) {
	o.SiteInfo = site
}

func (o *OrderDo) GetSiteInfo() *SiteDo {
	if o.SiteInfo == nil {
		return &SiteDo{}
	}
	return o.SiteInfo
}

func (o *OrderDo) GetSupplierInfo() *SupplierDo {
	if o.SupplierInfo == nil {
		return &SupplierDo{}
	}
	return o.SupplierInfo
}

func (o *OrderDo) GetPayOrderInfo() *PayOrderDo {
	if o.PayOrderInfo == nil {
		return &PayOrderDo{}
	}
	return o.PayOrderInfo
}

type SubRealOrderDetailDo struct {
	ID             int    `json:"id,omitempty"`
	PayOrderNumber string `json:"pay_order_number,omitempty"`
	// 订单号
	OrderNumber string `json:"order_number,omitempty"`
	// 用户ID
	UserID int `json:"user_id,omitempty"`
	// 订单金额
	TotalAmount decimal.Decimal `json:"total_amount,omitempty"`
	// 订单状态:1=待充值，2=充值中,3=充值完成,4=退款中,5=已退款,6=已收货,7=售后中,8=售后完成
	Status valobj.OrderStatusObj `json:"status,omitempty"`
	//售后状态 0=未申请 1=处理中 2=已处理
	AfterSaleStatus int `json:"after_sale_status,omitempty"`
	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`

	FreightFee decimal.Decimal `json:"freight_fee"`
	// 抵扣积分
	PayIntegral int `json:"pay_integral"`

	CustomerID int `json:"customer_id"` //
}

type MainRealOrderDetailDo struct {
	ID             int    `json:"id,omitempty"`
	PayOrderNumber string `json:"pay_order_number,omitempty"`
	// 订单号
	OrderNumber string `json:"order_number,omitempty"`
	// 用户ID
	UserID int `json:"user_id,omitempty"`
	// 订单状态:1=待充值，2=充值中,3=充值完成,4=退款中,5=已退款,6=已收货,7=售后中,8=售后完成
	Status int `json:"status,omitempty"`
	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`
	// 收货人姓名
	UserAddressName string `json:"user_address_name,omitempty"`

	// 电话
	PhoneNumber string `json:"phone_number,omitempty"`
	// 区域
	Area string `json:"area,omitempty"`
	// 地址详情
	Detail string `json:"detail,omitempty"`
	// 结算方式
	SettlementType int `json:"settlement_type,omitempty"`
	PayType        int `json:"pay_type,omitempty"`

	// 订单总金额
	TotalAmount decimal.Decimal `json:"total_amount,omitempty"`
	// 总支付金额
	TotalPayAmount decimal.Decimal `json:"total_pay_amount,omitempty"`
	// 积分抵扣金额
	IntegralDiscountAmount decimal.Decimal `json:"integral_discount_amount"`
	// 总支付积分
	TotalPayIntegral int `json:"total_pay_integral,omitempty"`
	// 优惠券实际优惠金额
	CouponDiscountAmount decimal.Decimal `json:"coupon_discount_amount,omitempty"`
	// 使用优惠券ID
	CouponCodeID int `json:"coupon_code_id,omitempty"`
	// 优惠券批次ID
	CouponID int `json:"coupon_id,omitempty"`

	PayStatus int `json:"pay_status,omitempty"`

	FreightFee decimal.Decimal `json:"freight_fee"`

	// 抵扣积分
	PayIntegral int `json:"pay_integral"`
}
type RealOrderGoodsListDo struct {
	// 订单号
	OrderNumber string `json:"order_number,omitempty"`
	// sku编码
	SkuNo string `json:"sku_no,omitempty"`
	// ID of the ent.
	GoodsSkuID int `json:"goods_sku_id,omitempty"`
	// 商品名称
	GoodsSkuName string `json:"goods_sku_name,omitempty"`
	// 商品名称
	GoodsName string `json:"goods_name,omitempty"`
	// 商品ID
	GoodsID int `json:"goods_id,omitempty"`
	// 商品图
	GoodsImage string `json:"goods_image,omitempty"`
	// 数量
	Quantity int `json:"quantity,omitempty"`
	//售价
	SalePrice decimal.Decimal `json:"sale_price,omitempty"`
	//原价
	OriginPrice decimal.Decimal `json:"origin_price,omitempty"`
	//上游结算价
	ChannelPrice decimal.Decimal `json:"channel_price,omitempty"`
	//供应价
	SupplierPrice decimal.Decimal `json:"supplier_price,omitempty"`

	// 优惠券实际优惠金额
	CouponDiscountAmount decimal.Decimal `json:"coupon_discount_amount"`
	// 实付金额
	PayAmount decimal.Decimal `json:"pay_amount"`
	// 抵扣积分
	PayIntegral int `json:"pay_integral"`
	// 积分抵扣金额
	IntegralDiscountAmount decimal.Decimal `json:"integral_discount_amount"`
	// 运费
	FreightFee decimal.Decimal `json:"freight_fee"`

	SaleIntegral           int `json:"sale_integral"`
	ActualDiscountIntegral int `json:"actual_discount_integral"`
}
type RealOrderDeliverDo struct {
	ID int `json:"id,omitempty"`
	// 订单ID
	OrderID int `json:"order_id,omitempty"`
	// 订单号
	OrderNumber string `json:"order_number,omitempty"`
	// 发货单号
	DeliverNumber string `json:"deliver_number,omitempty"`
	// 物流公司ID
	KdID int `json:"kd_id,omitempty"`
	// 物流公司编号
	KdCode string `json:"kd_code,omitempty"`
	// 快递公司名称
	KdName string `json:"kd_name,omitempty"`
	// 物流单号
	LogisticsNo string `json:"logistics_no,omitempty"`
	// 物流当前最新状态码
	LogisticsOpCode valobj.OrderLogisticsOpCodeObj `json:"logistics_op_code,omitempty"`
	// 物流当前最新状态
	LogisticsOpDesc string `json:"logistics_op_desc,omitempty"`
	// 创建时间
	CreateTime int `json:"create_time,omitempty"`
	// 更新时间
	UpdateTime int `json:"update_time,omitempty"`
	// 供应商Id
	SupplierID int `json:"supplier_id,omitempty"`
}
