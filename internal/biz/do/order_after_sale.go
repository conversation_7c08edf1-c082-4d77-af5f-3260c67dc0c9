//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"encoding/json"
	"github.com/shopspring/decimal"
)

type OrderAfterSaleDo struct {
	ID                       int
	UserID                   int                            //用户ID
	SupplierID               int                            // 供应商ID
	OrderID                  int                            // 订单ID
	OrderNumber              string                         // 订单号
	Reason                   string                         // 申请售后原因
	Images                   string                         // 图片
	Remark                   string                         // 补充描述
	RefundAmount             float64                        // 退款金额
	ReceiveStatus            valobj.AfterSaleReceiveStatus  // 收货状态 1-未收到货 2-已收货
	Status                   valobj.AfterSaleStatus         // 售后状态 // 退款退货：21-退货物流中 22-商家/供应商确认收货 23-确认退款// 换货：31-退货物流中 32-商家/供应商确认收货 33-商家/供应商发货// 公共状态// 1-待处理 2-审核通过 3-审核拒绝 7-买家主动关闭 8-卖家关闭 9-退款/换货完成 //
	PlatformStatus           valobj.AfterSalePlatformStatus // 售后状态 // 退款退货：21-退货物流中 22-商家/供应商确认收货 23-确认退款// 换货：31-退货物流中 32-商家/供应商确认收货 33-商家/供应商发货// 公共状态// 1-待处理 2-审核通过 3-审核拒绝 7-买家主动关闭 8-卖家关闭 9-退款/换货完成 //
	Type                     valobj.AfterSaleType           // 售后类型 1-仅退款 2-退款退货 3-换货
	CreateTime               int                            // 创建时间
	UpdateTime               int                            // 创建时间
	ExchangeOrderNo          string                         // 换货订单号
	AfterSaleNo              string                         // 售后单号
	OriginalOrderNo          string                         // 源订单号
	RefundGoodsAmount        float64                        // 退款商品金额
	RefundFreightFee         float64                        // 退款运费
	CustomerID               int                            // 企业ID
	ShopID                   int                            // 商城ID
	RefundCardGiftAmount     float64                        // 商品退款礼品卡金额
	RefundCardGiftFreightFee float64                        // 运费退款礼品卡金额

	// Edges holds the relations/edges for other nodes in the graph.
	OrderAfterSaleDeliver *OrderAfterSaleDeliverDo
	OrderAfterSaleGoods   []*OrderAfterSaleGoodsDo
	OrderAfterSaleLog     []*OrderAfterSaleLogDo
	OrderAfterSaleDeal    []*OrderAfterSaleDealDo
	OrderAfterSaleRel     *OrderAfterSaleRelDo
	SupplierInfo          *SupplierDo

	// 售后提示--C端展示
	AfterSaleTip []*AfterSaleTip

	OrderInfo              *OrderDo
	OrderGoods             []*OrderGoodsDo
	OrderAfterSaleResultDo *OrderAfterSaleResultDo
}

// GetSupplierRefuseReason 获取供应商拒绝原因
func (o *OrderAfterSaleDo) GetSupplierRefuseReason() string {
	content := ""
	if o.PlatformStatus.IsRefuseBySupplierToPlatform() {
		for _, logDo := range o.OrderAfterSaleLog {
			if logDo.PlatformStatus.IsRefuseBySupplierToPlatform() {
				content = logDo.Content
			}
		}
	}
	return content
}

// GetPlatformRefuseReason 获取最新的平台审核原因
func (o *OrderAfterSaleDo) GetPlatformRefuseReason() string {
	content := ""
	if o.PlatformStatus.IsRefuseByPlatformToSupplier() {
		for _, logDo := range o.OrderAfterSaleLog {
			if logDo.PlatformStatus.IsRefuseByPlatformToSupplier() {
				content = logDo.Content
			}
		}
	}
	return content
}

// GetSupplierInfo 获取供应商信息
func (o *OrderAfterSaleDo) GetSupplierInfo() *SupplierDo {
	if o.SupplierInfo == nil {
		return &SupplierDo{}
	}
	return o.SupplierInfo
}

// GetGoodsRefundTotalAmount 获取商品退款金额
func (o *OrderAfterSaleDo) GetGoodsRefundTotalAmount() decimal.Decimal {
	return decimal.NewFromFloat(o.RefundGoodsAmount).Add(decimal.NewFromFloat(o.RefundCardGiftAmount))
}

// GetFreightFeeRefundTotal 获取运费退款金额
func (o *OrderAfterSaleDo) GetFreightFeeRefundTotal() decimal.Decimal {
	return decimal.NewFromFloat(o.RefundFreightFee).Add(decimal.NewFromFloat(o.RefundCardGiftFreightFee))
}

// GetCardGiftAmount 获取礼品卡退款金额
func (o *OrderAfterSaleDo) GetCardGiftAmount() decimal.Decimal {
	return decimal.NewFromFloat(o.RefundCardGiftAmount).Add(decimal.NewFromFloat(o.RefundCardGiftFreightFee))
}

type OrderInfo struct {
	OrderNumber     string
	AfterSaleStatus valobj.OrderAfterSaleStatusObj
	AfterSaleAll    valobj.BooleanObj
	OrderStatus     valobj.OrderStatusObj
	OriginalOrderNo string // 源订单号
	ExtType         valobj.OrderExtTypeObj
	PayIntegral     int
}

// GetStatusText 获取售后状态文案
func (o *OrderAfterSaleDo) GetStatusText() string {
	return o.Status.GetShowText(o.Type)
}

type AfterSaleTip struct {
	Title string
	Extra string
	Msg   string
}

// CheckSupplierID 检测供应商ID
func (o *OrderAfterSaleDo) CheckSupplierID(SupplierId int) bool {
	return o.SupplierID == SupplierId
}

// GetImages 获取图片
func (o *OrderAfterSaleDo) GetImages() []string {
	var images []string
	_ = json.Unmarshal([]byte(o.Images), &images)
	return images
}

// GetBuyerDeliver 获取买家退货信息
func (o *OrderAfterSaleDo) GetBuyerDeliver() *OrderAfterSaleDeliverDo {
	return o.OrderAfterSaleDeliver
}

// CheckWaitReceive 卖家待收货
func (o *OrderAfterSaleDo) CheckWaitReceive() error {
	switch o.Type {
	case valobj.AfterSaleTypeRefund:
		return apierr.ErrorNotAllow("仅退款不需要确认收货")
	case valobj.AfterSaleTypeRefundReturn:
		if !o.Status.IsReturnWaitReceive() {
			return apierr.ErrorParam("售后状态错误，当前状态： %s", o.Status.String())
		}
	case valobj.AfterSaleTypeExchange:
		if !o.Status.IsExchangeWaitReceive() {
			return apierr.ErrorParam("售后状态错误，当前状态： %s", o.Status.String())
		}
	case valobj.AfterSaleTypeReissue:
		return apierr.ErrorNotAllow("补发不需要确认收货")
	default:
		return apierr.ErrorNotAllow("未知的售后类型")
	}
	return nil
}

// CheckRefund 可以进行退款
func (o *OrderAfterSaleDo) CheckRefund() error {
	switch o.Type {
	case valobj.AfterSaleTypeRefund, valobj.AfterSaleTypeRefundReturn:
		if !o.Status.IsWaitRefund() {
			return apierr.ErrorNotAllow("售后状态错误，当前状态： %s，不可进行退款操作", o.Status.String())
		}
	case valobj.AfterSaleTypeExchange, valobj.AfterSaleTypeReissue:
		return apierr.ErrorNotAllow("换货不能进行退款")
	default:
		return apierr.ErrorNotAllow("未知的售后类型")
	}
	return nil
}
func (o *OrderAfterSaleDo) GetSaleGoodsByType(afterSaleGoodsType valobj.AfterSaleGoodsType) []*OrderAfterSaleGoodsDo {
	if len(o.OrderAfterSaleGoods) == 0 {
		return nil
	}
	orderAfterSaleGoodsDos := make([]*OrderAfterSaleGoodsDo, 0)
	for _, good := range o.OrderAfterSaleGoods {
		if good.Type == afterSaleGoodsType {
			good.OrderAfterSale = o
			orderAfterSaleGoodsDos = append(orderAfterSaleGoodsDos, good)
		}
	}
	return orderAfterSaleGoodsDos
}

// IsOriginal 是否为原订单
func (o *OrderAfterSaleDo) IsOriginal() bool {
	return o.OriginalOrderNo == "" || o.OriginalOrderNo == o.OrderNumber
}

// GetApprovedStatus 获取商家/供应商审核通过后状态
func (o *OrderAfterSaleDo) GetApprovedStatus() valobj.AfterSaleStatus {
	return o.Type.GetApprovedStatus()
}
