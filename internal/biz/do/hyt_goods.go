//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
)

type HytGoodsDo struct {
	ID         int
	Type       valobj.UpstreamGoodsType // 上游商品类型
	UniqueKey  string                   // 唯一标识
	DataJSON   string                   // json 数据
	CustomerID int                      // 客户ID
	ShopID     int                      // 所属商城id
	SupplierID int                      // 供应商
	CreateTime int                      // 创建时间
	UpdateTime int                      // 最近一次修改时间
}
