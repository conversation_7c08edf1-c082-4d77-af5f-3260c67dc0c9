//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"github.com/shopspring/decimal"
	"time"
)

type AdminLoginLogDo struct {
	ID         int
	UserID     int                   // 订单id
	CustomerID int                   // 客户id
	ShopID     int                   // 所属商城id，客户级为0
	CreateTime int                   // 创建时间
	UpdateTime int                   // 修改时间
	IP         string                // ip
	UserAgent  string                // user-agent
	LoginType  valobj.AdminLoginType // 登录方式
}

// LoginIpValidateExpired 登录ip验证是否过期
func (a *AdminLoginLogDo) LoginIpValidateExpired() bool {
	return decimal.NewFromInt(time.Now().Add(-constants.AdminLoginNeedsSmsVerifyInterval).Unix()).
		Sub(decimal.NewFromInt(int64(a.CreateTime))).IsPositive()
}
