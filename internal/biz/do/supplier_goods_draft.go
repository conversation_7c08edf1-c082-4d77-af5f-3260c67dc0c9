//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"encoding/json"
)

type SupplierGoodsDraftDo struct {
	ID          int
	SupplierID  int                                // 供应商ID
	GoodsID     int                                // 商品ID
	Detail      string                             // 修改详情
	Status      valobj.SupplierGoodsDraftStatusObj // 审核状态 1-待审核 2-审核通过 4-审核驳回 9-保存为草稿 10-已作废
	OpType      valobj.SupplierGoodsDraftOpTypeObj // 操作类型 1-上架 2-修改信息 3-新增 4-草稿
	RejectTimes int                                // 驳回次数
	AuditLogID  int                                // 最新审核id
	CreateTime  int                                // 创建时间
	UpdateTime  int                                // 修改时间
	GoodsName   string                             // 商品名称 冗余
	SubmitTime  int                                // 提交时间
	OperaId     int                                // 操作人ID
	OperaName   string                             // 操作人名称
	CustomerID  int                                // 企业ID
	ShopID      int                                // 商城ID

	// Edges holds the relations/edges for other nodes in the graph.
	Goods *SupplierGoodsDo `json:"goods"`
}

// CheckShelfFrequently 检查是否频繁上架
func (s *SupplierGoodsDraftDo) CheckShelfFrequently() bool {
	now := helper.GetNow()
	return now-s.SubmitTime < 10
}

type SupplierGoodsDraftListDo struct {

	// 草稿id
	DraftId int `json:"draft_id,omitempty"`
	//商品类型:1=虚拟，2=实物
	Type int `json:"type,omitempty"`
	// 商品名称
	GoodsName string `json:"goods_name,omitempty"`
	// 商品ID
	GoodsID int `json:"goods_id,omitempty"`
	// 供应商ID
	SupplierID int `json:"supplier_id,omitempty"`
	// 供应商名称
	SupplierName string `json:"supplier_name,omitempty"`
	// 修改时间
	UpdateTime int `json:"update_time,omitempty"`
	// 操作类型 1-上架 2-修改信息 3-新增 4-草稿
	OpType int `json:"op_type,omitempty"`

	Status int32 `json:"status,omitempty"`
	// 商品图片
	MainImage string `json:"main_image,omitempty"`

	CategoryId int `json:"category_id,omitempty"` // 所属分类
	BrandId    int `json:"brand_id,omitempty"`    // 品牌名称

	GoodsBrandDo           *GoodsBrandDo
	GoodsCategoryDo        *GoodsCategoryDo
	GoodsCategoryAllNodeDo *GoodsCategoryAllNodeDo
}

func (s *SupplierGoodsDraftListDo) GetCategoryAllNodeName() string {
	if s.GoodsCategoryAllNodeDo == nil {
		return ""
	}
	return s.GoodsCategoryAllNodeDo.GetName()
}

// GetDraftDetail .
func (s *SupplierGoodsDraftDo) GetDraftDetail() (*DraftDetail, error) {
	detail := new(DraftDetail)

	if s.Detail != "" {
		_ = json.Unmarshal([]byte(s.Detail), &detail)
	}

	return detail, nil
}

// NoNeedAdd 是否为草稿
func (s *SupplierGoodsDraftDo) NoNeedAdd() bool {
	if s == nil {
		return true
	}
	return s.Status == valobj.SupplierGoodsDraftStatusNone || s.OpType == valobj.SupplierGoodsDraftOpTypeNone
}

// WaitForAudit 审核中
func (s *SupplierGoodsDraftDo) WaitForAudit() bool {
	if s == nil {
		return false
	}
	return s.Status == valobj.SupplierGoodsDraftStatusWaitForAudit
}

type DraftDetail struct {
	Goods       *SupplierGoodsDo      `json:"goods,omitempty"`
	GoodsSku    []*SupplierGoodsSkuDo `json:"goodsSku,omitempty"`
	OldGoods    *SupplierGoodsDo      `json:"oldGoods,omitempty"`
	OldGoodsSku []*SupplierGoodsSkuDo `json:"oldGoodsSku,omitempty"`

	// 保存草稿
	Draft string `json:"draft,omitempty"`
}

// NoEdit 是否有调整
func (s *DraftDetail) NoEdit(goodsDo *SupplierGoodsDo) bool {
	return s.Goods.NoEdit(goodsDo)
}

func (s *DraftDetail) ToJsonStr() string {
	jsonByte, _ := json.Marshal(s)
	return string(jsonByte)
}

// GetOldGoodsSkuNos .
func (s *DraftDetail) GetOldGoodsSkuNos() []string {
	var skuNos []string
	for _, sku := range s.OldGoodsSku {
		skuNos = append(skuNos, sku.SkuNo)
	}
	return skuNos
}
