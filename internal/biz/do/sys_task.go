//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
)

type SysTaskDo struct {
	ID            int                  // 任务id
	SysTaskName   string               // 任务名称
	SysTaskNumber string               // 任务编号
	SysTaskType   valobj.SysTaskType   // 任务类型1=导出
	SysTaskSource valobj.SysTaskSource // 任务来源1=礼包券码下载
	RelatedNo     string               // 关联单号
	Parameter     string               // 任务参数
	DownloadURL   string               // 文件下载地址
	Status        valobj.SysTaskStatus // 任务状态1=未开始，2=执行中，3=已完成，4=已失败
	Remark        string               // 备注
	FailReason    string               // 失败原因
	UserID        int                  // 会员ID
	UserName      string               // 会员名称
	CreateTime    int                  // 创建时间
	UpdateTime    int                  // 修改时间
	FinishTime    int                  // 修改时间
	CustomerID    int
	ShopID        int
}

func (s *SysTaskDo) IsDownloadNeedSMS() bool {
	return helper.InSlice(s.SysTaskSource, s.SysTaskSource.SMSValidateSource())
}
