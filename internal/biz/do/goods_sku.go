package do

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"fmt"
	"github.com/shopspring/decimal"
	"strconv"
	"strings"

	"encoding/json"
)

type GoodsSkuDo struct {
	Id            int
	GoodsId       int
	SupplierSkuId int
	SkuNo         string
	Name          string
	Image         string
	SpecItemIds   string
	MarketPrice   float64
	SalePrice     float64
	SaleIntegral  int
	SupplierPrice float64
	//ChannelPrice       float64
	FreePostagePrice   float64
	Barcode            string
	SupplierBarcode    string
	ProductId          string
	Stock              int
	Status             int
	Sort               int
	Integral           int
	Recommend          valobj.GoodsSkuRecommendObj
	CreateTime         int
	UpdateTime         int
	DeleteTime         int
	SupplierCustomerId int
	SettlementType     valobj.GoodsSkuSettlementTypeObj
	Discount           float64

	// 购买数量限制：0=不限
	NumLimit int

	// 限购周期 1自定义 2按自然日 3按自然周 4按自然月
	PurchaseLimitCycle int
	// 限购有效期开始
	PurchaseLimitStart int64
	// 限购有效期结束
	PurchaseLimitEnd int64
	// 购买数量--关联查询冗余
	Quantity int

	CustomerID int // 企业ID
	ShopID     int // 商城ID

	Goods *GoodsDo

	SalePriceFormula string
}

func (g *GoodsSkuDo) GetGoodsName() string {
	if g.Goods != nil {
		return g.Goods.Name
	}
	return ""
}

// GetSkuFullName 获取sku名称
func (g *GoodsSkuDo) GetSkuFullName() string {
	if g.Goods == nil {
		return g.Name
	}
	return g.Goods.Name + "/" + g.Name
}

// ValidateSkuInfo 获取sku名称
func (g *GoodsSkuDo) ValidateSkuInfo() error {
	if !g.Enable() {
		return apierr.ErrorDbNotFound("商品已下架")
	}
	if g.Goods != nil && g.Goods.Type == valobj.GoodsTypeVir {
		if g.ProductId == "" {
			return apierr.ErrorParam("商品配置有误")
		}
	}
	return nil
}

// CanDeliver 能发发货
func (g *GoodsSkuDo) CanDeliver() bool {
	if g.IsOffline() {
		return false
	}
	if g.IsDelete() {
		return false
	}
	return true
}

// IsOffline 能发发货
func (g *GoodsSkuDo) IsOffline() bool {
	return g.Status == 0
}

func (g *GoodsSkuDo) IsDelete() bool {
	if g == nil {
		return true
	}
	return g.DeleteTime > 0
}

func (g *GoodsSkuDo) Enable() bool {
	if g == nil || g.Id == 0 || g.Status != valobj.GoodsSkuStatusEnable.ToInt() || g.DeleteTime > 0 {
		return false
	}
	return true
}

func (g *GoodsSkuDo) IsOnSale() bool {
	return g.Status == int(valobj.GoodsSkuStatusEnable)
}

func (g *GoodsSkuDo) GetPayAmount() decimal.Decimal {
	return decimal.NewFromFloat(g.SalePrice).Mul(decimal.NewFromInt(int64(g.Quantity)))
}
func (g *GoodsSkuDo) IsVir() bool {
	return g.Goods != nil && g.Goods.Type.IsVir() && g.ProductId != ""
}

func (g *GoodsSkuDo) GetSalePriceByFormula(supplierPrice, marketPrice, customerPrice float64) (float64, error) {
	if g.SalePriceFormula == "" {
		return g.SalePrice, nil
	}
	if g.isIntegralFormula() {
		return 0, nil
	}
	formula := strings.ReplaceAll(g.SalePriceFormula, valobj.ProductAuthorizePriceChangeTypeSupplier.ToFormula(), fmt.Sprintf("%.4f", supplierPrice))
	formula = strings.ReplaceAll(formula, valobj.ProductAuthorizePriceChangeTypeMarket.ToFormula(), fmt.Sprintf("%.4f", marketPrice))
	formula = strings.ReplaceAll(formula, valobj.ProductAuthorizePriceChangeTypeSale.ToFormula(), fmt.Sprintf("%.4f", g.SalePrice))
	formula = strings.ReplaceAll(formula, valobj.ProductAuthorizePriceChangeTypeCustom.ToFormula(), fmt.Sprintf("%.4f", customerPrice))
	formula = strings.ReplaceAll(formula, valobj.ProductAuthorizePriceChangeTypeCustomIntegral.ToFormula(), "0.00")
	out, err := helper.Cel(formula)
	if err != nil {
		return 0, apierr.ErrorParam("【%s】授权价格计算失败，【%s】", g.SkuNo, formula)
	}
	//转为float64
	res, ok := out.Value().(float64)
	if !ok {
		return 0, apierr.ErrorParam("【%s】授权价格计算失败，结果类型为:%v", g.SkuNo, out.Type())
	}
	res = decimal.RequireFromString(fmt.Sprintf("%.8f", res)).RoundUp(2).InexactFloat64()
	if res <= 0 {
		return 0, apierr.ErrorParam("【%s】授权价格计算失败，【%s】结果小于等于0", g.SkuNo, formula)
	}
	return res, nil
}

func (g *GoodsSkuDo) isFormulaContainCustomer() bool {
	return strings.Contains(g.SalePriceFormula, valobj.ProductAuthorizePriceChangeTypeCustom.ToFormula())
}

func (g *GoodsSkuDo) isFormulaContainIntegral() bool {
	return strings.Contains(g.SalePriceFormula, valobj.ProductAuthorizePriceChangeTypeCustomIntegral.ToFormula())
}

func (g *GoodsSkuDo) isIntegralFormula() bool {
	return g.isFormulaContainIntegral() && !g.isFormulaContainCustomer()
}

type PoolGoodsUpdateDo struct {
	GoodsSkuIds []int
	Status      *int
	// 购买数量限制：0=不限
	NumLimit int

	// 限购周期 1自定义 2按自然日 3按自然周 4按自然月
	PurchaseLimitCycle int
	// 限购有效期开始
	PurchaseLimitStart int
	// 限购有效期结束
	PurchaseLimitEnd int
	// 是否推荐:1=是，2=否
	Recommend valobj.GoodsSkuRecommendObj
	// 结算类型
	SettlementType []valobj.GoodsSkuSettlementTypeObj
}
type PoolGoodsListDo struct {
	SupplierID int `json:"supplier_id,omitempty"` // 供应商ID

	SupplierSkuId int    `json:"supplier_sku_id,omitempty"`
	SkuNo         string `json:"sku_no"` // skuNo

	ProductId string `json:"product_id"`
	// 商品类型:1=虚拟，2=实物
	Type int `json:"type,omitempty"`
	// ID of the ent.
	GoodsSkuID int `json:"goods_sku_id,omitempty"`
	// 商品名称
	GoodsSkuName string `json:"goods_sku_name,omitempty"`
	// 商品名称
	GoodsName string `json:"goods_name,omitempty"`
	// 商品ID
	GoodsID int `json:"goods_id,omitempty"`

	// 市场价
	MarketPrice decimal.Decimal `json:"market_price,omitempty"`
	// 售价【建议零售价】
	SalePrice decimal.Decimal `json:"sale_price,omitempty"`
	// 售价-积分
	SaleIntegral int `json:"sale_integral,omitempty"`
	// 原价 【供应价】
	SupplierPrice decimal.Decimal `json:"supplier_price,omitempty"`

	// 库存
	Stock int `json:"stock,omitempty"`

	// 购买数量限制：0=不限
	NumLimit int `json:"num_limit,omitempty"`
	// 状态:0=下架,1=上架
	Status int `json:"status,omitempty"`

	// 是否推荐:1=是，2=否
	Recommend int `json:"recommend,omitempty"`

	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`

	// 商品图片
	Image string `json:"image,omitempty"`
	// 规格图片商品图片
	GoodsSkuImage string `json:"goods_sku_image,omitempty"`

	// 限购周期 1自定义 2按自然日 3按自然周 4按自然月
	PurchaseLimitCycle int `json:"purchase_limit_cycle,omitempty"`
	// 限购有效期开始
	PurchaseLimitStart int64 `json:"purchase_limit_start,omitempty"`
	// 限购有效期结束
	PurchaseLimitEnd int64 `json:"purchase_limit_end,omitempty"`

	TransportID    int                              `json:"transport_id"`   // 运费模板ID
	TransportName  string                           `json:"transport_name"` // 模板名称
	SettlementType valobj.GoodsSkuSettlementTypeObj `json:"settlement_type,omitempty"`

	SupplierCustomerID int `json:"supplier_customer_id,omitempty"` // 供应商客户ID

	CategoryId int `json:"category_id,omitempty"` // 所属分类
	BrandId    int `json:"brand_id,omitempty"`    // 品牌名称

	Sort int `json:"sort,omitempty"`

	SupplierDo      *SupplierDo
	GoodsBrandDo    *GoodsBrandDo
	GoodsCategoryDo *GoodsCategoryDo
}
type PoolGoodsDetailDo struct {
	SupplierSkuId int    `json:"supplier_sku_id,omitempty"`
	SkuNo         string `json:"sku_no"` // skuNo
	// ID of the ent.
	GoodsSkuID int `json:"goods_sku_id,omitempty"`
	// 商品名称
	GoodsSkuName string `json:"goods_sku_name,omitempty"`
	// 商品名称
	GoodsName string `json:"goods_name,omitempty"`
	// 商品ID
	GoodsID int `json:"goods_id,omitempty"`

	// 市场价
	MarketPrice decimal.Decimal `json:"market_price,omitempty"`
	// 售价【建议零售价】
	SalePrice decimal.Decimal `json:"sale_price,omitempty"`
	// 售价-积分
	SaleIntegral int `json:"sale_integral,omitempty"`
	// 原价 【供应价】
	SupplierPrice decimal.Decimal `json:"supplier_price,omitempty"`

	// 库存
	Stock int `json:"stock,omitempty"`

	// 购买数量限制：0=不限
	NumLimit int `json:"num_limit,omitempty"`
	// 状态:0=下架,1=上架
	Status int `json:"status,omitempty"`

	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`

	// 商品图片 JSON
	Images string `json:"images,omitempty"`
	// 商品图片
	Image string `json:"image,omitempty"`
	// 商品图片详情图片
	DetailImage string `json:"detail_image,omitempty"`
	// 规格图片商品图片
	GoodsSkuImage string `json:"goods_sku_image,omitempty"`

	CategoryId int `json:"category_id,omitempty"` // 所属分类
	//CategoryName string `json:"category_name,omitempty"` // 类目 TODO
	BrandId   int    `json:"brand_id,omitempty"`   // 品牌名称
	BrandName string `json:"brand_name,omitempty"` // 品牌名称
	//供应商
	SupplierId int `json:"supplier_id,omitempty"`
	//供应商名称
	SupplierName       string `json:"supplier_name,omitempty"`
	NotSaleArea        string `json:"not_sale_area"`                  // 限制销售区域
	SupplierCustomerID int    `json:"supplier_customer_id,omitempty"` // 供应商客户ID
}

func (s *PoolGoodsDetailDo) GetImages() ([]string, error) {
	images := make([]string, 0)
	if s.Images == "" {
		return images, nil
	}
	err := json.Unmarshal([]byte(s.Images), &images)
	return images, err
}
func (s *PoolGoodsDetailDo) GetDetailImages() ([]string, error) {
	images := make([]string, 0)
	if s.DetailImage != "" {
		images = strings.Split(s.DetailImage, ",")
	}
	return images, nil
}
func (s *PoolGoodsDetailDo) GetNotSaleArea() []int {
	areas := make([]int, 0)
	if s.NotSaleArea == "" {
		return areas
	}
	areasArr := strings.Split(s.NotSaleArea, ",")
	for _, area := range areasArr {
		areaId, _ := strconv.Atoi(area)
		areas = append(areas, areaId)
	}
	return areas
}

func (p *PoolGoodsUpdateDo) Validate() error {
	if len(p.SettlementType) == 0 && len(p.GoodsSkuIds) == 0 {
		return apierr.ErrorParam("参数错误")
	}
	return nil
}

type ShopGoodsSkuCountDo struct {
	ShopID int `json:"shop_id"`
	Count  int `json:"count"`
}
