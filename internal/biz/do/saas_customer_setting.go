//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

type SaasCustomerSettingDo struct {
	ID         int
	Name       string // 用户名
	CreateTime int    // CreateTime holds the value of the "create_time" field.
	UpdateTime int    // UpdateTime holds the value of the "update_time" field.
	CustomerID int    // CustomerID holds the value of the "customer_id" field.
	Icon       string // Icon holds the value of the "icon" field.
	Logo       string // Logo holds the value of the "logo" field.
	BgImage    string // BgImage holds the value of the "bg_image" field.
	Prompt     string // Prompt
	NameColor  string // NameColor
}

func GetDefaultSaasCustomerSetting() SaasCustomerSettingDo {
	return SaasCustomerSettingDo{
		ID:         0,
		Name:       "企业管理平台",
		CreateTime: 0,
		UpdateTime: 0,
		CustomerID: 0,
		Icon:       "https://attachment-public.oss-cn-hangzhou.aliyuncs.com/crmApi/public/1/eatcf3ghcdfk.ico",
		Logo:       "https://attachment-public.oss-cn-hangzhou.aliyuncs.com/crmApi/public/1/eatchvl7t69s.png",
		BgImage:    "https://attachment-public.oss-cn-hangzhou.aliyuncs.com/crmApi/public/1/eatcnh3jhwxs.png",
		Prompt:     "欢迎登录企业管理平台",
		NameColor:  "#2965ff",
	}
}
