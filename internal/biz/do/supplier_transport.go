//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import "cardMall/internal/biz/valobj"

type SupplierTransportDo struct {
	ID           int                            `json:"id,omitempty"`            // ID
	Name         string                         `json:"name,omitempty"`          // 模板名称
	Description  string                         `json:"description,omitempty"`   // 模板描述
	KdID         int                            `json:"kd_id,omitempty"`         // 默认快递公司
	KdName       string                         `json:"kd_name,omitempty"`       // 默认快递公司名称【冗余】
	KdCode       string                         `json:"kd_code,omitempty"`       // 默认快递公司code【冗余】
	PricingMode  valobj.TransportPricingModeObj `json:"pricing_mode,omitempty"`  // 计费规则(ENUM):1-免运费;2-按件数;3-按重量;4-按体积;
	SupplierID   int                            `json:"supplier_id,omitempty"`   // 供应商id
	DefaultNum   float64                        `json:"default_num,omitempty"`   // 默认数量
	DefaultPrice float64                        `json:"default_price,omitempty"` // 默认运费
	AddNum       float64                        `json:"add_num,omitempty"`       // 增加数量
	AddPrice     float64                        `json:"add_price,omitempty"`     // 增加运费
	CreateTime   int                            `json:"create_time,omitempty"`   // 创建时间
	UpdateTime   int                            `json:"update_time,omitempty"`   // 修改时间
	CustomerID   int                            `json:"customer_id,omitempty"`   // 企业ID
	ShopID       int                            `json:"shop_id,omitempty"`       // 商城ID
	// Edges holds the relations/edges for other nodes in the graph.
	SupplierTransportItem []*SupplierTransportItemDo `json:"supplier_transport_item,omitempty"`
	// SupplierTransportCity []*SupplierTransportCityDo
}

// GetName get the name
func (s *SupplierTransportDo) GetName() string {
	if s == nil {
		return ""
	}
	return s.Name
}

// CheckSupplierId check the SupplierId
func (s *SupplierTransportDo) CheckSupplierId(supplierId int) bool {
	return s.SupplierID == supplierId
}

func (s *SupplierTransportDo) GetDefaultNum() float64 {
	return s.DefaultNum
}

func (s *SupplierTransportDo) GetDefaultPrice() float64 {
	return s.DefaultPrice
}

func (s *SupplierTransportDo) GetAddNum() float64 {
	return s.AddNum
}

func (s *SupplierTransportDo) GetAddPrice() float64 {
	return s.AddPrice
}

// GetID get the id
func (s *SupplierTransportDo) GetID() int {
	if s == nil {
		return 0
	}
	return s.ID
}
