//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import "cardMall/internal/biz/valobj"

type GoodsBrandExtraDo struct {
	ID         int
	Code       string                        // 品牌编号
	ShopID     int                           // 商城ID
	CustomerID int                           // 企业ID
	Recommend  valobj.GoodsBrandRecommendObj // 是否推荐0=否,1=是
	Sort       int                           // 排序
	Status     valobj.GoodsBrandStatusObj    // 状态:1=启用，0=禁用
	CreateTime int                           // CreateTime holds the value of the "create_time" field.
	UpdateTime int                           // UpdateTime holds the value of the "update_time" field.
}

func (g *GoodsBrandExtraDo) IsEnable() bool {
	if g == nil {
		return true
	}
	return g.Status.IsEnable()
}
