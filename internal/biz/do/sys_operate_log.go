package do

type SysOperateLogDo struct {
	// ID of the ent.
	// 编号
	ID int `json:"id,omitempty"`
	// 系统模块
	Title string `json:"title,omitempty"`
	// 操作类型
	OperationType string `json:"operation_type,omitempty"`
	// 操作人员
	OperationName string `json:"operation_name,omitempty"`
	// 请求方式
	RequestMethod string `json:"request_method,omitempty"`
	// 操作方法
	OperationURL string `json:"operation_url,omitempty"`
	// 请求参数
	OperationParams string `json:"operation_params,omitempty"`
	// 响应参数
	OperationResponse string `json:"operation_response,omitempty"`
	// 操作状态
	OperationStatus int `json:"operation_status,omitempty"`
	// 执行时长(毫秒)
	UseTime int `json:"use_time,omitempty"`
	// 浏览器
	Browser string `json:"browser,omitempty"`
	// 操作信息
	Os string `json:"os,omitempty"`
	// 操作地址
	OperationIP string `json:"operation_ip,omitempty"`
	// 操作时间
	OperationTime int `json:"operation_time,omitempty"`
}
