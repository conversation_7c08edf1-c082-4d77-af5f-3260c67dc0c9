//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

type SupplierGoodsStockAlarmDo struct {
	ID         int
	GoodsID    int    // SPU ID
	SkuID      int    // SKU ID
	SupplierID int    // 库存预警
	Num        int    // 库存操作数量
	Mobile     string // 库存操作数量
	CreateTime int    // 创建时间
	UpdateTime int    // 修改时间
	CustomerID int    // 企业ID
	ShopID     int    // 商城ID
}

// NeedAlarm 是否需要预警
func (s *SupplierGoodsStockAlarmDo) NeedAlarm() bool {
	if s.Num <= 0 {
		return false
	}
	return s.Mobile != ""
}
