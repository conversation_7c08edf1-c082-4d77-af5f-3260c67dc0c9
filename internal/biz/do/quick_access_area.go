package do

import (
	"strconv"
	"strings"

	"cardMall/internal/biz/valobj"
)

type QuickAccessAreaDo struct {
	Id               int
	Name             string
	Status           valobj.QuickAccessAreaStatusObj
	RelationType     valobj.QuickAccessAreaRelationTypeObj
	RelationValue    string
	Icon             string
	Sort             int
	CreateTime       int
	UpdateTime       int
	RelationValueIds string
}

func (q *QuickAccessAreaDo) IsEnable() bool {
	if q == nil {
		return false
	}
	return q.Status.IsEnable()
}

func (q *QuickAccessAreaDo) GetRelationValueIds() []int {
	s := strings.Split(q.RelationValueIds, ",")
	ids := make([]int, 0, len(s))
	for _, v := range s {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, id)
	}
	return ids
}
