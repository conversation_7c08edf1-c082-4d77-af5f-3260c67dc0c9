//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import "cardMall/internal/biz/valobj"

type GoodsDo struct {
	ID              int
	Type            valobj.GoodsTypeObj   // 商品类型:1=虚拟，2=实物
	BrandID         int                   // 所属品牌
	Name            string                // 商品名称
	Image           string                // 商品图片
	DetailImage     string                // 商品图片详情图片
	Detail          string                // 商品详情
	Recommend       int                   // 是否首页推荐:0=否，1=是
	Status          valobj.GoodsStatusObj // 状态:0=下架,1=上架
	Sort            int                   // 排序
	CreateTime      int                   // 创建时间
	UpdateTime      int                   // 修改时间
	SupplierGoodsID int                   // 供应商商品ID
	CategoryID      int                   // 所属分类
	SupplierID      int                   // 供应商ID
	Images          string                // 商品图片 JSON
	TaxRate         float64               // 税率 13% => 0.13
	TaxRemark       string                // 税率备注
	NotSaleArea     string                // 限制销售区域
	SalesVolume     int                   // 销量

	CustomerID         int // 企业ID
	ShopID             int // 商城ID
	SupplierCustomerID int

	// Edges holds the relations/edges for other nodes in the graph.
	GoodsSku []*GoodsSkuDo
}

// IsOffline 是否下架
func (g *GoodsDo) IsOffline() bool {
	return g.Status == valobj.GoodsStatusDisable
}
