package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"fmt"
)

type AdminDo struct {
	Id            int
	Name          string
	Account       string
	Password      string
	Status        valobj.AdminStatusObj
	Type          valobj.AdminTypeObj
	CreateTime    int
	UpdateTime    int
	RoleID        int
	SupplierID    int
	Mobile        string
	CustomerId    int
	ShopId        int
	IsRoot        valobj.AdminIsRootObj
	NeedModifyPwd int

	Supplier     *SupplierDo
	CustomerShop *CustomerShopDo
	SaasCustomer *SaasCustomerDo

	LoginToken string
}

func (a *AdminDo) GetSaasCustomerName() string {
	if a.SaasCustomer != nil {
		return a.SaasCustomer.Name
	}
	return ""
}

func (a *AdminDo) GetLoginToken() string {
	if !a.Status.IsEnable() {
		return ""
	}
	return a.LoginToken
}

// GetSupplierName 获取供应商信息
func (a *AdminDo) GetSupplierName() string {
	if a.Supplier != nil {
		return a.Supplier.Name
	}
	return ""
}

func (a *AdminDo) GetCustomerShopName() string {
	if a.CustomerShop != nil {
		return a.CustomerShop.Name
	}
	return ""
}
func (a *AdminDo) GetShowName() string {
	switch a.Type {
	case valobj.AdminTypeShopAdmin:
		return a.GetCustomerShopName()
	case valobj.AdminTypeShopSupplier:
		return fmt.Sprintf("%s - %s", a.GetCustomerShopName(), a.GetSupplierName())
	case valobj.AdminTypeCustomer:
		return a.GetSaasCustomerName()
	case valobj.AdminTypeCustomerSupplier:
		return a.GetSupplierName()
	case valobj.AdminTypeSaasSupplier:
		return a.GetSupplierName()
	case valobj.AdminTypeSaasAdmin:
		fallthrough
	default:
		return ""
	}
}

// IsSupplier 是否为供应商登录账号
func (a *AdminDo) IsSupplier() bool {
	return a.Type.IsSupplier() && a.SupplierID > 0
}

// IsShopRootAdmin 是否为商城超级管理员
func (a *AdminDo) IsShopRootAdmin() bool {
	return a.Type.IsShopAdmin() && a.IsRoot.IsYes()
}

// GetUrlSuffix 获取登录后跳转后缀
func (a *AdminDo) GetUrlSuffix() string {
	return isolationcustomer.EncodeC(a.CustomerId, a.ShopId)
	//return fmt.Sprintf("%d-%d", a.CustomerId, a.ShopId)
}

// GetAdminUrl 获取登录后跳转地址
func (a *AdminDo) GetAdminUrl(conf *conf.Site) string {
	if a.CustomerId == constants.SaasPlatformCustomer {
		return fmt.Sprintf("%s/supplier/login", conf.GetSaasSupplier())
	}
	return fmt.Sprintf("%s/saaslogin?c=%s", conf.GetCustomerAdmin(), a.GetUrlSuffix())
}

func (a *AdminDo) NeedUpdatePwd() bool {
	return a.NeedModifyPwd != 0 && a.NeedModifyPwd < helper.GetNow()
}

// IsCustomer 是否为企业管理员
func (a *AdminDo) IsCustomer() bool {
	return a.Type.IsCustomer() && a.CustomerId > 0
}

// AdminLoginDo 登录信息DO
type AdminLoginDo struct {
	Id             int
	Name           string
	RoleID         int
	Type           valobj.AdminTypeObj
	SaasUserId     int
	SaasUserMobile string
}
