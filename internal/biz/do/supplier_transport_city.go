//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"fmt"
)

type SupplierTransportCityDo struct {
	ID          int    // ID
	ItemID      int    // item_id
	TransportID int    // 物流模板ID【冗余】
	CityID      int    // 供应商id
	CityName    string // 城市名称【冗余】
	CustomerID  int    // 企业ID
	ShopID      int    // 商城ID

	// Edges holds the relations/edges for other nodes in the graph.
	SupplierTransportItem *SupplierTransportItemDo
	SupplierTransport     *SupplierTransportDo
}

// GetTransportItem 获取物流模板
func (o *SupplierTransportCityDo) GetTransportItem() *SupplierTransportItemDo {
	if o == nil {
		return nil
	}
	return o.SupplierTransportItem
}

// UniqueKey returns the unique identifier for the node.
func (o *SupplierTransportCityDo) UniqueKey() string {
	return fmt.Sprintf("%d-%d", o.TransportID, o.CityID)
}
