//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import "cardMall/internal/biz/valobj"

type OrderOperatorLogDo struct {
	// ID of the ent.
	ID int `json:"id,omitempty"`
	// 订单id
	OrderID int `json:"order_id,omitempty"`
	// 订单号
	OrderNumber string `json:"order_number,omitempty"`
	// 订单状态：同订单表状态
	OrderStatus valobj.OrderStatusObj `json:"order_status,omitempty"`
	// 操作日志内容
	Content string `json:"content,omitempty"`
	// 操作人类型:1=商户 2=系统
	OperatorUserType int `json:"operator_user_type,omitempty"`
	// 操作人id
	OperatorUserID int `json:"operator_user_id,omitempty"`
	// 操作人名称 商户 or 系统
	OperatorUserName string `json:"operator_user_name,omitempty"`
	// 创建时间
	CreateTime int64 `json:"create_time,omitempty"`
	// 修改时间
	UpdateTime int64 `json:"update_time,omitempty"`

	Order *OrderDo
}
