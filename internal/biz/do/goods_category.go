package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"fmt"
)

type GoodsCategoryAddDo struct {
	Id int
}

type GoodsCategoryDo struct {
	Id         int                              `json:"id"`
	Name       string                           `json:"name"`
	Status     valobj.GoodsCategoryStatusObj    `json:"status"`
	Sort       int                              `json:"sort"`
	Pid        int                              `json:"pid"`
	Level      int                              `json:"level"`
	CreateTime int                              `json:"createTime"`
	UpdateTime int                              `json:"updateTime"`
	Type       valobj.GoodsCategoryTypeObj      `json:"type"`
	Image      string                           `json:"image"`
	GoodsNum   int                              `json:"goodsNum"`
	Recommend  valobj.GoodsCategoryRecommendObj `json:"recommend"`
	IndexShow  valobj.GoodsCategoryIndexShowObj `json:"indexShow"`
	CustomerID int                              `json:"customerID"` // 企业ID
	ShopID     int                              `json:"shopID"`     // 商城ID
	HytId      int                              // 货易通分类id
	HytName    string                           // 货易通分类名称
	Code       string                           `json:"code"` //编码

	Children []*GoodsCategoryDo `json:"children"`
	Parent   *GoodsCategoryDo   `json:"parent"`
}

func (g *GoodsCategoryDo) IsEnable() bool {
	if g == nil || !g.Status.IsEnable() {
		return false
	}
	return true
}

// GetID 获取ID
func (g *GoodsCategoryDo) GetID() int {
	if g == nil {
		return 0
	}
	return g.Id
}

func (g *GoodsCategoryDo) ShopDefaultInit(customerId, shopId int) {
	if g.ShopID == 0 {
		g.CustomerID = customerId
		g.ShopID = shopId
		g.Status = g.Status.GetDefault()
		g.Recommend = g.Recommend.GetDefault()
		g.IndexShow = g.IndexShow.GetDefault()
	}
}

func (g *GoodsCategoryDo) IsMaxLevel() bool {
	if g == nil {
		return false
	}
	return g.Level == constants.GoodsCategoryLevelMax
}

func (g *GoodsCategoryDo) IsMinLevel() bool {
	if g == nil {
		return false
	}
	return g.Level == constants.GoodsCategoryLevelMin
}

type GoodsCategoryAllNodeDo struct {
	ID              int
	Name            string                           // 品类名称
	Status          valobj.GoodsCategoryStatusObj    // 状态:1=启用，0=禁用
	Sort            int                              // C端顺序,升序
	Pid             int                              // 父级目录ID
	Level           int                              // 目录级别
	Type            valobj.GoodsCategoryTypeObj      // 类目类型:1=虚拟商品,2=实物
	Image           string                           // 类目图
	GoodsNum        int                              // 分类下的商品数量
	Recommend       valobj.GoodsCategoryRecommendObj // 是否推荐，1=是，2=否
	CreateTime      int                              // 创建时间
	UpdateTime      int                              // 最近一次修改时间
	FirstLevelID    int
	FirstLevelName  string
	SecondLevelID   int
	SecondLevelName string
}

func (g *GoodsCategoryAllNodeDo) Valid() bool {
	if g == nil {
		return false
	}
	if g.Level != constants.GoodsCategoryLevelMax {
		return false
	}
	if g.FirstLevelName == "" || g.SecondLevelName == "" {
		return false
	}
	return true
}

func (g *GoodsCategoryAllNodeDo) GetName() string {
	if g == nil {
		return ""
	}
	return fmt.Sprintf("%s/%s/%s", g.FirstLevelName, g.SecondLevelName, g.Name)
}
