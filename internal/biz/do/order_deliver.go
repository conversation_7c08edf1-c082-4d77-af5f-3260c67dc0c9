//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import "cardMall/internal/biz/valobj"

type OrderDeliverDo struct {
	ID              int
	OrderID         int                            // 订单ID
	OrderNumber     string                         // 订单号
	KdID            int                            // 物流公司ID
	KdCode          string                         // 物流公司编号
	KdName          string                         // 物流公司编号
	LogisticsNo     string                         // 物流单号
	LogisticsOpCode valobj.OrderLogisticsOpCodeObj // 物流当前最新状态码
	LogisticsOpDesc string                         // 物流当前最新状态
	CreateTime      int                            // 创建时间
	UpdateTime      int                            // 更新时间
	SupplierID      int                            // 供应商Id
	Enable          valobj.OrderDeliverEnableObj   // 是否启用
	CustomerID      int                            // 企业ID
	ShopID          int                            // 商城ID
	HytSubOrderNum  string                         // 货易通子订单号

	// ent Edges
	OrderDeliverGoods []*OrderDeliverGoodsDo

	// 用户地址 需要手动查询后关联
	OrderUserAddress *OrderUserAddressDo
}

// GetOrderUserAddress .
func (o *OrderDeliverDo) GetOrderUserAddress() *OrderUserAddressDo {
	return o.OrderUserAddress
}
