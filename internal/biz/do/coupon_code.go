//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

type CouponCodeDo struct {
	ID              int
	CouponID        int    // 优惠券活动ID
	CouponCode      string // 券码
	UserID          int    // 用户ID
	Status          int    // 状态:1=未领取，2=已领取， 3=已使用，4=使用中,5=已作废
	UseTime         int    // 使用时间
	CollectionTime  int    // 领取时间
	AbolishTime     int    // 作废时间
	OrderNumber     string // 使用订单
	EffectStartTime int    // 有效期-起始时间
	EffectEndTime   int    // 有效期-结束时间
	CreateTime      int    // 创建时间
	UpdateTime      int    // 修改时间
	CustomerID      int    // 企业ID
	ShopID          int    // 商城ID
}
