//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package do

import (
	"cardMall/internal/constants"
)

type OrderUserAddressDo struct {
	ID          int    // ID
	OrderID     int    // 订单id
	OrderNumber string // 订单号
	Name        string // 姓名
	PhoneNumber string // 电话
	ProvinceID  int    // 省
	CityID      int    // 市
	RegionID    int    // 区
	Area        string // 区域
	Detail      string // 地址详情
	Lng         string // 经度
	Lat         string // 纬度
	UserID      int    // UserID holds the value of the "user_id" field.
	CreateTime  int    // CreateTime holds the value of the "create_time" field.
	EditTimes   int    // CreateTime holds the value of the "create_time" field.

	// Edges holds the relations/edges for other nodes in the graph.
	Order *OrderDo

	// 扩展字段
	Province string
	City     string
	Region   string
}

// OverRangeEditTimes 是否超过编辑次数
func (o *OrderUserAddressDo) OverRangeEditTimes() bool {
	return o.EditTimes >= constants.MaxModifyAddressTimes
}

// GetName 获取姓名
func (o *OrderUserAddressDo) GetName() string {
	if o == nil {
		return ""
	}
	return o.Name
}

// GetPhoneNumber 获取电话
func (o *OrderUserAddressDo) GetPhoneNumber() string {
	if o == nil {
		return ""
	}
	return o.PhoneNumber
}

// GetAddress 获取地址
func (o *OrderUserAddressDo) GetAddress() string {
	if o == nil {
		return ""
	}
	return o.Area + o.Detail
}
