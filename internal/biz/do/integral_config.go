package do

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"encoding/json"
	"github.com/shopspring/decimal"
	"time"
)

type IntegralConfigDo struct {
	Id                 int                                `json:"id"`
	ExchangeRate       int                                `json:"exchangeRate"`
	Name               string                             `json:"name"`
	DeductionType      valobj.IntegralConfigDeductionType `json:"deductionType"`
	DeductionRate      float64                            `json:"deductionRate"`
	ExpireType         valobj.IntegralConfigExpireTypeObj `json:"expireType"`
	ExpireYear         int                                `json:"expireYear"`
	ShopId             int                                `json:"shopId"`
	CreateTime         int                                `json:"createTime"`
	UpdateTime         int                                `json:"updateTime"`
	IntegralCash       valobj.IntegralConfigCashObj       `json:"integralCash"`
	IntegralExchange   valobj.IntegralConfigExchangeObj   `json:"integralExchange"`
	IntegralShopStatus valobj.IntegralShopStatusObj       `json:"integralShopStatus"`
	CustomerId         int                                `json:"customerId"`
}

func (i *IntegralConfigDo) GetDeductionRatePercent() float64 {
	var v float64
	if i == nil {
		return v
	}
	if i.DeductionRate > 0 {
		v, _ = decimal.NewFromFloat(i.DeductionRate).Mul(decimal.NewFromInt(100)).Float64()
	}
	return v
}

func (i *IntegralConfigDo) IsIntegralShopEnable() bool {
	if i == nil {
		return false
	}
	return i.IntegralShopStatus.IsEnable()
}

func (i *IntegralConfigDo) DeductionTypeIsPayAmount() bool {
	if i == nil {
		return false
	}
	return i.DeductionType == valobj.IntegralConfigDeductionTypeOrderPayAmount
}

func (i *IntegralConfigDo) DeductionTypeIsOrderProfit() bool {
	if i == nil {
		return false
	}
	return i.DeductionType == valobj.IntegralConfigDeductionTypeOrderProfit
}

func (i *IntegralConfigDo) GetIntegralAmount(integral int) float64 {
	if i == nil {
		return 0
	}
	return decimal.NewFromInt32(int32(integral)).Div(decimal.NewFromInt32(int32(i.ExchangeRate))).InexactFloat64()
}

func (i *IntegralConfigDo) IntegralStatusIsEnable() bool {
	if i == nil {
		return false
	}
	return i.IntegralExchange.IsEnable()
}

func (i *IntegralConfigDo) IntegralCashStatusIsEnable() bool {
	if i == nil {
		return false
	}
	return i.IntegralCash.IsEnable()
}

func (i *IntegralConfigDo) IsEnable() bool {
	return i != nil && i.DeductionRate > 0
}

func (i *IntegralConfigDo) GetName() string {
	if i == nil {
		return constants.IntegralConfigDefaultName
	}
	return i.Name
}

func (i *IntegralConfigDo) GetExpireTime() int {
	switch i.ExpireType {
	case valobj.IntegralConfigExpireTypeNo:
		return 0
	case valobj.IntegralConfigExpireTypeYearTurn:
		// 返回从当前时间到一年后的时间戳
		return int(time.Now().AddDate(i.ExpireYear, 0, 0).Unix())
	case valobj.IntegralConfigExpireTypeYearAll:
		// 返回从当前时间到年底12月31日 23:59:59 的时间戳
		y := time.Now().Year() + i.ExpireYear
		return int(time.Date(y, 12, 31, 23, 59, 59, 0, time.Local).Unix())
	default:
		return 0
	}
}

func (i *IntegralConfigDo) GetDiscountAmount(totalAmount float64) float64 {
	if !i.IsEnable() {
		return 0
	}
	return decimal.NewFromFloat(totalAmount).Mul(decimal.NewFromFloat(i.DeductionRate)).Round(2).InexactFloat64()
}

func (i *IntegralConfigDo) GetIntegral(money float64) (integral int) {
	if !i.IsEnable() {
		return 0
	}
	integral = int(decimal.NewFromFloat(money).Mul(decimal.NewFromInt32(int32(i.ExchangeRate))).RoundCeil(0).IntPart())
	return
}

func (i *IntegralConfigDo) ToJson() string {
	b, _ := json.Marshal(i)
	return string(b)
}
