//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"context"
)

type SaasShopAppletRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SaasShopAppletDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.SaasShopAppletDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.SaasShopAppletDo) (*do.SaasShopAppletDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.SaasShopAppletDo) ([]*do.SaasShopAppletDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.SaasShopAppletDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.SaasShopAppletDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.SaasShopAppletSearchBo) (dos []*do.SaasShopAppletDo, respPage *bo.RespPageBo)

	FindByAppId(ctx context.Context, appId string) (*do.SaasShopAppletDo, error)

	FindByShopId(ctx context.Context, customerId, shopId int) (*do.SaasShopAppletDo, error)

	GetByShopId(ctx context.Context, customerId int, shopId ...int) ([]*do.SaasShopAppletDo, error)

	FindByType(ctx context.Context, customerId int, shopId int, appletType valobj.SaasShopAppletTypeObj) (*do.SaasShopAppletDo, error)

	ExistsCheckByAppId(ctx context.Context, appId string, customerId int) (bool, error)
}
