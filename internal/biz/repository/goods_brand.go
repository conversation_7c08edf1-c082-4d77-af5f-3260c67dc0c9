package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
)

type GoodsBrandRepo interface {
	// Add 新增
	Add(ctx context.Context, in *bo.GoodsBrandAddBo) (*do.GoodsBrandAddDo, error)

	//Query 筛选
	Query(ctx context.Context, in *bo.GoodsBrandQueryBo) ([]*do.GoodsBrandDo, error)

	//Update 修改
	Update(ctx context.Context, in *bo.GoodsBrandUpdateBo) (int, error)

	//List 分页列表
	List(ctx context.Context, in *bo.GoodsBrandQueryBo) (int, []*do.GoodsBrandDo, error)

	//ShopList 商城分页列表
	ShopList(ctx context.Context, in *bo.GoodsBrandQueryBo) (int, []*do.GoodsBrandDo, error)

	// GetByID ID集合查询
	GetByID(ctx context.Context, in []int) ([]*do.GoodsBrandDo, error)

	Del(ctx context.Context, id int) (int, error)

	// GetOneByID 单个查询
	GetOneByID(ctx context.Context, in int) (*do.GoodsBrandDo, error)

	Search(ctx context.Context, reqBo *bo.BrandSearchBo) (dos []*do.GoodsBrandDo, respPage *bo.RespPageBo)

	FindByIds(ctx context.Context, id []int) ([]*do.GoodsBrandDo, error)

	All(ctx context.Context) ([]*do.GoodsBrandDo, error)

	FindByIdsNotIn(ctx context.Context, id []int) ([]*do.GoodsBrandDo, error)

	Find(ctx context.Context, ids ...int) ([]*do.GoodsBrandDo, error)
	NameExistV2(ctx context.Context, name string, id int) (bool, error)
	NameExist(ctx context.Context, name string) (*do.GoodsBrandDo, error)
	FindByName(ctx context.Context, name string) (*do.GoodsBrandDo, error)
	//FindByHytNames(ctx context.Context, name ...string) ([]*do.GoodsBrandDo, error)
	FindByNames(ctx context.Context, name ...string) ([]*do.GoodsBrandDo, error)
	//FindByHytName(ctx context.Context, name string) (*do.GoodsBrandDo, error)
	BindHyt(ctx context.Context, reqBo *bo.BindBrandRequest) (int, error)
	UnbindBrand(ctx context.Context, name string) (int, error)
	GetByCode(ctx context.Context, code ...string) ([]*do.GoodsBrandDo, error)

	UpdateCode(ctx context.Context, id int, code string) (int, error)

	// GetRowByName 通过名称获取数据
	GetRowByName(ctx context.Context, name string) (*do.GoodsBrandDo, error)

	ShopQuery(ctx context.Context, in *bo.GoodsBrandQueryBo) ([]*do.GoodsBrandDo, error)
}
