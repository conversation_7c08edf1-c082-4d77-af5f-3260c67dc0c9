//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type OrderAfterSaleLogRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.OrderAfterSaleLogDo, error)

	// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
	GetWithEdges(ctx context.Context, id int, reqBo *bo.OrderAfterSaleLogEdgesBo) (*do.OrderAfterSaleLogDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleLogDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.OrderAfterSaleLogDo) (*do.OrderAfterSaleLogDo, error)

	FindByPids(ctx context.Context, pids ...int) ([]*do.OrderAfterSaleLogDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleLogSearchBo) (dos []*do.OrderAfterSaleLogDo, respPage *bo.RespPageBo)

	GetByPid(ctx context.Context, pid int) ([]*do.OrderAfterSaleLogDo, error)

	FindRefuseOne(ctx context.Context, pid int) (*do.OrderAfterSaleLogDo, error)
}
