package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type QuickAccessAreaRepo interface {
	Add(ctx context.Context, in *bo.QuickAccessAreaAddOrUpdateBo) (*do.QuickAccessAreaDo, error)

	Update(ctx context.Context, in *bo.QuickAccessAreaAddOrUpdateBo) (int, error)

	Insert(ctx context.Context, in []*bo.QuickAccessAreaAddOrUpdateBo) error

	All(ctx context.Context, in *bo.QuickAccessAreaQueryBo) ([]*do.QuickAccessAreaDo, error)

	Del(ctx context.Context, id ...int) (int, error)

	FineOne(ctx context.Context, in *bo.QuickAccessAreaQueryBo) (*do.QuickAccessAreaDo, error)

	Count(ctx context.Context, in *bo.QuickAccessAreaQueryBo) (int, error)
}
