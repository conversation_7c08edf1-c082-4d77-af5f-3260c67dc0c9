//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type FlashSaleTimeRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.FlashSaleTimeDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.FlashSaleTimeDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.FlashSaleTimeDo) (*do.FlashSaleTimeDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.FlashSaleTimeDo) ([]*do.FlashSaleTimeDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.FlashSaleTimeDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.FlashSaleTimeDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.FlashSaleTimeSearchBo) (dos []*do.FlashSaleTimeDo, respPage *bo.RespPageBo)

	DelByActivityId(ctx context.Context, id int) (int, error)

	GetByActivityId(ctx context.Context, ids ...int) ([]*do.FlashSaleTimeDo, error)

	GetToday(ctx context.Context, activityId int) ([]*do.FlashSaleTimeDo, error)

	FindStared(ctx context.Context, activityId int) (*do.FlashSaleTimeDo, error)
}
