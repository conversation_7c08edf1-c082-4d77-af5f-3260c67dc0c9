//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
)

type SupplierGoodsRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SupplierGoodsDo, error)

	// SupplierExistByName 判断供应商是否存在该商品名称
	SupplierExistByName(ctx context.Context, supplierId int, name string) (bool, error)

	SupplierExistByNameAndIdNEQ(ctx context.Context, supplierId int, name string, id int) (bool, error)

	// GetWithSku 通过 id 获取一条数据，并获取 sku
	GetWithSku(ctx context.Context, id int) (*do.SupplierGoodsDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, customerID int, ids ...int) ([]*do.SupplierGoodsDo, error)

	// IncrStock 增加/减少库存
	IncrStock(ctx context.Context, id int, num int) error

	// Shelf 上下架
	Shelf(ctx context.Context, id int, status valobj.SupplierGoodsStatusObj) error

	// Create 创建数据
	Create(ctx context.Context, d *do.SupplierGoodsDo, status valobj.SupplierGoodsStatusObj) (*do.SupplierGoodsDo, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, apierr.ErrorDbNotFound("数据更新失败")
	UpdateV2(ctx context.Context, d *do.SupplierGoodsDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.SupplierGoodsSearchBo) (dos []*do.SupplierGoodsDo, respPage *bo.RespPageBo)
	FindById(ctx context.Context, id int) (*do.SupplierGoodsDo, error)

	GetOffShelfCount(ctx context.Context, supplierId int) (int, error)
	FindByBrandId(ctx context.Context, brandId int) (*do.SupplierGoodsDo, error)

	// SupplierExist 判断供应商是否存在商品
	SupplierExist(ctx context.Context, supplierId []int) (bool, error)

	TransportIdExist(ctx context.Context, transportId int) (bool, error)

	FindBySupplierIdWithSku(ctx context.Context, supplierId int) ([]*do.SupplierGoodsDo, error)

	FindByBrandID(ctx context.Context, brandID int) ([]*do.SupplierGoodsDo, error)

	FindByCategoryID(ctx context.Context, categoryID int) (*do.SupplierGoodsDo, error)

	SkuChange(ctx context.Context, supplierGoodsId int, brandId int, category int) (int, error)

	FindByNamesByLoginInfo(ctx context.Context, names []string) []*do.SupplierGoodsDo
	UpdateLastAuditStatus(ctx context.Context, goodsId int, status valobj.SupplierGoodsDraftStatusObj) (int, error)
}
