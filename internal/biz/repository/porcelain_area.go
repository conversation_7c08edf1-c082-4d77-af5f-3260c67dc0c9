package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
)

type PorcelainAreaRepo interface {
	// Get 通过 id 获取一条数据，出错则 panic
	Get(ctx context.Context, id int) *do.PorcelainAreaDo

	GetE(ctx context.Context, id int) (*do.PorcelainAreaDo, error)

	// Find 通过多个 id 获取多条数据，出错则 panic
	Find(ctx context.Context, ids ...int) []*do.PorcelainAreaDo

	// Create 创建数据，出错则 panic
	Create(ctx context.Context, createData *do.PorcelainAreaDo) *do.PorcelainAreaDo

	// CreateBulk 批量创建数据，出错则 panic
	CreateBulk(ctx context.Context, dos []*do.PorcelainAreaDo) []*do.PorcelainAreaDo

	// Update 更新数据，出错则 panic
	Update(ctx context.Context, updateData *do.PorcelainAreaDo) int

	// SearchList 搜索列表，出错则 panic
	SearchList(ctx context.Context, reqBo *bo.PorcelainAreaQueryBo) (dos []*do.PorcelainAreaDo, respPage *bo.RespPageBo)

	// Delete 删除数据，出错则 panic
	Delete(ctx context.Context, ids ...int)

	// Count 获取数量
	Count(ctx context.Context) (int, error)

	// CheckName 检查名称是否存在
	CheckName(ctx context.Context, porcelain *do.PorcelainAreaDo) error
}
