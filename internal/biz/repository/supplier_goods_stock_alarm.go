//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/module/supplierbiz/bo"
	"context"
)

type SupplierGoodsStockAlarmRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SupplierGoodsStockAlarmDo, error)

	// ExistSupplierAlarmSetting 判断供应商是否有预警设置
	ExistSupplierAlarmSetting(ctx context.Context, supplierId int) (bool, error)

	// GetSupplierAlarmSetting 获取供应商预警设置
	GetSupplierAlarmSetting(ctx context.Context, supplierId int) (*do.SupplierGoodsStockAlarmDo, error)

	// ListSupplierAlarmSetting 获取供应商预警设置列表
	ListSupplierAlarmSetting(ctx context.Context) ([]*do.SupplierGoodsStockAlarmDo, error)

	// SetSupplierAlarmSetting 设置供应商预警设置
	SetSupplierAlarmSetting(ctx context.Context, reqBo *bo.GoodsStockAlarmBo) error

	// AddSupplierAlarmSetting 添加供应商预警设置
	AddSupplierAlarmSetting(ctx context.Context, reqBo *bo.GoodsStockAlarmBo) error
}
