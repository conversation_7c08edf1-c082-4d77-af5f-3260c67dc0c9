//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type SiteRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SiteDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.SiteDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.SiteDo) (*do.SiteDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.SiteDo) ([]*do.SiteDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.SiteDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.SiteDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.SiteSearchBo) (dos []*do.SiteDo, respPage *bo.RespPageBo)

	GetMapByIds(ctx context.Context, id []int) (map[int]*do.SiteDo, error)

	//DisableAll 禁用除主站点以外的所有站点
	DisableAll(ctx context.Context) (int, error)

	// FindDefault 查询商城的默认站点
	FindDefault(ctx context.Context) (*do.SiteDo, error)

	FindByUniqueStr(ctx context.Context, uniqueStr string) (*do.SiteDo, error)

	// GetDefault 获取企业上商城的默认站点
	GetDefault(ctx context.Context, shopIds []int) ([]*do.SiteDo, error)
}
