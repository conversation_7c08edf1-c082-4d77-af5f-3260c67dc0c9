package repository

import (
	"cardMall/internal/biz/do"
	"context"
)

type GoodsCategoryBrandRepo interface {
	Add(ctx context.Context, categoryId int, brandId []int) error

	Update(ctx context.Context, categoryId int, brandId []int) error

	Query(ctx context.Context, categoryId int) ([]*do.GoodsCategoryBrandDo, error)

	GetByCategoryIds(ctx context.Context, categoryIds []int) ([]*do.GoodsCategoryBrandDo, error)

	FindByBrandId(ctx context.Context, brandId int) (*do.GoodsCategoryBrandDo, error)

	DelByCategoryId(ctx context.Context, categoryId int) (int, error)

	Del(ctx context.Context, categoryId int, brandId int) (int, error)

	DelByBrandId(ctx context.Context, brandId int) (int, error)
}
