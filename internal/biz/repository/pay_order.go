//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
)

type PayOrderRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.PayOrderDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.PayOrderDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.PayOrderDo) (*do.PayOrderDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.PayOrderDo) ([]*do.PayOrderDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.PayOrderDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.PayOrderDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.PayOrderSearchBo) (dos []*do.PayOrderDo, respPage *bo.RespPageBo)

	FindByCardCouponNumber(ctx context.Context, cardCouponNumber string) ([]*do.PayOrderDo, error)

	FindByOrderNumber(ctx context.Context, orderNumber string) (*do.PayOrderDo, error)

	ListByOrderNumber(ctx context.Context, orderNos []string) ([]*do.PayOrderDo, error)

	ListByOrderNumberWithPayTypes(ctx context.Context, orderNos []string, payTypes ...valobj.PayOrderPayTypeObj) ([]*do.PayOrderDo, error)

	// GetByOrderNumber 获取订单详情
	GetByOrderNumber(ctx context.Context, orderNumber string, req *bo.PayOrderEdgeReqBo) (*do.PayOrderDo, error)

	ActivityOrderTotal(ctx context.Context, activityIds []int) ([]*do.PayOrderActivityTotalDo, error)
}
