package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type GoodsSpecRepo interface {
	FindByName(ctx context.Context, name string) (*do.GoodsSpecDo, error)

	Add(ctx context.Context, in *bo.GoodsSpecAddBo) (*do.GoodsSpecDo, error)

	Get(ctx context.Context, in *bo.GoodsSpecQueryBo) ([]*do.GoodsSpecDo, error)

	GetByIds(ctx context.Context, id []int) ([]*do.GoodsSpecDo, error)

	GetMapByIds(ctx context.Context, id []int) (map[int]*do.GoodsSpecDo, error)
}
