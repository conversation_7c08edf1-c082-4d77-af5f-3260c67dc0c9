//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
)

type BaseGoodsRepo interface {
	// Create 创建数据
	Create(ctx context.Context, d *do.BaseGoodsDo) (*do.BaseGoodsDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.BaseGoodsDo) ([]*do.BaseGoodsDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.BaseGoodsSearchBo) (dos []*do.BaseGoodsDo, respPage *bo.RespPageBo)

	All(ctx context.Context) (dos []*do.BaseGoodsDo)

	FindByProductId(ctx context.Context, productId string) *do.BaseGoodsDo

	FindByNamesByLoginInfo(ctx context.Context, names []string) []*do.BaseGoodsDo

	FindByProductIdsByLoginInfo(ctx context.Context, productIds []string) []*do.BaseGoodsDo
}
