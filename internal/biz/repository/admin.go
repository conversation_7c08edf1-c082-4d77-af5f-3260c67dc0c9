package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"context"
)

type AdminRepo interface {
	GetUserByAccountAndAdminType(ctx context.Context, account string, adminType valobj.AdminTypeObj) (*do.AdminDo, error)
	GetUserByAccount(ctx context.Context, account string) (*do.AdminDo, error)
	GetUserByMobile(ctx context.Context, mobile string) (*do.AdminDo, error)
	GetUserById(ctx context.Context, id int) (*do.AdminDo, error)

	AccountExist(ctx context.Context, account string, id int) (bool, error)
	MobileExist(ctx context.Context, mobile string, id int, adminTypeObj valobj.AdminTypeObj) (bool, error)

	ListByAccount(ctx context.Context, account string) ([]*do.AdminDo, error)
	ListByMobile(ctx context.Context, account string) ([]*do.AdminDo, error)
	ListAdmin(ctx context.Context, in *bo.AdminListBo) (int, []*do.AdminDo, error)

	ListSupplier(ctx context.Context, in *bo.AdminListBo) (int, []*do.AdminDo, error)

	AddAdmin(ctx context.Context, in *bo.AdminAddBo) (int, error)

	AddSupplier(ctx context.Context, in *bo.AddSupplierBo) (int, error)

	UpdateAdmin(ctx context.Context, in *bo.AdminUpdateBo) (int, error)

	UpdateSupplier(ctx context.Context, in *bo.UpdateSupplierBo) (int, error)

	DelAdmin(ctx context.Context, id int) (int, error)

	DelSupplier(ctx context.Context, id int) (int, error)

	UpdateStatus(ctx context.Context, in *bo.AdminUpdateStatusBo) (int, error)

	ExistSupplier(ctx context.Context, supplierIds []int) (bool, error)

	ExistRole(ctx context.Context, roleId int) (bool, error)

	FindShopRoot(ctx context.Context, shopId ...int) ([]*do.AdminDo, error)

	GetShopRootByMobile(ctx context.Context, mobile string) ([]*do.AdminDo, error)

	UpdatePhone(ctx context.Context, id int, phone string) (int, error)

	UpdatePwd(ctx context.Context, uid int, pwd string) error

	UpdateNeedModifyPwd(ctx context.Context, id, expireTime int) error

	FindTransferLoginAccount(ctx context.Context, customerId int, shopId ...int) (*do.AdminDo, error)
}
