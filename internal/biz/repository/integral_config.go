package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type IntegralConfigRepo interface {
	Find(ctx context.Context, shopId int) (*do.IntegralConfigDo, error)

	Add(ctx context.Context, in *bo.IntegralConfigAddBo) (int, error)

	Update(ctx context.Context, shopId int, in *bo.IntegralConfigUpdateBo) (int, error)

	GetByLastIdLimit(ctx context.Context, lastId int, limit int) ([]*do.IntegralConfigDo, error)
}
