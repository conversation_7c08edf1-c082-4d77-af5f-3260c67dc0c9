//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type OrderAfterSaleDeliverRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.OrderAfterSaleDeliverDo, error)

	// GetByPid 通过父级 id 获取一条数据
	GetByPid(ctx context.Context, pid int) (*do.OrderAfterSaleDeliverDo, error)

	// UpdateByPid 通过父级 id 更新数据
	UpdateByPid(ctx context.Context, pid int, data *do.OrderAfterSaleDeliverDo) error

	// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
	GetWithEdges(ctx context.Context, id int, reqBo *bo.OrderAfterSaleDeliverEdgesBo) (*do.OrderAfterSaleDeliverDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleDeliverDo, error)

	FindByPids(ctx context.Context, pid ...int) ([]*do.OrderAfterSaleDeliverDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.OrderAfterSaleDeliverDo) (*do.OrderAfterSaleDeliverDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleDeliverSearchBo) (dos []*do.OrderAfterSaleDeliverDo, respPage *bo.RespPageBo)

	// FindByPid 通过售后ID id 获取数据
	FindByPid(ctx context.Context, pid int) (*do.OrderAfterSaleDeliverDo, error)

	// AddDeliver 添加售后发货信息
	AddDeliver(ctx context.Context, in *do.OrderAfterSaleDeliverDo) (int, error)

	//GetByExpressNo 通过物流单获取售后退货单信息
	GetByExpressNo(ctx context.Context, expressNo string) ([]*do.OrderAfterSaleDeliverDo, error)

	//UpdateDeliver 用户修改退货物流信息
	UpdateDeliver(ctx context.Context, in *do.OrderAfterSaleDeliverDo) (int, error)
}
