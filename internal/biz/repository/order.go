//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	supplierbo "cardMall/internal/module/supplierbiz/bo"
)

type OrderRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.OrderDo, error)

	// SetDelayDeliverRemark 设置订单的延迟发货备注
	SetDelayDeliverRemark(ctx context.Context, reqBo *supplierbo.DelayDeliverRemarkBo) error

	SetEntityOrderDeliverPartial(ctx context.Context, orderNumber string, hasDeliver bool) error

	SetEntityOrderDeliverAll(ctx context.Context, orderNumber string, hasDeliver bool) error

	// GetWithEdges 通过 id 获取一条数据
	GetWithEdges(ctx context.Context, orderNumber string, reqBo *bo.OrderWithEdgeBo) (*do.OrderDo, error)

	GetByIdWithEdges(ctx context.Context, id int, reqBo *bo.OrderWithEdgeBo) (*do.OrderDo, error)

	// FindWithEdges .
	FindWithEdges(ctx context.Context, reqBo *bo.OrderWithEdgeBo, orderNumber ...string) ([]*do.OrderDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderSearchBo) (dos []*do.OrderDo, respPage *bo.RespPageBo)

	Confirm(ctx context.Context, orderNumber string) error

	Reject(ctx context.Context, orderNumber string) error

	SetEntityOrderWaitDeliver(ctx context.Context, orderNumber string) error
	SetEntityOrderWaitDeliverPartial(ctx context.Context, orderNumber string) error

	GetWaitDeliverOrderCount(ctx context.Context, supplierId int) (int, error)
	GetTimeoutOrderCount(ctx context.Context, supplierId int) (int, error)
	GetAfterSaleOrderCount(ctx context.Context, supplierId int) (int, error)

	GetByPayOrderNumber(ctx context.Context, payOrderNumber string, reqBo *bo.OrderWithEdgeBo) (dos []*do.OrderDo, err error)
	Create(ctx context.Context, in *bo.OrderCreateBo) (int, error)

	UpdateAfterSaleStatus(ctx context.Context, reqBo *bo.UpdateOrderAfterSaleStatusBo) error

	CloseAfterSaleStatus(ctx context.Context, orderNumber string) error

	GetNotCompletedSkuNo(ctx context.Context, skuNos []string) ([]string, error)
	CountNotCompleted(ctx context.Context, skuNos []string) (int, error)

	FindByPayOrderNumber(ctx context.Context, payOrderNumber []string) ([]*do.OrderDo, error)

	GetNotFinishedOrderNo(ctx context.Context, orderNos []string) ([]string, error)

	CustomerRechargeList(ctx context.Context, in *bo.OrderListBo) (int, []*do.OrderDo, error)

	CustomerThirdList(ctx context.Context, in *bo.OrderListBo) (int, []*do.OrderDo, error)

	CustomerRealOrderList(ctx context.Context, in *bo.RealOrderListBo) (int, []*do.OrderDo, error)

	CustomerRealOrderMainList(ctx context.Context, in *bo.RealOrderMainListBo) ([]*do.OrderDo, error)

	CustomerExportRealOrderList(ctx context.Context, in *bo.ExportRealOrderListBo) ([]*do.OrderDo, error)

	CustomerThirdAll(ctx context.Context, in *bo.ExportOrderListBo) ([]*do.OrderDo, error)

	CustomerRechargeAll(ctx context.Context, in *bo.ExportOrderListBo) ([]*do.OrderDo, error)

	// FindHandingOne 查询是否有处理中的订单
	FindHandingOne(ctx context.Context, customerId, shopId int) (*do.OrderDo, error)

	FindAnyOne(ctx context.Context) (*do.OrderDo, error)

	HomeDay(ctx context.Context, startTime int, customerID int) (*bo.DayResult, error)

	HomeDayAverage(ctx context.Context, dayAverageQuery *bo.DayAverageQuery) (*bo.DayAverageResult, error)

	HomeSalesTrend(ctx context.Context, salesTrendQuery *bo.SalesTrendQuery) ([]*bo.SalesTrendResult, error)

	HomeSalesProportion(ctx context.Context, salesTrendQuery *bo.SalesProportionQuery) ([]*bo.SalesProportionResult, error)

	FindByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderDo, error)

	SubRealOrderByOrderNumber(ctx context.Context, orderNumber string) (*do.SubRealOrderDetailDo, error)

	MainRealOrderByPayOrderNumber(ctx context.Context, payOrderNumber string) (*do.MainRealOrderDetailDo, error)

	RealOrderGoodsList(ctx context.Context, in *bo.RealOrderGoodsListBo) ([]*do.RealOrderGoodsListDo, error)

	ShopHomeStatistics(ctx context.Context, customerID int, shopId int) (*bo.ShopHomeAmount, error)

	RealOrderNumByDeliverStatus(ctx context.Context, deliverStatus []valobj.OrderDeliverStatusObj) (int, error)
	ThirdOrderByDeliverStatus(ctx context.Context, deliverStatus []valobj.OrderDeliverStatusObj) (int, error)
	RechargeOrderByDeliverStatus(ctx context.Context, deliverStatus []valobj.OrderDeliverStatusObj) (int, error)

	RealOrderNum(ctx context.Context, createTimeStart int, createTimeEnd int) (int, error)
	ThirdOrderNum(ctx context.Context, createTimeStart int, createTimeEnd int) (int, error)
	RechargeOrderNum(ctx context.Context, createTimeStart int, createTimeEnd int) (int, error)

	RealSalesTrend(ctx context.Context, salesTrendQuery *bo.SalesTrendQuery) ([]*bo.SalesTrendResult, error)
	ThirdSalesTrend(ctx context.Context, salesTrendQuery *bo.SalesTrendQuery) ([]*bo.SalesTrendResult, error)
	RechargeSalesTrend(ctx context.Context, salesTrendQuery *bo.SalesTrendQuery) ([]*bo.SalesTrendResult, error)

	ShopHomeSalesProportion(ctx context.Context, shopSalesProportionQuery *bo.ShopSalesProportionQuery) ([]*bo.ShopSalesProportionResult, error)
	ThirdShopHomeSalesProportion(ctx context.Context, shopSalesProportionQuery *bo.ShopSalesProportionQuery) ([]*bo.ShopSalesProportionResult, error)

	ShopHomeSalesTop(ctx context.Context, salesTopQuery *bo.SalesTopQuery) ([]*bo.SalesTop, error)

	// FetchOrders 获取已支付订单
	FetchOrders(ctx context.Context, reqBo *bo.FetchPaidOrdersReqBo) []*do.OrderDo
	UpdateSubmitHytStatus(ctx context.Context, orderNumber string, status valobj.SubmitHytStatus) error
}
