package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/data/ent"
	"context"
)

type SysRoleRepo interface {
	SysRoleList(ctx context.Context, in *bo.SysRoleListBo) (int, []*do.SysRoleDo, error)

	FindById(ctx context.Context, id int) (*do.SysRoleDo, error)

	AddSysRole(ctx context.Context, in *ent.SysRole) (int, error)

	UpdateSysRole(ctx context.Context, in *ent.SysRole) (int, error)

	DeleteSysRole(ctx context.Context, in []int) (int, error)

	FindByRoleName(ctx context.Context, menuName string, roleId int) (*do.SysRoleDo, error)

	FindByRoleIds(ctx context.Context, ids []int) ([]*do.SysRoleDo, error)
}
