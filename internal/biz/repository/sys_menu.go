package repository

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data/ent"
	"context"
)

type SysMenuRepo interface {
	SysMenuList(ctx context.Context, sysMenuType valobj.SysMenuType) ([]*do.SysMenuDo, error)

	SysMenuListByIds(ctx context.Context, menuIds []int) ([]*do.SysMenuDo, error)

	SysMenuById(ctx context.Context, menuId int) (*do.SysMenuDo, error)

	AddSysMenu(ctx context.Context, in *ent.SysMenu) (int, error)

	UpdateSysMenu(ctx context.Context, in *ent.SysMenu) (int, error)

	DeleteSysMenu(ctx context.Context, in []int) (int, error)

	FindByApiPath(ctx context.Context, apiPath string, menuId int) (*do.SysMenuDo, error)

	SysMenuByApiPath(ctx context.Context, apiPath string) (*do.SysMenuDo, error)

	FindMigrateOne(ctx context.Context, menu *do.SysMenuDo) (*do.SysMenuDo, error)

	UpdateOfficialMenuId(ctx context.Context, id, officialMenuId int) (int, error)

	All(ctx context.Context) ([]*do.SysMenuDo, error)
}
