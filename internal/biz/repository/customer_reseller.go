//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type CustomerResellerRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.CustomerResellerDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.CustomerResellerDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.CustomerResellerDo) (*do.CustomerResellerDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.CustomerResellerDo) ([]*do.CustomerResellerDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.CustomerResellerDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.CustomerResellerDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.CustomerResellerSearchBo) (dos []*do.CustomerResellerDo, respPage *bo.RespPageBo)

	FindWithCustomerId(ctx context.Context) (*do.CustomerResellerDo, error)

	// GetByLastIdLimit 通过 lastId 获取多条数据
	GetByLastIdLimit(ctx context.Context, lastId int, limit int) ([]*do.CustomerResellerDo, error)
}
