//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type SysConfigRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SysConfigDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.SysConfigDo, error)

	FindByConfigKey(ctx context.Context, configKey string) (*do.SysConfigDo, error)

	FindByConfigKeys(ctx context.Context, configKeys ...string) ([]*do.SysConfigDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.SysConfigDo) (*do.SysConfigDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.SysConfigDo) ([]*do.SysConfigDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.SysConfigDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.SysConfigDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.SysConfigSearchBo) (dos []*do.SysConfigDo, respPage *bo.RespPageBo)

	Upsert(ctx context.Context, d *do.SysConfigDo) error

	GetByShopId(ctx context.Context, ShopId int, keys ...string) ([]*do.SysConfigDo, error)

	GetByShopIdByKeys(ctx context.Context, ShopId int, keys ...string) ([]*do.SysConfigDo, error)

	GetByShopIdsByKeys(ctx context.Context, ShopId []int, keys ...string) ([]*do.SysConfigDo, error)
}
