//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type OrderDeliverRepo interface {
	// Get .
	Get(ctx context.Context, id int) (*do.OrderDeliverDo, error)
	GetV2(ctx context.Context, id int, orderNo string) (*do.OrderDeliverDo, error)

	InvalidDeliver(ctx context.Context, id int) error

	// ExistDeliver 订单发货是否存在
	ExistDeliver(ctx context.Context, id int, orderNumber string) (bool, error)

	// FindWithGoodsByOrderNumber 通过 orderId 获取订单发货信息
	FindWithGoodsByOrderNumber(ctx context.Context, orderNumber ...string) ([]*do.OrderDeliverDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.OrderDeliverDo) (*do.OrderDeliverDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderDeliverSearchBo) (dos []*do.OrderDeliverDo, respPage *bo.RespPageBo)

	FindByOrderNumber(ctx context.Context, orderNumbers []string) ([]*do.RealOrderDeliverDo, error)

	FindOneByOrderNumber(ctx context.Context, orderNumber string) ([]*do.RealOrderDeliverDo, error)
	FindByLogisticsNo(ctx context.Context, logisticsNo string) (*do.OrderDeliverDo, error)

	FindByLogisticsNoExists(ctx context.Context, logisticsNo string) (bool, error)
	FindByLogisticsNoAndSubOrderNumExists(ctx context.Context, subOrderNum, logisticsNo string) (bool, error)

	FindOrderLogisticsNoExists(ctx context.Context, logisticsNo string, orderNumber string) (bool, error)
}
