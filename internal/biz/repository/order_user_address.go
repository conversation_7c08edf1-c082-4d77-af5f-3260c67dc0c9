//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type OrderUserAddressRepo interface {
	// FindByOrderNumber 根据订单号查询
	FindByOrderNumber(ctx context.Context, orderNumbers ...string) ([]*do.OrderUserAddressDo, error)
	FindByOrderIds(ctx context.Context, orderIds ...int) ([]*do.OrderUserAddressDo, error)
	Find(ctx context.Context, orderNumber string) (*do.OrderUserAddressDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.OrderUserAddressDo) (*do.OrderUserAddressDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderUserAddressSearchBo) (dos []*do.OrderUserAddressDo, respPage *bo.RespPageBo)
}
