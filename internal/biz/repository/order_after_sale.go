//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"context"
)

type OrderAfterSaleRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.OrderAfterSaleDo, error)
	GetBySaas(ctx context.Context, id int, orderNo string) (*do.OrderAfterSaleDo, error)

	// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
	GetWithEdges(ctx context.Context, id int, reqBo *bo.OrderAfterSaleEdgesBo) (*do.OrderAfterSaleDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.OrderAfterSaleDo) (*do.OrderAfterSaleDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.OrderAfterSaleDo) ([]*do.OrderAfterSaleDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.OrderAfterSaleDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.OrderAfterSaleDo) (int, error)

	// UpdateStatus 更新状态
	UpdateStatus(ctx context.Context, id int, oldStatus valobj.AfterSaleStatus, newStatus valobj.AfterSaleStatus, exchangeOrderNumbers ...string) error

	UpdatePlatformStatus(ctx context.Context, id int, oldStatus valobj.AfterSalePlatformStatus, newStatus valobj.AfterSalePlatformStatus, tpe valobj.AfterSaleType) error

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleSearchBo) (dos []*do.OrderAfterSaleDo, respPage *bo.RespPageBo)

	// UserClose 用户撤销售后申请
	UserClose(ctx context.Context, id int) (int, error)

	// HasHanding 判断订单是否还有处理中的售后申请
	HasHanding(ctx context.Context, orderId int) (bool, error)

	// FindLast 获取最后一条售后记录
	FindLast(ctx context.Context, orderId int) (*do.OrderAfterSaleDo, error)
	// GetLastByOrderNumber 取最新得订单售后信息
	GetLastByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderAfterSaleDo, error)
	// GetByOrderNumber 取所有售后信息
	GetByOrderNumber(ctx context.Context, orderNumber string) ([]*do.OrderAfterSaleDo, error)
	// GetByOriginalOrderNo 取所有已完结子售后信息
	GetByOriginalOrderNo(ctx context.Context, originalOrderNo string) ([]*do.OrderAfterSaleDo, error)
	// GetFinishAll 获取已完成的售后记录
	GetFinishAll(ctx context.Context, orderId int) ([]*do.OrderAfterSaleDo, error)
	// GetAll 获取全部的售后记录
	GetAll(ctx context.Context, orderId int) ([]*do.OrderAfterSaleDo, error)

	AfterSaleDayNum(ctx context.Context, startTime int) ([]*bo.AfterSaleDayResult, error)

	FindByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderAfterSaleDo, error)
	ListWaitSupplierApprove(ctx context.Context, supplierId int) ([]*do.OrderAfterSaleDo, error)
	GetAllWaitAuditCount(ctx context.Context) (int, error)

	// GetRefundOrdersByOrigin 根据原始订单号获取售后订单
	GetRefundOrdersByOrigin(ctx context.Context, status valobj.AfterSaleStatus, orderNumbers ...string) ([]*do.OrderAfterSaleDo, error)
}
