//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type OrderAfterSaleGoodsRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.OrderAfterSaleGoodsDo, error)

	// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
	GetWithEdges(ctx context.Context, id int, reqBo *bo.OrderAfterSaleGoodsEdgesBo) (*do.OrderAfterSaleGoodsDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleGoodsDo, error)

	FindByPids(ctx context.Context, pids ...int) ([]*do.OrderAfterSaleGoodsDo, error)

	// FindFinishGoods 获取已完成售后商品
	FindFinishGoods(ctx context.Context, orderNumber string) (dos []*do.OrderAfterSaleGoodsDo, err error)

	FindRefundGoods(ctx context.Context, orderNumber string) (dos []*do.OrderAfterSaleGoodsDo, err error)

	GetNotCompletedSkuNo(ctx context.Context, skuNos []string) ([]string, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.OrderAfterSaleGoodsDo) (*do.OrderAfterSaleGoodsDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.OrderAfterSaleGoodsDo) ([]*do.OrderAfterSaleGoodsDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleGoodsSearchBo) (dos []*do.OrderAfterSaleGoodsDo, respPage *bo.RespPageBo)

	// GetAfterSaleGoodsByPid 获取用户售后商品
	GetAfterSaleGoodsByPid(ctx context.Context, pid int) (dos []*do.OrderAfterSaleGoodsDo, err error)
	GetAllAfterSaleGoodsByPid(ctx context.Context, pid int) (dos []*do.OrderAfterSaleGoodsDo, err error)

	GetExchangeOriginalOrderNumberSku(ctx context.Context, originalOrderNumber string, skuNo string) (dos *do.OrderAfterSaleGoodsDo, err error)

	// GetAfterSaleGoodsByOrderId 获取用户售后商品
	GetAfterSaleGoodsByOrderId(ctx context.Context, orderId int, AfterSaleIds ...int) (dos []*do.OrderAfterSaleGoodsDo, err error)

	FindNotNeedDeliverGoods(ctx context.Context, orderNumber ...string) ([]*do.OrderAfterSaleGoodsDo, error)

	GetOriginalAllRefundGoods(ctx context.Context, originalOrderNumber string) ([]*do.OrderAfterSaleGoodsDo, error)
	DeleteByPid(ctx context.Context, pid int) error
}
