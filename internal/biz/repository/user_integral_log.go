//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"context"
)

type UserIntegralLogRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.UserIntegralLogDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.UserIntegralLogDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.UserIntegralLogDo) (*do.UserIntegralLogDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.UserIntegralLogDo) ([]*do.UserIntegralLogDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.UserIntegralLogDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.UserIntegralLogDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.UserIntegralLogSearchBo) (dos []*do.UserIntegralLogDo, respPage *bo.RespPageBo)

	UserIntegralCount(ctx context.Context, userIds []int, timeEnd int) ([]*do.UserIntegralNumResult, error)

	FindByExpireTaskStatus(ctx context.Context, expireTaskStatus valobj.UserIntegralLogExpireTaskStatus, userIds []int) ([]*do.UserIntegralLogDo, error)

	UpdateByUserId(ctx context.Context, expireTaskStatus valobj.UserIntegralLogExpireTaskStatus, ids []int, userId int) (int, error)
}
