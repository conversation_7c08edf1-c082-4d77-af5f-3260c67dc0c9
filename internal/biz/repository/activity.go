package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
)

type ActivityRepo interface {
	// Get 通过 id 获取一条数据，出错则 panic
	Get(ctx context.Context, id int) *do.ActivityDo

	// Find 通过多个 id 获取多条数据，出错则 panic
	Find(ctx context.Context, ids ...int) []*do.ActivityDo

	// Create 创建数据，出错则 panic
	Create(ctx context.Context, createData *do.ActivityDo) *do.ActivityDo

	// Update 更新数据，出错则 panic
	Update(ctx context.Context, updateData *do.ActivityDo) int

	// Delete 删除数据，出错则 panic
	Delete(ctx context.Context, ids ...int)

	// SearchList 搜索列表，出错则 panic
	SearchList(ctx context.Context, reqBo *bo.ActivityListReq) (dos []*do.ActivityDo, respPage *bo.RespPageBo)

	// UpdateStatus 更新状态
	UpdateStatus(ctx context.Context, reqBo *bo.ActivityUpdateStatusReq)

	FindWithCommonById(ctx context.Context, id int) *do.ActivityDo

	NormalCount(ctx context.Context, id []int) []*do.ActivityDo

	NameExists(ctx context.Context, name string, id ...int) bool
}
