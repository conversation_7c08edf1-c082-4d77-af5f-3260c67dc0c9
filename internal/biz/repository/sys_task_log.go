//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type SysTaskLogRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SysTaskLogDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.SysTaskLogDo, error)

	// FindByTaskId 通过sysTaskID获取多条数据
	FindByTaskId(ctx context.Context, sysTaskID int) ([]*do.SysTaskLogDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.SysTaskLogDo) (*do.SysTaskLogDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.SysTaskLogDo) ([]*do.SysTaskLogDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.SysTaskLogDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.SysTaskLogDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.SysTaskLogSearchBo) (dos []*do.SysTaskLogDo, respPage *bo.RespPageBo)
}
