//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type CardBatchGiftRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.CardBatchGiftDo, error)

	// Find 通过多个 id 获取多条数据
	Find(ctx context.Context, ids ...int) ([]*do.CardBatchGiftDo, error)

	// Create 创建数据
	Create(ctx context.Context, d *do.CardBatchGiftDo) (*do.CardBatchGiftDo, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, dos []*do.CardBatchGiftDo) ([]*do.CardBatchGiftDo, error)

	// Update 更新数据，如果没有更新到数据，返回 0, nil
	Update(ctx context.Context, d *do.CardBatchGiftDo) (int, error)

	// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
	UpdateV2(ctx context.Context, d *do.CardBatchGiftDo) (int, error)

	// Delete 删除数据
	Delete(ctx context.Context, ids ...int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.CardBatchGiftSearchBo) (dos []*do.CardBatchGiftDo, respPage *bo.RespPageBo)

	// UseAble 查询用户可用储值卡
	UseAble(ctx context.Context, userId int, no ...string) ([]*do.CardBatchGiftDo, error)

	CancelByID(ctx context.Context, cardBatchGiftIds []int) (int, error)

	Num(ctx context.Context, in []int) ([]*do.BatchGiftNumResult, error)

	FindByUserId(ctx context.Context, userId int) ([]*do.CardBatchGiftDo, error)

	FindByCardGiftNumber(ctx context.Context, cardGiftNumber string) (*do.CardBatchGiftDo, error)

	Exchange(ctx context.Context, userId int, userName string, cardGiftNumber string) (int, error)

	CancelByCardBatchID(ctx context.Context, cardBatchIds []int) (int, error)

	GetByCardGiftNumber(ctx context.Context, cardGiftNumber ...string) ([]*do.CardBatchGiftDo, error)

	BalanceChange(ctx context.Context, cardGiftNumber string, amount float64, oldBalance float64) (int, error)

	FindByCardBatchNumber(ctx context.Context, cardBatchNumber string) ([]*do.CardBatchGiftDo, error)

	FindUseAbleOne(ctx context.Context, userId int) (*do.CardBatchGiftDo, error)
}
