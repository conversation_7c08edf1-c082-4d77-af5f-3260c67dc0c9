//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
)

type SupplierGoodsSkuRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.SupplierGoodsSkuDo, error)

	GetNum(ctx context.Context, supplierId int, status []valobj.SupplierGoodsStatusObj) (int, error)

	// FindByIds 获取列表
	FindByIds(ctx context.Context, ids ...int) ([]*do.SupplierGoodsSkuDo, error)

	// FindWithGoods 获取商品sku信息
	FindWithGoods(ctx context.Context, ids ...int) ([]*do.SupplierGoodsSkuDo, error)

	FindWithGoodsBySkuNo(ctx context.Context, skuNos ...string) ([]*do.SupplierGoodsSkuDo, error)

	// IncrStock 增加/减少库存
	IncrStock(ctx context.Context, id int, num int) error

	// Shelf 上/下架
	Shelf(ctx context.Context, goodsId int, status valobj.SupplierGoodsStatusObj) error

	// ListGoodsSku 获取商品sku列表
	ListGoodsSku(ctx context.Context, id int) ([]*do.SupplierGoodsSkuDo, error)

	// Create 创建数据
	Create(ctx context.Context, goodsId int, d *do.SupplierGoodsSkuDo, status valobj.SupplierGoodsStatusObj) (*do.SupplierGoodsSkuDo, error)

	GetBySkuInfo(ctx context.Context, goodsId int, specStr string) (*do.SupplierGoodsSkuDo, error)

	Update(ctx context.Context, id int, d *do.SupplierGoodsSkuDo, status valobj.SupplierGoodsStatusObj) error

	DeleteBySkuInfos(ctx context.Context, goodsId int, specStr []string) ([]int, error)

	UpdateStatusBySkuInfos(ctx context.Context, goodsId int, specStr []string, status valobj.SupplierGoodsStatusObj) ([]int, error)

	// CreateBulk 批量创建数据
	CreateBulk(ctx context.Context, goodsId int, dos []*do.SupplierGoodsSkuDo, status valobj.SupplierGoodsStatusObj) ([]*do.SupplierGoodsSkuDo, error)

	// DeleteByGoodsId 根据商品id删除
	DeleteByGoodsId(ctx context.Context, goodsId int) (int, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.SupplierGoodsSkuSearchBo) (dos []*do.SupplierGoodsSkuDo, respPage *bo.RespPageBo)

	ProductAuthorizeSkuSearch(ctx context.Context, reqBo *bo.ProductAuthorizeSkuSearchBo) (dos []*do.SupplierGoodsSkuDo, respPage *bo.RespPageBo)

	FindById(ctx context.Context, id int) (*do.SupplierGoodsSkuDo, error)

	AddSalesVolume(ctx context.Context, skuNo string, num int) (int, error)

	GetStockAlarmNum(ctx context.Context, supplierId int, num int) (int, error)

	GetOffShelfCount(ctx context.Context, supplierId int) (int, error)
	GetOnSaleCount(ctx context.Context, supplierId int) (int, error)
	GetAuditCount(ctx context.Context, supplierId int, status valobj.SupplierGoodsDraftStatusObj) (int, error)
	GetAllAuditCount(ctx context.Context, supplierId int) (int, error)

	FindBySkuNo(ctx context.Context, skuNo string) (*do.SupplierGoodsSkuDo, error)

	FindWithDeleteBySkuNo(ctx context.Context, skuNo string) (*do.SupplierGoodsSkuDo, error)

	GetWithGoodsBySkuNo(ctx context.Context, skuNo string) (*do.SupplierGoodsSkuDo, error)

	GetWithGoodsBySkuNos(ctx context.Context, skuNo []string) ([]*do.SupplierGoodsSkuDo, error)

	GetBySkuNo(ctx context.Context, skuNo []string) ([]*do.SupplierGoodsSkuDo, error)

	GetByProductIds(ctx context.Context, productIds []string, customerID int) ([]*do.SupplierGoodsSkuDo, error)

	GetMapBySkuNo(ctx context.Context, skuNo []string) (map[string]*do.SupplierGoodsSkuDo, error)

	IncrStockBySkuNo(ctx context.Context, skuNo string, num int) error

	// DecrStockBySkuNo 只能减少库存,num传入正负数皆是
	DecrStockBySkuNo(ctx context.Context, skuNo string, num int) error

	SearchAll(ctx context.Context, reqBo *bo.ProductAuthorizeSearchBo) (dos []*do.SupplierGoodsSkuDo)

	OfficialList(ctx context.Context, in *bo.SupplierGoodsSkuBo) (int, []*do.SupplierGoodsSkuDo, error)

	CustomerList(ctx context.Context, in *bo.SupplierGoodsSkuBo) (int, []*do.SupplierGoodsSkuDo, error)

	ShopList(ctx context.Context, in *bo.SupplierGoodsSkuBo) (int, []*do.SupplierGoodsSkuDo, error)

	All(ctx context.Context) ([]*do.SupplierGoodsSkuDo, error)

	GetBySkuNoAndCustomerID(ctx context.Context, skuNo []string, customerID int) ([]*do.SupplierGoodsSkuDo, error)
	FindByHytGoodsNum(ctx context.Context, productId string) ([]*do.SupplierGoodsSkuDo, error)
	// UpdateSupplierChannelPrice 更新供应商价格
	UpdateSupplierChannelPrice(ctx context.Context, productId string, supplierPrice, channelPrice float64) (int, error)

	GetWithGoodsByGoodsId(ctx context.Context, goodsId int) ([]*do.SupplierGoodsSkuDo, error)

	// GetWithGoodsByProductId 根据商品id获取商品sku信息
	GetWithGoodsByProductId(ctx context.Context, productId ...string) ([]*do.SupplierGoodsSkuDo, error)
	FindByHytGoodsNums(ctx context.Context, productId []string) ([]*do.SupplierGoodsSkuDo, error)
	FindHytWithSpu(ctx context.Context, productId string) (*do.SupplierGoodsSkuDo, error)
}
