//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/do"
	"context"
)

type OrderAfterSaleDealRepo interface {
	// Create 创建数据
	Create(ctx context.Context, d *do.OrderAfterSaleDealDo) (*do.OrderAfterSaleDealDo, error)

	// HasDealing 订单售后处理中
	HasDealing(ctx context.Context, orderNumber string) (bool, error)

	// GetByExchangeOrderNo 通过换货单号获取售后单信息
	GetByExchangeOrderNo(ctx context.Context, exchangeOrderNo string) (*do.OrderAfterSaleDealDo, error)

	// FindExchangeInfoByOrderNos 获取订单下面的换货售后信息
	FindExchangeInfoByOrderNos(ctx context.Context, orderNos ...string) ([]*do.OrderAfterSaleDealDo, error)

	// FindOriginalOrderApplyItems 通过原单号获取售后单信息
	FindOriginalOrderApplyItems(ctx context.Context, orderNumber ...string) ([]*do.OrderAfterSaleDealDo, error)

	// GetAllApplyTimes 获取售后单申请次数
	GetAllApplyTimes(ctx context.Context, orderNumber string) (int, error)

	DealComplete(ctx context.Context, pid int, exchangeOrderNumbers ...string) error
	GetRelatedOrderNos(ctx context.Context, orderNumber string) ([]string, error)

	Invalid(ctx context.Context, pid int, exchangeOrderNumbers ...string) error
	DeleteByPid(ctx context.Context, pid int) error
}
