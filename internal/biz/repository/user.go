//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repository

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"context"
)

type UserRepo interface {
	// Get 通过 id 获取一条数据
	Get(ctx context.Context, id int) (*do.UserDo, error)

	// SearchList 搜索列表
	SearchList(ctx context.Context, reqBo *bo.UserSearchBo) (dos []*do.UserDo, respPage *bo.RespPageBo)

	// ShopCount 统计商城用户数量
	ShopCount(ctx context.Context, shopId ...int) ([]*do.ShopUserCountDo, error)

	// UpsertByUnionLogin 根据第三方登录信息更新用户信息
	UpsertByUnionLogin(ctx context.Context, req *bo.RegisterUserUnionLoginBo) error

	// GetByUnionId 根据第三方登录信息获取用户信息
	GetByUnionId(ctx context.Context, unionUserId string) *do.UserDo
}
