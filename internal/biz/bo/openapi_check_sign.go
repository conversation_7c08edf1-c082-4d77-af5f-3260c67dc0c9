package bo

type OpenApiCheckSignBo struct {
	AlgorithmType OpenApiCheckSignAlgorithmTypeBo
	Path          string
	Body          string
	MchId         string // 商城标识符号
	Signature     string
	Timestamp     int
}

type OpenApiCheckSignAlgorithmTypeBo string

const (
	OpenApiCheckSignAlgorithmTypeBoMd5 OpenApiCheckSignAlgorithmTypeBo = "MD5"
)

// IsMD5 是否是MD5算法
func (t OpenApiCheckSignAlgorithmTypeBo) IsMD5() bool {
	return t == OpenApiCheckSignAlgorithmTypeBoMd5
}
