package bo

type NotifyThirdOrderEventBo struct {
	EventType     string  `json:"eventType"`
	EventTime     string  `json:"eventTime"`
	OrderNo       string  `json:"orderNo"`
	PayOrderNo    string  `json:"payOrderNo"`
	RefundOrderNo string  `json:"refundOrderNo"`
	RefundAmount  float32 `json:"refundAmount"`
}

// SetFinished 设置事件类型
func (n *NotifyThirdOrderEventBo) SetFinished() {
	n.EventType = "finished"
}

// SetRefund 设置退款事件类型
func (n *NotifyThirdOrderEventBo) SetRefund(refundOrderNO string, refundAmount float32) {
	n.EventType = "refund"
	n.RefundOrderNo = refundOrderNO
	n.RefundAmount = refundAmount
}
