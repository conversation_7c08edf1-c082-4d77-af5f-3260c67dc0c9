package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/constants"
	"cardMall/internal/pkg/helper"
)

type SaveCustomerSettingBo struct {
	Icon       string
	Logo       string
	BgImage    string
	Name       string
	NameColor  string
	Prompt     string
	CustomerId int
}

// Validate 校验
func (s SaveCustomerSettingBo) Validate() error {
	if s.CustomerId <= 0 {
		return apierr.ErrorParam("请选择客户")
	}
	if s.Name == "" {
		return apierr.ErrorParam("请输入网站名称")
	}
	if helper.Utf8StrLength(s.Name) > constants.CustomerSiteNameMaxLen {
		return apierr.ErrorParam("网站名称不能超过%d个字符", constants.CustomerSiteNameMaxLen)
	}
	if s.Prompt == "" {
		return apierr.ErrorParam("请输入网站欢迎语")
	}
	if helper.Utf8StrLength(s.Prompt) > constants.CustomerPromptMaxLen {
		return apierr.ErrorParam("网站欢迎语不能超过%d个字符", constants.CustomerPromptMaxLen)
	}
	return nil
}

type SaveShopSettingBo struct {
	Icon       string
	Logo       string
	BgImage    string
	Name       string
	NameColor  string
	Prompt     string
	CustomerId int
	ShopId     int
}

// Validate 校验
func (s SaveShopSettingBo) Validate() error {
	if s.CustomerId <= 0 {
		return apierr.ErrorParam("请选择客户")
	}
	if s.ShopId <= 0 {
		return apierr.ErrorParam("请选择商城")
	}
	if s.Name == "" {
		return apierr.ErrorParam("请输入网站名称")
	}
	if helper.Utf8StrLength(s.Name) > constants.CustomerSiteNameMaxLen {
		return apierr.ErrorParam("网站名称不能超过%d个字符", constants.CustomerSiteNameMaxLen)
	}
	if s.Prompt == "" {
		return apierr.ErrorParam("请输入网站欢迎语")
	}
	if helper.Utf8StrLength(s.Prompt) > constants.CustomerPromptMaxLen {
		return apierr.ErrorParam("网站欢迎语不能超过%d个字符", constants.CustomerPromptMaxLen)
	}
	return nil
}
