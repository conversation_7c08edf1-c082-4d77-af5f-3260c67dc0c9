//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package bo

import (
	"encoding/json"
	"errors"
	"time"
	"unicode/utf8"

	"cardMall/internal/biz/valobj"
)

type OrderSearchBo struct {
	Id             int                         `json:"id,omitempty"`          // 订单id
	OrderNumber    string                      `json:"orderNumber,omitempty"` // 订单编号
	SkuId          int                         `json:"skuId,omitempty"`       // SKU id
	GoodsId        int                         `json:"goodsId,omitempty"`     // 商品id
	GoodsName      string                      `json:"goodsName,omitempty"`   // 商品名称
	ClientStatus   valobj.OrderClientStatusObj `json:"clientStatus,omitempty"`
	OrderDateStart int                         `json:"orderDateStart,omitempty"` // 下单时间
	OrderDateEnd   int                         `json:"orderDateEnd,omitempty"`   // 下单时间
	IsPayOrder     bool                        `json:"isPayOrder,omitempty"`
	PayOrder       bool                        `json:"payOrder,omitempty"`
	UserId         int                         `json:"userId,omitempty"`
	SupplierId     int                         `json:"supplierId,omitempty"`
	SkuNo          string                      `json:"skuNo,omitempty"`
	OnlyEntity     bool                        `json:"onlyEntity,omitempty"`
	OnlyTimeout    bool                        `json:"onlyTimeout,omitempty"`

	// 关联
	Edges    *OrderWithEdgeBo
	Page     *ReqPageBo
	SortData []*CommonSortBo
}

type OrderWithEdgeBo struct {
	WithGoods       bool // 商品
	WithUserAddress bool // 地址
	WithOperatorLog bool // 操作记录
	WithSite        bool
	WithSupplier    bool
	WithPayOrder    bool
	WithCardGift    bool
	WithUser        bool
}

func (o *OrderSearchBo) JoinGoods() bool {
	if o.SkuId > 0 || o.GoodsId > 0 || o.GoodsName != "" || o.SkuNo != "" {
		return true
	}
	return false
}

type OrderListBo struct {
	OrderNumber string

	CreateTimeStart int
	CreateTimeEnd   int
	ShopId          int
	ReqPageBo
	PayChannelType valobj.PayChannel
}

func (o *OrderListBo) IsPayOrderNumber() bool {
	if o.OrderNumber == "" {
		return false
	}
	return string(o.OrderNumber[0]) == "P"
}

type ExportOrderListBo struct {
	//订单号
	OrderNumber string
	//下单时间-起始
	CreateTimeStart int
	//下单时间-结束
	CreateTimeEnd int
	ShopId        int
}

func (o *ExportOrderListBo) IsPayOrderNumber() bool {
	if o.OrderNumber == "" {
		return false
	}
	return string(o.OrderNumber[0]) == "P"
}
func (m *ExportOrderListBo) String() string {
	if data, err := json.Marshal(m); err == nil {
		return string(data)
	}
	return "{}"
}
func (m *ExportOrderListBo) Validate() error {
	if m == nil {
		return nil
	}

	if l := utf8.RuneCountInString(m.OrderNumber); l < 0 || l > 50 {
		err := errors.New("订单号需小于50个字符")
		return err
	}

	return nil
}

type RealOrderListBo struct {
	//订单号
	OrderNumber string
	//下单时间-起始
	CreateTimeStart int64
	//下单时间-结束
	CreateTimeEnd int64
	ShopId        int
	OnlyTimeout   bool
	ReqPageBo
	PayChannel valobj.PayChannel
}

func (m *RealOrderListBo) Validate() error {
	if m == nil {
		return nil
	}

	if l := utf8.RuneCountInString(m.OrderNumber); l < 0 || l > 50 {
		err := errors.New("订单号需小于50个字符")
		return err
	}

	if val := m.Page; val < 1 {
		err := errors.New("页码需要大于1")
		return err
	}
	if val := m.PageSize; val < 1 {
		err := errors.New("页数需要大于1")
		return err
	}
	return nil
}

type RealOrderMainListBo struct {
	PayOrderNumbers []string
}
type RealOrderGoodsListBo struct {
	OrderNumbers []string
}
type ExportRealOrderListBo struct {
	//订单号
	OrderNumber string
	//下单时间-起始
	CreateTimeStart int64
	//下单时间-结束
	CreateTimeEnd int64
	ShopId        int
}

func (m *ExportRealOrderListBo) String() string {
	if data, err := json.Marshal(m); err == nil {
		return string(data)
	}
	return "{}"
}
func (m *ExportRealOrderListBo) Validate() error {
	if m == nil {
		return nil
	}

	if l := utf8.RuneCountInString(m.OrderNumber); l < 0 || l > 50 {
		err := errors.New("订单号需小于50个字符")
		return err
	}

	return nil
}

type FetchPaidOrdersReqBo struct {
	UpdateTimeBegin *time.Time
	UpdateTimeEnd   *time.Time
	ScrollId        int
	PageSize        int
	Edges           *OrderWithEdgeBo
	NotStatus       []valobj.OrderStatusObj
	UserId          int
}
