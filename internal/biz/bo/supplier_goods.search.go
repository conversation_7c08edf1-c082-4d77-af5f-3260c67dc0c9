//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package bo

import (
	"cardMall/internal/biz/valobj"
)

type SupplierGoodsSearchBo struct {
	Name            string                      // 商品名称
	Type            valobj.SupplierGoodsTypeObj // 商品类型 1-实物 2-虚拟
	CategoryId      int                         // 商品分类ID
	BrandId         int                         // 品牌ID
	Online          *bool                       // 是否上架
	Id              int                         // id 商品编号
	SupplierBarcode string                      // supplier_barcode 供应商商品编码【第三方编码】
	Barcode         string                      // barcode 商品条码
	Page            *ReqPageBo                  // 分页
	SupplierId      int                         // 供应商ID
	SkuNo           string                      // 商品编号
	Tab             valobj.GoodsTabList         // 1-全部商品 2-售卖中 3-已下架 4-商品审核 5-商品草稿

	WithSku       bool
	WithTransport bool
	WithCategory  bool
	WithBrand     bool
	SortData      []*CommonSortBo

	*AdminLoginInfoBo
	SkuNos []string
}

// Validate 验证参数
func (s *SupplierGoodsSearchBo) Validate() error {
	return nil
}
