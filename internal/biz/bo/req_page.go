package bo

// ReqPageBo 分页请求实体
type ReqPageBo struct {
	PageSize int `json:"PageSize"` //分页大小
	Page     int `json:"Page"`     //页码，从第1页开始
}

// GetOffset 获取便宜量
func (r *ReqPageBo) GetOffset() int {
	if r == nil {
		return 0
	}
	offset := (r.GetPage() - 1) * r.GetPageSize()
	if offset < 0 {
		return 0
	}
	return offset
}

// GetPageSize 获取分页
func (r *ReqPageBo) GetPageSize() int {
	if r == nil || r.PageSize <= 0 {
		return 20
	}
	return r.PageSize
}

// GetPage 获取页码
func (r *ReqPageBo) GetPage() int {
	if r == nil || r.Page <= 0 {
		return 1
	}
	return r.Page
}
