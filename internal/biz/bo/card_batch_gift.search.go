//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package bo

import (
	"cardMall/internal/biz/valobj"
	"errors"
	"unicode/utf8"
)

type CardBatchGiftSearchBo struct {

	//创建时间-起始
	CreateTimeStart int
	//创建时间-结束
	CreateTimeEnd int
	//激活时间-起始
	ActivateTimeStart int
	//激活时间-结束
	ActivateTimeEnd int
	//到期时间-起始
	UseExpireStart int
	//到期时间-结束
	UseExpireEnd int
	//激活状态1=未激活，2=已激活
	Status valobj.CardBatchStatus
	//卡券批次号
	CardBatchNumber string
	//券码/兑换码
	CardGiftNumber string
	//兑换状态 卡券状态 1 待使用 2 已使用 3 已作废
	GiftStatus valobj.CardBatchGiftStatus
	BindStatus valobj.CardBatchCouponBindStatus

	Edges *CardBatchGiftEdgesBo
	Page  *ReqPageBo
}
type CardBatchGiftEdgesBo struct {
	WithCardBatch           bool
	WithCardGiftOperatorLog bool
}

func (m *CardBatchGiftSearchBo) Validate() error {
	if m == nil {
		return nil
	}
	if l := utf8.RuneCountInString(m.CardBatchNumber); l < 0 || l > 50 {
		err := errors.New("卡券批次号需小于50个字符")
		return err
	}
	if l := utf8.RuneCountInString(m.CardGiftNumber); l < 0 || l > 50 {
		err := errors.New("兑换码需小于50个字符")
		return err
	}
	return nil
}
