package bo

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"errors"
	"golang.org/x/crypto/bcrypt"
	"regexp"
)

type AdminListBo struct {
	SupplierId int
	Name       string
	Account    string
	Mobile     string
	RoleId     int
	Status     valobj.AdminStatusObj
	Type       valobj.AdminTypeObj
	ReqPageBo
}

// NewAdminListBo 创建一个管理员列表Bo
func NewAdminListBo(s *SupplierAccountListBo) *AdminListBo {
	return &AdminListBo{
		SupplierId: s.SupplierId,
		Name:       "",
		Account:    s.Account,
		Status:     valobj.AdminStatusObj(s.Status),
		Type:       s.Type,
		ReqPageBo:  ReqPageBo{PageSize: s.PageSize, Page: s.Page},
		Mobile:     s.PhoneNumber,
	}
}

type AdminAddBo struct {
	Name     string
	Account  string
	Status   valobj.AdminStatusObj
	Password string
	RoleId   int
	Mobile   string
	IsRoot   valobj.AdminIsRootObj
	Type     valobj.AdminTypeObj
}

type AdminUpdateBo struct {
	Id       int
	Account  string
	Name     string
	Status   valobj.AdminStatusObj
	Password string
	RoleId   int
	Mobile   string
}

type AdminUpdateStatusBo struct {
	Id     int
	Status valobj.AdminStatusObj
}

func (a *AdminUpdateBo) GetPwdHash() (string, error) {
	if a.Password == "" {
		return "", nil
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(a.Password), bcrypt.DefaultCost)
	return string(hashedPassword), err
}

func checkAccount(account string) bool {
	re := regexp.MustCompile(`^[a-zA-Z0-9]{6,20}$`)
	return re.MatchString(account)
}

func (a *AdminUpdateBo) Validate() error {
	if a.Id <= 0 {
		return errors.New("id不能为空")
	}
	if a.Account != "" {
		if !checkAccount(a.Account) {
			return errors.New("账号只能6-20位英文或数字")
		}
	}
	if a.Mobile != "" {
		if !helper.IsAccountPhone(a.Mobile) {
			return errors.New("请输入正确手机号")
		}
	}
	return nil
}

func (a *AdminAddBo) GetPwdHash() (string, error) {
	if a.Password == "" {
		return "", nil
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(a.Password), bcrypt.DefaultCost)
	return string(hashedPassword), err
}

func (a *AdminAddBo) Validate() error {
	if a.Account == "" {
		return errors.New("账号不能为空")
	}
	if !checkAccount(a.Account) {
		return errors.New("账号只能6-20位英文或数字")
	}
	if a.Name == "" {
		return errors.New("名称不能为空")
	}
	if a.Password == "" {
		return errors.New("密码不能为空")
	}
	if a.Mobile != "" {
		if !helper.IsAccountPhone(a.Mobile) {
			return errors.New("请输入正确手机号")
		}
	}
	return nil
}

func (a *AdminAddBo) RootCreateValidate() error {
	if !a.IsRoot.IsYes() {
		return errors.New("非超级管理员，不允许创建")
	}
	if a.Account == "" {
		return errors.New("账号不能为空")
	}
	if len([]rune(a.Account)) > 20 || len([]rune(a.Account)) < 6 {
		return errors.New("账号长度需在6-20之间")
	}
	if a.Name == "" {
		return errors.New("名称不能为空")
	}
	if a.Mobile == "" {
		return errors.New("手机号不能为空")
	}
	if !helper.IsAccountPhone(a.Mobile) {
		return errors.New("请输入正确手机号")
	}
	return nil
}

func (a *AdminUpdateStatusBo) Validate() error {
	if !a.Status.IsValid() {
		return errors.New("参数错误")
	}
	if a.Id <= 0 {
		return errors.New("参数错误")
	}
	return nil
}

type AdminCheckSmsBo struct {
	VerifyCode string
	VerifyId   string
}
