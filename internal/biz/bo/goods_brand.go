package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
)

type GoodsBrandAddBo struct {
	Name       string
	Sort       int
	Status     int
	Label      string
	Logo       string
	Recommend  *valobj.GoodsBrandRecommendObj
	Remark     string
	HytName    string
	CustomerId int
	ShopId     int
}

type GoodsBrandQueryBo struct {
	GoodsCategoryId int
	Name            string
	Status          *int
	Recommend       *int
	ReqPageBo
}

type GoodsBrandUpdateBo struct {
	Id        int
	Name      string
	Sort      *int
	Status    *int
	Label     string
	Logo      string
	Remark    string
	Recommend *valobj.GoodsBrandRecommendObj
}

func (g *GoodsBrandAddBo) Validate() error {
	if g.Name == "" {
		return apierr.ErrorParam("品牌名称不能为空")
	}
	if len([]rune(g.Name)) > constants.GoodsBrandNameLenMax || len([]rune(g.Name)) < constants.GoodsBrandNameLenMin {
		return apierr.ErrorParam("品牌名称长度需在%v-%v个字符之间", constants.GoodsBrandNameLenMin, constants.GoodsBrandNameLenMax)
	}
	if g.Status != 0 && g.Status != 1 {
		return apierr.ErrorParam("品牌状态不正确")
	}
	if g.Logo == "" {
		return apierr.ErrorParam("品牌logo不能为空")
	}
	if len([]rune(g.Remark)) > constants.GoodsBrandRemarkMaxLen {
		return apierr.ErrorParam("品牌备注不能超过%d个字符", constants.GoodsBrandRemarkMaxLen)
	}
	if len([]rune(g.Label)) > constants.GoodsBrandLabelLenMax {
		return apierr.ErrorParam("品牌标签不能超过%v个字符", constants.GoodsBrandLabelLenMax)
	}
	return nil
}

func (g *GoodsBrandUpdateBo) Validate() error {
	if g.Id == 0 {
		return apierr.ErrorParam("品牌id不能为空")
	}
	if g.Name == "" {
		return apierr.ErrorParam("品牌名称不能为空")
	}
	if len([]rune(g.Name)) > constants.GoodsBrandNameLenMax || len([]rune(g.Name)) < constants.GoodsBrandNameLenMin {
		return apierr.ErrorParam("品牌名称长度需在%v-%v个字符之间", constants.GoodsBrandNameLenMin, constants.GoodsBrandNameLenMax)
	}
	if g.Status != nil {
		if *g.Status != 0 && *g.Status != 1 {
			return apierr.ErrorParam("品牌状态不正确")
		}
	}
	if g.Logo == "" {
		return apierr.ErrorParam("品牌logo不能为空")
	}
	if len([]rune(g.Remark)) > constants.GoodsBrandRemarkMaxLen {
		return apierr.ErrorParam("品牌备注不能超过%d个字符", constants.GoodsBrandRemarkMaxLen)
	}
	if len([]rune(g.Label)) > constants.GoodsBrandLabelLenMax {
		return apierr.ErrorParam("品牌标签不能超过%v个字符", constants.GoodsBrandLabelLenMax)
	}
	return nil
}

func (g *GoodsBrandUpdateBo) ShopExtraChange() bool {
	if *g.Sort > 0 {
		return true
	}
	if g.Status != nil {
		status := valobj.GoodsBrandStatusObj(*g.Status)
		if status != status.GetDefault() {
			return true
		}
	}
	if g.Recommend != nil {
		recommend := *g.Recommend
		if recommend != recommend.GetDefault() {
			return true
		}
	}
	return false
}
