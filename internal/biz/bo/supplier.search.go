//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package bo

import (
	"cardMall/internal/biz/valobj"
	"errors"
)

type SupplierSearchBo struct {
	Page *ReqPageBo
}
type SupplierQueryBo struct {
	Name   string
	Status valobj.SupplierStatusObj
	Pid    int
	*ReqPageBo
}

type SupplierAddBo struct {
	Name   string
	Status valobj.SupplierStatusObj
	Pid    int
	ShopId int
}

type SupplierUpdateBo struct {
	Id     int
	Name   string
	Status valobj.SupplierStatusObj
}

func (s *SupplierUpdateBo) Validate() error {
	if s.Id <= 0 {
		return errors.New("id不能为空")
	}
	if s.Name == "" && !s.Status.IsValid() {
		return errors.New("名称和状态不能同时为空")
	}
	if len([]rune(s.Name)) > 20 || len([]rune(s.Name)) < 2 {
		return errors.New("名称长度必须在2-20之间")
	}
	return nil
}

func (s *SupplierAddBo) Validate() error {
	if s.Name == "" {
		return errors.New("名称不能为空")
	}
	if len([]rune(s.Name)) > 20 || len([]rune(s.Name)) < 2 {
		return errors.New("名称长度必须在2-20之间")
	}
	if !s.Status.IsValid() {
		return errors.New("状态不合法")
	}
	return nil
}
