package bo

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"errors"
	"github.com/shopspring/decimal"
)

type IntegralConfigAddBo struct {
	ExchangeRate       int
	Name               string
	DeductionType      valobj.IntegralConfigDeductionType
	DeductionRate      float64
	ExpireType         valobj.IntegralConfigExpireTypeObj
	ExpireYear         int
	ShopId             int
	IntegralExchange   valobj.IntegralConfigExchangeObj
	IntegralCash       valobj.IntegralConfigCashObj
	IntegralShopStatus valobj.IntegralShopStatusObj
	CustomerId         int
}

type IntegralConfigUpdateBo struct {
	Id                 int
	ExchangeRate       int
	Name               string
	DeductionType      valobj.IntegralConfigDeductionType
	DeductionRate      float64
	ExpireType         valobj.IntegralConfigExpireTypeObj
	ExpireYear         int
	IntegralExchange   valobj.IntegralConfigExchangeObj
	IntegralCash       valobj.IntegralConfigCashObj
	IntegralShopStatus valobj.IntegralShopStatusObj
}

func (i *IntegralConfigAddBo) Validate() error {
	if i.ExchangeRate <= 0 {
		return errors.New("积分抵扣比例必须大于0")
	}
	if len([]rune(i.Name)) > 20 || len([]rune(i.Name)) < 1 {
		return errors.New("积分名称长度必须在1-20之间")
	}
	if !i.DeductionType.IsValid() {
		return errors.New("积分抵扣类型不合法")
	}
	if i.DeductionRate < 0 {
		return errors.New("积分最大抵扣比例不能为负数")
	}
	if !i.ExpireType.IsValid() {
		return errors.New("积分过期类型不合法")
	}
	if i.ExpireType == valobj.IntegralConfigExpireTypeNo {
		i.ExpireYear = 0
	} else {
		if i.ExpireYear < 0 {
			return errors.New("积分过期类型不为永不过期时，积分过期年限不能小于0")
		}
		if i.ExpireType.IsYearTurn() && i.ExpireYear == 0 {
			return errors.New("积分过期年限必须大于0")
		}
	}
	if i.ShopId <= 0 {
		return errors.New("缺少参数")
	}
	if i.IntegralCash.IsNone() || !i.IntegralCash.Exists() {
		return errors.New("请选择是否开启积分+钱购")
	}
	if i.IntegralExchange.IsNone() || !i.IntegralExchange.Exists() {
		return errors.New("请选择是否开启积分兑换")
	}
	if i.IntegralShopStatus.IsNone() || !i.IntegralShopStatus.Exists() {
		return errors.New("请选择是否开启积分商城")
	}
	return nil
}

func (i *IntegralConfigAddBo) SetDeductionRate(v float64) {
	i.DeductionRate, _ = decimal.NewFromFloat(v).Div(decimal.NewFromInt(100)).Float64()
}

func (i *IntegralConfigUpdateBo) Validate() error {
	if i.Id <= 0 {
		return errors.New("缺少参数")
	}
	if i.ExchangeRate <= 0 {
		return errors.New("积分抵扣比例必须大于0")
	}
	if len([]rune(i.Name)) > 20 || len([]rune(i.Name)) < 1 {
		return errors.New("积分名称长度必须在1-20之间")
	}
	if !i.DeductionType.IsValid() {
		return errors.New("积分抵扣类型不合法")
	}
	if i.DeductionRate < 0 {
		return errors.New("积分最大抵扣比例不能为负数")
	}
	if !i.ExpireType.IsValid() {
		return errors.New("积分过期类型不合法")
	}
	if i.ExpireType == valobj.IntegralConfigExpireTypeNo {
		i.ExpireYear = 0
	} else {
		if i.ExpireYear < 0 {
			return errors.New("积分过期类型不为永不过期时，积分过期年限不能小于0")
		}
		if i.ExpireType.IsYearTurn() && i.ExpireYear == 0 {
			return errors.New("积分过期年限必须大于0")
		}
	}
	if i.IntegralCash.IsNone() || !i.IntegralCash.Exists() {
		return errors.New("请选择是否开启积分+钱购")
	}
	if i.IntegralExchange.IsNone() || !i.IntegralExchange.Exists() {
		return errors.New("请选择是否开启积分兑换")
	}
	if i.IntegralShopStatus.IsNone() || !i.IntegralShopStatus.Exists() {
		return errors.New("请选择是否开启积分商城")
	}
	return nil
}

func (i *IntegralConfigUpdateBo) SetDeductionRate(v float64) {
	i.DeductionRate, _ = decimal.NewFromFloat(v).Div(decimal.NewFromInt(100)).Float64()
}

func NewIntegralConfigAddDefaultBo(isEnable bool) *IntegralConfigAddBo {
	obj := &IntegralConfigAddBo{
		ExchangeRate:       100,
		Name:               constants.IntegralConfigDefaultName,
		DeductionType:      valobj.IntegralConfigDeductionTypeOrderPayAmount,
		DeductionRate:      0,
		ExpireType:         valobj.IntegralConfigExpireTypeYearTurn,
		ExpireYear:         1,
		ShopId:             0,
		IntegralExchange:   valobj.IntegralConfigExchangeDisable,
		IntegralCash:       valobj.IntegralConfigCashDisable,
		IntegralShopStatus: valobj.IntegralShopStatusDisable,
		CustomerId:         0,
	}
	if isEnable {
		obj.IntegralExchange = valobj.IntegralConfigExchangeEnable
		obj.IntegralCash = valobj.IntegralConfigCashEnable
		obj.IntegralShopStatus = valobj.IntegralShopStatusEnable
	}
	return obj
}
