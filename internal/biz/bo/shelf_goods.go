package bo

import (
	"cardMall/api/apierr"
)

type ShelfGoodsBo struct {
	GoodsId int
	Online  bool

	*AdminLoginInfoBo
}

// Validate .
func (s *ShelfGoodsBo) Validate() error {
	if s.GoodsId <= 0 {
		return apierr.ErrorParam("商品id不能为空")
	}
	return nil
}

// IsOffline 下架
func (s *ShelfGoodsBo) IsOffline() bool {
	return !s.Online
}

// IsOnline 上架
func (s *ShelfGoodsBo) IsOnline() bool {
	return s.Online
}
