package bo

import (
	"cardMall/internal/biz/rpc/calvalobj"
	"cardMall/internal/biz/valobj"
)

type PayCenterConfigAddBo struct {
	Name           string                            `json:"name"`
	ShortName      string                            `json:"shortName"`
	Status         calvalobj.PayCenterMerchantStatus `json:"status"`
	Remark         string                            `json:"remark"`
	Contact        string                            `json:"contact"`
	Phone          string                            `json:"phone"`
	NotifyUrl      string                            `json:"notifyUrl"`
	PublicKey      string                            `json:"publicKey"`
	Logo           string                            `json:"logo"`
	PayChannelList []*PayCenterConfigChannel         `json:"payChannelList"`
}

type PayCenterConfigChannel struct {
	Type           calvalobj.PayChannelType          `json:"type"`
	AppId          string                            `json:"appId"`
	PublicKey      string                            `json:"publicKey"`
	PrivateKey     string                            `json:"privateKey"`
	ExtJson        string                            `json:"extJson"`
	Id             int                               `json:"id"`
	AlipaySignType valobj.PayConfigAlipaySignTypeObj `json:"alipaySignType"`
}

type PayCenterConfigChannelExtJson struct {
	SerialNo string `json:"serialNo"`
	MchId    string `json:"mchid"`
}
