//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package bo

import "cardMall/internal/biz/valobj"

type OrderAfterSaleSearchBo struct {
	OrderNumber             string
	GoodsName               string
	AfterSaleType           valobj.AfterSaleType
	AfterSaleStatus         valobj.AdminAfterSaleStatus
	AfterSalePlatformStatus valobj.AfterSalePlatformStatus
	AfterSaleNo             string

	OrderDateStart int
	OrderDateEnd   int
	UserId         int
	SupplierId     int

	Edges    *OrderAfterSaleEdgesBo
	Page     *ReqPageBo
	SortData []*CommonSortBo
}

type OrderAfterSaleEdgesBo struct {
	WithOrderAfterSaleDeliver       bool
	WithOrderAfterSaleGoods         bool // with所有商品
	WithOrderAfterSaleExchangeGoods bool // 仅with换货商品
	WithOrderAfterSaleApplyGoods    bool // 仅with申请的商品
	WithOrderAfterSaleLog           bool
	WithSupplier                    bool
}
