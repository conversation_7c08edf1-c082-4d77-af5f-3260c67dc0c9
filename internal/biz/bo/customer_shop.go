package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
)

type CustomerShopAddBo struct {
	Name              string
	Scene             valobj.SysConfigShopSceneSlice
	LoginPlatform     valobj.SysConfigLoginPlatformSlice
	LoginModel        valobj.ConfigLoginMode
	LoginType         valobj.ConfigLoginType
	RegisterBindPhone valobj.ConfigRegisterBindPhone
	DefaultBanner     string
	ShopLogo          string
	ShopColor         string
	Remark            string
	Status            valobj.CustomerShopStatusObj
	CustomerId        int
	Applet            []*CustomerShopAppletBo
	ShopShowBalance   valobj.SysConfigShopShowBalanceObj

	//服务条款与服务政策、常见问题、商务合作电话、售后电话、客服电话
	SysConfigService              string
	SysConfigQA                   string
	SysConfigPartnerPhone         string
	SysConfigAfterSalePhone       string
	SysConfigCustomerSupportPhone string
}

// GetDefaultSysConfigService 获取默认服务条款与服务政策
func (m *CustomerShopAddBo) GetDefaultSysConfigService(shopName, phone string) string {
	if m.SysConfigService == "" {
		return `<div style="text-indent: 50rpx;">
    ` + shopName + `尊重和保护用户的隐私。本隐私政策将告诉您我们如何收集和使用有关您的信息，以及我们如何保护这些信息的安全。您成为` + shopName + `用户前务必仔细阅读本隐私条款并同意所有隐私条款。本隐私政策条款在您注册成为` + shopName + `用户后立即生效，并对您及` + shopName + `产生约束力。
  </div>
  <div>一、我们可能收集的用户信息</div>
  <div>我们提供服务时，可能会收集、储存和使用下列与您有关的信息。如果您不提供相关信息，可能无法注册成为我们的用户或无法享受我们提供的某些服务，或者无法达到相关服务拟达到的效果。</div>
  <div>1、您提供的信息</div>
  <div>• 您在注册账户或使用我们的服务时，向我们提供的相关个人信息，例如电话号码、身份证号码、银行卡号等。</div>
  <div>• 您通过我们的服务向其他方提供的共享信息，以及您使用我们的服务时所储存的信息。</div>
  <div>2、其他方分享的您的信息</div>
  <div>其他方使用我们的服务时所提供有关您的共享信息。</div>
  <div>3、我们获取的您的信息。 您使用服务时我们可能收集如下信息：</div>
  <div>•
    日志信息，指您使用我们的服务时，系统可能通过cookies、webbeacon或其他方式自动采集的技术信息，包括：设备或软件信息，例如您的移动设备、网页浏览器或用于接入我们服务的其他程序所提供的配置信息、您的IP地址和移动设备所用的版本和设备识别码；以及您在使用我们服务时要求提供的其他信息和内容详情。
  </div>
  <div>• 位置信息，指您开启设备定位功能并使用我们基于位置提供的相关服务时，收集的有关您位置的信息。您可以通过关闭定位功能，停止对您的地理位置信息的收集。</div>
  <div>• 交易信息，指我们对您在` + shopName + `平台上形成的交易数据信息进行分析并形成用户信用数据库。</div>
  <div>4、合作机构提供的您的信息</div>
  <div>
    我们会通过第三方机构（包括但不限于通过合法的征信机构个人信用信息基础数据库及外部数据）查询您的信用报告中的身份、联系方式等个人身份、电信信息、地址、位置等基本信息和关联人信息以及个人贷款、各类信用卡和对外担保等信用及在商业活动中形成的各类交易记录，个人信贷交易信息及公共费用缴纳、违法违规信息及非银行类信息的其他信息，获取的您的肖像、工作情况、收入情况、家庭情况、社会关系情况、招聘应聘以及您在` + shopName + `的交易记录和使用信息等。
  </div>
  <div> 二、我们可能如何使用用户信息</div>
  <div>1、在我们提供服务时，用于身份验证、客户服务、安全防范、诈骗监测、存档和备份用途，确保我们向您提供的产品和服务的安全性；</div>
  <div>2、帮助我们设计新服务，改善我们现有服务；</div>
  <div>3、使我们更加了解您如何接入和使用我们的服务，从而针对性地回应您的个性化需求；</div>
  <div>4、软件认证或管理软件升级；</div>
  <div>5、让您参与有关我们产品和服务的调查。</div>
  <div>
    为了让您有更好的体验、改善我们的服务或您同意的其他用途，在符合相关法律法规的前提下，我们可能将通过某一项服务所收集的信息，以汇集信息或者个性化的方式，用于我们的其他服务。例如，在您使用我们的一项服务时所收集的信息，可能在另一服务中用于向您提供特定内容，或向您展示与您相关的、非普遍推送的信息。如果我们在相关服务中提供了相应选项，您也可以授权我们将该服务所提供和储存的信息用于我们的其他服务。
  </div>
  <div>三、我们可能与第三方共享的用户信息</div>
  <div>在下列情况下，我们可能会共享您的个人信息：</div>
  <div>1、您逾期未偿还借款本息，我们将向公众（包括但不限于出借人、您手机通讯录中不时更新的联系人、该等联系人手机中不时更新的联系人、您提供的社交账号的联系人或群体）披露您的用户信息；</div>
  <div>2、其他注册用户就您在` + shopName + `活动中的违约行为发起诉讼程序时，我们将应该等注册用户的请求向该等注册用户披露您的用户信息；</div>
  <div>3、应行政、司法部门的要求向该等机构提供您的用户信息；</div>
  <div>4、债权人委托催收时，向受托催收主体提供您的用户信息；</div>
  <div>5、您通过` + shopName + `平台向第三方合作机构申请相关产品服务时，我们将根据您的授权向第三方提供您的用户信息；</div>
  <div>
    6、我们以及我们的关联公司，可能将您的个人信息与我们的关联公司、合作伙伴及第三方服务供应商、承包商及代理（例如代表我们发出短信或推送通知的通讯服务提供商、为我们提供位置数据的地图服务供应商）分享（他们可能并非位于您所在的法域），为了我们向您提供或改善我们的服务；
  </div>
  <div>• 随着我们业务的持续发展，我们以及我们的关联公司有可能进行合并、收购、资产转让或类似的交易，您的个人信息有可能作为此类交易的一部分而被转移；</div>
  <div>• 依据法律要求必须向第三方提供您的用户信息的情形；</div>
  <div>四、我们如何保护用户信息</div>
  <div>
    我们使用各种安全技术和程序，以防信息的丢失、不当使用、未经授权阅览或披露。但请您理解，由于技术的限制以及可能存在的各种恶意手段，在互联网行业，即便竭尽所能加强安全措施，也不可能始终保证信息百分之百的安全。您需要了解，您接入我们的服务所用的系统和通讯网络，有可能因我们可控范围外的因素而出现问题。
  </div>
  <div>五、账户注销</div>
  <div>当用户需要注销本账号时，可联系 ` + shopName + ` 客服人员进行验证注销。</div>
  <div>六、隐私政策的修改</div>
  <div>
    由于法律法规的变更，以及为了与互联网的新发展和可能的发展趋势保持同步，我们可能会不定时修改本政策。因此，我们保留自行决定实施此类修改的权利，如该等修订造成您在本《隐私政策》下权利的实质减少，我们将在修订生效前通过在主页上显著位置提示或向您推送通知或以其他方式通知您。在该种情况下，若您继续使用我们的服务，即表示同意受经修订的本《隐私政策》的约束。
  </div>`
	}
	return m.SysConfigService
}

// GetDefaultSysConfigQA 获取默认常见问题
func (m *CustomerShopAddBo) GetDefaultSysConfigQA(shopName string, phone string) string {
	return `<h4>如何申请注销账号</h4>
    <div>【重要提醒】</div>
    <div>注销账号是不可恢复的操作，你应自行备份账号相关的信息和数据。请谨记：注销账号，你将无法再使用本账号（即使你使用相同的手机号码再次注册并使用），包括但不限于：</div>
    <div>（1）你将无法登录、使用本账号。</div>
    <div>
      （2）你账号的个人资料和历史信息（包含充值订单充值加款记录等）都将无法找回。请注意，注销你的账号并不代表本账号注销前的账号行为和相关责任得到豁免或减轻。请认真阅读【重要提醒】，确认是否需要注销您的账号。如果您需要注销账号，请联系客服，客服将帮助您解決您的账号问题！
    </div>
    <h4>如何投诉、建议个人信息保护相关事</h4>
    <div>我们设立了专门的个人信息保护团队和个人信息保 护责任人，如果你对个人信息保护相关事宜有任何疑问或投诉、建议时，你可以通过以下方式与我们联系：</div>
    <div>电话：` + phone + `。</div>
    <div>我们将尽快审核所涉问题，并在验证你的用户身份后的七个工作日内予以回复。</div>`
}

func (m *CustomerShopAddBo) Validate() error {
	if m.Name == "" {
		return apierr.ErrorParam("商城名称不能为空")
	}
	if len([]rune(m.Name)) > 20 {
		return apierr.ErrorParam("商城名称不能超过20个字符")
	}
	if len(m.Scene) == 0 {
		return apierr.ErrorParam("请选择经营场景")
	}
	if !m.Scene.IsValid() {
		return apierr.ErrorParam("经营场景参数错误")
	}
	if len(m.LoginPlatform) == 0 {
		return apierr.ErrorParam("请选择登录平台")
	}
	for !m.LoginPlatform.IsValid() {
		return apierr.ErrorParam("登录平台参数错误")
	}
	if !m.LoginModel.Exists() {
		return apierr.ErrorParam("登录模式参数错误")
	}
	if !m.LoginType.Exists() {
		return apierr.ErrorParam("登录方式参数错误")
	}
	if m.LoginType.IsWechat() {
		if !m.RegisterBindPhone.IsYes() {
			return apierr.ErrorParam("OpenID静默登录注册时必须强制绑定手机号")
		}
		if m.LoginPlatform.IsH5Enable() {
			return apierr.ErrorParam("H5不支持OpenID静默登录方式")
		}
	}
	if m.LoginType.IsWechat() {
		if !m.LoginPlatform.HasOpenIdLoginPlatform() {
			return apierr.ErrorParam("登录方式参数错误")
		}
	}
	if !m.LoginType.IsCanRegisterBindPhone() && m.RegisterBindPhone.IsYes() {
		return apierr.ErrorParam("当前登录方式不支持强制绑定手机号")
	}
	if !m.Status.Exists() {
		return apierr.ErrorParam("商城状态参数错误")
	}
	if m.ShopColor == "" {
		return apierr.ErrorParam("请选择商城背景色")
	}
	if len([]rune(m.Remark)) > 200 {
		return apierr.ErrorParam("备注不能超过200个字符")
	}
	if m.LoginPlatform.IsAppletEnable() {
		if len(m.Applet) == 0 {
			return apierr.ErrorParam("请录入小程序信息")
		}
		for _, v := range m.Applet {
			if err := v.Validate(); err != nil {
				return err
			}
		}
	}
	if !m.ShopShowBalance.Exists() {
		return apierr.ErrorParam("请选择是否透传企业余额授信")
	}
	return nil
}

type CustomerShopUpdateBo struct {
	Id                int
	Name              string
	Scene             valobj.SysConfigShopSceneSlice
	LoginModel        valobj.ConfigLoginMode
	LoginType         valobj.ConfigLoginType
	RegisterBindPhone valobj.ConfigRegisterBindPhone
	Remark            string
	Status            valobj.CustomerShopStatusObj
	LoginPlatform     valobj.SysConfigLoginPlatformSlice
	ShopColor         string
	Applet            []*CustomerShopAppletBo
	ShopShowBalance   valobj.SysConfigShopShowBalanceObj
	ShopLogo          string
}

func (m *CustomerShopUpdateBo) Validate() error {
	if m.Id <= 0 {
		return apierr.ErrorParam("参数错误")
	}
	if m.Name == "" {
		return apierr.ErrorParam("商城名称不能为空")
	}
	if len([]rune(m.Name)) > 20 {
		return apierr.ErrorParam("商城名称不能超过20个字符")
	}
	if len(m.Scene) == 0 {
		return apierr.ErrorParam("请选择经营场景")
	}
	if !m.Scene.IsValid() {
		return apierr.ErrorParam("经营场景参数错误")
	}
	if len(m.LoginPlatform) == 0 {
		return apierr.ErrorParam("请选择登录平台")
	}
	for !m.LoginPlatform.IsValid() {
		return apierr.ErrorParam("登录平台参数错误")
	}
	if !m.LoginModel.Exists() {
		return apierr.ErrorParam("登录模式参数错误")
	}
	if !m.LoginType.Exists() {
		return apierr.ErrorParam("登录方式参数错误")
	}
	if m.LoginType.IsWechat() {
		if !m.RegisterBindPhone.IsYes() {
			return apierr.ErrorParam("OpenID静默登录注册时必须强制绑定手机号")
		}
		if m.LoginPlatform.IsH5Enable() {
			return apierr.ErrorParam("H5不支持OpenID静默登录方式")
		}
	}
	if m.LoginType.IsWechat() {
		if !m.LoginPlatform.HasOpenIdLoginPlatform() {
			return apierr.ErrorParam("登录方式参数错误")
		}
	}
	if !m.LoginType.IsCanRegisterBindPhone() && m.RegisterBindPhone.IsYes() {
		return apierr.ErrorParam("当前登录方式不支持强制绑定手机号")
	}
	if m.ShopColor == "" {
		return apierr.ErrorParam("请选择商城背景色")
	}
	if len([]rune(m.Remark)) > 200 {
		return apierr.ErrorParam("备注不能超过200个字符")
	}
	if m.LoginPlatform.IsAppletEnable() {
		if len(m.Applet) == 0 {
			return apierr.ErrorParam("请录入小程序信息")
		}
		for _, v := range m.Applet {
			if err := v.Validate(); err != nil {
				return err
			}
		}
	}
	if !m.ShopShowBalance.Exists() {
		return apierr.ErrorParam("请选择是否透传企业余额授信")
	}
	if m.ShopLogo == "" {
		return apierr.ErrorParam("请上传商城Logo")
	}
	return nil
}

type CustomerShopAppletBo struct {
	AppId     string
	AppSecret string
	AppType   valobj.SaasShopAppletTypeObj
}

func (m *CustomerShopAppletBo) Validate() error {
	if m.AppId == "" {
		return apierr.ErrorParam("小程序AppId不能为空")
	}
	if m.AppSecret == "" {
		return apierr.ErrorParam("小程序秘钥不能为空")
	}
	if !m.AppType.Exists() {
		return apierr.ErrorParam("小程序类型参数错误")
	}
	return nil
}
