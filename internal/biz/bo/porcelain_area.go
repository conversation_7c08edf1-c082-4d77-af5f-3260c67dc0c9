package bo

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/valobj"
	"strings"
	"unicode/utf8"
)

type QuickAccessAreaSavePorcelainReq struct {
	Id               int
	Name             string
	Icon             string
	Sort             int
	RelationValueIds []int
	RelationType     valobj.QuickAccessAreaRelationTypeObj
	RelationValue    string
	Status           valobj.QuickAccessAreaStatusObj
}

func (b *QuickAccessAreaSavePorcelainReq) Validate() error {
	if strings.TrimSpace(b.Name) == "" {
		return apierr.ErrorParam("名称不能为空")
	}
	if utf8.RuneCountInString(b.Name) > 20 {
		return apierr.ErrorParam("名称长度不能超过20个字符")
	}
	if !b.Status.Exists() {
		return apierr.ErrorParam("状态值错误")
	}
	if !b.RelationType.Exists() {
		return apierr.ErrorParam("关联类型错误")
	}
	if b.RelationType.IsRelationValeNeed() && strings.TrimSpace(b.RelationValue) == "" {
		return apierr.ErrorParam("关联值不能为空")
	}
	if utf8.RuneCountInString(b.RelationValue) > 1024 {
		return apierr.ErrorParam("关联节点长度不能超过1024个字符")
	}
	if strings.TrimSpace(b.Icon) == "" {
		return apierr.ErrorParam("图标不能为空")
	}
	if b.Sort < 0 {
		return apierr.ErrorParam("排序值不能小于0")
	}
	if b.Sort > 20 {
		return apierr.ErrorParam("排序值不能大于20")
	}
	if len(b.RelationValueIds) > 10 {
		return apierr.ErrorParam("关联节点不能超过10个")
	}
	if b.RelationType.IsBrand() || b.RelationType.IsCategory() {
		if len(b.RelationValueIds) == 0 {
			return apierr.ErrorParam("关联节点id不能为空")
		}
		for _, v := range b.RelationValueIds {
			if v <= 0 {
				return apierr.ErrorParam("关联节点id不能为空")
			}
		}
	}
	return nil
}

type PorcelainItem struct {
	Id               int
	Name             string
	Icon             string
	Sort             int
	Status           valobj.AdminStatusObj
	RelationValueIds []int
	RelationType     valobj.QuickAccessAreaRelationTypeObj
	RelationValue    string
}

type PorcelainAreaQueryBo struct {
	*ReqPageBo
	Status valobj.QuickAccessAreaStatusObj
	*ReqSortBo
}
