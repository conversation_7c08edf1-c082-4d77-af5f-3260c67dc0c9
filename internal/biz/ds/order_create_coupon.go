package ds

import (
	"cardMall/internal/pkg/helper"
)

type OrderCreateCouponParams struct {
	DiscountAmount float64
	SkuNo          []string
	LimitAmount    float64
}

func OrderCreateCouponParamsInit(discountAmount float64, skuNos []string, limitAmount float64) *OrderCreateCouponParams {
	return &OrderCreateCouponParams{
		DiscountAmount: discountAmount,
		SkuNo:          skuNos,
		LimitAmount:    limitAmount,
	}
}
func (c *OrderCreateCouponParams) IsDiscount(skuNo string, amount float64) bool {
	if c == nil || c.DiscountAmount == 0 {
		return false
	}
	if amount > 0 {
		if c.LimitAmount > 0 && amount < c.LimitAmount {
			return false
		}
	}
	if len(c.SkuNo) > 0 && !helper.InSlice(skuNo, c.SkuNo) {
		return false
	}
	return true
}
