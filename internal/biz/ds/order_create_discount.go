package ds

import (
	bizBo "cardMall/internal/biz/bo"
	"github.com/shopspring/decimal"
)

// hasIntegralDiscount 是否有积分抵扣
func (c *CreateOrderParamsV2) hasIntegralDiscount() bool {
	return c.PayOrder.TotalPayIntegral > 0
}

// hasCouponDiscount 是否有优惠券抵扣
func (c *CreateOrderParamsV2) hasCouponDiscount() bool {
	return c.PayOrder.CouponDiscountAmount > 0
}

func (c *CreateOrderParamsV2) getOrderGoodsBosNum() int {
	var count int
	for _, orderGoodsBos := range c.OrderGoodsBosMap {
		count += len(orderGoodsBos)
	}
	return count
}

// orderGoodsDiscountCoupon 优惠券抵扣计算 给每个order_goods bo 赋值优惠券抵扣金额
func (c *CreateOrderParamsV2) orderGoodsDiscountCoupon() *CreateOrderParamsV2 {
	//判断是否有优惠券抵扣
	if !c.hasCouponDiscount() {
		return c
	}

	//统计参与优惠券抵扣的订单商品
	c.CouponOrderGoodsCount()

	var index int
	var totalDiscountAmount float64
	for _, orderGoodsBos := range c.OrderGoodsBosMap {
		for _, v := range orderGoodsBos {
			if c.Extra.couponUsable(v.SkuNo, c.CouponOrderGoodsAmountTotal) {
				goodsTotalAmount := c.GetOrderGoodsTotalAmount(v.SalePrice, v.Quantity)
				// 最后一个订单商品为了保证四舍五入不产生误差，直接用优惠券抵扣金额减去已处理的优惠抵扣金额
				if index == c.CouponOrderGoodsNum-1 {
					v.CouponDiscountAmount = decimal.NewFromFloat(c.PayOrder.CouponDiscountAmount).Sub(decimal.NewFromFloat(totalDiscountAmount)).InexactFloat64()
				} else {
					v.CouponDiscountAmount = c.getCouponDiscountAmount(c.CouponOrderGoodsAmountTotal, goodsTotalAmount, c.PayOrder.CouponDiscountAmount)
				}
				//优惠券抵扣金额不能大于订单商品总金额
				if v.CouponDiscountAmount >= goodsTotalAmount {
					v.CouponDiscountAmount = goodsTotalAmount
				}
				v.GoodsPayAmount = decimal.NewFromFloat(v.GoodsPayAmount).Sub(decimal.NewFromFloat(v.CouponDiscountAmount)).InexactFloat64()
				totalDiscountAmount = decimal.NewFromFloat(totalDiscountAmount).Add(decimal.NewFromFloat(v.CouponDiscountAmount)).InexactFloat64()
				index++
			}
		}
	}
	return c
}

// orderGoodsDiscountIntegral 积分抵扣计算 给每个order_goods bo 赋值积分抵扣数量和金额
func (c *CreateOrderParamsV2) orderGoodsDiscountIntegral() *CreateOrderParamsV2 {
	if !c.hasIntegralDiscount() {
		return c
	}

	var index int
	var totalDiscountIntegral int
	totalDiscountAmount := decimal.Zero
	// 获取订商品参与积分抵扣的总金额以及商品数量(按照商品的种类，不是个数)
	skuTotalAmount, count := c.getIntegralDiscountTotalAmount()
	for _, orderGoodsBos := range c.OrderGoodsBosMap {
		for _, v := range orderGoodsBos {
			//积分抵扣
			if c.canDiscountIntegral(*v) {
				//参数积分抵扣的金额
				skuDiscountAmount := c.getSkuIntegralDiscountTotalAmount(*v)
				if index == count-1 {
					v.PayIntegral = c.PayOrder.TotalPayIntegral - totalDiscountIntegral
					v.IntegralDiscountAmount = decimal.NewFromFloat(c.PayOrder.IntegralDiscountAmount).Sub(totalDiscountAmount).InexactFloat64()
					v.ActualDiscountIntegral = c.getIntegralByAmount(v.IntegralDiscountAmount)
				} else {
					v.ActualDiscountIntegral = c.getIntegral(skuTotalAmount, skuDiscountAmount, c.PayOrder.TotalPayIntegral)
					v.PayIntegral = v.ActualDiscountIntegral
					v.IntegralDiscountAmount = c.getIntegralDiscountAmount(v.ActualDiscountIntegral)
					checkAmount := decimal.NewFromFloat(skuDiscountAmount).Sub(decimal.NewFromFloat(v.IntegralDiscountAmount)).InexactFloat64()
					// 积分抵扣金额大于商品，则抵扣金额=商品金额，并反算实际抵扣积分
					if checkAmount < 0 {
						v.IntegralDiscountAmount = skuDiscountAmount
						v.ActualDiscountIntegral = c.getIntegralByAmount(v.IntegralDiscountAmount)
					}
				}
				v.GoodsPayAmount = decimal.NewFromFloat(v.GoodsPayAmount).Sub(decimal.NewFromFloat(v.IntegralDiscountAmount)).InexactFloat64()
				totalDiscountIntegral += v.PayIntegral
				totalDiscountAmount = totalDiscountAmount.Add(decimal.NewFromFloat(v.IntegralDiscountAmount))
				index++
			} else if c.isSaleIntegralSku(v.SkuNo) {
				//商品是积分+钱购的情况, 直接用商品销售积分
				v.PayIntegral = c.getSkuSaleIntegral(*v)
				v.ActualDiscountIntegral = v.PayIntegral
			}
		}
	}
	return c
}

// getIntegralDiscountTotalAmount 在对积分进行分配到各个子订单时，总订单金额需要剔除订单完全被优惠券抵扣的订单
func (c *CreateOrderParamsV2) getIntegralDiscountTotalAmount() (float64, int) {
	var num int
	totalAmount := decimal.NewFromFloat(0)
	for _, orderGoodsBos := range c.OrderGoodsBosMap {
		for _, v := range orderGoodsBos {
			if c.canDiscountIntegral(*v) {
				var amount float64
				amount = c.getSkuIntegralDiscountTotalAmount(*v)
				totalAmount = totalAmount.Add(decimal.NewFromFloat(amount))
				num++
			}
		}
	}
	return totalAmount.InexactFloat64(), num
}

// canDiscountIntegral 判断是否可以抵扣积分
func (c *CreateOrderParamsV2) canDiscountIntegral(v bizBo.OrderGoodsCreateBo) bool {
	// 积分+钱购的商品不能再参与积分抵扣
	if c.isSaleIntegralSku(v.SkuNo) {
		return false
	}
	// 获取积分配置，如果积分抵扣是按商品利润，则计算商品利润是否大于优惠券抵扣金额
	// 如果积分抵扣是按商品总价，则计算商品总价是否大于优惠券抵扣金额
	var totalAmount float64
	totalAmount = c.getSkuIntegralDiscountTotalAmount(v)
	return totalAmount > 0
}

// isSaleIntegralSku 判断是否是积分+钱或者纯积分兑换商品
func (c *CreateOrderParamsV2) isSaleIntegralSku(skuNo string) bool {
	for _, v := range c.SupplierSku {
		if v.SkuInfo.SkuNo == skuNo {
			return v.SkuInfo.IsIntegralSku()
		}
	}
	return false
}

// getSkuSaleIntegral 获取购买商品所需积分
func (c *CreateOrderParamsV2) getSkuSaleIntegral(orderGood bizBo.OrderGoodsCreateBo) int {
	for _, v := range c.SupplierSku {
		if v.SkuInfo.SkuNo == orderGood.SkuNo {
			return v.SkuInfo.SaleIntegral * orderGood.Quantity
		}
	}
	return 0
}

// getSkuIntegralDiscountTotalAmount 获取商品进行积分抵扣的金额
// 要么是按商品利润，要么是按商品总价
func (c *CreateOrderParamsV2) getSkuIntegralDiscountTotalAmount(v bizBo.OrderGoodsCreateBo) float64 {
	var totalAmount float64
	if c.Extra.Integral.GetRule().DeductionTypeIsOrderProfit() {
		totalAmount = c.GetOrderGoodsTotalProfitAmount(v)
	} else if c.Extra.Integral.GetRule().DeductionTypeIsPayAmount() {
		totalAmount = c.GetOrderGoodsTotalAmount(v.SalePrice, v.Quantity)
	}
	totalAmount = decimal.NewFromFloat(totalAmount).Sub(decimal.NewFromFloat(v.CouponDiscountAmount)).InexactFloat64()
	return c.Extra.Integral.GetRule().GetDiscountAmount(totalAmount)
}
