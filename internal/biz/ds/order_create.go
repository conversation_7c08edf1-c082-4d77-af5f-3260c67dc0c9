package ds

import (
	bizBo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/pkg/helper"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"github.com/shopspring/decimal"
)

type CreateOrderParamsV2 struct {
	UserId                      int
	UserName                    string
	PayOrder                    *bizBo.PayOrderCreateBo
	OrderType                   valobj.OrderTypeObj
	SupplierGoods               []*SupplierOrderParams
	SupplierSku                 []*OrderSkuParams
	RechargeAccount             string
	OrderBos                    map[string]*bo.OrderCreateBo
	OrderGoodsBos               map[string][]*bizBo.OrderGoodsCreateBo
	OrderGoodsBosMap            map[int][]*bizBo.OrderGoodsCreateBo
	CouponOrderGoodsAmountTotal float64 // 使用优惠券的商品的原总金额
	Extra                       *OrderCreateExtraParams
	CouponOrderGoodsNum         int //使用优惠券的order_goods bo的数量
	idGenerator                 *idgenerator.Generator
}

func CreateOrderParamsV2Init(
	in *bo.OrderCreateNewBo,
	payOrder *bizBo.PayOrderCreateBo,
	supplierOrderSku []*OrderSkuParams,
	orderType valobj.OrderTypeObj,
	extra *OrderCreateExtraParams,
	idGenerator *idgenerator.Generator,
) *CreateOrderParamsV2 {
	c := &CreateOrderParamsV2{
		UserId:           in.User.UserId,
		UserName:         in.User.UserName,
		PayOrder:         payOrder,
		OrderType:        orderType,
		RechargeAccount:  in.Account,
		SupplierSku:      supplierOrderSku,
		SupplierGoods:    make([]*SupplierOrderParams, 0),
		OrderGoodsBosMap: make(map[int][]*bizBo.OrderGoodsCreateBo),
		OrderGoodsBos:    make(map[string][]*bizBo.OrderGoodsCreateBo),
		OrderBos:         make(map[string]*bo.OrderCreateBo),
		Extra:            extra,
		idGenerator:      idGenerator,
	}
	//构建SupplierGoods成员变量
	c.newSupplierGoods().
		//构建OrderGoodsBosMap成员变量
		newOrderGoodsBosMap().
		//计算优惠券抵扣并赋值给每个参加优惠券抵扣的order_goods bo
		orderGoodsDiscountCoupon().
		//计算积分抵扣并赋值给每个参加积分抵扣的order_goods bo
		orderGoodsDiscountIntegral()
	//构建order表的支付单数据
	//buildPOrderBo().
	//构建order表的供应商订单数据
	//buildOrderBos()
	return c
}

// GetOrderBos 获取订单实体
func (c *CreateOrderParamsV2) GetOrderBos() map[string]*bo.OrderCreateBo {
	if len(c.OrderBos) == 0 {
		c.orderGoodsBosRefresh().buildPOrderBo().buildOrderBos()
	}
	return c.OrderBos
}

// GetOrderGoodsBos 获取订单商品实体
func (c *CreateOrderParamsV2) GetOrderGoodsBos(orderNumber string, orderId int) []*bizBo.OrderGoodsCreateBo {
	orderGoodsBos := c.OrderGoodsBos[orderNumber]
	for _, orderGoodsBo := range orderGoodsBos {
		orderGoodsBo.OrderId = orderId
	}
	return orderGoodsBos
}

//-----------------------------------------------以下都是内部方法，外部调用无需关心------------------------------------------

// newSupplierGoods 构建供应商商品参数
func (c *CreateOrderParamsV2) newSupplierGoods() *CreateOrderParamsV2 {
	paramsMap := make(map[int]*SupplierOrderParams)
	params := make([]*SupplierOrderParams, 0)
	skuNum := 0
	for _, g := range c.SupplierSku {
		v, ok := paramsMap[g.GoodsInfo.SupplierId]
		if !ok {
			v = &SupplierOrderParams{
				Goods:       make([]*OrderSkuParams, 0),
				TotalAmount: 0,
				SupplierId:  g.GoodsInfo.SupplierId,
				TotalNum:    0,
			}
		}
		v.Goods = append(v.Goods, g)
		totalAmount, _ := decimal.NewFromFloat(g.SkuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(g.Num))).Float64()
		v.TotalAmount += totalAmount
		v.TotalNum += g.Num
		paramsMap[g.GoodsInfo.SupplierId] = v
		skuNum += g.Num
	}
	for _, v := range paramsMap {
		params = append(params, v)
	}
	c.SupplierGoods = params
	return c
}

// newOrderGoodsBosMap 将商品按照供应商的不同分类并且生成订单商品实体
// 返回值: map[供应商id][]订单商品实体
// splitByGoodsNum 是否根据商品数量拆单，虚拟商品购买一个商品算作一个子订单，实物商品则按照供应商不同拆单
func (c *CreateOrderParamsV2) newOrderGoodsBosMap() *CreateOrderParamsV2 {
	//根据供应商分拆订单商品
	OrderGoodsDos := make(map[int][]*bizBo.OrderGoodsCreateBo, 0)
	for _, v := range c.SupplierGoods {
		orderNumber := helper.GetOrderNumber(c.idGenerator)
		for _, g := range v.Goods {
			goodsImage := g.SkuInfo.Image
			if goodsImage == "" {
				goodsImage = g.GoodsInfo.Image
			}
			if c.OrderType.IsRecharge() {
				for i := 0; i < g.Num; i++ {
					b := &bizBo.OrderGoodsCreateBo{
						OrderId:           0,
						OrderNumber:       helper.GetOrderNumber(c.idGenerator),
						GoodsId:           g.GoodsInfo.Id,
						GoodsName:         g.GoodsInfo.Name,
						GoodsImage:        goodsImage,
						CategoryId:        g.GoodsInfo.CategoryId,
						Quantity:          1,
						SalePrice:         g.SkuInfo.SalePrice,
						OriginPrice:       g.SkuInfo.SupplierPrice,
						ChannelPrice:      g.GetCustomerCostPrice(),
						SupplierPrice:     g.GetShopCostPrice(),
						GoodsSkuId:        g.SkuInfo.ID,
						SupplierId:        g.GoodsInfo.SupplierId,
						GoodsSkuName:      g.SkuInfo.Name,
						BrandName:         g.BrandName,
						CategoryName:      g.CategoryName,
						ProductId:         g.SkuInfo.ProductID,
						SkuNo:             g.SkuInfo.SkuNo,
						PayAmount:         g.SkuInfo.SalePrice,
						RealSupplierPrice: g.GetSupplierCostPrice(),
						BrandId:           g.GoodsInfo.BrandId,
						GoodsPayAmount:    g.SkuInfo.SalePrice,
						TotalAmount:       g.SkuInfo.SalePrice,
						MarketPrice:       g.SkuInfo.MarketPrice,
					}
					OrderGoodsDos[v.SupplierId] = append(OrderGoodsDos[v.SupplierId], b)
				}
			} else {
				//货易通的订单，拆单需要按照sku来拆
				if g.GoodsInfo.SupplierId == int(c.Extra.conf.Hyt.GetSupplierId()) {
					orderNumber = helper.GetOrderNumber(c.idGenerator)
				}
				b := &bizBo.OrderGoodsCreateBo{
					OrderId:           0,
					OrderNumber:       orderNumber,
					GoodsId:           g.GoodsInfo.Id,
					GoodsName:         g.GoodsInfo.Name,
					GoodsImage:        goodsImage,
					CategoryId:        g.GoodsInfo.CategoryId,
					Quantity:          g.Num,
					SalePrice:         g.SkuInfo.SalePrice,
					OriginPrice:       g.SkuInfo.SupplierPrice,
					ChannelPrice:      g.GetCustomerCostPrice(),
					SupplierPrice:     g.GetShopCostPrice(),
					GoodsSkuId:        g.SkuInfo.ID,
					SupplierId:        g.GoodsInfo.SupplierId,
					GoodsSkuName:      g.SkuInfo.Name,
					BrandName:         g.BrandName,
					CategoryName:      g.CategoryName,
					SkuNo:             g.SkuInfo.SkuNo,
					ProductId:         g.SkuInfo.ProductID,
					GoodsPayAmount:    c.GetOrderGoodsTotalAmount(g.SkuInfo.SalePrice, g.Num),
					FreightFee:        c.Extra.GetSkuOriginFreightFee(g.GoodsInfo.SupplierId, g.SkuInfo.SkuNo),
					RealSupplierPrice: g.GetSupplierCostPrice(),
					BrandId:           g.GoodsInfo.BrandId,
					MarketPrice:       g.SkuInfo.MarketPrice,
				}
				b.PayAmount = decimal.NewFromFloat(b.GoodsPayAmount).Add(decimal.NewFromFloat(b.FreightFee)).InexactFloat64()
				b.TotalAmount = b.PayAmount
				OrderGoodsDos[v.SupplierId] = append(OrderGoodsDos[v.SupplierId], b)
			}
		}
	}
	c.OrderGoodsBosMap = OrderGoodsDos
	return c
}

// buildOrderBos 初始化订单插入数据实体
func (c *CreateOrderParamsV2) buildOrderBos() *CreateOrderParamsV2 {
	if c.OrderType.IsRecharge() {
		c.buildVirOrderBos()
	} else {
		c.buildEntityOrderBos()
	}
	return c
}

// buildEntityOrderBos 初始化实物订单插入数据实体
func (c *CreateOrderParamsV2) buildEntityOrderBos() {
	for supplierId, orderGoods := range c.OrderGoodsBosMap {
		//货易通的订单，拆单需要按照sku来拆
		if supplierId == int(c.Extra.conf.Hyt.GetSupplierId()) {
			for _, orderGood := range orderGoods {
				orderBo := c.newOrderBo(supplierId, []*bizBo.OrderGoodsCreateBo{orderGood})
				c.OrderBos[orderBo.OrderNumber] = orderBo
				c.setOrderGoodsBos(orderBo.OrderNumber, orderGood)
			}
		} else {
			orderBo := c.newOrderBo(supplierId, orderGoods)
			c.OrderBos[orderBo.OrderNumber] = orderBo
			c.setOrderGoodsBos(orderBo.OrderNumber, orderGoods...)
		}

	}
}

// buildVirOrderBos 初始化虚拟订单插入数据实体
func (c *CreateOrderParamsV2) buildVirOrderBos() {
	for supplierId, orderGoods := range c.OrderGoodsBosMap {
		for _, orderGood := range orderGoods {
			orderBo := c.newOrderBo(supplierId, []*bizBo.OrderGoodsCreateBo{orderGood})
			c.OrderBos[orderBo.OrderNumber] = orderBo
			c.setOrderGoodsBos(orderBo.OrderNumber, orderGood)
		}
	}
}

// buildPOrderBo 构建支付订单插入数据实体
func (c *CreateOrderParamsV2) buildPOrderBo() *CreateOrderParamsV2 {
	orderBo := c.newPOrderBo()
	for _, orderGoods := range c.OrderGoodsBosMap {
		c.setOrderGoodsBos(orderBo.OrderNumber, orderGoods...)
		for _, v := range orderGoods {
			orderBo.Num += v.Quantity
			orderBo.ActualDiscountIntegral += v.ActualDiscountIntegral
			orderBo.IntegralDiscountAmount = decimal.NewFromFloat(orderBo.IntegralDiscountAmount).Add(decimal.NewFromFloat(v.IntegralDiscountAmount)).InexactFloat64()
		}
	}
	c.OrderBos[orderBo.OrderNumber] = orderBo
	return c
}

// newOrderBo 构建订单主信息实体
func (c *CreateOrderParamsV2) newOrderBo(supplierId int, orderGoodsBos []*bizBo.OrderGoodsCreateBo) *bo.OrderCreateBo {
	//订单主信息
	status := valobj.OrderStatusUnPaid
	if c.isPaid() {
		// 充值订单状态 为待发货，实物订单为待确认
		status = helper.TernaryAny(c.OrderType.IsRecharge(), valobj.OrderStatusAwaitingShip, valobj.OrderStatusToBeConfirmed)
	}
	orderBo := &bo.OrderCreateBo{
		PayOrderNumber:         c.PayOrder.OrderNumber,
		OrderNumber:            helper.GetOrderNumber(c.idGenerator),
		OrderType:              c.OrderType,
		UserId:                 c.UserId,
		Status:                 status,
		Num:                    0, //表示商品数
		SiteId:                 c.PayOrder.SiteId,
		TotalAmount:            0,
		PayAmount:              0,
		PayIntegral:            0,
		CouponDiscountAmount:   0,
		IntegralDiscountAmount: 0,
		Account:                c.RechargeAccount,
		SupplierId:             supplierId,
		//运费的计算改为从order goods bo中获取
		//FreightFee:             c.Extra.GetSupFreightFee(supplierId),
		ExtType:           c.getOrderExtType(),
		DeliverExpireTime: c.getEntityDeliverExpireTime(supplierId),
		ActivityId:        c.PayOrder.ActivityId,
	}
	//如果order goods bo 带有订单号，则直接使用，否则使用本方法计算
	//解决后续代码如果有操作order goods bo 并且需要获取order number进行数据关联的问题
	if orderGoodsBos[0].OrderNumber != "" {
		orderBo.OrderNumber = orderGoodsBos[0].OrderNumber
	}

	payAmount := decimal.NewFromFloat(0)
	couponDiscountAmount := decimal.NewFromFloat(0)
	integralDiscountAmount := decimal.NewFromFloat(0)
	orderGoodsTotalAmount := decimal.NewFromFloat(0)
	FreightFee := decimal.NewFromFloat(0)
	goodsPayAmount := decimal.NewFromFloat(0)
	actualDiscountIntegral := 0
	for _, orderGoodsBo := range orderGoodsBos {
		orderBo.Num += orderGoodsBo.Quantity
		orderBo.PayIntegral += orderGoodsBo.PayIntegral
		//计算商品总金额 商品金额+运费
		orderGoodsTotalAmount = orderGoodsTotalAmount.Add(decimal.NewFromFloat(orderGoodsBo.TotalAmount))
		//计算支付金额
		payAmount = payAmount.Add(decimal.NewFromFloat(orderGoodsBo.PayAmount))
		//计算商品支付金额
		goodsPayAmount = goodsPayAmount.Add(decimal.NewFromFloat(orderGoodsBo.GoodsPayAmount))
		//计算优惠券抵扣金额
		couponDiscountAmount = couponDiscountAmount.Add(decimal.NewFromFloat(orderGoodsBo.CouponDiscountAmount))
		//计算积分抵扣金额
		integralDiscountAmount = integralDiscountAmount.Add(decimal.NewFromFloat(orderGoodsBo.IntegralDiscountAmount))
		//计算实际积分抵扣
		actualDiscountIntegral += orderGoodsBo.ActualDiscountIntegral
		//计算运费
		FreightFee = FreightFee.Add(decimal.NewFromFloat(orderGoodsBo.FreightFee))
	}

	//总金额
	orderBo.TotalAmount = orderGoodsTotalAmount.InexactFloat64()
	//支付金额
	orderBo.PayAmount = payAmount.InexactFloat64()
	//商品支付金额
	orderBo.GoodsPayAmount = goodsPayAmount.InexactFloat64()
	//运费
	orderBo.FreightFee = FreightFee.InexactFloat64()
	//优惠券抵扣金额
	orderBo.CouponDiscountAmount = couponDiscountAmount.InexactFloat64()
	//积分抵扣金额
	orderBo.IntegralDiscountAmount = integralDiscountAmount.InexactFloat64()
	//实际积分抵扣
	orderBo.ActualDiscountIntegral = actualDiscountIntegral

	return orderBo
}

// newPOrderBo 构建支付单主信息实体
func (c *CreateOrderParamsV2) newPOrderBo() *bo.OrderCreateBo {
	status := valobj.OrderStatusPayCreate
	if c.isPaid() {
		status = valobj.OrderStatusPaySuccess
	}
	orderBo := &bo.OrderCreateBo{
		PayOrderNumber:       c.PayOrder.OrderNumber,
		OrderNumber:          c.PayOrder.OrderNumber,
		OrderType:            c.OrderType,
		UserId:               c.UserId,
		Status:               status,
		Num:                  0, //表示商品数
		SiteId:               c.PayOrder.SiteId,
		TotalAmount:          c.PayOrder.TotalAmount,
		PayAmount:            c.PayOrder.TotalPayAmount,
		PayIntegral:          c.PayOrder.TotalPayIntegral,
		CouponDiscountAmount: c.PayOrder.CouponDiscountAmount,
		Account:              c.RechargeAccount,
		SupplierId:           0,
		FreightFee:           c.PayOrder.FreightFee,
		GoodsPayAmount:       c.PayOrder.GoodsPayAmount,
		ExtType:              c.getOrderExtType(),
		ActivityId:           c.PayOrder.ActivityId,
	}
	//if c.PayOrder.SettlementType.IsCashAndIntegral() {
	//	orderBo.IntegralDiscountAmount = c.getIntegralDiscountAmount(c.PayOrder.TotalPayIntegral)
	//}
	return orderBo
}

// setOrderGoodsBos 设置订单商品信息
func (c *CreateOrderParamsV2) setOrderGoodsBos(orderNumber string, orderGoodsBos ...*bizBo.OrderGoodsCreateBo) {
	if _, ok := c.OrderGoodsBos[orderNumber]; !ok {
		c.OrderGoodsBos[orderNumber] = make([]*bizBo.OrderGoodsCreateBo, 0, len(orderGoodsBos))
	}
	for _, orderGoodsBo := range orderGoodsBos {
		v := *orderGoodsBo
		v.OrderNumber = orderNumber
		c.OrderGoodsBos[orderNumber] = append(c.OrderGoodsBos[orderNumber], &v)
	}
}

// getIntegral 获取子订单使用积分
// totalAmount 订单总金额
// orderTotalAmount 子订单总金额
// totalIntegral 订单总使用积分
func (c *CreateOrderParamsV2) getIntegral(totalAmount, orderTotalAmount float64, totalIntegral int) (integralAverage int) {
	if totalAmount < constants.PayAmountMin {
		return
	}
	percent := decimal.NewFromFloat(orderTotalAmount).Div(decimal.NewFromFloat(totalAmount)).Mul(decimal.NewFromInt32(int32(totalIntegral)))
	//四舍五入保留整数
	integralAverage = int(percent.Round(0).IntPart())
	return
}

// getIntegralDiscountAmount 获取积分抵扣金额
func (c *CreateOrderParamsV2) getIntegralDiscountAmount(integral int) (r float64) {
	if integral > 0 {
		return c.Extra.Integral.GetIntegralAmount(integral)
	}
	return r
}

// getIntegralByAmount 获取积分抵扣金额
func (c *CreateOrderParamsV2) getIntegralByAmount(amount float64) (r int) {
	if amount > 0 {
		return c.Extra.Integral.GetIntegral(amount)
	}
	return r
}

// getCouponDiscountAmount 获取子订单优惠券抵扣金额
func (c *CreateOrderParamsV2) getCouponDiscountAmount(totalAmount, orderTotalAmount float64, couponDiscountAmount float64) (r float64) {
	if totalAmount < constants.PayAmountMin || couponDiscountAmount == 0 {
		return
	}
	percent := decimal.NewFromFloat(orderTotalAmount).Div(decimal.NewFromFloat(totalAmount)).Mul(decimal.NewFromFloat(couponDiscountAmount))
	//四舍五入保留两位小数
	r = percent.Round(2).InexactFloat64()
	return
}

// CouponOrderGoodsCount 使用优惠券的商品的原总金额累加
func (c *CreateOrderParamsV2) CouponOrderGoodsCount() {
	for _, bos := range c.OrderGoodsBosMap {
		for _, orderGoods := range bos {
			if c.Extra.couponProductLimit(orderGoods.SkuNo) {
				d := decimal.NewFromFloat(c.CouponOrderGoodsAmountTotal)
				totalAmountDecimal := c.GetOrderGoodsTotalAmount(orderGoods.SalePrice, orderGoods.Quantity)
				c.CouponOrderGoodsAmountTotal = d.Add(decimal.NewFromFloat(totalAmountDecimal)).InexactFloat64()
				c.CouponOrderGoodsNum++
			}
		}
	}
}

// GetOrderGoodsTotalAmount 计算订单商品总金额
func (c *CreateOrderParamsV2) GetOrderGoodsTotalAmount(salePrice float64, quantity int) (totalAmount float64) {
	totalAmount = decimal.NewFromFloat(salePrice).Mul(decimal.NewFromInt(int64(quantity))).InexactFloat64()
	return
}

// GetOrderGoodsTotalProfitAmount 计算订单商品总利润
func (c *CreateOrderParamsV2) GetOrderGoodsTotalProfitAmount(v bizBo.OrderGoodsCreateBo) (totalAmount float64) {
	totalAmount = decimal.NewFromFloat(v.SalePrice).Sub(decimal.NewFromFloat(v.SupplierPrice)).Mul(decimal.NewFromInt(int64(v.Quantity))).InexactFloat64()
	return
}

// isPaid 是否已支付
func (c *CreateOrderParamsV2) isPaid() bool {
	if c == nil || c.PayOrder == nil {
		return false
	}
	return c.PayOrder.IsPaid()
}

func (c *CreateOrderParamsV2) getOrderExtType() valobj.OrderExtTypeObj {
	if c == nil || c.PayOrder == nil {
		return valobj.OrderExtTypeDefault
	}
	switch c.PayOrder.SettlementType {
	case valobj.PayOrderSettlementTypeCardBatchCoupon:
		return valobj.OrderExtTypeCardBatchCoupon
	case valobj.PayOrderSettlementTypeGoodsCashAndIntegral:
		return valobj.OrderExtTypeGoodsIntegralCash
	case valobj.PayOrderSettlementTypeGoodsIntegral:
		return valobj.OrderExtTypeGoodsIntegral
	default:
		return valobj.OrderExtTypeDefault
	}
}

// getEntityDeliverExpireTime 获取商品供应商的发货有效期
// supplierId 供应商ID
func (c *CreateOrderParamsV2) getEntityDeliverExpireTime(supplierId int) int {
	for _, item := range c.SupplierGoods {
		if item.SupplierId == supplierId {
			return item.GetDeliverExpireTime()
		}
	}
	return 0
}

// orderGoodsBosRefresh 刷新订单商品信息
func (c *CreateOrderParamsV2) orderGoodsBosRefresh() *CreateOrderParamsV2 {
	for _, bos := range c.OrderGoodsBosMap {
		for _, orderGoods := range bos {
			orderGoods.PayAmount = orderGoods.GetPayAmount()
		}
	}
	return c
}
