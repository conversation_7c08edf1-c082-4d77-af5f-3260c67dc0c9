package ds

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	appBo "cardMall/internal/module/appbiz/bo"
	appDO "cardMall/internal/module/appbiz/do"
	appRepo "cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"context"
	"errors"
	"fmt"
	errors2 "github.com/go-kratos/kratos/v2/errors"
	"github.com/go-kratos/kratos/v2/log"
	"net/url"
	"strconv"

	"github.com/go-kratos/kratos/v2/transport/http"
	"github.com/shopspring/decimal"
)

type PayDs struct {
	repo               rpc.PayRepo
	orderRepo          appRepo.OrderRepo
	payOrderRepo       appRepo.PayOrderRepo
	orderRefundLogRepo appRepo.OrderRefundLogRepo
	payMerchantRepo    appRepo.PayMerchantRepo
	payConfigRepo      appRepo.PayConfigRepo
	conf               *conf.Bootstrap
	log                *log.Helper
	idGenerator        *idgenerator.Generator
	siteRepo           repository.SiteRepo
}

func NewPayDs(repo rpc.PayRepo, orderRepo appRepo.OrderRepo, payOrderRepo appRepo.PayOrderRepo, orderRefundLogRepo appRepo.OrderRefundLogRepo, payMerchantRepo appRepo.PayMerchantRepo, payConfigRepo appRepo.PayConfigRepo, conf *conf.Bootstrap, log *log.Helper, idGenerator *idgenerator.Generator, siteRepo repository.SiteRepo) *PayDs {
	return &PayDs{repo: repo, orderRepo: orderRepo, payOrderRepo: payOrderRepo, orderRefundLogRepo: orderRefundLogRepo, payMerchantRepo: payMerchantRepo, payConfigRepo: payConfigRepo, conf: conf, log: log, idGenerator: idGenerator, siteRepo: siteRepo}
}

type PayDsRefundExtendParams struct {
	OrderNumber  string
	RefundAmount float64
	RefundReason string
}

// Pay 请求支付中心预下单
func (p *PayDs) Pay(ctx context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*do.PayOrderPrePayDo, error) {
	//判断订单是否是待支付状态且未超时
	if payOrder.Status != valobj.PayOrderStatusUnpaid {
		return nil, errors.New("订单不是待支付状态")
	}
	if payOrder.IsExpire() {
		return nil, errors.New("订单已超时")
	}

	//获取请求支付中心所需的商户号，秘钥
	payCenterRequestConf, err := p.getPayRequestConfByPayType(ctx, payOrder.PayType.GetPayConfigChannel())
	if err != nil {
		return nil, err
	}

	//校验支付渠道
	//payCenterRequestConf, err = p.payConfigValidate(ctx, payOrder.PayType, payCenterRequestConf)
	//if err != nil {
	//	return nil, err
	//}
	payOrder.PayMerchantId = payCenterRequestConf.MerchantId

	//获取支付中心请求参数
	goodsName = p.goodsNameFormat(goodsName)
	prePayParams, err := p.getPrePayParams(ctx, payOrder, goodsName, openId)
	if err != nil {
		return nil, err
	}

	//发起请求
	rsp, err := p.repo.PrePay(ctx, prePayParams, payCenterRequestConf)
	if err != nil {
		p.log.Errorf("[pay]请求支付中心下单失败:%s", err)
		return nil, errors.New("系统繁忙，请稍后再试")
	}

	//处理响应
	rsp, err = p.prePayResponseHandle(ctx, rsp, payOrder)
	if err != nil {
		return nil, err
	}
	return &do.PayOrderPrePayDo{
		OutTradeNo: rsp.OrderId,
		PayParams:  rsp.PayParams,
	}, nil
}

// getPrePayParams 获取支付请求参数
func (p *PayDs) getPrePayParams(ctx context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*bo.PayRequest, error) {
	switch payOrder.PayType {
	case valobj.PayOrderPayTypeWxApplet:
		return p.getWXAppletPrePayParams(ctx, payOrder, goodsName, openId)
	case valobj.PayOrderPayTypeWxH5:
		return p.getWxH5PrePayParams(ctx, payOrder, goodsName, openId)
	case valobj.PayOrderPayTypeAliPayH5:
		return p.alipayH5PrePayParams(ctx, payOrder, goodsName, openId)
	case valobj.PayOrderPayTypeWxJsApi:
		return p.getWxJsApiPrePayParams(ctx, payOrder, goodsName, openId)
	case valobj.PayOrderPayTypeAliPayJsApi:
		return p.getAliJsApiPrePayParams(ctx, payOrder, goodsName, openId)
	default:
		return nil, errors.New("无效的支付方式")
	}
}

// getWXAppletPrePayParams 获取微信小程序支付请求参数
func (p *PayDs) getWXAppletPrePayParams(ctx context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*bo.PayRequest, error) {
	if openId == "" {
		return nil, errors.New("openId为空")
	}
	return &bo.PayRequest{
		MerchantId:       int32(payOrder.PayMerchantId),
		OutTradeNo:       payOrder.GetSerialNumber(),
		Amount:           p.getRequestAmount(payOrder.TotalPayAmount),
		PayModeType:      payOrder.PayType.GetPayModel(),
		PayChannel:       payOrder.PayType.GetPayChannel(),
		RedirectUrl:      "",
		ClientIp:         helper.GetClientIp(ctx),
		ClientType:       "",
		OpenId:           openId,
		ExtJson:          nil,
		UseCustomProduct: true,
		NotifyURL:        p.GetNotifyUrl(payOrder),
		CustomProduct: &bo.PayCenterProduct{
			ID:         0,
			Name:       goodsName,
			Price:      p.getRequestAmount(payOrder.TotalPayAmount),
			Remark:     fmt.Sprintf("购买商品:%s", goodsName),
			CustomerId: uint32(payOrder.CustomerId),
			ShopId:     uint32(payOrder.ShopId),
		},
	}, nil
}

// getWxH5PrePayParams 获取微信H5支付请求参数
func (p *PayDs) getWxH5PrePayParams(ctx context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*bo.PayRequest, error) {
	prePayParams := &bo.PayRequest{
		MerchantId:       int32(payOrder.PayMerchantId),
		OutTradeNo:       payOrder.GetSerialNumber(),
		Amount:           p.getRequestAmount(payOrder.TotalPayAmount),
		PayModeType:      payOrder.PayType.GetPayModel(),
		PayChannel:       payOrder.PayType.GetPayChannel(),
		RedirectUrl:      "",
		ClientIp:         helper.GetClientIp(ctx),
		ClientType:       "ios",
		OpenId:           openId,
		ExtJson:          nil,
		UseCustomProduct: true,
		NotifyURL:        p.GetNotifyUrl(payOrder),
		CustomProduct: &bo.PayCenterProduct{
			ID:         0,
			Name:       goodsName,
			Price:      p.getRequestAmount(payOrder.TotalPayAmount),
			Remark:     fmt.Sprintf("购买商品"),
			CustomerId: uint32(payOrder.CustomerId),
			ShopId:     uint32(payOrder.ShopId),
		},
	}
	return prePayParams, nil
}

// getAlipayH5PrePayParams 获取支付宝H5支付请求参数
func (p *PayDs) alipayH5PrePayParams(ctx context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*bo.PayRequest, error) {
	prePayParams := &bo.PayRequest{
		MerchantId:       int32(payOrder.PayMerchantId),
		OutTradeNo:       payOrder.GetSerialNumber(),
		Amount:           p.getRequestAmount(payOrder.TotalPayAmount),
		PayModeType:      payOrder.PayType.GetPayModel(),
		PayChannel:       payOrder.PayType.GetPayChannel(),
		RedirectUrl:      p.getReturnUrl(payOrder),
		ClientIp:         helper.GetClientIp(ctx),
		ClientType:       "ios",
		OpenId:           openId,
		ExtJson:          nil,
		UseCustomProduct: true,
		NotifyURL:        p.GetNotifyUrl(payOrder),
		CustomProduct: &bo.PayCenterProduct{
			ID:         0,
			Name:       goodsName,
			Price:      p.getRequestAmount(payOrder.TotalPayAmount),
			Remark:     fmt.Sprintf("购买商品:%s", goodsName),
			CustomerId: uint32(payOrder.CustomerId),
			ShopId:     uint32(payOrder.ShopId),
		},
	}
	return prePayParams, nil
}

// getWxJsApiPrePayParams 获取微信jsapi支付请求参数
func (p *PayDs) getWxJsApiPrePayParams(ctx context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*bo.PayRequest, error) {
	prePayParams := &bo.PayRequest{
		MerchantId:       int32(payOrder.PayMerchantId),
		OutTradeNo:       payOrder.GetSerialNumber(),
		Amount:           p.getRequestAmount(payOrder.TotalPayAmount),
		PayModeType:      payOrder.PayType.GetPayModel(),
		PayChannel:       payOrder.PayType.GetPayChannel(),
		ClientIp:         helper.GetClientIp(ctx),
		ClientType:       "android",
		OpenId:           openId,
		ExtJson:          nil,
		UseCustomProduct: true,
		NotifyURL:        p.GetNotifyUrl(payOrder),
		CustomProduct: &bo.PayCenterProduct{
			ID:         0,
			Name:       goodsName,
			Price:      p.getRequestAmount(payOrder.TotalPayAmount),
			Remark:     fmt.Sprintf("购买商品:%s", goodsName),
			CustomerId: uint32(payOrder.CustomerId),
			ShopId:     uint32(payOrder.ShopId),
		},
	}
	return prePayParams, nil
}

// getAliJsApiPrePayParams 获取支付宝jsapi支付请求参数
func (p *PayDs) getAliJsApiPrePayParams(_ context.Context, payOrder *appDO.PayOrderDo, goodsName, openId string) (*bo.PayRequest, error) {
	prePayParams := &bo.PayRequest{
		MerchantId:  int32(payOrder.PayMerchantId),
		OutTradeNo:  payOrder.GetSerialNumber(),
		Amount:      p.getRequestAmount(payOrder.TotalPayAmount),
		PayModeType: payOrder.PayType.GetPayModel(),
		PayChannel:  payOrder.PayType.GetPayChannel(),
		OpenId:      openId,
		ExtJson: map[string]any{
			"product_code":    "JSAPI_PAY",
			"subject":         goodsName,
			"buyer_open_id":   openId,
			"timeout_express": "15m",
			"op_app_id":       p.conf.GetAlipayMiniProgram().GetAppId(),
			"seller_id":       p.conf.GetAlipayMiniProgram().GetSellerId(),
		},
		UseCustomProduct: true,
		NotifyURL:        p.GetNotifyUrl(payOrder),
		CustomProduct: &bo.PayCenterProduct{
			ID:         0,
			Name:       goodsName,
			Price:      p.getRequestAmount(payOrder.TotalPayAmount),
			Remark:     fmt.Sprintf("购买商品:%s", goodsName),
			CustomerId: uint32(payOrder.CustomerId),
			ShopId:     uint32(payOrder.ShopId),
		},
	}
	if payOrder.IsAlipayMiniOrder() {
		prePayParams.ExtJson["extend_params"] = map[string]string{"trade_component_order_id": payOrder.GetAlipayMiniOrder().GetMiniOrderNo()}
	}
	return prePayParams, nil
}

// getPayAmount 获取支付金额
func (p *PayDs) getRequestAmount(amount float64) int64 {
	payAmountDecimal := decimal.NewFromFloat(amount).Mul(decimal.NewFromFloat(100))
	return payAmountDecimal.IntPart()
}

// getReturnUrl 获取支付同步返回地址
func (p *PayDs) getReturnUrl(payOrder *appDO.PayOrderDo) string {
	return payOrder.ReturnUrl
}

// PrePayResponseHandle 预支付返参处理
func (p *PayDs) prePayResponseHandle(ctx context.Context, rsp *bo.PayResponse, payOrder *appDO.PayOrderDo) (*bo.PayResponse, error) {
	if payOrder.PayType == valobj.PayOrderPayTypeWxH5 {
		customerId := isolationcustomer.GetCustomerIdZero(ctx)
		shopId := isolationcustomer.GetShopIdZero(ctx)
		returnUrl := p.getReturnUrl(payOrder)
		if len(returnUrl) > 0 {
			returnUrl = fmt.Sprintf("%s?c=%s&token=%s", returnUrl, isolationcustomer.EncodeC(customerId, shopId), helper.GetClientToken(ctx))
			returnUrl = url.QueryEscape(returnUrl)
			returnUrl = p.conf.GetPayCenter().GetWxH5PayUrl() + returnUrl
			returnUrl = url.QueryEscape(returnUrl)
			rsp.PayParams = rsp.PayParams + "&redirect_url=" + returnUrl
			rsp.PayParams = url.QueryEscape(rsp.PayParams)
			rsp.PayParams = p.conf.GetPayCenter().GetWxH5PayUrl() + rsp.PayParams
		}
	}
	return rsp, nil
}

// PayNotifyHandle 支付回调解析
func (p *PayDs) PayNotifyHandle(ctx context.Context, req *http.Request) (*acldo.PayNotifyResponseDo, error) {
	payMerchantIdStr := req.URL.Query().Get("payMerchantId")
	payMerchantId, err := strconv.Atoi(payMerchantIdStr)
	if err != nil {
		return nil, apierr.ErrorException("payMerchantId参数错误:%s", payMerchantIdStr)
	}
	//目前通信秘钥固定且不可修改
	payConf, err := p.getPayRequestConf(ctx, payMerchantId)
	if err != nil {
		return nil, err
	}
	return p.repo.PayNotify(ctx, req, payConf)
}

// Refund 请求支付中心退款
func (p *PayDs) Refund(ctx context.Context, payOrder *appDO.PayOrderDo, in *PayDsRefundExtendParams) (*appDO.OrderRefundLogQueryDo, error) {
	//写入退款日志
	refundNo := helper.GetRefundNo(p.idGenerator)
	orderRefundLog, err := p.orderRefundLogRepo.Create(ctx, &appBo.OrderRefundLogCreateBo{
		PayOrderNumber:  payOrder.OrderNumber,
		OrderNumber:     in.OrderNumber,
		RefundAmount:    in.RefundAmount,
		RefundReason:    in.RefundReason,
		RefundNo:        refundNo,
		PaySerialNumber: payOrder.GetSerialNumber(),
	})
	if err != nil {
		return nil, err
	}

	//将退款结果，退款单号保存到退款日志中
	refundLogUpdateBo := &appBo.OrderRefundLogUpdateBo{
		Id:     orderRefundLog.Id,
		Status: valobj.OrderRefundLogStatusSuccess,
	}

	refundAmount := p.getRequestAmount(in.RefundAmount)
	if refundAmount > 0 {
		if payOrder.PayMerchantId > 0 {
			// 存在支付商户，表示是咱们平台收款，需要调用支付中心原路退款
			// 获取支付配置
			var payConf *bo.PayCenterRequestConfigBo
			payConf, err = p.getPayRequestConf(ctx, payOrder.PayMerchantId)
			if err != nil {
				return nil, err
			}

			//组装退款参数
			req := &bo.RefundRequest{
				OrderId:    payOrder.OutTradeNo,
				Amount:     refundAmount,
				Reason:     in.RefundReason,
				MerchantId: int32(payOrder.PayMerchantId),
				OutTradeNo: payOrder.GetSerialNumber(),
				RefundNo:   refundNo,
			}
			if err = req.Validate(); err != nil {
				return nil, err
			}

			//发起退款
			var rsp *bo.RefundResponse
			rsp, err = p.repo.Refund(ctx, req, payConf)
			if err != nil {
				return nil, fmt.Errorf("发起退款失败:%s", errors2.FromError(err))
			}
			if rsp.Status.IsFail() {
				refundLogUpdateBo.Status = valobj.OrderRefundLogStatusFail
				_, _ = p.orderRefundLogRepo.Update(ctx, refundLogUpdateBo)
				return nil, errors.New("发起退款失败")
			}
			refundLogUpdateBo.OutRefundNo = rsp.RefundId
		} else if !payOrder.PayeeType.IsThirdUrl() {
			return nil, errors.New("该订单不能发起原路退款")
		}
		// 第三方收款页面自己收款，这里直接退款成功，后续再通知商户进行退款
	}

	_, err = p.orderRefundLogRepo.Update(ctx, refundLogUpdateBo)
	if err != nil {
		p.log.Errorf("[refund]order_refund_log:id:%d修改信息失败:%s", orderRefundLog.Id, err.Error())
		return nil, errors.New("系统异常，请联系客服处理")
	}
	return orderRefundLog, nil
}

// Close 请求支付中心关闭订单
func (p *PayDs) Close(ctx context.Context, payOrder *appDO.PayOrderDo) error {
	if payOrder.PayMerchantId <= 0 {
		return errors.New("该订单无法关闭")
	}

	// 获取支付配置
	payConf, err := p.getPayRequestConf(ctx, payOrder.PayMerchantId)
	if err != nil {
		return err
	}

	//组装退款参数
	req := &bo.CloseRequest{
		OrderId: payOrder.OutTradeNo,
	}
	if err = req.Validate(); err != nil {
		return err
	}

	//关闭订单
	_, err = p.repo.Close(ctx, req, payConf)
	if err != nil {
		err = fmt.Errorf("关闭订单失败:%s", err)
	}
	return err
}

// payConfigValidate 支付配置校验
func (p *PayDs) payConfigValidate(ctx context.Context, payType valobj.PayOrderPayTypeObj, in *bo.PayCenterRequestConfigBo) (*bo.PayCenterRequestConfigBo, error) {
	if in.MerchantId == 0 {
		return nil, errors.New("支付商户不能为空")
	}
	//小程序支付独立的支付配置
	if payType == valobj.PayOrderPayTypeWxApplet {
		in.MerchantId = int(p.conf.GetWechatApplet().GetPayMerchantId())
		return in, nil
	}
	if payType.IsAliPayJsApi() {
		in.MerchantId = int(p.conf.GetAlipayMiniProgram().GetPayMerchantId())
		return in, nil
	}

	//校验是否开启支付渠道
	if payType.GetPayConfigChannel() == valobj.PayConfigChannelTypeWxPay && in.WxPay == valobj.PayMerchantPayStatusDisable {
		return nil, errors.New("支付方式已被禁用")
	}
	if payType.GetPayConfigChannel() == valobj.PayConfigChannelTypeAliPay && in.Alipay == valobj.PayMerchantPayStatusDisable {
		return nil, errors.New("支付方式已被禁用")
	}

	//如果使用的是默认支付商户，则不校验支付商户，交给支付中心处理
	if int(p.conf.GetPayCenter().GetMerchantId()) == in.MerchantId {
		return in, nil
	}

	payMerchant, _ := p.payMerchantRepo.FindByPayMerchantId(ctx, in.MerchantId)
	if payMerchant == nil || payMerchant.IsDelete() || payMerchant.MerchantId == 0 {
		return nil, errors.New("支付配置有误")
	}
	//校验支付渠道是否已启用
	payConfigParams := &appBo.PayConfigBo{
		PayChannel:    payType.GetPayConfigChannel(),
		PayMerchantId: payMerchant.Id,
	}
	payChannel, _ := p.payConfigRepo.Find(ctx, payConfigParams)
	//如果没有配置支付渠道,使用默认配置
	if payChannel == nil || payChannel.Status != valobj.PayConfigStatusEnable {
		in.MerchantId = int(p.conf.PayCenter.GetMerchantId())
		in.PrivateKey = p.conf.PayCenter.GetPrivateKey()
		in.PayPublicKey = p.conf.PayCenter.GetPublicKey()
	}
	return in, nil
}

// getPayRequestConf 获取支付请求的加解密配置
func (p *PayDs) getPayRequestConf(ctx context.Context, payMerchantId int) (*bo.PayCenterRequestConfigBo, error) {
	payMerchant := &appDO.PayMerchantDo{}
	//基础配置的支付商户
	if payMerchantId == int(p.conf.PayCenter.GetMerchantId()) {
		return &bo.PayCenterRequestConfigBo{
			MerchantId:   int(p.conf.PayCenter.GetMerchantId()),
			PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
			PayPublicKey: p.conf.PayCenter.GetPublicKey(),
		}, nil
	}
	payMerchant, _ = p.payMerchantRepo.FindByPayMerchantId(ctx, payMerchantId)
	if payMerchant == nil {
		return nil, errors.New("支付配置不存在")
	}
	return &bo.PayCenterRequestConfigBo{
		MerchantId:   payMerchant.MerchantId,
		PrivateKey:   payMerchant.PrivateKey,
		PayPublicKey: payMerchant.PayPublicKey,
		Alipay:       payMerchant.Alipay,
		WxPay:        payMerchant.WxPay,
	}, nil
}

// getPayRequestConfByPayType 获取支付请求的加解密配置
func (p *PayDs) getPayRequestConfByPayType(ctx context.Context, channelType valobj.PayConfigChannelTypeObj) (*bo.PayCenterRequestConfigBo, error) {
	payMerchant := &appDO.PayMerchantDo{}
	//先查询是否有支付配置
	payChannel, err := p.payConfigRepo.FindChannel(ctx, channelType)
	if err != nil {
		return nil, err
	}
	//小程序支付必须要有支付配置，不能启用默认支付配置
	if channelType.IsApplet() && (payChannel == nil || !payChannel.Status.IsValid()) {
		return nil, apierr.ErrorDbNotFound("未找到支付配置")
	}

	//有支付配置，取支付配置
	if payChannel != nil {
		payMerchant, _ = p.payMerchantRepo.FindById(ctx, payChannel.PayMerchantId)
		if payMerchant == nil {
			return nil, apierr.ErrorDbNotFound("未找到支付配置")
		}
		// 判断支付配置是否被禁用
		if (channelType.IsWx() && !payMerchant.WxPay.IsEnable()) || (channelType.IsAlipay() && !payMerchant.Alipay.IsEnable()) {
			return nil, apierr.ErrorNotAllow("支付方式已被禁用")
		}
		//如果没有被禁用，判断支付渠道是否启用，没有启用要使用默认的支付配置
		if !payChannel.Status.IsValid() {
			//小程序是特殊情况，只有官方企业才能使用小程序的默认配置
			//if channelType.IsApplet() {
			//	//不是官方企业，则不能使用支付配置
			//	if customerId := isolationcustomer.GetCustomerIdZero(ctx); customerId != isolationcustomer.SaasPlatformCustomer {
			//		return nil, apierr.ErrorNotAllow("小程序支付已被禁用")
			//	}
			//	//判断是微信小程序还是支付宝小程序,并返回对应的默认支付配置
			//	if channelType.IsWxApplet() {
			//		return &bo.PayCenterRequestConfigBo{
			//			MerchantId:   int(p.conf.GetWechatApplet().GetPayMerchantId()),
			//			PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
			//			PayPublicKey: p.conf.PayCenter.GetPublicKey(),
			//		}, nil
			//	} else if channelType.IsAlipayApplet() {
			//		return &bo.PayCenterRequestConfigBo{
			//			MerchantId:   int(p.conf.GetAlipayMiniProgram().GetPayMerchantId()),
			//			PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
			//			PayPublicKey: p.conf.PayCenter.GetPublicKey(),
			//		}, nil
			//	}
			//} else {
			//返回默认的支付配置
			return &bo.PayCenterRequestConfigBo{
				MerchantId:   int(p.conf.PayCenter.GetMerchantId()),
				PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
				PayPublicKey: p.conf.PayCenter.GetPublicKey(),
			}, nil
			//}

		}
		return &bo.PayCenterRequestConfigBo{
			MerchantId:   payMerchant.MerchantId,
			PrivateKey:   payMerchant.PrivateKey,
			PayPublicKey: payMerchant.PayPublicKey,
			Alipay:       payMerchant.Alipay,
			WxPay:        payMerchant.WxPay,
		}, nil
	} else {
		//没有配置支付渠道
		//先查询支付配置
		payMerchant, _ = p.payMerchantRepo.Find(ctx, channelType.GetPayMerchantPayType())
		//如果有配置，这种情况属于用户勾选了支付方式，但是没有配置支付配置，则使用默认的支付配置的情况
		if payMerchant != nil {
			//判断支付配置是否被禁用
			if (channelType.IsWx() && !payMerchant.WxPay.IsEnable()) || (channelType.IsAlipay() && !payMerchant.Alipay.IsEnable()) {
				return nil, apierr.ErrorNotAllow("支付方式已被禁用")
			}
		}
		//小程序是特殊情况，只有官方企业才能使用小程序的默认配置
		//if channelType.IsApplet() {
		//	//不是官方企业，则不能使用支付配置
		//	if customerId := isolationcustomer.GetCustomerIdZero(ctx); customerId != isolationcustomer.SaasPlatformCustomer {
		//		return nil, apierr.ErrorNotAllow("小程序支付已被禁用")
		//	}
		//	//判断是微信小程序还是支付宝小程序,并返回对应的默认支付配置
		//	if channelType.IsWxApplet() {
		//		return &bo.PayCenterRequestConfigBo{
		//			MerchantId:   int(p.conf.GetWechatApplet().GetPayMerchantId()),
		//			PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
		//			PayPublicKey: p.conf.PayCenter.GetPublicKey(),
		//		}, nil
		//	} else if channelType.IsAlipayApplet() {
		//		return &bo.PayCenterRequestConfigBo{
		//			MerchantId:   int(p.conf.GetAlipayMiniProgram().GetPayMerchantId()),
		//			PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
		//			PayPublicKey: p.conf.PayCenter.GetPublicKey(),
		//		}, nil
		//	}
		//} else {
		//返回默认的H5支付配置
		return &bo.PayCenterRequestConfigBo{
			MerchantId:   int(p.conf.PayCenter.GetMerchantId()),
			PrivateKey:   p.conf.PayCenter.GetPrivateKey(),
			PayPublicKey: p.conf.PayCenter.GetPublicKey(),
		}, nil
		//}
	}
}

// goodsNameFormat 商品名称格式化
func (p *PayDs) goodsNameFormat(goodsName string) string {
	//判断商品名称长度是否超过20个字符，超过则截取20个字符
	if len([]rune(goodsName)) > 20 {
		return string([]rune(goodsName)[:20])
	}
	return goodsName
}

func (p *PayDs) GetNotifyUrl(payOrder *appDO.PayOrderDo) string {
	return fmt.Sprintf("%s?customerId=%d&shopId=%d&payMerchantId=%d", p.conf.PayCenter.GetNotifyUrl(), payOrder.CustomerId, payOrder.ShopId, payOrder.PayMerchantId)
}
