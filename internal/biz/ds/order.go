package ds

import (
	bizBo "cardMall/internal/biz/bo"
	bizDo "cardMall/internal/biz/do"
	bizRepo "cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	adminDo "cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/helper"
	"codeup.aliyun.com/5f9118049cffa29cfdd3be1c/util/idgenerator"
	"context"
	"errors"
	"fmt"
	"github.com/shopspring/decimal"
	"math"
	"strings"
	"time"
)

type OrderDs struct {
	payOrderRepo         repository.PayOrderRepo
	orderRepo            repository.OrderRepo
	orderGoodsRepo       bizRepo.OrderGoodsRepo
	goodsRepo            bizRepo.GoodsRepo
	goodsSkuRepo         bizRepo.GoodsSkuRepo
	userOrderAddressRepo bizRepo.OrderUserAddressRepo
	orderOperatorLogRepo bizRepo.OrderOperatorLogRepo
	idGenerator          *idgenerator.Generator
	conf                 *conf.Bootstrap
}

type CreatePayOrderParams struct {
	UserId                 int
	Num                    int
	CouponCodeId           int
	CouponId               int
	PayAmount              float64
	CouponDiscountAmount   float64
	PayOrderType           valobj.PayOrderTypeObj
	IntegralLog            *adminDo.UserIntegralLogDo
	PayExpireTime          int
	ThirdTradeNo           string
	SiteId                 int
	TotalAmount            float64
	CardCouponNumber       string // 券码兑换
	Status                 valobj.PayOrderStatusObj
	PayType                valobj.PayOrderPayTypeObj
	SettlementType         valobj.PayOrderSettlementTypeObj
	FreightFee             float64
	GoodsPayAmount         float64
	IntegralDiscountAmount float64
	CardGiftAmount         float64
	PayeeType              valobj.PayOrderPayeeTypeObj
	ActivityId             int
}

type CreateOrderParams struct {
	UserId         int
	Num            int
	Account        string
	PayAmount      float64
	Integral       int
	PayOrderNumber string
	OrderType      valobj.OrderTypeObj
	BaseGoodsInfo  *do.BaseGoodsDo
	BrandInfo      *do.GoodsBrandInfoDo
	GoodsInfo      *do.GoodsInfoDo
	ExtraInfo      string
	SiteId         int
}

// CreateQZOrderParams 创建千猪订单参数
type CreateQZOrderParams struct {
	UserId         int
	Num            int
	PayAmount      float64
	Integral       int
	PayOrderNumber string
	OrderType      valobj.OrderTypeObj
	Goods          []*CreateQZOrderGoodsParams
	SiteId         int
	TotalAmount    float64
}

type CreateQZOrderGoodsParams struct {
	Name          string
	Quantity      int
	SalePrice     float64
	Image         string
	GoodsId       int
	SkuNo         string
	PayAmount     float64
	OriginPrice   float64
	SupplierPrice float64
}

// CreateMTOrderParams 创建美团订单参数
type CreateMTOrderParams struct {
	UserId         int
	Num            int
	PayAmount      float64
	Integral       int
	PayOrderNumber string
	OrderType      valobj.OrderTypeObj
	Goods          []*CreateMTOrderGoodsParams
	SiteId         int
	TotalAmount    float64
}

type CreateMTOrderGoodsParams struct {
	Name      string
	Quantity  int
	SalePrice float64
}

type CreateOrderNewParams struct {
	UserId               int
	UserName             string
	TotalAmount          float64
	PayAmount            float64
	Integral             int
	CouponDiscountAmount float64
	PayOrderNumber       string
	OrderType            valobj.OrderTypeObj
	SiteId               int
	SupplierOrder        []*SupplierOrderParams
	UserAddress          *do.UserAddressDo
	RechargeAccount      string

	IsPaid          bool // 是否已支付
	OperaLogContent string
	ExtType         valobj.OrderExtTypeObj
}

func (c *CreateOrderNewParams) GetDeliverExpireTime(supplierId int) int {
	for _, supplierOrder := range c.SupplierOrder {
		if supplierOrder.SupplierId == supplierId {
			return supplierOrder.GetDeliverExpireTime()
		}
	}
	return 0
}

type OrderSkuParams struct {
	GoodsInfo         *do.GoodsInfoDo
	SkuInfo           *do.GoodsSkuDo
	SupplierSkuInfo   *bizDo.SupplierGoodsSkuDo
	SupplierGoodsInfo *bizDo.SupplierGoodsDo
	Num               int
	BrandName         string
	CategoryName      string
	ProductAuthorize  *bizDo.ProductAuthorizeDo
}

// GetSupplierCostPrice 获取供应商供应价
func (c *OrderSkuParams) GetSupplierCostPrice() float64 {
	return c.SupplierSkuInfo.SupplierPrice
}

// GetCustomerCostPrice 获取企业成本价
func (c *OrderSkuParams) GetCustomerCostPrice() float64 {
	// 商城自己的供应商，没有授权关系直接返回0
	if c.ProductAuthorize == nil {
		return 0
	}
	// 企业的供应商，虽然有授权管理，但是没有成本价，也是0
	if c.SupplierGoodsInfo.CustomerID == c.GoodsInfo.CustomerId && c.SupplierGoodsInfo.ShopID == 0 {
		return 0
	}
	return c.ProductAuthorize.SrcSupplierPrice
}

// GetShopCostPrice 获取商城成本价
func (c *OrderSkuParams) GetShopCostPrice() float64 {
	if c.ProductAuthorize == nil {
		return c.SupplierSkuInfo.SupplierPrice
	}
	return c.ProductAuthorize.AuthorizePrice
}

func (c *CreatePayOrderParams) validate() error {
	if c.UserId <= 0 {
		return errors.New("用户不存在")
	}
	if c.Num <= 0 {
		return errors.New("商品数量不能小于等于0")
	}
	if c.PayAmount < constants.PayAmountMin && c.CardCouponNumber == "" {
		return fmt.Errorf("支付金额不能小于%v", constants.PayAmountMin)
	}
	if !c.PayOrderType.IsValid() {
		return errors.New("订单类型错误")
	}
	//if c.PayOrderType == valueobj.PayOrderTypeRecharge {
	//	if c.GoodsInfo == nil {
	//		return errors.New("商品已下架")
	//	}
	//	if c.GoodsInfo.ProductId == 0 {
	//		return errors.New("商品配置有误")
	//	}
	//	if !c.GoodsInfo.Status.IsEnable() {
	//		return errors.New("商品已下架")
	//	}
	//}
	return nil
}

func (c *CreatePayOrderParams) getSettlementType() valobj.PayOrderSettlementTypeObj {
	// 积分+钱购商品、积分购商品、换货订单需要外部直接设置结算类型，从这里直接返回，其他结算类型则走后面逻辑
	if c.SettlementType != valobj.PayOrderSettlementTypeUnknown {
		return c.SettlementType
	}
	//非积分+钱购商品或者积分购商品
	if c.CardGiftAmount > 0 {
		if c.IntegralDiscountAmount > 0 {
			if c.PayAmount > 0 {
				return valobj.PayOrderSettlementTypeCardGiftAndCashAndIntegral
			} else {
				return valobj.PayOrderSettlementTypeCardGiftAndIntegral
			}
		} else {
			if c.PayAmount > 0 {
				return valobj.PayOrderSettlementTypeCardGiftAndCash
			} else {
				return valobj.PayOrderSettlementTypeCardGift
			}
		}
	} else if c.IntegralDiscountAmount > 0 && c.PayAmount == 0 {
		return valobj.PayOrderSettlementTypeIntegral
	} else if c.CardCouponNumber != "" {
		return valobj.PayOrderSettlementTypeCardBatchCoupon
	} else if c.IntegralDiscountAmount > 0 && c.PayAmount > 0 {
		return valobj.PayOrderSettlementTypeCashAndIntegral
	} else if c.PayAmount > 0 {
		return valobj.PayOrderSettlementTypeCash
	} else {
		return valobj.PayOrderSettlementTypeUnknown
	}
}

// CreatePayOrderBoInit 构建支付单 Bo
func (c *CreatePayOrderParams) CreatePayOrderBoInit(idGenerator *idgenerator.Generator) (*bizBo.PayOrderCreateBo, error) {
	if err := c.validate(); err != nil {
		return nil, err
	}
	orderNumber := helper.GetPayOrderNumber(idGenerator)
	var payOrder = &bizBo.PayOrderCreateBo{
		OrderNumber:            orderNumber,
		UserId:                 c.UserId,
		TotalAmount:            c.TotalAmount,
		TotalPayAmount:         c.PayAmount,
		TotalPayIntegral:       0,
		Num:                    c.Num,
		Status:                 valobj.PayOrderStatusUnpaid,
		CouponCodeId:           c.CouponCodeId,
		CouponDiscountAmount:   c.CouponDiscountAmount,
		BrandId:                0,
		OrderType:              c.PayOrderType,
		SiteId:                 c.SiteId,
		ExpireTime:             c.PayExpireTime,
		ThirdTradeNo:           c.ThirdTradeNo,
		SettlementType:         valobj.PayOrderSettlementTypeCash,
		PayType:                valobj.PayOrderPayTypeUnknown,
		CouponId:               c.CouponId,
		CardCouponNumber:       c.CardCouponNumber,
		FreightFee:             c.FreightFee,
		GoodsPayAmount:         c.GoodsPayAmount,
		IntegralDiscountAmount: c.IntegralDiscountAmount,
		CardGiftAmount:         c.CardGiftAmount,
		PayeeType:              c.PayeeType,
		ActivityId:             c.ActivityId,
	}
	if c.PayType != valobj.PayOrderPayTypeUnknown {
		payOrder.PayType = c.PayType
	}
	payOrder.SettlementType = c.getSettlementType()
	if c.PayAmount == 0 {
		payOrder.Status = valobj.PayOrderStatusPaid
	}
	if c.Status != 0 {
		payOrder.Status = c.Status
	}
	//使用积分的情况
	if c.IntegralLog != nil {
		payOrder.UserIntegralLogId = c.IntegralLog.Id
		payOrder.TotalPayIntegral = c.IntegralLog.Integral
		// 只有商品是现金结算的时候，才能使用用户的积分，积分+钱购、积分兑换、券码兑换都不能使用积分
		if payOrder.SettlementType == valobj.PayOrderSettlementTypeCash {
			payOrder.SettlementType = valobj.PayOrderSettlementTypeCashAndIntegral
		}
	}
	//订单有效期
	now := time.Now()
	payOrder.ExpireTime = int(now.Add(constants.PayOrderExpireTime).Unix())
	//秒杀活动订单，订单有效期时五分钟
	if payOrder.ActivityId > 0 {
		payOrder.ExpireTime = int(now.Add(constants.PayOrderActivityExpireTime).Unix())
	}
	if c.PayExpireTime > 0 {
		payOrder.ExpireTime = c.PayExpireTime
	}
	//第三方订单号
	if c.ThirdTradeNo != "" {
		payOrder.ThirdTradeNo = c.ThirdTradeNo
	}

	return payOrder, nil
}

func (c *CreateOrderParams) validate() error {
	if c.UserId <= 0 {
		return errors.New("用户不存在")
	}
	if c.Num <= 0 {
		return errors.New("商品数量不能小于等于0")
	}
	if c.PayAmount < constants.PayAmountMin {
		return fmt.Errorf("支付金额不能小于%v", constants.PayAmountMin)
	}
	if c.PayOrderNumber == "" {
		return errors.New("订单号不能为空")
	}
	if !c.OrderType.IsValid() {
		return errors.New("订单类型错误")
	}

	return nil
}

// CreateThirdParams 创建三方订单参数
type CreateThirdParams struct {
	UserId         int
	Num            int
	PayAmount      float64
	Integral       int
	PayOrderNumber string
	OrderType      valobj.OrderTypeObj
	Goods          []*CreateThirdOrderGoodsParams
	SiteId         int
	TotalAmount    float64
}

type CreateThirdOrderGoodsParams struct {
	Quantity      int
	SalePrice     float64
	PayAmount     float64
	SkuNo         string
	GoodsId       int
	GoodsName     string
	GoodsImage    string
	GoodsSkuName  string
	OriginPrice   float64
	SupplierPrice float64
}

func NewOrderDs(
	payOrderRepo repository.PayOrderRepo,
	orderRepo repository.OrderRepo,
	goodsRepo bizRepo.GoodsRepo,
	goodsSkuRepo bizRepo.GoodsSkuRepo,
	orderGoodsRepo bizRepo.OrderGoodsRepo,
	userOrderAddressRepo bizRepo.OrderUserAddressRepo,
	orderOperatorLogRepo bizRepo.OrderOperatorLogRepo,
	idGenerator *idgenerator.Generator,
	conf *conf.Bootstrap,
) *OrderDs {
	return &OrderDs{
		payOrderRepo:         payOrderRepo,
		orderRepo:            orderRepo,
		goodsRepo:            goodsRepo,
		goodsSkuRepo:         goodsSkuRepo,
		orderGoodsRepo:       orderGoodsRepo,
		userOrderAddressRepo: userOrderAddressRepo,
		orderOperatorLogRepo: orderOperatorLogRepo,
		idGenerator:          idGenerator,
		conf:                 conf,
	}
}

// PreGoodsValidate 下单前商品校验
func (o *OrderDs) PreGoodsValidate(goodsInfo *do.GoodsInfoDo) error {
	if goodsInfo == nil {
		return errors.New("商品不存在或已被删除")
	}
	if !goodsInfo.Status.Enable() {
		return errors.New("商品已下架")
	}
	return nil
}

// PreGoodsSkuValidate 下单前商品sku校验
func (o *OrderDs) PreGoodsSkuValidate(sku *do.GoodsSkuDo, goodsType valobj.GoodsTypeObj, num int) error {
	if !sku.Enable() {
		return errors.New("商品已下架")
	}
	if goodsType == valobj.GoodsTypeVir {
		if sku.ProductID == "" {
			return errors.New("商品配置有误")
		}
	}
	//if sku.NumLimit > 0 && num > sku.NumLimit {
	//	return fmt.Errorf("最多只能购买:%d件商品", sku.NumLimit)
	//}
	return nil
}

// PreGoodsSkuPurchaseLimitValidate 下单前商品sku限购校验
func (o *OrderDs) PreGoodsSkuPurchaseLimitValidate(ctx context.Context, sku *do.GoodsSkuDo, userId int, num int, orderNumber string) error {
	if sku.NumLimit == 0 || sku.PurchaseLimitCycle == valobj.GoodsSkuPurchaseLimitCycleUnKnown {
		return nil
	}
	startTime, endTime := sku.GetPurchaseLimitCycleTime()
	if startTime <= 0 || endTime <= 0 {
		return errors.New("商品限购配置有误")
	}
	if sku.PurchaseLimitCycle == valobj.GoodsSkuPurchaseLimitCycleCustom {
		if time.Now().Unix() > int64(endTime) {
			return nil
		}
	}
	buySum, _ := o.orderRepo.UserSkuQuantitySum(ctx, &bo.UserSkuQuantitySumBo{
		UserId:      userId,
		GoodsId:     sku.GoodsID,
		GoodsSkuNo:  sku.SkuNo,
		StartTime:   startTime,
		EndTime:     endTime,
		OrderNumber: orderNumber,
	})

	if (buySum + num) > sku.NumLimit {
		return fmt.Errorf("%s-%s期间内下单的商品数量不能超过%d件,您已下单%d件,请修改商品数量或取消待支付的订单", helper.GetTimeDate(startTime), helper.GetTimeDate(endTime), sku.NumLimit, buySum)
	}
	return nil
}

// PreBrandValidate 下单前商品品牌校验
func (o *OrderDs) PreBrandValidate(brandInfo *do.GoodsBrandInfoDo) error {
	//if brandInfo == nil || !brandInfo.Status.IsEnable() {
	//	return errors.New("商品已下架")
	//}
	if brandInfo == nil {
		return errors.New("品牌不存在")
	}
	//if !brandInfo.Status.IsEnable() {
	//	return errors.New("商品已下架")
	//}
	return nil
}

// PreBaseGoodsValidate 下单前映射商品校验
func (o *OrderDs) PreBaseGoodsValidate(baseGoodsInfo *do.BaseGoodsDo, account string) error {
	if baseGoodsInfo == nil || !baseGoodsInfo.Status.Enable() {
		return errors.New("商品已下架")
	}
	if baseGoodsInfo.Type.IsUnknown() {
		return errors.New("商品配置有误")
	}

	accountTypes := baseGoodsInfo.ToAccountType()
	if len(accountTypes) > 0 && baseGoodsInfo.Type.IsRecharge() {
		accountValidateRes := false
		for _, accountType := range accountTypes {
			if accountType.Validate(account) {
				accountValidateRes = true
				break
			}
		}
		if !accountValidateRes {
			accountTypesStr := make([]string, 0, len(accountTypes))
			for _, accountType := range accountTypes {
				accountTypesStr = append(accountTypesStr, accountType.String())
			}
			return fmt.Errorf("商品只支持【%s】充值", strings.Join(accountTypesStr, "/"))
		}
	}

	//if baseGoodsInfo.Type.IsCard() && baseGoodsInfo.CardExpireTime < int(time.Now().Unix()) {
	//	return errors.New("商品已过期")
	//}
	return nil
}

// PreSupplierGoodsValidate 下单前供应商商品校验
func (o *OrderDs) PreSupplierGoodsValidate(supplierGoods *bizDo.SupplierGoodsDo, provinceInfo *do.AreaDo) error {
	if supplierGoods == nil || !supplierGoods.Status.IsOnline() {
		return errors.New("商品已下架")
	}
	if provinceInfo.Id > 0 && len(supplierGoods.GetNotSaleArea()) > 0 {
		if helper.InSliceInt(provinceInfo.Id, supplierGoods.GetNotSaleArea()) {
			return fmt.Errorf("不支持【%s】地区配送", provinceInfo.Name)
		}
	}
	return nil
}

// PreSupplierGoodsSkuValidate 下单前供应商商品sku校验
func (o *OrderDs) PreSupplierGoodsSkuValidate(supplierGoods *bizDo.SupplierGoodsSkuDo, quantity int) error {
	if supplierGoods == nil || supplierGoods.IsDelete() {
		return errors.New("商品已下架")
	}
	if quantity > 0 && supplierGoods.Stock < quantity {
		return errors.New("商品库存不足")
	}
	return nil
}

// GetPayAmount 获取下单时的支付金额，支付积分，优惠券相关信息
// 返回参数: 实际支付金额， 实际优惠金额，实际使用积分
func (o *OrderDs) GetPayAmount(price, couponPrice float64, integral int) (payAmount float64, discountAmount float64, discountIntegral int) {

	//商品价格为0的情况
	if price == 0 {
		return constants.PayAmountMin, 0, 0
	}

	priceDecimal := decimal.NewFromFloat(price)
	couponPriceDecimal := decimal.NewFromFloat(couponPrice)

	integralAmountDecimal := decimal.NewFromInt32(int32(integral)).Div(decimal.NewFromInt32(valobj.IntegralRate))

	//优惠券抵扣金额大于商品总价，最低支付PayAmountMin元
	if price <= couponPrice {
		payAmount = constants.PayAmountMin
		discountAmountDecimal := priceDecimal.Sub(decimal.NewFromFloat(constants.PayAmountMin))
		discountAmount, _ = discountAmountDecimal.Float64()
		discountIntegral = 0
		return
	}

	//全额扣除优惠券
	payAmountDecimal := priceDecimal.Sub(couponPriceDecimal)
	payAmount, _ = payAmountDecimal.Float64()

	//全额扣除积分以后，检查支付金额结果
	prePayAmountDecimal := payAmountDecimal.Sub(integralAmountDecimal)
	prePayAmount, _ := prePayAmountDecimal.Float64()

	//积分抵扣金额大于支付金额
	if prePayAmount < constants.PayAmountMin {
		discountIntegralAmount := int(math.Ceil(payAmount))
		payAmount = constants.PayAmountMin
		discountAmount = couponPrice
		discountIntegral = discountIntegralAmount * valobj.IntegralRate
		return
	}

	//正常情况，总支付金额-（优惠券+积分） > PayAmountMin的情况
	payAmount = prePayAmount

	return payAmount, couponPrice, integral
}

// GetPayAmountV2 获取下单时的支付金额，支付积分，优惠券相关信息
// 返回参数: 实际支付金额， 实际优惠金额，实际使用积分抵扣金额
func (o *OrderDs) GetPayAmountV2(price, couponPrice float64, integralAmount float64) (payAmount float64, discountAmount float64, discountIntegralAmount float64) {
	//商品价格为0的情况
	if price == 0 {
		return constants.PayAmountMin, 0, 0
	}

	priceDecimal := decimal.NewFromFloat(price)
	couponPriceDecimal := decimal.NewFromFloat(couponPrice)

	integralAmountDecimal := decimal.NewFromFloat(integralAmount)

	//优惠券抵扣金额大于商品总价，最低支付PayAmountMin元
	if price <= couponPrice {
		payAmount = constants.PayAmountMin
		discountAmountDecimal := priceDecimal.Sub(decimal.NewFromFloat(constants.PayAmountMin))
		discountAmount, _ = discountAmountDecimal.Float64()
		integralAmount = 0
		return
	}

	//全额扣除优惠券
	payAmountDecimal := priceDecimal.Sub(couponPriceDecimal)
	payAmount, _ = payAmountDecimal.Float64()

	//全额扣除积分以后，检查支付金额结果
	prePayAmountDecimal := payAmountDecimal.Sub(integralAmountDecimal)
	prePayAmount, _ := prePayAmountDecimal.Float64()
	discountIntegralAmount = integralAmount
	//积分抵扣金额大于支付金额
	if prePayAmount < constants.PayAmountMin {
		discountIntegralAmount, _ = decimal.NewFromFloat(payAmount).Sub(decimal.NewFromFloat(constants.PayAmountMin)).Float64()
		payAmount = constants.PayAmountMin
		discountAmount = couponPrice
		return
	}
	//正常情况，总支付金额-（优惠券+积分） > PayAmountMin的情况
	payAmount = prePayAmount

	return payAmount, couponPrice, discountIntegralAmount
}

// GetChildOrderPayAmount 获取子订单实际支付金额
// totalAmount 订单总金额
// totalPayAmount 订单总支付金额
// orderTotalAmount 子订单总金额
func (o *OrderDs) GetChildOrderPayAmount(totalAmount, totalPayAmount, orderTotalAmount float64) (payAmountAverage float64) {
	if totalAmount < constants.PayAmountMin {
		return
	}
	if totalAmount == 0 {
		return 0
	}
	percent := decimal.NewFromFloat(orderTotalAmount).Div(decimal.NewFromFloat(totalAmount)).Mul(decimal.NewFromFloat(totalPayAmount))
	//四舍五入保留两位小数
	payAmountAverage = percent.Round(2).InexactFloat64()
	return
}

// GetChildOrderIntegral 获取子订单使用积分
// totalAmount 订单总金额
// orderTotalAmount 子订单总金额
// totalIntegral 订单总使用积分
func (o *OrderDs) GetChildOrderIntegral(totalAmount, orderTotalAmount float64, totalIntegral int) (integralAverage int) {
	if totalAmount < constants.PayAmountMin {
		return
	}
	if totalAmount == 0 {
		return 0
	}
	percent := decimal.NewFromFloat(orderTotalAmount).Div(decimal.NewFromFloat(totalAmount)).Mul(decimal.NewFromInt32(int32(totalIntegral)))
	//四舍五入保留整数
	integralAverage = int(percent.Round(0).IntPart())
	return
}

// GetIntegralDiscountAmount 获取积分抵扣金额
func (o *OrderDs) GetIntegralDiscountAmount(integral int) (r float64) {
	if integral > 0 {
		r, _ = decimal.NewFromInt32(int32(integral)).Div(decimal.NewFromInt(valobj.IntegralRate)).Float64()
	}
	return r
}

// GetChildOrderCouponDiscountAmount 获取子订单优惠券抵扣金额
func (o *OrderDs) GetChildOrderCouponDiscountAmount(totalAmount, orderTotalAmount float64, couponDiscountAmount float64) (r float64) {
	if totalAmount < constants.PayAmountMin || couponDiscountAmount == 0 {
		return
	}
	percent := decimal.NewFromFloat(orderTotalAmount).Div(decimal.NewFromFloat(totalAmount)).Mul(decimal.NewFromFloat(couponDiscountAmount))
	//四舍五入保留两位小数
	r = percent.Round(2).InexactFloat64()
	return
}

// BuildSupplierOrder 构建供应商订单参数
func (o *OrderDs) BuildSupplierOrder(in []*OrderSkuParams) (SupplierOrderParamsSlice, int) {
	paramsMap := make(map[int]*SupplierOrderParams)
	params := make([]*SupplierOrderParams, 0)
	skuNum := 0
	for _, g := range in {
		v, ok := paramsMap[g.GoodsInfo.SupplierId]
		if !ok {
			v = &SupplierOrderParams{
				Goods:       make([]*OrderSkuParams, 0),
				TotalAmount: 0,
				SupplierId:  g.GoodsInfo.SupplierId,
				TotalNum:    0,
			}
		}
		v.Goods = append(v.Goods, g)
		totalAmount, _ := decimal.NewFromFloat(g.SkuInfo.SalePrice).Mul(decimal.NewFromInt32(int32(g.Num))).Float64()
		v.TotalAmount += totalAmount
		v.TotalNum += g.Num
		paramsMap[g.GoodsInfo.SupplierId] = v
		skuNum += g.Num
	}
	for _, v := range paramsMap {
		params = append(params, v)
	}
	return params, skuNum
}

func (o *OrderDs) GetOrderGoodsTotalAmount(salePrice float64, quantity int) (totalAmount float64) {
	totalAmount = decimal.NewFromFloat(salePrice).Mul(decimal.NewFromInt(int64(quantity))).InexactFloat64()
	return
}

// buildOrderGoodsDos 将商品按照供应商的不同分类并且生成订单商品实体
// 返回值: map[供应商id][]订单商品实体
// splitByGoodsNum 是否根据商品数量拆单，虚拟商品购买一个商品算作一个子订单，实物商品则按照供应商不同拆单
func (o *OrderDs) buildOrderGoodsDos(in *CreateOrderNewParams, splitByGoodsNum bool) map[int][]*bizBo.OrderGoodsCreateBo {
	//根据供应商分拆订单商品
	OrderGoodsDos := make(map[int][]*bizBo.OrderGoodsCreateBo, 0)
	for _, v := range in.SupplierOrder {
		for _, g := range v.Goods {
			goodsImage := g.SkuInfo.Image
			if goodsImage == "" {
				goodsImage = g.GoodsInfo.Image
			}
			if splitByGoodsNum {
				for i := 0; i < g.Num; i++ {
					OrderGoodsDos[v.SupplierId] = append(OrderGoodsDos[v.SupplierId], &bizBo.OrderGoodsCreateBo{
						OrderId:           0,
						OrderNumber:       "",
						GoodsId:           g.GoodsInfo.Id,
						GoodsName:         g.GoodsInfo.Name,
						GoodsImage:        goodsImage,
						CategoryId:        g.GoodsInfo.CategoryId,
						Quantity:          1,
						SalePrice:         g.SkuInfo.SalePrice,
						OriginPrice:       g.SkuInfo.SupplierPrice,
						ChannelPrice:      g.GetCustomerCostPrice(),
						SupplierPrice:     g.GetShopCostPrice(),
						GoodsSkuId:        g.SkuInfo.ID,
						SupplierId:        g.GoodsInfo.SupplierId,
						GoodsSkuName:      g.SkuInfo.Name,
						BrandName:         g.BrandName,
						CategoryName:      g.CategoryName,
						ProductId:         g.SkuInfo.ProductID,
						SkuNo:             g.SkuInfo.SkuNo,
						RealSupplierPrice: g.GetSupplierCostPrice(),
						BrandId:           g.GoodsInfo.BrandId,
						MarketPrice:       g.SkuInfo.MarketPrice,
					})
				}
			} else {
				OrderGoodsDos[v.SupplierId] = append(OrderGoodsDos[v.SupplierId], &bizBo.OrderGoodsCreateBo{
					OrderId:           0,
					OrderNumber:       "",
					GoodsId:           g.GoodsInfo.Id,
					GoodsName:         g.GoodsInfo.Name,
					GoodsImage:        goodsImage,
					CategoryId:        g.GoodsInfo.CategoryId,
					Quantity:          g.Num,
					SalePrice:         g.SkuInfo.SalePrice,
					OriginPrice:       g.SkuInfo.SupplierPrice,
					ChannelPrice:      g.GetCustomerCostPrice(),
					SupplierPrice:     g.GetShopCostPrice(),
					GoodsSkuId:        g.SkuInfo.ID,
					SupplierId:        g.GoodsInfo.SupplierId,
					GoodsSkuName:      g.SkuInfo.Name,
					BrandName:         g.BrandName,
					CategoryName:      g.CategoryName,
					ProductId:         g.SkuInfo.ProductID,
					SkuNo:             g.SkuInfo.SkuNo,
					RealSupplierPrice: g.GetSupplierCostPrice(),
					BrandId:           g.GoodsInfo.BrandId,
					MarketPrice:       g.SkuInfo.MarketPrice,
				})
			}
		}
	}
	return OrderGoodsDos
}

// buildOrderInsertDo 构建订单插入数据实体
func (o *OrderDs) buildOrderInsertDo(
	in *CreateOrderNewParams,
	orderGoodsDos []*bizBo.OrderGoodsCreateBo,
	isPOrder bool,
	accumulatePayAmount float64,
	accumulateDiscountAmount float64,
	accumulateIntegral int,
) (
	*bo.OrderCreateBo,
	[]*bizBo.OrderGoodsCreateBo,
	float64,
	float64,
	int,
) {
	orderNumber := in.PayOrderNumber
	if !isPOrder {
		orderNumber = helper.GetOrderNumber(o.idGenerator)
	}

	var num int
	//先计算出订单商品总金额
	orderTotalAmountDecimal := decimal.NewFromFloat(0)
	for _, orderGoodsDo := range orderGoodsDos {
		num += orderGoodsDo.Quantity
		orderTotalAmountDecimal = orderTotalAmountDecimal.Add(decimal.NewFromFloat(o.GetOrderGoodsTotalAmount(orderGoodsDo.SalePrice, orderGoodsDo.Quantity)))
	}
	orderTotalAmount := orderTotalAmountDecimal.InexactFloat64()

	//按照比例计算出订单实际支付金额、优惠券抵扣金额、抵扣积分和抵扣积分对应的抵扣金额
	orderTotalPayAmount := o.GetChildOrderPayAmount(in.TotalAmount, in.PayAmount, orderTotalAmount)
	if decimal.NewFromFloat(accumulatePayAmount).Add(decimal.NewFromFloat(orderTotalPayAmount)).InexactFloat64() > in.PayAmount {
		orderTotalPayAmount = decimal.NewFromFloat(in.PayAmount).Sub(decimal.NewFromFloat(accumulatePayAmount)).InexactFloat64()
	}
	orderTotalDiscountAmount := o.GetChildOrderCouponDiscountAmount(in.TotalAmount, orderTotalAmount, in.CouponDiscountAmount)
	if decimal.NewFromFloat(accumulateDiscountAmount).Add(decimal.NewFromFloat(orderTotalDiscountAmount)).InexactFloat64() > in.CouponDiscountAmount {
		orderTotalDiscountAmount = decimal.NewFromFloat(in.CouponDiscountAmount).Sub(decimal.NewFromFloat(accumulateDiscountAmount)).InexactFloat64()
	}
	orderTotalIntegral := o.GetChildOrderIntegral(in.TotalAmount, orderTotalAmount, in.Integral)
	if int(decimal.NewFromInt(int64(accumulateIntegral)).Add(decimal.NewFromInt(int64(orderTotalIntegral))).IntPart()) > in.Integral {
		orderTotalIntegral = int(decimal.NewFromInt(int64(in.Integral)).Sub(decimal.NewFromInt(int64(accumulateIntegral))).IntPart())
	}

	orderGoodsTotalPayAmount := decimal.NewFromFloat(0)
	orderGoodsTotalIntegral := decimal.NewFromInt(0)
	orderGoodsTotalDiscountAmount := decimal.NewFromFloat(0)

	goodsNum := len(orderGoodsDos)
	//订单的商品信息数据
	for index, orderGoodsDo := range orderGoodsDos {
		orderGoodsDo.OrderNumber = orderNumber

		// 如果是最后一个商品，则计算剩余的支付金额
		if index == goodsNum-1 {
			orderGoodsDo.PayAmount = decimal.NewFromFloat(orderTotalPayAmount).Sub(orderGoodsTotalPayAmount).InexactFloat64()

			orderGoodsDo.PayIntegral = int(decimal.NewFromInt(int64(orderTotalIntegral)).Sub(orderGoodsTotalIntegral).IntPart())
			orderGoodsDo.IntegralDiscountAmount = o.GetIntegralDiscountAmount(orderGoodsDo.PayIntegral)

			orderGoodsDo.CouponDiscountAmount = decimal.NewFromFloat(orderTotalDiscountAmount).Sub(orderGoodsTotalDiscountAmount).InexactFloat64()
			break
		}

		//计算订单商品的总金额
		goodsTotalAmount := o.GetOrderGoodsTotalAmount(orderGoodsDo.SalePrice, orderGoodsDo.Quantity)
		//按比例计算订单商品的实际支付金额
		orderGoodsDo.PayAmount = o.GetChildOrderPayAmount(orderTotalAmount, orderTotalPayAmount, goodsTotalAmount)
		//总支付金额累加
		orderGoodsTotalPayAmount = orderGoodsTotalPayAmount.Add(decimal.NewFromFloat(orderGoodsDo.PayAmount))

		//按比例计算订单商品的优惠券抵扣金额
		if orderTotalDiscountAmount > 0 {
			orderGoodsDo.CouponDiscountAmount = o.GetChildOrderCouponDiscountAmount(orderTotalAmount, goodsTotalAmount, orderTotalDiscountAmount)
			//总优惠金额累加
			orderGoodsTotalDiscountAmount = orderGoodsTotalDiscountAmount.Add(decimal.NewFromFloat(orderGoodsDo.CouponDiscountAmount))
		}

		//按比例计算订单商品的抵扣积分和抵扣积分对应的抵扣金额
		if orderTotalIntegral > 0 {
			orderGoodsDo.PayIntegral = o.GetChildOrderIntegral(orderTotalAmount, goodsTotalAmount, orderTotalIntegral)
			orderGoodsDo.IntegralDiscountAmount = o.GetIntegralDiscountAmount(orderGoodsDo.PayIntegral)
			//总抵扣积分累加
			orderGoodsTotalIntegral = orderGoodsTotalIntegral.Add(decimal.NewFromInt32(int32(orderGoodsDo.PayIntegral)))
		}
	}

	//订单主信息
	status := valobj.OrderStatusPayCreate
	if !isPOrder {
		status = valobj.OrderStatusUnPaid
	}
	if in.IsPaid {
		status = valobj.OrderStatusPaySuccess
		if !isPOrder {
			// 充值订单状态 为待发货，实物订单为待确认
			status = helper.TernaryAny(in.OrderType.IsRecharge(), valobj.OrderStatusAwaitingShip, valobj.OrderStatusToBeConfirmed)
		}
	}

	orderDo := &bo.OrderCreateBo{
		PayOrderNumber:         in.PayOrderNumber,
		OrderNumber:            orderNumber,
		OrderType:              in.OrderType,
		UserId:                 in.UserId,
		PayAmount:              orderTotalPayAmount,
		PayIntegral:            orderTotalIntegral,
		Status:                 status,
		Num:                    num, //表示商品数
		SiteId:                 in.SiteId,
		TotalAmount:            orderTotalAmount,
		IntegralDiscountAmount: o.GetIntegralDiscountAmount(orderTotalIntegral),
		CouponDiscountAmount:   orderTotalDiscountAmount,
		Account:                in.RechargeAccount,
	}

	accumulatePayAmount = decimal.NewFromFloat(accumulatePayAmount).Add(decimal.NewFromFloat(orderTotalPayAmount)).InexactFloat64()
	accumulateDiscountAmount = decimal.NewFromFloat(accumulateDiscountAmount).Add(decimal.NewFromFloat(orderTotalDiscountAmount)).InexactFloat64()
	accumulateIntegral = int(decimal.NewFromInt(int64(accumulateIntegral)).Add(decimal.NewFromInt(int64(orderTotalIntegral))).IntPart())

	return orderDo, orderGoodsDos, accumulatePayAmount, accumulateDiscountAmount, accumulateIntegral
}

// buildOrderInsertDoByPayAmount 重新测算订单支付金额、优惠券抵扣金额、抵扣积分和抵扣积分对应的抵扣金额
func (o *OrderDs) buildOrderInsertDoByPayAmount(
	orderDo *bo.OrderCreateBo,
	orderGoodsDos []*bizBo.OrderGoodsCreateBo,
) (
	*bo.OrderCreateBo,
	[]*bizBo.OrderGoodsCreateBo,
) {
	var num int
	//先计算出订单商品总金额
	orderTotalAmountDecimal := decimal.NewFromFloat(0)
	for _, orderGoodsDo := range orderGoodsDos {
		num += orderGoodsDo.Quantity
		orderTotalAmountDecimal = orderTotalAmountDecimal.Add(decimal.NewFromFloat(o.GetOrderGoodsTotalAmount(orderGoodsDo.SalePrice, orderGoodsDo.Quantity)))
	}
	orderTotalAmount := orderTotalAmountDecimal.InexactFloat64()

	//按照比例计算出订单实际支付金额、优惠券抵扣金额、抵扣积分和抵扣积分对应的抵扣金额
	orderTotalPayAmount := orderDo.PayAmount
	orderTotalDiscountAmount := orderDo.CouponDiscountAmount
	orderTotalIntegral := orderDo.PayIntegral
	orderDo.IntegralDiscountAmount = o.GetIntegralDiscountAmount(orderDo.PayIntegral)

	orderGoodsTotalPayAmount := decimal.NewFromFloat(0)
	orderGoodsTotalIntegral := decimal.NewFromInt(0)
	orderGoodsTotalDiscountAmount := decimal.NewFromFloat(0)

	goodsNum := len(orderGoodsDos)
	//订单的商品信息数据
	for index, orderGoodsDo := range orderGoodsDos {
		// 如果是最后一个商品，则计算剩余的支付金额
		if index == goodsNum-1 {
			orderGoodsDo.PayAmount = decimal.NewFromFloat(orderTotalPayAmount).Sub(orderGoodsTotalPayAmount).InexactFloat64()

			orderGoodsDo.PayIntegral = int(decimal.NewFromInt(int64(orderTotalIntegral)).Sub(orderGoodsTotalIntegral).IntPart())
			orderGoodsDo.IntegralDiscountAmount = o.GetIntegralDiscountAmount(orderGoodsDo.PayIntegral)

			orderGoodsDo.CouponDiscountAmount = decimal.NewFromFloat(orderTotalDiscountAmount).Sub(orderGoodsTotalDiscountAmount).InexactFloat64()
			break
		}

		//计算订单商品的总金额
		goodsTotalAmount := o.GetOrderGoodsTotalAmount(orderGoodsDo.SalePrice, orderGoodsDo.Quantity)
		//按比例计算订单商品的实际支付金额
		orderGoodsDo.PayAmount = o.GetChildOrderPayAmount(orderTotalAmount, orderTotalPayAmount, goodsTotalAmount)
		//总支付金额累加
		orderGoodsTotalPayAmount = orderGoodsTotalPayAmount.Add(decimal.NewFromFloat(orderGoodsDo.PayAmount))

		//按比例计算订单商品的优惠券抵扣金额
		if orderTotalDiscountAmount > 0 {
			orderGoodsDo.CouponDiscountAmount = o.GetChildOrderCouponDiscountAmount(orderTotalAmount, goodsTotalAmount, orderTotalDiscountAmount)
			//总优惠金额累加
			orderGoodsTotalDiscountAmount = orderGoodsTotalDiscountAmount.Add(decimal.NewFromFloat(orderGoodsDo.CouponDiscountAmount))
		}

		//按比例计算订单商品的抵扣积分和抵扣积分对应的抵扣金额
		if orderTotalIntegral > 0 {
			orderGoodsDo.PayIntegral = o.GetChildOrderIntegral(orderTotalAmount, goodsTotalAmount, orderTotalIntegral)
			orderGoodsDo.IntegralDiscountAmount = o.GetIntegralDiscountAmount(orderGoodsDo.PayIntegral)
			//总抵扣积分累加
			orderGoodsTotalIntegral = orderGoodsTotalIntegral.Add(decimal.NewFromInt32(int32(orderGoodsDo.PayIntegral)))
		}
	}

	//订单主信息
	return orderDo, orderGoodsDos
}

// CreatePayOrder 创建支付单
func (o *OrderDs) CreatePayOrder(ctx context.Context, in *CreatePayOrderParams) (*do.PayOrderDo, error) {
	if err := in.validate(); err != nil {
		return nil, err
	}
	orderNumber := helper.GetPayOrderNumber(o.idGenerator)
	var payOrder = &bizBo.PayOrderCreateBo{
		OrderNumber:          orderNumber,
		UserId:               in.UserId,
		TotalAmount:          in.TotalAmount,
		TotalPayAmount:       in.PayAmount,
		TotalPayIntegral:     0,
		Num:                  in.Num,
		Status:               valobj.PayOrderStatusUnpaid,
		CouponCodeId:         in.CouponCodeId,
		CouponDiscountAmount: in.CouponDiscountAmount,
		BrandId:              0,
		OrderType:            in.PayOrderType,
		SiteId:               in.SiteId,
		ExpireTime:           in.PayExpireTime,
		ThirdTradeNo:         in.ThirdTradeNo,
		SettlementType:       valobj.PayOrderSettlementTypeCash,
		PayType:              valobj.PayOrderPayTypeUnknown,
		CouponId:             in.CouponId,
		CardCouponNumber:     in.CardCouponNumber,
		FreightFee:           in.FreightFee,
		GoodsPayAmount:       in.GoodsPayAmount,
	}
	if in.PayType != valobj.PayOrderPayTypeUnknown {
		payOrder.PayType = in.PayType
	}
	if in.SettlementType != valobj.PayOrderSettlementTypeUnknown {
		payOrder.SettlementType = in.SettlementType
	}
	if in.Status != 0 {
		payOrder.Status = in.Status
	}
	//使用积分的情况
	if in.IntegralLog != nil {
		payOrder.UserIntegralLogId = in.IntegralLog.Id
		payOrder.TotalPayIntegral = in.IntegralLog.Integral
		// 只有商品是现金结算的时候，才能使用用户的积分，积分+钱购、积分兑换、券码兑换都不能使用积分
		if payOrder.SettlementType == valobj.PayOrderSettlementTypeCash {
			payOrder.SettlementType = valobj.PayOrderSettlementTypeCashAndIntegral
		}
		payOrder.IntegralDiscountAmount = o.GetIntegralDiscountAmount(in.IntegralLog.Integral)
	}
	//订单有效期
	payOrder.ExpireTime = int(time.Now().Add(constants.PayOrderExpireTime).Unix())
	if in.PayExpireTime > 0 {
		payOrder.ExpireTime = in.PayExpireTime
	}
	//第三方订单号
	if in.ThirdTradeNo != "" {
		payOrder.ThirdTradeNo = in.ThirdTradeNo
	}

	return o.payOrderRepo.Create(ctx, payOrder)
}

// CreatePayOrderV2 创建支付单
func (o *OrderDs) CreatePayOrderV2(ctx context.Context, in *CreatePayOrderParams) (*do.PayOrderDo, error) {
	if err := in.validate(); err != nil {
		return nil, err
	}
	orderNumber := helper.GetPayOrderNumber(o.idGenerator)
	var payOrder = &bizBo.PayOrderCreateBo{
		OrderNumber:            orderNumber,
		UserId:                 in.UserId,
		TotalAmount:            in.TotalAmount,
		TotalPayAmount:         in.PayAmount,
		TotalPayIntegral:       0,
		Num:                    in.Num,
		Status:                 valobj.PayOrderStatusUnpaid,
		CouponCodeId:           in.CouponCodeId,
		CouponDiscountAmount:   in.CouponDiscountAmount,
		BrandId:                0,
		OrderType:              in.PayOrderType,
		SiteId:                 in.SiteId,
		ExpireTime:             in.PayExpireTime,
		ThirdTradeNo:           in.ThirdTradeNo,
		SettlementType:         valobj.PayOrderSettlementTypeCash,
		PayType:                valobj.PayOrderPayTypeUnknown,
		CouponId:               in.CouponId,
		CardCouponNumber:       in.CardCouponNumber,
		FreightFee:             in.FreightFee,
		GoodsPayAmount:         in.GoodsPayAmount,
		IntegralDiscountAmount: in.IntegralDiscountAmount,
		CardGiftAmount:         in.CardGiftAmount,
	}
	if in.PayType != valobj.PayOrderPayTypeUnknown {
		payOrder.PayType = in.PayType
	}
	payOrder.SettlementType = in.getSettlementType()
	if in.PayAmount == 0 {
		payOrder.Status = valobj.PayOrderStatusPaid
	}
	if in.Status != 0 {
		payOrder.Status = in.Status
	}
	//使用积分的情况
	if in.IntegralLog != nil {
		payOrder.UserIntegralLogId = in.IntegralLog.Id
		payOrder.TotalPayIntegral = in.IntegralLog.Integral
		// 只有商品是现金结算的时候，才能使用用户的积分，积分+钱购、积分兑换、券码兑换都不能使用积分
		if payOrder.SettlementType == valobj.PayOrderSettlementTypeCash {
			payOrder.SettlementType = valobj.PayOrderSettlementTypeCashAndIntegral
		}
	}
	//订单有效期
	payOrder.ExpireTime = int(time.Now().Add(constants.PayOrderExpireTime).Unix())
	if in.PayExpireTime > 0 {
		payOrder.ExpireTime = in.PayExpireTime
	}
	//第三方订单号
	if in.ThirdTradeNo != "" {
		payOrder.ThirdTradeNo = in.ThirdTradeNo
	}

	return o.payOrderRepo.Create(ctx, payOrder)
}

// CreateOrder 创建充值单
func (o *OrderDs) CreateOrder(ctx context.Context, in *CreateOrderParams, hasChildren bool) error {
	if err := in.validate(); err != nil {
		return err
	}
	//主订单写入订单表中，方便C端展示
	order := &bo.OrderCreateBo{
		PayOrderNumber: in.PayOrderNumber,
		OrderNumber:    in.PayOrderNumber,
		OrderType:      in.OrderType,
		UserId:         in.UserId,
		PayAmount:      in.PayAmount,
		PayIntegral:    in.Integral,
		Status:         valobj.OrderStatusPayCreate,
		Account:        in.Account,
		Num:            in.Num,
		SiteId:         in.SiteId,
	}
	if in.BrandInfo != nil {
		order.BrandId = in.BrandInfo.Id
		order.BrandName = in.BrandInfo.Name
	}
	if in.ExtraInfo != "" {
		order.ExtraInfo = in.ExtraInfo
	}
	_, err := o.orderRepo.Create(ctx, order)
	if err != nil {
		return err
	}

	//没有子订单，直接返回
	if !hasChildren {
		return nil
	}

	integralAverage := in.Integral / in.Num
	payAmountAverage := in.PayAmount / float64(in.Num)
	payAmountAverage = math.Trunc(payAmountAverage*100) / 100
	//创建子订单
	for i := 0; i < in.Num; i++ {
		order = &bo.OrderCreateBo{
			PayOrderNumber: in.PayOrderNumber,
			OrderNumber:    helper.GetOrderNumber(o.idGenerator),
			OrderType:      in.OrderType,
			UserId:         in.UserId,
			PayAmount:      payAmountAverage,
			PayIntegral:    integralAverage,
			Status:         valobj.OrderStatusUnPaid,
			Account:        in.Account,
			Num:            1,
			SiteId:         in.SiteId,
		}
		if in.BrandInfo != nil {
			order.BrandId = in.BrandInfo.Id
			order.BrandName = in.BrandInfo.Name
		}
		if in.BaseGoodsInfo != nil && in.BaseGoodsInfo.Type == valobj.BaseGoodsTypeCard {
			order.CardExpireTime = in.BaseGoodsInfo.CardExpireTime
		}
		if in.ExtraInfo != "" {
			order.ExtraInfo = in.ExtraInfo
		}
		_, err = o.orderRepo.Create(ctx, order)
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateEntityOrderNew 创建实物订单--新
func (o *OrderDs) CreateEntityOrderNew(ctx context.Context, in *CreateOrderNewParams) error {
	if in.UserAddress == nil {
		return errors.New("用户收货地址不存在")
	}

	//根据供应商分拆订单商品
	OrderGoodsDos := o.buildOrderGoodsDos(in, false)
	OrderGoodsDosAll := make([]*bizBo.OrderGoodsCreateBo, 0)
	for _, goods := range OrderGoodsDos {
		OrderGoodsDosAll = append(OrderGoodsDosAll, goods...)
	}
	//主订单写入订单表中，方便C端展示
	orderDo, orderGoodsDos, _, _, _ := o.buildOrderInsertDo(in, OrderGoodsDosAll, true, 0, 0, 0)
	extType := valobj.OrderExtTypeDefault
	if in.ExtType.Exists() {
		extType = in.ExtType
		orderDo.ExtType = extType
	}
	orderId, err := o.orderRepo.Create(ctx, orderDo)
	if err != nil {
		return err
	}

	//创建主订单收货地址
	_, err = o.userOrderAddressRepo.Create(ctx, &bizDo.OrderUserAddressDo{
		OrderID:     orderId,
		OrderNumber: orderDo.OrderNumber,
		Name:        in.UserAddress.Name,
		PhoneNumber: in.UserAddress.Phone,
		ProvinceID:  in.UserAddress.ProvinceId,
		CityID:      in.UserAddress.CityId,
		RegionID:    in.UserAddress.RegionId,
		Area:        in.UserAddress.Area,
		Detail:      in.UserAddress.Detail,
		Lng:         "",
		Lat:         "",
		UserID:      in.UserId,
		CreateTime:  int(time.Now().Unix()),
	})
	if err != nil {
		return err
	}

	//创建主订单的商品信息数据
	for _, orderGoodsDo := range orderGoodsDos {
		orderGoodsDo.OrderId = orderId
		_, err = o.orderGoodsRepo.Create(ctx, orderGoodsDo)
		if err != nil {
			return err
		}
	}

	//根据供应商分拆订单商品生成子订单
	var accumulatePayAmount float64
	var accumulateDiscountAmount float64
	var accumulateIntegral int
	type supplierOrder struct {
		Order      *bo.OrderCreateBo
		OrderGoods []*bizBo.OrderGoodsCreateBo
	}
	supplierOrderSlice := make([]*supplierOrder, 0)
	var orderAmountMaxOrderNumber string
	var orderAmountMax float64
	for supplierId, goods := range OrderGoodsDos {
		//货易通的商品，按照sku拆单
		if supplierId == int(o.conf.Hyt.GetSupplierId()) {
			for _, good := range goods {
				orderDo, orderGoodsDos, accumulatePayAmount, accumulateDiscountAmount, accumulateIntegral = o.buildOrderInsertDo(
					in,
					[]*bizBo.OrderGoodsCreateBo{good},
					false,
					accumulatePayAmount,
					accumulateDiscountAmount,
					accumulateIntegral,
				)
				orderDo.SupplierId = supplierId
				supplierOrderSlice = append(supplierOrderSlice, &supplierOrder{Order: orderDo, OrderGoods: orderGoodsDos})
				if orderAmountMax < orderDo.TotalAmount {
					orderAmountMax = orderDo.TotalAmount
					orderAmountMaxOrderNumber = orderDo.OrderNumber
				}
			}
		} else {
			orderDo, orderGoodsDos, accumulatePayAmount, accumulateDiscountAmount, accumulateIntegral = o.buildOrderInsertDo(
				in,
				goods,
				false,
				accumulatePayAmount,
				accumulateDiscountAmount,
				accumulateIntegral,
			)
			orderDo.SupplierId = supplierId
			supplierOrderSlice = append(supplierOrderSlice, &supplierOrder{Order: orderDo, OrderGoods: orderGoodsDos})
			if orderAmountMax < orderDo.TotalAmount {
				orderAmountMax = orderDo.TotalAmount
				orderAmountMaxOrderNumber = orderDo.OrderNumber
			}
		}

	}
	for _, sOrder := range supplierOrderSlice {
		orderDo = sOrder.Order
		orderGoodsDos = sOrder.OrderGoods
		if orderDo.OrderNumber == orderAmountMaxOrderNumber {
			orderDo.PayAmount += decimal.NewFromFloat(in.PayAmount).Sub(decimal.NewFromFloat(accumulatePayAmount)).InexactFloat64()
			orderDo.PayIntegral += int(decimal.NewFromInt(int64(in.Integral)).Sub(decimal.NewFromInt(int64(accumulateIntegral))).IntPart())
			orderDo.CouponDiscountAmount += decimal.NewFromFloat(in.CouponDiscountAmount).Sub(decimal.NewFromFloat(accumulateDiscountAmount)).InexactFloat64()
			orderDo, orderGoodsDos = o.buildOrderInsertDoByPayAmount(orderDo, orderGoodsDos)
		}
		if in.ExtType.Exists() {
			orderDo.ExtType = extType
		}
		orderDo.DeliverExpireTime = in.GetDeliverExpireTime(orderDo.SupplierId)

		orderId, err = o.orderRepo.Create(ctx, orderDo)
		if err != nil {
			return err
		}
		for _, orderGoodsDo := range orderGoodsDos {
			orderGoodsDo.OrderId = orderId
			_, err = o.orderGoodsRepo.Create(ctx, orderGoodsDo)
			if err != nil {
				return err
			}
		}
		//创建子订单收货地址
		_, err = o.userOrderAddressRepo.Create(ctx, &bizDo.OrderUserAddressDo{
			OrderID:     orderId,
			OrderNumber: orderDo.OrderNumber,
			Name:        in.UserAddress.Name,
			PhoneNumber: in.UserAddress.Phone,
			ProvinceID:  in.UserAddress.ProvinceId,
			CityID:      in.UserAddress.CityId,
			RegionID:    in.UserAddress.RegionId,
			Area:        in.UserAddress.Area,
			Detail:      in.UserAddress.Detail,
			Lng:         "",
			Lat:         "",
			UserID:      in.UserId,
			CreateTime:  int(time.Now().Unix()),
		})
		if err != nil {
			return err
		}
		content := orderDo.Status.GetOperatorContent()
		if in.OperaLogContent != "" {
			content = in.OperaLogContent
		}
		//创建子订单操作日志
		_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			OrderId:          orderId,
			OrderNumber:      orderDo.OrderNumber,
			OrderStatus:      orderDo.Status,
			Content:          content,
			OperatorUserType: valobj.OrderOperatorLogUserTypeUser,
			OperatorUserId:   in.UserId,
			OperatorUserName: in.UserName,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateVirOrderNew 创建虚拟订单--新
func (o *OrderDs) CreateVirOrderNew(ctx context.Context, in *CreateOrderNewParams) error {
	//根据供应商分拆订单商品
	OrderGoodsDos := o.buildOrderGoodsDos(in, false)
	OrderGoodsDosAll := make([]*bizBo.OrderGoodsCreateBo, 0)
	for _, goods := range OrderGoodsDos {
		OrderGoodsDosAll = append(OrderGoodsDosAll, goods...)
	}
	//主订单写入订单表中，方便C端展示
	orderDo, orderGoodsDos, _, _, _ := o.buildOrderInsertDo(in, OrderGoodsDosAll, true, 0, 0, 0)
	extType := valobj.OrderExtTypeDefault
	if in.ExtType.Exists() {
		extType = in.ExtType
		orderDo.ExtType = extType
	}
	orderId, err := o.orderRepo.Create(ctx, orderDo)
	if err != nil {
		return err
	}
	//创建主订单的商品信息数据
	for _, orderGoodsDo := range orderGoodsDos {
		orderGoodsDo.OrderId = orderId
		_, err = o.orderGoodsRepo.Create(ctx, orderGoodsDo)
		if err != nil {
			return err
		}
	}

	//根据供应商分拆订单商品生成子订单
	var accumulatePayAmount float64
	var accumulateDiscountAmount float64
	var accumulateIntegral int
	type supplierOrder struct {
		Order      *bo.OrderCreateBo
		OrderGoods []*bizBo.OrderGoodsCreateBo
	}
	supplierOrderSlice := make([]*supplierOrder, 0)
	var orderAmountMax float64
	OrderGoodsDos = o.buildOrderGoodsDos(in, true)
	for supplierId, goods := range OrderGoodsDos {
		for _, good := range goods {
			orderDo, orderGoodsDos, accumulatePayAmount, accumulateDiscountAmount, accumulateIntegral = o.buildOrderInsertDo(
				in,
				[]*bizBo.OrderGoodsCreateBo{good},
				false,
				accumulatePayAmount,
				accumulateDiscountAmount,
				accumulateIntegral,
			)
			orderDo.SupplierId = supplierId
			supplierOrderSlice = append(supplierOrderSlice, &supplierOrder{
				Order:      orderDo,
				OrderGoods: orderGoodsDos,
			})
			if orderAmountMax < orderDo.TotalAmount {
				orderAmountMax = orderDo.TotalAmount
			}
		}
	}

	flag := false
	for _, sOrder := range supplierOrderSlice {
		orderDo = sOrder.Order
		orderGoodsDos = sOrder.OrderGoods
		if !flag && orderDo.TotalAmount == orderAmountMax {
			orderDo.PayAmount += decimal.NewFromFloat(in.PayAmount).Sub(decimal.NewFromFloat(accumulatePayAmount)).InexactFloat64()
			orderDo.PayIntegral += int(decimal.NewFromInt(int64(in.Integral)).Sub(decimal.NewFromInt(int64(accumulateIntegral))).IntPart())
			orderDo.CouponDiscountAmount += decimal.NewFromFloat(in.CouponDiscountAmount).Sub(decimal.NewFromFloat(accumulateDiscountAmount)).InexactFloat64()
			orderDo, orderGoodsDos = o.buildOrderInsertDoByPayAmount(orderDo, orderGoodsDos)
			flag = true
		}
		if in.ExtType.Exists() {
			orderDo.ExtType = extType
		}
		orderId, err = o.orderRepo.Create(ctx, orderDo)
		if err != nil {
			return err
		}
		for _, orderGoodsDo := range orderGoodsDos {
			orderGoodsDo.OrderId = orderId
			_, err = o.orderGoodsRepo.Create(ctx, orderGoodsDo)
			if err != nil {
				return err
			}
		}
		content := orderDo.Status.GetOperatorContent()
		if in.OperaLogContent != "" {
			content = in.OperaLogContent
		}
		//创建子订单操作日志
		_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			OrderId:          orderId,
			OrderNumber:      orderDo.OrderNumber,
			OrderStatus:      orderDo.Status,
			Content:          content,
			OperatorUserType: valobj.OrderOperatorLogUserTypeUser,
			OperatorUserId:   in.UserId,
			OperatorUserName: in.UserName,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateQianZhuOrder 创建千猪订单
func (o *OrderDs) CreateQianZhuOrder(ctx context.Context, in *CreateQZOrderParams) error {
	//主订单写入订单表中，方便C端展示
	order := &bo.OrderCreateBo{
		PayOrderNumber: in.PayOrderNumber,
		OrderNumber:    in.PayOrderNumber,
		OrderType:      in.OrderType,
		UserId:         in.UserId,
		PayAmount:      in.PayAmount,
		PayIntegral:    in.Integral,
		Status:         valobj.OrderStatusPayCreate,
		Num:            in.Num,
		SiteId:         in.SiteId,
		TotalAmount:    in.TotalAmount,
	}

	orderId, err := o.orderRepo.Create(ctx, order)
	if err != nil {
		return err
	}
	for _, good := range in.Goods {
		orderGoodsBo := &bizBo.OrderGoodsCreateBo{
			OrderId:     orderId,
			OrderNumber: in.PayOrderNumber,
			GoodsName:   good.Name,
			GoodsImage:  good.Image,
			Quantity:    good.Quantity,
			SalePrice:   good.SalePrice,
			//GoodsSkuName: good.Name,
			GoodsId:       good.GoodsId,
			SkuNo:         good.SkuNo,
			PayAmount:     good.PayAmount,
			OriginPrice:   good.OriginPrice,
			SupplierPrice: good.SupplierPrice,
		}
		_, err = o.orderGoodsRepo.Create(ctx, orderGoodsBo)
		if err != nil {
			return err
		}
	}
	//创建子订单操作日志
	_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
		OrderId:          orderId,
		OrderNumber:      order.OrderNumber,
		OrderStatus:      valobj.OrderStatusUnPaid,
		Content:          valobj.OrderStatusUnPaid.GetOperatorContent(),
		OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
		OperatorUserId:   0,
		OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
	})
	return err
}

// CreateMeiTuanOrder 创建美团订单
func (o *OrderDs) CreateMeiTuanOrder(ctx context.Context, in *CreateMTOrderParams) error {
	//主订单写入订单表中，方便C端展示
	order := &bo.OrderCreateBo{
		PayOrderNumber: in.PayOrderNumber,
		OrderNumber:    in.PayOrderNumber,
		OrderType:      in.OrderType,
		UserId:         in.UserId,
		PayAmount:      in.PayAmount,
		PayIntegral:    in.Integral,
		Status:         valobj.OrderStatusPayCreate,
		Num:            in.Num,
		SiteId:         in.SiteId,
		TotalAmount:    in.TotalAmount,
	}

	orderId, err := o.orderRepo.Create(ctx, order)
	if err != nil {
		return err
	}
	for _, good := range in.Goods {
		orderGoodsBo := &bizBo.OrderGoodsCreateBo{
			OrderId:       orderId,
			OrderNumber:   in.PayOrderNumber,
			GoodsName:     good.Name,
			Quantity:      good.Quantity,
			SalePrice:     good.SalePrice,
			SupplierPrice: good.SalePrice,
			//GoodsSkuName: good.Name,
		}
		_, err = o.orderGoodsRepo.Create(ctx, orderGoodsBo)
		if err != nil {
			return err
		}
	}
	//创建子订单操作日志
	_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
		OrderId:          orderId,
		OrderNumber:      order.OrderNumber,
		OrderStatus:      valobj.OrderStatusUnPaid,
		Content:          valobj.OrderStatusUnPaid.GetOperatorContent(),
		OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
		OperatorUserId:   0,
		OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
	})
	return err
}

// CreateEntityOrderV2 创建实物订单V2版本
func (o *OrderDs) CreateEntityOrderV2(ctx context.Context, in *CreateOrderParamsV2) error {
	if !in.Extra.AddressExist() {
		return errors.New("请选择收货地址")
	}
	userAddress := bizDo.OrderUserAddressDo{
		OrderID:     0,
		OrderNumber: "",
		Name:        in.Extra.GetAddress().Name,
		PhoneNumber: in.Extra.GetAddress().Phone,
		ProvinceID:  in.Extra.GetAddress().ProvinceId,
		CityID:      in.Extra.GetAddress().CityId,
		RegionID:    in.Extra.GetAddress().RegionId,
		Area:        in.Extra.GetAddress().Area,
		Detail:      in.Extra.GetAddress().Detail,
		Lng:         "",
		Lat:         "",
		UserID:      in.UserId,
		CreateTime:  int(time.Now().Unix()),
	}

	if len(in.GetOrderBos()) == 0 {
		return errors.New("创建订单失败")
	}

	for _, orderCreateBo := range in.GetOrderBos() {
		//写入order表
		orderId, err := o.orderRepo.Create(ctx, orderCreateBo)
		if err != nil {
			return err
		}
		//写入order_goods表
		orderGoodsDos := in.GetOrderGoodsBos(orderCreateBo.OrderNumber, orderId)
		for _, orderGoodsCreateBo := range orderGoodsDos {
			_, err = o.orderGoodsRepo.Create(ctx, orderGoodsCreateBo)
		}
		//写入用户收货地址
		orderUserAddress := userAddress
		orderUserAddress.OrderID = orderId
		orderUserAddress.OrderNumber = orderCreateBo.OrderNumber
		_, err = o.userOrderAddressRepo.Create(ctx, &orderUserAddress)
		if err != nil {
			return err
		}
		content := orderCreateBo.Status.GetOperatorContent()
		if in.Extra.OperaLogContent != "" {
			content = in.Extra.OperaLogContent
		}
		//创建子订单操作日志
		_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			OrderId:          orderId,
			OrderNumber:      orderCreateBo.OrderNumber,
			OrderStatus:      orderCreateBo.Status,
			Content:          content,
			OperatorUserType: valobj.OrderOperatorLogUserTypeUser,
			OperatorUserId:   in.UserId,
			OperatorUserName: in.UserName,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateVirOrderV2 创建虚拟订单V2版本
func (o *OrderDs) CreateVirOrderV2(ctx context.Context, in *CreateOrderParamsV2) error {
	if len(in.GetOrderBos()) == 0 {
		return errors.New("创建订单失败")
	}
	for _, orderCreateBo := range in.GetOrderBos() {
		//写入order表
		orderId, err := o.orderRepo.Create(ctx, orderCreateBo)
		if err != nil {
			return err
		}
		//写入order_goods表
		orderGoodsDos := in.GetOrderGoodsBos(orderCreateBo.OrderNumber, orderId)
		for _, orderGoodsCreateBo := range orderGoodsDos {
			_, err = o.orderGoodsRepo.Create(ctx, orderGoodsCreateBo)
		}
		content := orderCreateBo.Status.GetOperatorContent()
		if in.Extra.OperaLogContent != "" {
			content = in.Extra.OperaLogContent
		}
		//创建子订单操作日志
		_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
			OrderId:          orderId,
			OrderNumber:      orderCreateBo.OrderNumber,
			OrderStatus:      orderCreateBo.Status,
			Content:          content,
			OperatorUserType: valobj.OrderOperatorLogUserTypeUser,
			OperatorUserId:   in.UserId,
			OperatorUserName: in.UserName,
		})
		if err != nil {
			return err
		}
	}
	return nil
}

// CreateThirdOrder 创建第三方订单
func (o *OrderDs) CreateThirdOrder(ctx context.Context, in *CreateThirdParams) error {
	//主订单写入订单表中，方便C端展示
	order := &bo.OrderCreateBo{
		PayOrderNumber: in.PayOrderNumber,
		OrderNumber:    in.PayOrderNumber,
		OrderType:      in.OrderType,
		UserId:         in.UserId,
		PayAmount:      in.PayAmount,
		TotalAmount:    in.TotalAmount,
		PayIntegral:    in.Integral,
		Status:         valobj.OrderStatusPayCreate,
		Num:            in.Num,
		SiteId:         in.SiteId,
	}

	orderId, err := o.orderRepo.Create(ctx, order)
	if err != nil {
		return err
	}
	for _, good := range in.Goods {
		orderGoodsBo := &bizBo.OrderGoodsCreateBo{
			OrderId:       orderId,
			OrderNumber:   in.PayOrderNumber,
			GoodsName:     good.GoodsName,
			Quantity:      good.Quantity,
			SalePrice:     good.SalePrice,
			GoodsSkuName:  good.GoodsSkuName,
			GoodsId:       good.GoodsId,
			SkuNo:         good.SkuNo,
			PayAmount:     good.PayAmount,
			GoodsImage:    good.GoodsImage,
			OriginPrice:   good.OriginPrice,
			SupplierPrice: good.SupplierPrice,
		}
		_, err = o.orderGoodsRepo.Create(ctx, orderGoodsBo)
		if err != nil {
			return err
		}
	}
	//创建子订单操作日志
	_, err = o.orderOperatorLogRepo.Add(ctx, &bizBo.OrderOperatorLogAddBo{
		OrderId:          orderId,
		OrderNumber:      order.OrderNumber,
		OrderStatus:      valobj.OrderStatusUnPaid,
		Content:          valobj.OrderStatusUnPaid.GetOperatorContent(),
		OperatorUserType: valobj.OrderOperatorLogUserTypeSystem,
		OperatorUserId:   0,
		OperatorUserName: valobj.OrderOperatorLogUserTypeSystem.String(),
	})
	return err
}
