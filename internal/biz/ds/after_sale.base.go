package ds

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"context"
	"github.com/duke-git/lancet/v2/slice"
)

// OrderAfterSaleBaseDs 售后基础业务，可被其他业务复用
type OrderAfterSaleBaseDs struct {
	orderAfterSaleDealRepo repository.OrderAfterSaleDealRepo
	orderGoodsRepo         repository.OrderGoodsRepo
	orderRepo              repository.OrderRepo
}

func NewOrderAfterSaleBaseDs(orderAfterSaleDealRepo repository.OrderAfterSaleDealRepo, orderGoodsRepo repository.OrderGoodsRepo, orderRepo repository.OrderRepo) *OrderAfterSaleBaseDs {
	return &OrderAfterSaleBaseDs{orderAfterSaleDealRepo: orderAfterSaleDealRepo, orderGoodsRepo: orderGoodsRepo, orderRepo: orderRepo}
}

type GetOrderApplyInfoInterface interface {
	GetOriginalOrderNo() string
	GetOrderNo() string
	IsExchangeOrder() bool
}

// GetOrderApplyInfo 获取订单售后申请信息
// checkMaxApplyTimes 不传值，则默认检查最大售后次数，传值false则不检查
func (o *OrderAfterSaleBaseDs) GetOrderApplyInfo(ctx context.Context, orderDo GetOrderApplyInfoInterface) ([]*bo.OrderAfterSaleSkuBo, error) {
	bos, err := o.getOriginalOrderApplyInfo(ctx, orderDo.GetOriginalOrderNo())
	return o.FilterExchangeApplyInfo(orderDo, bos), err
}

// GetApplyAfterSaleStatus 获取是否可申请售后
// 1. 是否可以换货
// 2. 是否可以退款信
// 3. 是否处理中
func (o *OrderAfterSaleBaseDs) GetApplyAfterSaleStatus(orderDo GetOrderApplyInfoInterface, dealDos []*do.OrderAfterSaleDealDo, goodsDos []*do.OrderGoodsDo, notFinishOrderNos ...string) *bo.CheckAfterSaleStatusV2Bo {
	bos := o.FilterExchangeApplyInfo(orderDo, o.HandleApplyInfo(dealDos, goodsDos, notFinishOrderNos...))
	res := &bo.CheckAfterSaleStatusV2Bo{
		CanExchange: false,
		CanRefund:   false,
		IsDealing:   false,
	}
	for _, item := range bos {
		if item.CanExchange {
			// 非本次换货的换货
			if item.ParentOrderNo != "" && item.OrderNo != orderDo.GetOrderNo() {

			} else {
				res.CanExchange = true
			}
		}
		if item.CanRefund {
			res.CanRefund = true
		}
		if item.IsDealing {
			res.IsDealing = true
		}
	}
	return res
}

// GetApplyAfterSaleStatusByBos 获取是否可申请售后
// 1. 是否可以换货
// 2. 是否可以退款信
// 3. 是否处理中
func (o *OrderAfterSaleBaseDs) GetApplyAfterSaleStatusByBos(orderDo GetOrderApplyInfoInterface, bos []*bo.OrderAfterSaleSkuBo) *bo.CheckAfterSaleStatusV2Bo {
	res := &bo.CheckAfterSaleStatusV2Bo{
		CanExchange: false,
		CanRefund:   false,
		IsDealing:   false,
	}
	for _, item := range bos {
		// 申请订单与售后订单号相同的情况下才能换货
		if item.OrderNo == orderDo.GetOrderNo() {
			res.CanExchange = item.CanExchange
		}

		if item.CanRefund {
			res.CanRefund = true
		}
		if item.IsDealing {
			res.IsDealing = true
		}
	}
	return res
}

// GetAfterSaleStatus 获取是否售后情况
// 1. 获取是否全部进行售后了
// 2. 获取是否有换货
// 3. 获取是否有退款
// 4. 获取是否有处理中
func (o *OrderAfterSaleBaseDs) GetAfterSaleStatus(bos []*bo.OrderAfterSaleSkuBo) *bo.CheckAfterSaleStatusBo {
	res := &bo.CheckAfterSaleStatusBo{
		HasExchange:   false,
		HasRefund:     false,
		AfterSaleAll:  true,
		IsDealing:     false,
		RefundAllSkus: make([]string, 0),
	}
	for _, item := range bos {
		if item.GetCanApplyNum() > 0 {
			res.AfterSaleAll = false
		}
		if item.ExchangeNum > 0 {
			res.HasExchange = true
		}
		if item.RefundNum > 0 {
			res.HasRefund = true
		}
		if item.IsDealing {
			res.IsDealing = true
		}
		if item.RootIsRefundAll {
			res.RefundAllSkus = append(res.RefundAllSkus, item.OriginalSkuNo)
		}
	}
	return res

}

func (o *OrderAfterSaleBaseDs) FilterExchangeApplyInfo(orderDo GetOrderApplyInfoInterface, bos []*bo.OrderAfterSaleSkuBo) []*bo.OrderAfterSaleSkuBo {
	returnBos := make([]*bo.OrderAfterSaleSkuBo, 0)
	if orderDo.IsExchangeOrder() {
		for _, itemBo := range bos {
			if slice.Contain(itemBo.NextParentOrderNos, orderDo.GetOrderNo()) || itemBo.OrderNo == orderDo.GetOrderNo() {
				returnBos = append(returnBos, itemBo)
			}
		}
	} else {
		returnBos = bos
	}
	return returnBos
}

func (o *OrderAfterSaleBaseDs) HandleApplyInfo(dealDos []*do.OrderAfterSaleDealDo, goodsDos []*do.OrderGoodsDo, notFinishOrderNos ...string) []*bo.OrderAfterSaleSkuBo {
	// map[orderNo-skuNo]退款数量
	refundMap := make(map[string]int)
	// map[skuNo]退款数量
	rootRefundMap := make(map[string]int)
	// map[skuNo]退款数量
	allRefundMap := make(map[string]int)
	// map[parentOrderNo-skuNo]退款数量
	exchangeMap := make(map[string]int)
	// map[parentOrderNo-skuNo]退款数量
	rootExchangeMap := make(map[string]int)
	exchangeDealDos := make([]*do.OrderAfterSaleDealDo, 0)
	skuDealingMap := make(map[string]bool)

	for _, tmpDo := range dealDos {
		if val := skuDealingMap[tmpDo.OriginalSkuNo]; !val {
			skuDealingMap[tmpDo.OriginalSkuNo] = tmpDo.IsDealing.IsTrue()
		}
		if tmpDo.Type.IsAfterSaleExchange() {
			exchangeDealDos = append(exchangeDealDos, tmpDo)
			if tmpDo.IsExchangeRootApply() {
				_, ok := rootExchangeMap[tmpDo.GetExchangeApplyUniqueNo()]
				if !ok {
					rootExchangeMap[tmpDo.GetExchangeApplyUniqueNo()] = 0
				}
				rootExchangeMap[tmpDo.GetExchangeApplyUniqueNo()] += tmpDo.GoodsNum
			} else {
				_, ok := exchangeMap[tmpDo.GetExchangeApplyUniqueNo()]
				if !ok {
					exchangeMap[tmpDo.GetExchangeApplyUniqueNo()] = 0
				}
				exchangeMap[tmpDo.GetExchangeApplyUniqueNo()] += tmpDo.GoodsNum
			}
		}
		if tmpDo.Type.IsRefundReturn() || tmpDo.Type.IsRefund() {
			if _, ok := allRefundMap[tmpDo.OriginalSkuNo]; !ok {
				allRefundMap[tmpDo.OriginalSkuNo] = 0
			}
			allRefundMap[tmpDo.OriginalSkuNo] += tmpDo.GoodsNum
			if tmpDo.IsRefundRootApply() {
				_, ok := rootRefundMap[tmpDo.OriginalSkuNo]
				if !ok {
					rootRefundMap[tmpDo.OriginalSkuNo] = 0
				}
				rootRefundMap[tmpDo.OriginalSkuNo] += tmpDo.GoodsNum
			} else {
				_, ok := refundMap[tmpDo.GetRefundApplyUniqueNo()]
				if !ok {
					refundMap[tmpDo.GetRefundApplyUniqueNo()] = 0
				}
				refundMap[tmpDo.GetRefundApplyUniqueNo()] += tmpDo.GoodsNum
			}
		}
	}

	var orderGoodsDos []*bo.OrderAfterSaleSkuBo

	for _, tmpDo := range goodsDos {
		refundNum, ok := rootRefundMap[tmpDo.SkuNo]
		if !ok {
			refundNum = 0
		}
		allRefundNum, ok := allRefundMap[tmpDo.SkuNo]
		if !ok {
			allRefundNum = 0
		}
		exchangedNum, ok := rootExchangeMap[tmpDo.GetSkuUniqueKey()]
		if !ok {
			exchangedNum = 0
		}
		canApplyNum := tmpDo.Quantity - refundNum - exchangedNum
		canApply := tmpDo.Quantity-refundNum-exchangedNum > 0
		orderGoodsDos = append(orderGoodsDos, &bo.OrderAfterSaleSkuBo{
			OrderNo:            tmpDo.OrderNumber,
			ParentOrderNo:      "",
			NextParentOrderNos: []string{tmpDo.OrderNumber},
			SkuNo:              tmpDo.SkuNo,
			OriginalSkuNo:      tmpDo.SkuNo,
			GoodsNum:           tmpDo.Quantity,
			RefundNum:          refundNum,
			ExchangeNum:        exchangedNum,
			GoodsName:          tmpDo.GetSkuFullName(),
			MainImage:          tmpDo.GoodsImage,
			SalePrice:          tmpDo.SalePrice,
			SaleIntegral:       tmpDo.GetSaleIntegralInt(),
			IsDealing:          skuDealingMap[tmpDo.SkuNo],
			GoodsId:            tmpDo.GoodsID,
			Children:           nil,
			CanRefund:          canApply,
			CanExchange:        canApply && !slice.Contain(notFinishOrderNos, tmpDo.OrderNumber),
			CanApplyNum:        canApplyNum,
			OrderIsComplete:    !slice.Contain(notFinishOrderNos, tmpDo.OrderNumber),
			RootIsRefundAll:    allRefundNum == tmpDo.Quantity,
		})
	}

	for _, tmpDo := range exchangeDealDos {
		tmpDo.ExchangeNum = exchangeMap[tmpDo.GetSelfExchangeApplyUniqueNo()]
		tmpDo.RefundNum = refundMap[tmpDo.GetSelfExchangeApplyUniqueNo()]

		orderGoodsDos = append(orderGoodsDos, &bo.OrderAfterSaleSkuBo{
			OrderNo:            tmpDo.ExchangeOrderNo,
			ParentOrderNo:      tmpDo.ParentOrderNo,
			NextParentOrderNos: tmpDo.GetNextParentOrderNosSlice(tmpDo.ExchangeOrderNo),
			OriginalSkuNo:      tmpDo.OriginalSkuNo,
			SkuNo:              tmpDo.SkuNo,
			GoodsNum:           tmpDo.GoodsNum,
			ExchangeNum:        tmpDo.ExchangeNum,
			RefundNum:          tmpDo.RefundNum,
			GoodsName:          tmpDo.GoodsName,
			MainImage:          tmpDo.MainImage,
			SalePrice:          tmpDo.SalePrice,
			SaleIntegral:       tmpDo.SaleIntegral,
			IsDealing:          tmpDo.IsDealing.IsTrue(),
			Tags:               nil,
			GoodsId:            tmpDo.GoodsID,
			OrderIsComplete:    !slice.Contain(notFinishOrderNos, tmpDo.ExchangeOrderNo),
			Children:           nil,
			CanApplyNum:        0,
			CanRefund:          tmpDo.GetCanApplyNum() > 0,
			CanExchange:        tmpDo.GetCanApplyNum() > 0 && !slice.Contain(notFinishOrderNos, tmpDo.ExchangeOrderNo),
		})
	}
	// orderGoodsTree := bo.OrderAfterSaleSkuBoSliceToTree(orderGoodsDos, "")
	return orderGoodsDos
}

// GetOriginalOrderApplyInfo 获取原始订单可售后商品信息
func (o *OrderAfterSaleBaseDs) getOriginalOrderApplyInfo(ctx context.Context, originalOrderNo string) ([]*bo.OrderAfterSaleSkuBo, error) {
	dealDos, err := o.orderAfterSaleDealRepo.FindOriginalOrderApplyItems(ctx, originalOrderNo)
	if err != nil {
		return nil, err
	}
	// 默认检查售后次数
	appliedAfterSaleIds := make([]int, 0)
	exchangeOrderNos := make([]string, 0)
	exchangeOrderNos = append(exchangeOrderNos, originalOrderNo)

	for _, v := range dealDos {
		if v.ExchangeOrderNo != "" {
			exchangeOrderNos = append(exchangeOrderNos, v.ExchangeOrderNo)
		}
		// 统计申请次数
		appliedAfterSaleIds = append(appliedAfterSaleIds, v.Pid)
	}

	goodsDos, err := o.orderGoodsRepo.FindByOrderNumber(ctx, originalOrderNo)
	if err != nil {
		return nil, err
	}
	notFinishOrderNos := make([]string, 0)
	if len(exchangeOrderNos) > 0 {
		notFinishOrderNos, err = o.orderRepo.GetNotFinishedOrderNo(ctx, exchangeOrderNos)
		if err != nil {
			return nil, err
		}
	}

	return o.HandleApplyInfo(dealDos, goodsDos, notFinishOrderNos...), nil
}
