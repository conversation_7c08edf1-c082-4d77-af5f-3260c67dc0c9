package ds

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"

	"github.com/duke-git/lancet/v2/slice"
)

type SysConfigDs struct {
	sysConfigRepo repository.SysConfigRepo
}

func NewSysConfigSceneDs(sysConfigRepo repository.SysConfigRepo) *SysConfigDs {
	return &SysConfigDs{sysConfigRepo: sysConfigRepo}
}

// getSceneConfig 获取经营场景配置
func (s *SysConfigDs) getSceneConfig(ctx context.Context) (*do.SysConfigDo, error) {
	return s.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigShopScene)
}

// getGoodsShowTypeConfig 获取商品展示维度配置
func (s *SysConfigDs) getGoodsShowTypeConfig(ctx context.Context) (*do.SysConfigDo, error) {
	return s.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigGoodsShowType)
}

// getShopShowBalanceConfig 获取商城是否展示分销商余额
func (s *SysConfigDs) getShopShowBalanceConfig(ctx context.Context) (*do.SysConfigDo, error) {
	return s.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigShopShowBalance)
}

// IsSceneVirEnable 是否经营虚拟商品
func (s *SysConfigDs) IsSceneVirEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsVirEnable()
}

// IsSceneEntityEnable 是否经营实体商品
func (s *SysConfigDs) IsSceneEntityEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsEntityEnable()
}

// IsSceneCardEnable 是否经营礼品兑换
func (s *SysConfigDs) IsSceneCardEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsCardEnable()
}

// IsSceneDistributionEnable 是否启用分销
func (s *SysConfigDs) IsSceneDistributionEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsDistributionEnable()
}

// IsSceneIntegralEnable 是否启用积分
func (s *SysConfigDs) IsSceneIntegralEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsIntegralEnable()
}

// IsSceneCinemaEnable 是否经营电影票
func (s *SysConfigDs) IsSceneCinemaEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsCinemaEnable()
}

// IsSceneKfcEnable 是否经营肯德基
func (s *SysConfigDs) IsSceneKfcEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsKfcEnable()
}

// IsSceneMeiTuanEnable 是否经营美团外卖
func (s *SysConfigDs) IsSceneMeiTuanEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsMeiTuanEnable()
}

// IsSceneDGSSEnable 是否经营蛋糕甜品
func (s *SysConfigDs) IsSceneDGSSEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsDGSSEnable()
}

// IsSceneStarBucksEnable 是否经营星巴克
func (s *SysConfigDs) IsSceneStarBucksEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsStarBucksEnable()
}

// IsSceneCouponEnable 是否可以使用优惠券
func (s *SysConfigDs) IsSceneCouponEnable(ctx context.Context) bool {
	config, _ := s.getSceneConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	sceneSlice := config.GetShopScene()
	return sceneSlice.IsCouponEnable()
}

// getLoginPlatformConfig 获取登录平台配置
func (s *SysConfigDs) getLoginPlatformConfig(ctx context.Context) (*do.SysConfigDo, error) {
	return s.sysConfigRepo.FindByConfigKey(ctx, valobj.SysConfigLoginPlatform)
}

// IsLoginPlatformH5 是否启用H5端
func (s *SysConfigDs) IsLoginPlatformH5(ctx context.Context) bool {
	config, _ := s.getLoginPlatformConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	platformSlice := config.GetShopLoginPlatform()
	return platformSlice.IsH5Enable()
}

// IsLoginPlatformAlipayMiniProgram 是否启用支付宝小程序
func (s *SysConfigDs) IsLoginPlatformAlipayMiniProgram(ctx context.Context) bool {
	config, _ := s.getLoginPlatformConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	platformSlice := config.GetShopLoginPlatform()
	return platformSlice.IsAlipayAppletEnable()
}

// IsLoginPlatformWechatOfficialAccount 是否启用微信公众号
func (s *SysConfigDs) IsLoginPlatformWechatOfficialAccount(ctx context.Context) bool {
	config, _ := s.getLoginPlatformConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	platformSlice := config.GetShopLoginPlatform()
	return platformSlice.IsWechatOfficialAccountEnable()
}

// IsLoginPlatformWechatMiniProgram 是否启用微信小程序
func (s *SysConfigDs) IsLoginPlatformWechatMiniProgram(ctx context.Context) bool {
	config, _ := s.getLoginPlatformConfig(ctx)
	if !config.IsEnable() {
		return false
	}
	platformSlice := config.GetShopLoginPlatform()
	return platformSlice.IsWechatAppletEnable()
}

// GetGoodsShowType 获取商品展示维度
func (s *SysConfigDs) GetGoodsShowType(ctx context.Context) valobj.SysConfigGoodsShowTypeObj {
	config, _ := s.getGoodsShowTypeConfig(ctx)
	return config.GetGoodsShowType()
}

// LoadDefault 加载默认配置
func (s *SysConfigDs) LoadDefault(ctx context.Context, configs []*do.SysConfigDo, configKeys ...string) []*do.SysConfigDo {
	if len(configKeys) > 0 {
		existsKeys := slice.Map(configs, func(index int, item *do.SysConfigDo) string {
			return item.ConfigKey
		})
		for _, v := range configKeys {
			if !helper.InSlice(v, existsKeys) {
				val := valobj.GetSysConfigDefaultVal(v)
				if val != "" {
					configs = append(configs, &do.SysConfigDo{
						ConfigKey:   v,
						ConfigValue: val,
						CustomerId:  isolationcustomer.GetCustomerIdZero(ctx),
						ShopId:      isolationcustomer.GetShopIdZero(ctx),
					})
				}
			}
		}
	} else {
		existsKeys := slice.Map(configs, func(index int, item *do.SysConfigDo) string {
			return item.ConfigKey
		})
		for key, val := range valobj.GetSysConfigDefaultObj() {
			if !helper.InSlice(key, existsKeys) {
				configs = append(configs, &do.SysConfigDo{
					ConfigKey:   key,
					ConfigValue: val,
					CustomerId:  isolationcustomer.GetCustomerIdZero(ctx),
					ShopId:      isolationcustomer.GetShopIdZero(ctx),
				})
			}
		}
	}

	return configs
}

// IsShopShowBalance 商城是否展示分销商余额
func (s *SysConfigDs) IsShopShowBalance(ctx context.Context) bool {
	config, _ := s.getShopShowBalanceConfig(ctx)
	return config.GetShowShowBalance().IsYes()
}
