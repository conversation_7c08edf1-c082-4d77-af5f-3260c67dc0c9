package ds

import (
	"cardMall/api/apierr"
	"cardMall/grpc/reseller/resellerv1"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/rpc"
	bizDo "cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/data/ent/order"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/redis/go-redis/v9"
	"github.com/shopspring/decimal"
	"strconv"
)

type ResellerDs struct {
	resellerRpc          rpc.ResellerRpc
	customerResellerRepo repository.CustomerResellerRepo
	balanceOrderTccRpc   rpc.BalanceOrderTccRpc
	data                 *data.Data
	conf                 *conf.Bootstrap
	log                  *log.Helper
}

func NewResellerDs(resellerRpc rpc.ResellerRpc, customerResellerRepo repository.CustomerResellerRepo, balanceOrderTccRpc rpc.BalanceOrderTccRpc, data *data.Data, conf *conf.Bootstrap, log *log.Helper) *ResellerDs {
	return &ResellerDs{resellerRpc: resellerRpc, customerResellerRepo: customerResellerRepo, balanceOrderTccRpc: balanceOrderTccRpc, data: data, conf: conf, log: log}
}

func (r *ResellerDs) GetResellerId(ctx context.Context) (resellerId int, err error) {
	resellerConf, _ := r.customerResellerRepo.FindWithCustomerId(ctx)
	if resellerConf == nil {
		err = apierr.ErrorDbNotFound("未找到分销商配置")
		return
	}
	return resellerConf.ResellerID, nil
}

func (r *ResellerDs) GetPlatformReseller(context.Context) (resellerInfo *bizDo.ResellerDo, err error) {
	return &bizDo.ResellerDo{
		Id:        helper.IgnoreErr(strconv.Atoi(r.conf.Recharge.MerchantId)),
		Name:      "",
		SecretKey: r.conf.Recharge.SecretKey,
		Status:    valobj.ResellerStatusEnable,
	}, nil

}
func (r *ResellerDs) GetResellerInfo(ctx context.Context) (resellerInfo *bizDo.ResellerDo, err error) {
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	if customerId == 0 {
		err = apierr.ErrorSystemPanic("获取企业信息失败")
		return
	}
	ctx = isolationcustomer.WithCustomerAndDisableShopCtx(ctx, customerId)

	//从缓存获取
	resellerInfo = r.getFromCache(ctx)
	if !resellerInfo.IsNil() {
		return
	}

	var resellerId int
	resellerId, err = r.GetResellerId(ctx)
	if err != nil {
		return
	}
	rsp, err := r.resellerRpc.GetResellerClient().GetResellerDetail(ctx, &resellerv1.GetResellerDetailReq{Id: int32(resellerId)})
	if err != nil {
		return nil, err
	}
	resellerInfo = &bizDo.ResellerDo{
		Id:        int(rsp.Id),
		Name:      rsp.Name,
		SecretKey: rsp.GetApiSetting().GetSecretKey(),
		Status:    valobj.ResellerStatusObj(rsp.GetStatus()),
		Balance:   rsp.Balance.Balance,
	}
	if resellerInfo.IsNil() {
		err = apierr.ErrorSystemPanic("获取分销商信息失败")
	}
	if !resellerInfo.Status.IsEnable() {
		err = apierr.ErrorNotAllow("分销商状态异常")
		return
	}

	//写入缓存
	r.setCache(ctx, resellerInfo)
	return
}

func (r *ResellerDs) setCache(ctx context.Context, reseller *bizDo.ResellerDo) {
	//写入缓存
	key := r.getCacheKey(ctx)
	_, err := r.data.Rdb.Set(ctx, key, reseller.Json(), constants.CustomerResellerCacheTTL).Result()
	if err != nil && err != redis.Nil {
		log.Errorf("写入分销商缓存失败: %s", err.Error())
	}
	return
}

func (r *ResellerDs) getFromCache(ctx context.Context) (reseller *bizDo.ResellerDo) {
	resellerJson, err := r.data.Rdb.Get(ctx, r.getCacheKey(ctx)).Result()
	if err != nil && err != redis.Nil {
		log.Errorf("获取分销商缓存失败: %s", err.Error())
		return
	}
	if resellerJson == "" {
		return
	}

	reseller = &bizDo.ResellerDo{}
	err = json.Unmarshal([]byte(resellerJson), &reseller)
	if err != nil {
		log.Errorf("解析分销商缓存数据失败: %s", err.Error())
		return
	}
	return reseller
}

func (r *ResellerDs) getCacheKey(ctx context.Context) string {
	return constants.CustomerResellerCachePrefixTpl.GetKey(ctx)
}

func (r *ResellerDs) GetResellerBalance(ctx context.Context) (balance float64, err error) {
	resellerInfo, err := r.GetResellerInfo(ctx)
	if err != nil {
		return
	}
	return resellerInfo.Balance, nil
}

// DeductBalance 分销商扣款
func (r *ResellerDs) DeductBalance(ctx context.Context, orderNumber string, balance float64, fn func(context.Context, string) error) error {
	var err error
	requestId := helper.GenRequestId(orderNumber)
	defer func() {
		if err != nil {
			if logErr := r.balanceOrderTccRpc.Cancel(ctx, &bo.BalanceOrderTccCancelReqBo{RequestId: requestId, Type: bo.PayType}); logErr != nil {
				r.log.Errorf("tcc cancel %v", logErr)
			}
		}
	}()

	resellerInfo, err := r.GetResellerInfo(ctx)
	if err != nil {
		return err
	}

	// 1. 发起支付
	if err = r.balanceOrderTccRpc.Try(ctx, &bo.BalanceOrderTccTryReqBo{
		DistributorId: resellerInfo.Id,
		Amount:        int(decimal.NewFromFloat(balance).Mul(decimal.NewFromFloat(10000)).IntPart()),
		RequestId:     requestId,
		Remark:        "蓝熊saas: " + orderNumber,
	}); err != nil {
		r.log.Errorf("订单<%s>分销商：%d TCC ERR:%v", order.OrderNumber, resellerInfo.Id, err)
		return err
	}

	err = fn(ctx, requestId)
	if err != nil {
		return err
	}

	if err = r.balanceOrderTccRpc.Confirm(ctx, &bo.BalanceOrderTccConfirmReqBo{
		RequestId: requestId,
		Type:      bo.PayType,
	}); err != nil {
		r.log.Errorw("Confirm", err)
		return err
	}

	return nil
}

// Refund 分销商退款
func (r *ResellerDs) Refund(ctx context.Context, order *do.OrderDo, refundNo string, balance float64, fn func(context.Context) error) error {
	var err error
	var refundRequestId string
	if balance > 0 {
		refundRequestId = helper.GenRefundRequestId(refundNo)
		defer func() {
			if err != nil {
				if logErr := r.balanceOrderTccRpc.Cancel(ctx, &bo.BalanceOrderTccCancelReqBo{RequestId: refundRequestId, Type: bo.RefundType}); logErr != nil {
					r.log.Errorf("tcc cancel %v", logErr)
				}
			}
		}()

		if err = r.balanceOrderTccRpc.TryRefund(ctx, &bo.BalanceOrderTccTryRefundReqBo{
			Amount:       int(decimal.NewFromFloat(balance).Mul(decimal.NewFromFloat(10000)).IntPart()),
			RequestId:    refundRequestId,
			PayRequestId: order.RequestId,
		}); err != nil {
			r.log.Errorw("tcc try", err)
			return err
		}
	}

	err = fn(ctx)
	if err != nil {
		return err
	}

	if balance > 0 {
		if err = r.balanceOrderTccRpc.Confirm(ctx, &bo.BalanceOrderTccConfirmReqBo{
			RequestId: refundRequestId,
			Type:      bo.RefundType,
		}); err != nil {
			r.log.Errorf("Refund confirm error: %+v", err)
			return err
		}
	}

	return nil
}
