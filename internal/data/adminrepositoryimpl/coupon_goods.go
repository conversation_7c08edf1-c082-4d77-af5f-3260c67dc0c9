package adminrepositoryimpl

import (
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/coupongood"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/adminbiz/repository"
	"context"
)

type CouponGoodsRepoImpl struct {
	Base[ent.CouponGood, do.CouponGoodsDo, ent.CouponGoodQuery]
	data *data.Data
}

func NewCouponGoodsRepoImpl(data *data.Data) repository.CouponGoodsRepo {
	return &CouponGoodsRepoImpl{data: data}
}

func (c *CouponGoodsRepoImpl) Add(ctx context.Context, in *bo.CouponGoodsAddBo) (int, error) {
	res := c.data.GetDb(ctx).CouponGood.Create().
		SetCouponID(in.CouponId).
		SetGoodsID(in.GoodsId).
		SetGoodsSkuID(in.SkuId).
		SetGoodsSkuNo(in.SkuNo).
		SaveX(ctx)
	return res.ID, nil
}

func (c *CouponGoodsRepoImpl) GetByCouponId(ctx context.Context, CouponId int) ([]*do.CouponGoodsDo, error) {
	res := c.data.GetDb(ctx).CouponGood.Query().Where(coupongood.CouponIDEQ(CouponId)).AllX(ctx)
	return c.ToEntities(res), nil
}

func (c *CouponGoodsRepoImpl) DelByCouponId(ctx context.Context, couponId int) (int, error) {
	return c.data.GetDb(ctx).CouponGood.Delete().Where(coupongood.CouponIDEQ(couponId)).Exec(ctx)
}
