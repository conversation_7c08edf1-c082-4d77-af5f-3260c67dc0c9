package adminrepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/coupon"
	"cardMall/internal/module/adminbiz/bo"
	"cardMall/internal/module/adminbiz/do"
	"cardMall/internal/module/adminbiz/repository"
	"context"
	"time"
)

type CouponRepoImpl struct {
	Base[ent.Coupon, do.CouponInfo, ent.CouponQuery]
	data *data.Data
}

func NewCouponRepoImpl(data *data.Data) repository.CouponRepo {
	return &CouponRepoImpl{data: data}
}

func (c *CouponRepoImpl) Add(ctx context.Context, bo *bo.CouponAddBo) (*do.CouponInfo, error) {
	sql := c.data.GetDb(ctx).Coupon.Create().
		SetQuantity(bo.Quantity).
		SetTitle(bo.Title).
		SetRemark(bo.Remark).
		SetCollectionStartTime(bo.CollectionStartTime).
		SetCollectionEndTime(bo.CollectionEndTime).SetEffectTimeType(bo.EffectTimeType).SetProductLimit(bo.ProductLimit)

	//生效时间类型：1=时间段，2=领取后xx天有效
	if bo.EffectTimeType == valobj.CouponEffectTimeTypeTimeSlot {
		sql.SetEffectStartTime(bo.EffectStartTime).SetEffectEndTime(bo.EffectEndTime)
	} else if bo.EffectTimeType == valobj.CouponEffectTimeTypeAfterCollection {
		sql.SetEffectStartDay(bo.EffectStartDay).SetEffectEndDay(bo.EffectEndDay)
	}

	res := sql.SetLimitAmount(bo.LimitAmount).
		SetDiscountAmount(bo.DiscountAmount).
		SetCollectionQuantity(bo.CollectionQuantity).
		SetCollectionType(bo.CollectionType).
		SetSurplusNum(bo.Quantity).
		SetCreateTime(int(time.Now().Unix())).
		SetUpdateTime(int(time.Now().Unix())).SaveX(ctx)
	return c.ToEntity(res), nil
}

func (c *CouponRepoImpl) List(ctx context.Context, in *bo.CouponListBo) (int, []*do.CouponInfo, error) {
	sql := c.data.GetDb(ctx).Coupon.Query()

	now := time.Now().Unix()
	if in.Status == valobj.CouponStatusNotStart {
		sql.Where(coupon.CollectionStartTimeGT(int(now)))
	} else if in.Status == valobj.CouponStatusStarting {
		sql.Where(coupon.CollectionStartTimeLTE(int(now))).Where(coupon.CollectionEndTimeGTE(int(now)))
	} else if in.Status == valobj.CouponStatusFinish {
		sql.Where(coupon.CollectionEndTimeLT(int(now)))
	}

	if in.Title != "" {
		sql.Where(coupon.TitleContains(in.Title))
	}

	countSql := sql.Clone()
	count := countSql.CountX(ctx)
	if count == 0 {
		return 0, nil, nil
	}

	res := sql.Offset(in.GetOffset()).Limit(in.GetPageSize()).Order(ent.Desc(coupon.FieldID)).AllX(ctx)
	return count, c.ToEntities(res), nil
}

func (c *CouponRepoImpl) Detail(ctx context.Context, id int) (*do.CouponInfo, error) {
	res := c.data.GetDb(ctx).Coupon.Query().Where(coupon.IDEQ(id)).FirstX(ctx)
	return c.ToEntity(res), nil
}

func (c *CouponRepoImpl) Update(ctx context.Context, in *bo.CouponUpdateBo) (int, error) {
	sql := c.data.GetDb(ctx).Coupon.Update().Where(coupon.IDEQ(in.Id))
	if in.Title != "" {
		sql.SetTitle(in.Title)
	}
	if in.Quantity > 0 {
		sql.SetQuantity(in.Quantity)
	}
	if in.Remark != "" {
		sql.SetRemark(in.Remark)
	}
	if in.Quantity-in.OldQuantity > 0 {
		sql.AddSurplusNum(in.Quantity - in.OldQuantity)
	}
	sql.SetUpdateTime(int(time.Now().Unix()))
	row := sql.SaveX(ctx)
	return row, nil
}

func (c *CouponRepoImpl) Abolish(ctx context.Context, id int, abolishNum int) (int, error) {
	sql := c.data.GetDb(ctx).Coupon.Update().Where(coupon.IDEQ(id))
	sql.SetIsAbolish(valobj.CouponIsAbolishYes).SetUpdateTime(int(time.Now().Unix())).AddAbolishNum(abolishNum).AddSurplusNum(-abolishNum)
	row := sql.SaveX(ctx)
	return row, nil
}

func (c *CouponRepoImpl) Del(ctx context.Context, id int) (int, error) {
	return c.data.GetDb(ctx).Coupon.Delete().Where(coupon.IDEQ(id)).Exec(ctx)
}
