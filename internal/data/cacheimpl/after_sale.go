package cacheimpl

import (
	"cardMall/internal/biz/cache"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"context"
)

type AfterSaleCacheImpl struct {
	data *data.Data
}

func NewAfterSaleCacheImpl(data *data.Data) cache.AfterSaleCache {
	return &AfterSaleCacheImpl{data: data}
}

func (o *AfterSaleCacheImpl) LockCreateAfterSale(ctx context.Context, orderNumber string) error {
	return o.data.Rdb.SetNX(ctx, constants.AfterSaleCreateLockTpl.GetKey(ctx, orderNumber), 0, constants.AfterSaleCreateLockTpl.GetTTL()).Err()
}

func (o *AfterSaleCacheImpl) UnlockCreateAfterSale(ctx context.Context, orderNumber string) error {
	return o.data.Rdb.Del(ctx, constants.AfterSaleCreateLockTpl.GetKey(ctx, orderNumber)).Err()
}
