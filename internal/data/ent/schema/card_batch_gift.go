// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type CardBatchGift struct {
	ent.Schema
}

func (CardBatchGift) Fields() []ent.Field {
	return []ent.Field{field.Int("id").Comment("ID"),
		field.String("card_gift_number").Comment("券码/兑换码"),
		field.Int("card_batch_id").Comment("卡券批次id"),
		field.String("card_batch_number").Comment("卡券批次号"),
		field.String("card_batch_name").Comment("卡券批次名称"),
		field.Float("exchange_amount").Comment("金额"),
		field.Int("card_number_type").GoType(valobj.CardBatchNumberType(1)).Comment("兑换码样式 1.卡密 2.链接 3.二维码 4.白名单"),
		field.Int("use_expire_start").Comment("到期时间开始"),
		field.Int("use_expire_end").Comment("到期时间结束"),
		field.Int("user_id").Comment("会员ID"),
		field.String("user_name").Comment("会员名称"),
		field.Int("bind_status").GoType(valobj.CardBatchCouponBindStatus(1)).Comment("绑定状态 1.未绑定 2已绑定"),
		field.Int("bind_time").Comment("绑定时间"),
		field.Float("card_gift_balance").Comment("余额"),
		field.Int("status").GoType(valobj.CardBatchGiftStatus(1)).Comment("卡券状态 1 待使用 2 已使用 3 已作废"),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间")}
}
func (CardBatchGift) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("card_batch", CardBatch.Type).Ref("card_batch_gift").Field("card_batch_id").Unique().Required(),
		edge.To("card_gift_operator_log", CardGiftOperatorLog.Type),
	}
}
func (CardBatchGift) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "card_batch_gift"}}
}
