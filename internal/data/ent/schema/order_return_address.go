// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type OrderReturnAddress struct {
	ent.Schema
}

func (OrderReturnAddress) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("supplier_id").Comment("供应商ID"),
		field.String("contact_name").Comment("联系人"),
		field.String("contact_phone").Comment("联系电话"),
		field.String("address").Comment("收货地址【用户需要寄商品到的地址】"),
		field.String("title").Comment("地址标题"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("更新时间"),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
	}
}
func (OrderReturnAddress) Edges() []ent.Edge {
	return nil
}
func (OrderReturnAddress) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "order_return_address"}}
}
