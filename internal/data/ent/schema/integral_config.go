// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type IntegralConfig struct {
	ent.Schema
}

func (IntegralConfig) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("exchange_rate").Comment("汇率，xx积分=1元"),
		field.String("name").Comment("积分名称"),
		field.Int("deduction_type").GoType(valobj.IntegralConfigDeductionType(0)).Comment("抵扣类型：1=订单金额抵扣，2=利润部分抵扣"),
		field.Float("deduction_rate").Comment("最大抵扣百分比"),
		field.Int("expire_type").GoType(valobj.IntegralConfigExpireTypeObj(0)).Comment("过期策略类型：1=永不过期，2=相对年逐笔过期，3=相对年所有清零"),
		field.Int("expire_year").Comment("过期策略：xxx年后过期"),
		field.Int("integral_cash").Comment("是否开启积分+钱购 1=否，2=是").GoType(valobj.IntegralConfigCashObj(0)).Default(1),
		field.Int("integral_exchange").Comment("是否开启积分兑换1=否，2=是").GoType(valobj.IntegralConfigExchangeObj(0)).Default(1),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("integral_shop_status").Comment("是否展示积分商城入口：1=否，2=是").GoType(valobj.IntegralShopStatusObj(0)).Default(1),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
	}
}
func (IntegralConfig) Edges() []ent.Edge {
	return nil
}
func (IntegralConfig) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "integral_config"}}
}
