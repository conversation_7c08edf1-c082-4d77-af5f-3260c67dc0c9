// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type SysTask struct {
	ent.Schema
}

func (SysTask) Fields() []ent.Field {
	return []ent.Field{field.Int("id").Comment("任务id"),
		field.String("sys_task_name").Comment("任务名称"),
		field.String("sys_task_number").Comment("任务编号"),
		field.Int("sys_task_type").GoType(valobj.SysTaskType(1)).Comment("任务类型1=导出"),
		field.Int("sys_task_source").GoType(valobj.SysTaskSource(1)).Comment("任务来源1=礼包券码下载"),
		field.String("parameter").Comment("任务参数"),
		field.String("related_no").Comment("关联单号"),
		field.String("download_url").Comment("文件下载地址"),
		field.Int("status").GoType(valobj.SysTaskStatus(1)).Comment("任务状态1=未开始，2=执行中，3=已完成，4=已失败"),
		field.String("remark").Comment("备注"),
		field.String("fail_reason").Comment("失败原因"),
		field.Int("user_id").Comment("会员ID"),
		field.String("user_name").Comment("会员名称"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("finish_time").Comment("修改时间"),
		field.Int("customer_id").Comment("客户id"),
		field.Int("shop_id").Comment("所属商城id，客户级为0")}
}
func (SysTask) Edges() []ent.Edge {
	return nil
}
func (SysTask) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "sys_task"}}
}
