// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type OrderLogistics struct {
	ent.Schema
}

func (OrderLogistics) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("order_number").Comment("订单编号"),
		field.String("kd_code").Comment("快递平台编号"),
		field.String("logistics_no").Comment("物流单号"),
		field.Int("op_time").Comment("发生时间"),
		field.String("op_message").Comment("物流变化信息"),
		field.String("op_desc").Comment("状态"),
		field.String("op_code").GoType(valobj.OrderLogisticsOpCodeObj("")).Comment("状态码"),
		field.String("address_text").Comment("所在城市"),
		field.Int("create_time").Comment("消息推送时间"),
		field.Int("from").GoType(valobj.OrderLogisticsFromObj(0)).Comment("消息获取方式:0=订阅返回，1=推送，2=主动查询"),
		field.Int("customer_id").Comment("客户id"),
		field.Int("shop_id").Comment("所属商城id，客户级为0"),
	}
}
func (OrderLogistics) Edges() []ent.Edge {
	return nil
}
func (OrderLogistics) Annotations() []schema.Annotation {
	return nil
}
