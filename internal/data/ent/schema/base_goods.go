// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type BaseGoods struct {
	ent.Schema
}

func (BaseGoods) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("product_id").Comment("产品编码"),
		field.Float("channel_price").Comment("合作商折扣价"),
		field.Float("original_price").Comment("原价(面额)"),
		field.String("product_name").Comment("产品名称"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("status").Comment("0=禁用,1=启用"),
		field.Int("type").Comment("商品类型:1=直充,2=卡密"),
		field.Int("card_expire_time").Comment("卡密有效时间").Default(0),
		field.Int("account_type").GoType(valobj.BaseGoodsAccountTypeObj(0)).Comment("充值账号类型").Default(valobj.BaseGoodsAccountTypeOther.GetValue()),
		field.Int("customer_id").Comment("客户ID"),
		field.Int("update_time").Default(0).UpdateDefault(func() int { return int(time.Now().Unix()) }).Comment("更改时间"),
	}
}
func (BaseGoods) Edges() []ent.Edge {
	return nil
}
func (BaseGoods) Annotations() []schema.Annotation {
	return nil
}
