// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type SysTaskLog struct {
	ent.Schema
}

func (SysTaskLog) Fields() []ent.Field {
	return []ent.Field{field.Int("id").Comment("ID"),
		field.Int("sys_task_id").Comment("任务id"),
		field.String("content").Comment("日志内容 【 描述】"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("customer_id").Comment("客户id"),
		field.Int("shop_id").Comment("所属商城id，客户级为0")}
}
func (SysTaskLog) Edges() []ent.Edge {
	return nil
}
func (SysTaskLog) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "sys_task_log"}}
}
