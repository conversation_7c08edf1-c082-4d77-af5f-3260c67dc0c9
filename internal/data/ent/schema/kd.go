// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type Kd struct {
	ent.Schema
}

func (Kd) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("name").Comment("快递公司名称"),
		field.String("code").Comment("公司编码"),
		field.Int("status").GoType(valobj.KdStatusObj(0)).Comment("状态:1=启用，2=禁用"),
	}
}
func (Kd) Edges() []ent.Edge {
	return nil
}
func (Kd) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "kd"}}
}
