// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type Banner struct {
	ent.Schema
}

func (Banner) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("name").Comment("名称"),
		field.String("image").Comment("图片"),
		field.Int("sort").Comment("排序"),
		field.Int("status").Comment("状态:0=禁用,1=启用").Default(0),
		field.String("url").Comment("跳转地址").Default(""),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("relation_type").Comment("跳转类型:0=无，1=类目，2=品牌，2=积分商城，4=链接").GoType(valobj.BannerRelationTypeObj(0)).Default(0),
		field.String("relation_value").Comment("跳转值").Default(""),
		field.String("relation_value_ids").Comment("关联节点ID,1=类目，2=品牌时有效, ,拼接").Default(""),
		field.Int("is_default").Comment("是否是默认banner 1=否，2=是").GoType(valobj.BannerIsDefaultObj(0)).Default(valobj.BannerIsDefaultNo.GetValue()),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
	}
}
func (Banner) Edges() []ent.Edge {
	return nil
}
func (Banner) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "banner"}}
}
