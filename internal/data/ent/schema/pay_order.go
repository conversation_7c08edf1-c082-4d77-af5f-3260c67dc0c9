// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type PayOrder struct {
	ent.Schema
}

func (PayOrder) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("order_number").Comment("支付单号"),
		field.String("out_trade_no").Comment("外部订单号").Default(""),
		field.String("third_trade_no").Comment("第三方订单号").Default(""),
		field.Int("pay_type").GoType(valobj.PayOrderPayTypeObj(0)).Comment("支付方式:0=未知,1=微信小程序,2=微信H5,3=支付宝H5").Default(0),
		field.Int("settlement_type").GoType(valobj.PayOrderSettlementTypeObj(0)).Comment("结算方式:1=现金余额,2=现金+积分").Default(0),
		field.Int("order_type").GoType(valobj.PayOrderTypeObj(0)).Comment("订单类型:1=充值，2=美团").Default(1),
		field.Int("user_id").Comment("用户ID"),
		field.Int("brand_id").Comment("品牌ID"),
		field.Int("goods_id").Comment("商品ID"),
		field.Float("total_amount").Comment("订单总金额"),
		field.Float("total_pay_amount").Comment("总支付金额"),
		field.Int("total_pay_integral").Comment("总支付积分").Default(0),
		field.Int("num").Comment("购买数量"),
		field.Int("status").GoType(valobj.PayOrderStatusObj(0)).Comment("状态:1=待支付,2=已支付,3=退款中,4=全额退款,5=部分退款，6=已取消").Default(1),
		field.Int("pay_time").Comment("支付时间").Default(0),
		field.Int("refund_time").Comment("最近一次退款时间").Default(0),
		field.Float("refund_amount").Comment("累计退款金额").Default(0),
		field.Int("refund_integral").Comment("累计回退积分").Default(0),
		field.Int("coupon_code_id").Comment("使用优惠券ID").Default(0),
		field.Int("coupon_id").Comment("优惠券批次ID").Default(0),
		field.Float("coupon_discount_amount").Comment("优惠券实际优惠金额").Default(0),
		field.Float("integral_discount_amount").Comment("积分抵扣金额").Default(0),
		field.Int("user_integral_log_id").Comment("积分使用日志ID").Default(0),
		field.String("pay_params").Comment("支付信息").Default(""),
		field.String("return_url").Comment("外部订单支付同步返回地址").Default(""),
		field.String("pay_url").Comment("跳转支付的url").Default(""),
		field.Int("expire_time").Comment("外部订单支付有效期").Default(0),
		field.Int("site_id").Comment("订单来源站点").Default(0),
		field.Int("pay_merchant_id").Comment("支付中心商户ID").Default(0),
		field.Int("create_time").Comment("订单创建时间"),
		field.Int("update_time").Comment("最近一次修改时间"),
		field.String("alipay_mini_order_no").Comment("支付宝交易组件订单号").Default(""),
		field.String("card_coupon_number").Comment("券码/兑换码").Default(""),
		field.String("alipay_mini_order_json").Comment("支付宝交易组件订单信息").Default(""),
		field.Float("freight_fee").Comment("运费").Default(0),
		field.Float("goods_pay_amount").Comment("商品支付金额").Default(0),
		field.String("pay_serial_number").Comment("支付流水号").Optional(),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
		field.Float("card_gift_amount").Comment("礼品卡抵扣金额").Default(0),
		field.Int("payee_type").Optional().Comment("收款人类型：1-蓝熊SaaS收款，2-三方商户号收款，3-三方自己收款"),
		field.Int("activity_id").Default(0).Comment("活动ID"),
	}
}
func (PayOrder) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("user", User.Type).Field("user_id").Unique().Required(),
	}
}
func (PayOrder) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "pay_order"}}
}
