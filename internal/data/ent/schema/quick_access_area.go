// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type QuickAccessArea struct {
	ent.Schema
}

func (QuickAccessArea) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("name").Comment("名称"),
		field.Int("status").Comment("状态 1=禁用，2=启用").GoType(valobj.QuickAccessAreaStatusObj(0)).Default(1),
		field.Int("relation_type").Comment("关联类型:0=无，1=类目，2=品牌，2=积分商城，4=链接").GoType(valobj.QuickAccessAreaRelationTypeObj(0)).Default(0),
		field.String("relation_value").Comment("关联值"),
		field.String("icon").Comment("图标"),
		field.Int("sort").Comment("排序，数字越大，图标越靠后"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.String("relation_value_ids").Comment("关联节点ID,1=类目，2=品牌时有效 ,拼接").Default(""),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
	}
}
func (QuickAccessArea) Edges() []ent.Edge {
	return nil
}
func (QuickAccessArea) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "quick_access_area"}}
}
