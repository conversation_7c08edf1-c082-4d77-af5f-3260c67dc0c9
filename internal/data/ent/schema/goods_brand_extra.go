// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type GoodsBrandExtra struct {
	ent.Schema
}

func (GoodsBrandExtra) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("code").Comment("品牌编号"),
		field.Int("shop_id").Comment("商城ID"),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("recommend").Comment("是否推荐0=否,1=是").GoType(valobj.GoodsBrandRecommendObj(0)).Default(valobj.GoodsBrandRecommendNo.GetInt()),
		field.Int("sort").Comment("排序"),
		field.Int("status").Comment("状态:1=启用，0=禁用").GoType(valobj.GoodsBrandStatusObj(0)).Default(valobj.GoodsBrandStatusEnable.GetInt()),
		field.Int("create_time"),
		field.Int("update_time")}
}
func (GoodsBrandExtra) Edges() []ent.Edge {
	return nil
}
func (GoodsBrandExtra) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "goods_brand_extra"}}
}
