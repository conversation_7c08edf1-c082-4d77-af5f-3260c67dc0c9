// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type HytBrand struct {
	ent.Schema
}

func (HytBrand) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("hyt_id").Comment("id"),
		field.String("hyt_name").Comment("品牌名称"),
		field.Int("customer_id").Comment("客户ID"),
		field.Int("shop_id").Comment("所属商城id"),
		field.Int("supplier_id").Comment("供应商"),
		field.Int("create_time"),
		field.Int("update_time"),
	}
}
func (HytBrand) Edges() []ent.Edge {
	return nil
}
func (HytBrand) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "hyt_brand"}}
}
