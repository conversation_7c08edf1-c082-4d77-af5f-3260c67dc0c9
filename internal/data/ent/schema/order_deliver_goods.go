// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type OrderDeliverGoods struct {
	ent.Schema
}

func (OrderDeliverGoods) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("order_deliver_id"),
		field.Int("order_id").Comment("冗余"),
		field.String("sku_no").Comment("sku no"),
		field.String("order_number").Comment("冗余"),
		field.Int("goods_id").Comment("发货商品ID"),
		field.Int("quantity").Comment("发货数量"),
		field.Int("goods_sku_id").Comment("发货商品SKU"),
		field.String("goods_name").Comment("商品名称"),
		field.String("goods_sku_name").Comment("规格名称"),
		field.String("barcode").Comment("条形码【原厂编码】"),
		field.String("supplier_barcode").Comment("供应商编码【第三方编码】"),
		field.Int("customer_id").Comment("客户id"),
		field.Int("shop_id").Comment("所属商城id，客户级为0"),
		field.Int("create_time").Comment("创建时间").Default(0),
	}
}
func (OrderDeliverGoods) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("order_deliver", OrderDeliver.Type).Ref("order_deliver_goods").Field("order_deliver_id").Unique().Required(),
	}
}
func (OrderDeliverGoods) Annotations() []schema.Annotation {
	return nil
}
