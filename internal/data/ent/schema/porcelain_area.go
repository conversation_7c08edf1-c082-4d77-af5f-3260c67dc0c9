// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type PorcelainArea struct {
	ent.Schema
}

func (PorcelainArea) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("name").Comment("名称"),
		field.Int("status").Comment("状态 1=禁用，2=启用"),
		field.Int("relation_type").Comment("关联类型:1=肯德基，2=美团外卖，3=电影订票， 4=类目，5=品牌，6=积分商城，7=链接"),
		field.String("relation_value").Optional().Comment("关联值"),
		field.String("icon").Optional().Comment("小图标"),
		field.Int("sort").Optional().Comment("排序，数字越大，图标越靠后"),
		field.Time("created_at").Optional().Comment("创建时间"),
		field.Time("updated_at").Optional().Comment("修改时间"),
		field.String("relation_value_ids").Optional().Comment("关联节点ID,拼接"),
		field.Int("customer_id").Optional().Comment("企业ID"),
		field.Int("shop_id").Optional().Comment("商城ID"),
	}
}
func (PorcelainArea) Edges() []ent.Edge {
	return nil
}
func (PorcelainArea) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "porcelain_area"}}
}
