// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type SaasShopApplet struct {
	ent.Schema
}

func (SaasShopApplet) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
		field.String("app_id").Comment("小程序appid"),
		field.Int("app_type").Comment("小程序类型 1=微信，2=支付宝").GoType(valobj.SaasShopAppletTypeObj(0)),
		field.String("app_secret").Comment("小程序应用秘钥"),
	}
}
func (SaasShopApplet) Edges() []ent.Edge {
	return nil
}
func (SaasShopApplet) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "saas_shop_applet"}}
}
