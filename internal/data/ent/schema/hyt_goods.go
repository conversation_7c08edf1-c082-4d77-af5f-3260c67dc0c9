// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type HytGoods struct {
	ent.Schema
}

func (HytGoods) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("type").GoType(valobj.UpstreamGoodsType(0)).Comment("上游商品类型"),
		field.String("unique_key").Comment("唯一标识"),
		field.String("data_json").Comment("json 数据"),
		field.Int("customer_id").Comment("客户ID"),
		field.Int("shop_id").Comment("所属商城id"),
		field.Int("supplier_id").Comment("供应商"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("最近一次修改时间"),
	}
}
func (HytGoods) Edges() []ent.Edge {
	return nil
}
func (HytGoods) Annotations() []schema.Annotation {
	return nil
}
