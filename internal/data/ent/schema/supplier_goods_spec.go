// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type SupplierGoodsSpec struct {
	ent.Schema
}

func (SupplierGoodsSpec) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Comment("规格类别编号"),
		field.Int("supplier_id").Comment("分销商ID 冗余"),
		field.String("name").Comment("规格类别名称"),
		field.String("remark").Comment("规格类别注释"),
		field.Int("is_tpl").GoType(valobj.SupplierGoodsSpecTplObj(0)).Optional().Comment("是否常用 【用于常用规格管理】"),
		field.Int("sort").Comment("排序"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
	}
}
func (SupplierGoodsSpec) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("goods_spec_items", SupplierGoodsSpecItem.Type),
	}
}
func (SupplierGoodsSpec) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "supplier_goods_spec"}}
}
