// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type OrderAfterSaleDeal struct {
	ent.Schema
}

func (OrderAfterSaleDeal) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("supplier_id").Comment("供应商ID"),
		field.Int("pid").Comment("售后id"),
		field.String("order_number").Comment("订单号"),
		field.String("original_order_no").Comment("源订单号"),
		field.String("exchange_order_no").Comment("换货的订单号"),
		field.Int("type").GoType(valobj.AfterSaleType(0)).Comment("售后类型 1-仅退款 2-退款退货 3-换货"),
		field.Int("is_dealing").GoType(valobj.BooleanObj(0)).Comment("是否处理中"),
		field.Int("enable").GoType(valobj.BooleanObj(0)).Comment("更新时间"),
		field.String("parent_order_no").Comment("上级订单号"),
		field.String("parent_order_nos").Comment("所有上级订单号【JSON】"),
		field.String("sku_no").Comment("sku no"),
		field.String("goods_name").Comment("商品名称【包含规格信息】 冗余"),
		field.String("main_image").Comment("商品主图"),
		field.Float("sale_price").Comment("售价").Default(0),
		field.Int("sale_integral").Comment("抵扣积分(积分+钱购或者积分兑换)").Default(0),
		field.String("original_sku_no").Comment("源商品 sku_no"),
		field.Int("goods_id").Default(0),
		field.Int("goods_num"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("更新时间"),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
	}
}
func (OrderAfterSaleDeal) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("order_after_sale", OrderAfterSale.Type).Ref("order_after_sale_deal").Field("pid").Unique().Required(),
	}
}
func (OrderAfterSaleDeal) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "order_after_sale_deal"}}
}
