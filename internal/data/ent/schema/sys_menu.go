// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type SysMenu struct {
	ent.Schema
}

func (SysMenu) Fields() []ent.Field {
	return []ent.Field{field.Int("id").Comment("编号"), field.String("menu_name").Comment("菜单名称"),
		field.Int("parent_id").Comment("父菜单ID，一级菜单为0"),
		field.String("menu_path").Comment("前端路由"),
		field.String("menu_icon").Comment("菜单图标"),
		field.Int("menu_sort").Comment("菜单排序"),
		field.Int("menu_type").Comment("类型 1：顶部菜单,2：二级菜单,3：按钮"),
		field.String("api_path").Comment("后端api的path地址"),
		field.Int("api_method").Comment("后端 api请求的 http method:1-GET,2-POST,3-PUT,4-DELETE"),
		field.String("front_component").Comment("前端组件"),
		field.String("front_url").Comment("前端路由"),
		field.Int("process_type").Comment("处理类型，按二进制位表示，第1位-需要登录，2-需要鉴权，3-需要日志，4-需要显示"),
		field.Int("sys_menu_type").GoType(valobj.SysMenuType(1)).Comment("菜单类型1=商城 2=企业"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("official_menu_id").Comment("saas平台对应的源菜单的ID").Default(0),
	}
}
func (SysMenu) Edges() []ent.Edge {
	return nil
}
func (SysMenu) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "sys_menu"}}
}
