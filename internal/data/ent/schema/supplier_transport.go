// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type SupplierTransport struct {
	ent.Schema
}

func (SupplierTransport) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Comment("ID"),
		field.String("name").Comment("模板名称"),
		field.String("description").Comment("模板描述"),
		field.Int("kd_id").Comment("默认快递公司"),
		field.String("kd_name").Comment("默认快递公司名称"),
		field.String("kd_code").Comment("默认快递公司code"),
		field.Int("pricing_mode").GoType(valobj.TransportPricingModeObj(0)).Comment("计费规则(ENUM):1-免运费;2-按件数;3-按重量;4-按体积;"),
		field.Int("supplier_id").Comment("供应商id"),
		field.Float("default_num").Comment("默认数量"),
		field.Float("default_price").Comment("默认运费"),
		field.Float("add_num").Comment("增加数量"),
		field.Float("add_price").Comment("增加运费"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
	}
}
func (SupplierTransport) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("supplier_transport_item", SupplierTransportItem.Type),
		edge.To("supplier_transport_city", SupplierTransportCity.Type),
	}
}
func (SupplierTransport) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "supplier_transport"}}
}
