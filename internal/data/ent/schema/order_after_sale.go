// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type OrderAfterSale struct {
	ent.Schema
}

func (OrderAfterSale) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("user_id").Comment("用户ID"),
		field.Int("supplier_id").Comment("供应商ID"),
		field.Int("order_id").Comment("订单ID"),
		field.String("order_number").Comment("订单号"),
		field.String("reason").Comment("申请售后原因"),
		field.Float("refund_amount").Comment("申请售后金额"),
		field.Float("refund_freight_fee").Comment("已退的运费金额").Default(0),
		field.Float("refund_goods_amount").Comment("累积退款商品金额").Default(0),
		field.String("images").Comment("图片"),
		field.String("remark").Comment("补充描述"),
		field.Int("receive_status").GoType(valobj.AfterSaleReceiveStatus(0)).Comment("收货状态 1-未收到货 2-已收货"),
		field.Int("status").GoType(valobj.AfterSaleStatus(0)).Comment("售后状态 \r\n退款退货：21-退货物流中 22-商家/供应商确认收货 23-确认退款\r\n换货：31-退货物流中 32-商家/供应商确认收货 33-商家/供应商发货\r\n公共状态\r\n1-待处理 2-审核通过 3-审核拒绝 7-买家主动关闭 8-卖家关闭 9-退款/换货完成 \r\n"),
		field.Int("platform_status").GoType(valobj.AfterSalePlatformStatus(0)).Comment("平台售后状态"),
		field.Int("type").GoType(valobj.AfterSaleType(0)).Comment("售后类型 1-仅退款 2-退款退货 3-换货"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("更新时间"),
		field.String("exchange_order_no").Comment("换货订单号").Default(""),
		field.String("after_sale_no").Comment("售后单号").Default(""),
		field.String("original_order_no").Comment("源订单号").Default(""),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
		field.Float("refund_card_gift_amount").Comment("商品退款礼品卡金额").Default(0),
		field.Float("refund_card_gift_freight_fee").Comment("运费退款礼品卡金额").Default(0),
	}
}
func (OrderAfterSale) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("order_after_sale_deliver", OrderAfterSaleDeliver.Type).Unique(),
		edge.To("order_after_sale_goods", OrderAfterSaleGoods.Type),
		edge.To("order_after_sale_deal", OrderAfterSaleDeal.Type),
		edge.To("order_after_sale_log", OrderAfterSaleLog.Type),
	}
}
func (OrderAfterSale) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "order_after_sale"}}
}
