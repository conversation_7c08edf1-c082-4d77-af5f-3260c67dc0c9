// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent/schema/edge"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type SupplierGoodsDraft struct {
	ent.Schema
}

func (SupplierGoodsDraft) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("supplier_id").Comment("供应商ID"),
		field.Int("goods_id").Comment("商品ID"),
		field.String("detail").Comment("修改详情"),
		field.Int("status").GoType(valobj.SupplierGoodsDraftStatusObj(0)).Comment("审核状态 1-待审核 2-审核通过 4-审核驳回 9-保存为草稿"),
		field.Int("op_type").GoType(valobj.SupplierGoodsDraftOpTypeObj(0)).Comment("操作类型 1-上架 2-修改信息 3-新增 4-草稿"),
		field.Int("reject_times").Comment("驳回次数"),
		field.Int("audit_log_id").Comment("最新审核id"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("submit_time").Comment("提交时间"),
		field.String("goods_name").Comment("商品名称 冗余"),
		field.Int("opera_id").Comment("操作人").Default(0),
		field.String("opera_name").Comment("操作人").Default(""),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
	}
}
func (SupplierGoodsDraft) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("goods", SupplierGoods.Type).Ref("goods_draft").Field("goods_id").Unique().Required(),
	}
}
func (SupplierGoodsDraft) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "supplier_goods_draft"}}
}
