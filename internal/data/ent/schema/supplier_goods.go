// Code generated by entimport, DO NOT EDIT.

package schema

import (
	valobj2 "cardMall/internal/biz/valobj"

	"entgo.io/ent/schema/edge"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type SupplierGoods struct {
	ent.Schema
}

func (SupplierGoods) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("type").GoType(valobj2.SupplierGoodsTypeObj(0)).Comment("商品类型:1=虚拟，2=实物"),
		field.Int("category_id").Comment("所属分类"),
		field.Int("brand_id").Comment("品牌名称"),
		field.Int("transport_id").Comment("运费模版"),
		field.Int("supplier_id").Comment("分销商ID"),
		field.String("spec_json").Optional().Comment("spec json 保存规格和规格值顺序"),
		field.String("name").Comment("商品名称"),
		field.String("main_image").Comment("商品主图"),
		field.String("images").Comment("商品图片 JSON"),
		field.String("detail").Comment("商品详情"),
		field.Int("status").GoType(valobj2.SupplierGoodsStatusObj(0)).Comment("状态:0=下架,1=上架"),
		field.Int("stock").Comment("总库存"),
		field.Int("sort").Comment("排序"),
		field.Float("tax_rate").Comment("税率 13% => 0.13"),
		field.String("tax_remark").Comment("税率备注"),
		field.String("not_sale_area").Comment("限制销售区域 为空表示不限售 DOT"),
		field.String("sale_area").Comment("销售区域"),
		field.String("deliver_addr").Comment("发货地址 DOT"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
		// 禁用的sku
		field.String("ban_sku_json").Comment("禁用的sku 保存禁用的sku列表json字符串"),
		field.Int("deliver_timeline").Comment("发货失效 0-未设置；1-当日;2-24h内；3-48h内").GoType(valobj2.SupplierGoodeDeliverTimeline(0)).Default(0),
		field.Int("last_audit_status").Comment("最后的审核状态").GoType(valobj2.SupplierGoodsDraftStatusObj(0)).Default(0),
		// 货易通供应商信息
		field.Int("hyt_supplier_id").Comment("货易通供应商ID").Default(0),
		field.String("hyt_supplier_name").Comment("货易通供应商名称").Default(""),
	}
}

func (SupplierGoods) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("goods_sku", SupplierGoodsSku.Type),
		edge.To("goods_draft", SupplierGoodsDraft.Type),
	}
}

func (SupplierGoods) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "supplier_goods"}}
}
