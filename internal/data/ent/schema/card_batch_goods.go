// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type CardBatchGoods struct {
	ent.Schema
}

func (CardBatchGoods) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Comment("ID"),
		field.Int("card_batch_id").Comment("卡券批次id"),
		field.String("sku_no").Comment("sku编码"),
		field.Int("goods_id").Comment("商品ID"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
	}
}
func (CardBatchGoods) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("card_batch", CardBatch.Type).Ref("card_batch_goods").Field("card_batch_id").Unique().Required(),
	}
}
func (CardBatchGoods) Annotations() []schema.Annotation {
	return nil
}
