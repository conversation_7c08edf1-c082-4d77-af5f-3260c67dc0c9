// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
)

type OrderAfterSaleGoods struct {
	ent.Schema
}

func (OrderAfterSaleGoods) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.Int("order_id").Comment("订单ID"),
		field.String("order_number").Comment("订单号"),
		field.Int("pid").Comment("after_sale_id"),
		field.Int("create_time").Comment("创建时间"),
		field.String("sku_no").Comment("SKU NO"),
		field.Int("sku_id").Comment("sku ID"),
		field.Int("goods_id").Comment("goods id"),
		field.Int("goods_num").Comment("商品数量"),
		field.String("goods_name").Comment("商品名称【包含规格信息】 冗余"),
		field.String("main_images").Comment("商品主图"),
		field.Int("type").GoType(valobj.AfterSaleGoodsType(0)).Comment("操作用户类型 1-售后商品 2-置换的商品"),
		field.Float("sale_price").Comment("售价"),
		field.Float("refund_amount").Comment("商品退款折算金额"),
		field.Int("exchange_id").Comment("换货商品ID").Default(0),
		field.Int("sale_integral").Comment("抵扣积分(积分+钱购或者积分兑换)").Default(0),
		field.Float("supplier_price").Comment("供应价").Default(0),
		field.String("original_sku_no").Comment("源商品 sku_no").Default(""),
		field.Int("shop_id").Comment("saas店铺ID"),
		field.Int("customer_id").Comment("saas客户ID"),
	}
}
func (OrderAfterSaleGoods) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("order_after_sale", OrderAfterSale.Type).Ref("order_after_sale_goods").Field("pid").Unique().Required(),
	}
}
func (OrderAfterSaleGoods) Annotations() []schema.Annotation {
	return nil
}
