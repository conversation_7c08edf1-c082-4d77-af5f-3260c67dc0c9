// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type PayOrderLog struct {
	ent.Schema
}

func (PayOrderLog) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id"),
		field.String("order_number").Comment("支付单号"),
		field.String("out_trade_no").Comment("支付中心订单号"),
		field.String("pay_serial_number").Comment("支付流水号"),
		field.Int("pay_status").Comment("状态：1=未支付，2=已支付").GoType(valobj.PayOrderLogPayStatusObj(0)).Default(1),
		field.Int("create_time").Comment("创建时间-向支付中心发起支付请求时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("customer_id").Comment("企业ID"),
		field.Int("shop_id").Comment("商城ID"),
	}
}
func (PayOrderLog) Edges() []ent.Edge {
	return nil
}
func (PayOrderLog) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "pay_order_log"}}
}
