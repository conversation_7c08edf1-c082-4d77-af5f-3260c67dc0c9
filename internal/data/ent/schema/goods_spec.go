// Code generated by entimport, DO NOT EDIT.

package schema

import (
	"cardMall/internal/biz/valobj"

	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
)

type GoodsSpec struct {
	ent.Schema
}

func (GoodsSpec) Fields() []ent.Field {
	return []ent.Field{
		field.Int("id").Comment("规格类别编号"),
		field.String("name").Comment("规格类别名称"),
		field.String("remark").Comment("规格类别注释"),
		field.Int("format").GoType(valobj.GoodsSpecFormatObj(0)).Comment("显示类型规格: 1-图片 2-文字"),
		field.Int("sort").Comment("排序"),
		field.Int("type").GoType(valobj.GoodsSpecTypeObj(0)).Comment("规格类型:1=实物商品，2=虚拟商品"),
		field.Int("create_time").Comment("创建时间"),
		field.Int("update_time").Comment("修改时间"),
		field.Int("customer_id").Comment("客户id"),
		field.Int("shop_id").Comment("所属商城id，客户级为0"),
	}
}
func (GoodsSpec) Edges() []ent.Edge {
	return nil
}
func (GoodsSpec) Annotations() []schema.Annotation {
	return []schema.Annotation{entsql.Annotation{Table: "goods_spec"}}
}
