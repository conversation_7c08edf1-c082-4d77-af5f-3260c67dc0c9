package apprepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodsbrand"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type GoodsBrandRepoImpl struct {
	Base[ent.GoodsBrand, do.GoodsBrandInfoDo, ent.GoodsBrandQuery]
	data *data.Data
}

func NewGoodsBrandRepoImpl(d *data.Data) repository.GoodsBrandRepo {
	return &GoodsBrandRepoImpl{data: d}
}

func (g *GoodsBrandRepoImpl) All(ctx context.Context, in *bo.GoodsBrandQueryBo) ([]*do.GoodsBrandInfoDo, error) {
	query := g.data.GetDb(ctx).GoodsBrand.Query()
	if in.Status > 0 {
		query.Where(goodsbrand.StatusEQ(int(in.Status)))
	}
	if in.Recommend != nil {
		recommend := *in.Recommend
		query.Where(goodsbrand.RecommendEQ(int(recommend)))
	}
	if in.Size > 0 {
		query.Limit(in.Size)
	}
	res := query.Order(ent.Desc(goodsbrand.FieldSort)).
		AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsBrandRepoImpl) FindById(ctx context.Context, id int) (*do.GoodsBrandInfoDo, error) {
	res := g.data.GetDb(ctx).GoodsBrand.Query().
		Where(goodsbrand.IDEQ(id)).
		FirstX(ctx)
	return g.ToEntity(res), nil
}

func (g *GoodsBrandRepoImpl) GetById(ctx context.Context, id []int) ([]*do.GoodsBrandInfoDo, error) {
	res := g.data.GetDb(ctx).GoodsBrand.Query().
		Where(goodsbrand.IDIn(id...)).
		Where(goodsbrand.StatusEQ(valobj.GoodsBrandStatusEnable.GetInt())).
		AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsBrandRepoImpl) GetMapById(ctx context.Context, id []int) (map[int]*do.GoodsBrandInfoDo, error) {
	d, err := g.GetById(ctx, id)
	if err != nil {
		return nil, err
	}
	if d == nil {
		return nil, nil
	}

	res := make(map[int]*do.GoodsBrandInfoDo, len(d))
	for _, val := range d {
		res[val.Id] = val
	}
	return res, nil
}
