package apprepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/couponcode"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
	"time"
)

type CouponCodeRepoImpl struct {
	Base[ent.CouponCode, do.CouponCodeDo, ent.CouponCodeQuery]
	data *data.Data
}

func NewCouponCodeRepoImpl(d *data.Data) repository.CouponCodeRepo {
	return &CouponCodeRepoImpl{data: d}
}

func (c *CouponCodeRepoImpl) UserCount(ctx context.Context, in *bo.CouponCodeUserCountBo) (int, error) {
	sql := c.data.GetDb(ctx).
		CouponCode.Query().
		Where(couponcode.UserIDEQ(in.UserId))
	if in.Status > 0 {
		sql.Where(couponcode.StatusEQ(int(in.Status)))
	}
	if in.CouponId > 0 {
		sql.Where(couponcode.CouponIDEQ(in.CouponId))
	}
	if in.IsValid {
		now := int(time.Now().Unix())
		sql.Where(couponcode.EffectEndTimeGTE(now))
	}
	return sql.CountX(ctx), nil
}

func (c *CouponCodeRepoImpl) GetUserCoupon(ctx context.Context, in *bo.CouponCodeGetUserBo) ([]*do.CouponCodeDo, error) {
	sql := c.data.GetDb(ctx).CouponCode.Query()
	now := int(time.Now().Unix())
	if in.Status == valobj.CouponCodeClientStatusUnused {
		sql.Where(couponcode.StatusEQ(valobj.CouponCodeStatusClaimed)).Where(couponcode.EffectEndTimeGTE(now))
	} else if in.Status == valobj.CouponCodeClientStatusUsed {
		sql.Where(couponcode.StatusIn(valobj.CouponCodeStatusUsed, valobj.CouponCodeStatusUsing))
	} else if in.Status == valobj.CouponCodeClientStatusExpired {
		sql.Where(couponcode.StatusEQ(valobj.CouponCodeStatusClaimed)).Where(couponcode.EffectEndTimeLT(now))
	}
	if in.UserId > 0 {
		sql.Where(couponcode.UserIDEQ(in.UserId))
	}
	res := sql.AllX(ctx)
	return c.ToEntities(res), nil
}

func (c *CouponCodeRepoImpl) Get(ctx context.Context, in *bo.CouponCodeGetBo) ([]*do.CouponCodeDo, error) {
	sql := c.data.GetDb(ctx).CouponCode.Query()
	if in.ForUpdate {
		sql.ForUpdate()
	}
	if in.CouponId > 0 {
		sql.Where(couponcode.CouponIDEQ(in.CouponId))
	}

	sql.Where(couponcode.StatusEQ(valobj.CouponCodeStatusUnclaimed))
	if in.Num > 0 {
		sql.Limit(in.Num)
	}
	res := sql.AllX(ctx)
	return c.ToEntities(res), nil
}

func (c *CouponCodeRepoImpl) Collection(ctx context.Context, in *bo.CouponCodeCollectionBo) (int, error) {
	row := c.data.GetDb(ctx).CouponCode.Update().
		Where(couponcode.IDIn(in.Id...)).
		SetUpdateTime(int(time.Now().Unix())).
		SetCollectionTime(int(time.Now().Unix())).
		SetStatus(valobj.CouponCodeStatusClaimed).
		SetUserID(in.UserId).
		SetEffectStartTime(in.EffectStartTime).
		SetEffectEndTime(in.EffectEndTime).
		SaveX(ctx)
	return row, nil
}

func (c *CouponCodeRepoImpl) FindById(ctx context.Context, id int) (*do.CouponCodeDo, error) {
	res := c.data.GetDb(ctx).CouponCode.Query().Where(couponcode.IDEQ(id)).FirstX(ctx)
	return c.ToEntity(res), nil
}

func (c *CouponCodeRepoImpl) Update(ctx context.Context, where *bo.CouponCodeUpdateWhereBo, updateData *bo.CouponCodeUpdateDataBo) (int, error) {
	sql := c.data.GetDb(ctx).CouponCode.Update()
	sql.Where(couponcode.IDEQ(where.Id))
	if where.UserId > 0 {
		sql.Where(couponcode.UserIDEQ(where.UserId))
	}
	if where.Status > 0 {
		sql.Where(couponcode.StatusEQ(int(where.Status)))
	}
	row := sql.SetUpdateTime(int(time.Now().Unix())).
		SetUseTime(updateData.UseTime).
		SetStatus(int(updateData.Status)).
		SetOrderNumber(updateData.OrderNumber).
		SaveX(ctx)
	return row, nil
}

func (c *CouponCodeRepoImpl) Add(ctx context.Context, in *bo.CouponCodeAddBo) (int, error) {
	now := int(time.Now().Unix())
	row := c.data.GetDb(ctx).CouponCode.Create().
		SetCouponID(in.CouponId).
		SetCouponCode(in.CouponCode).
		SetStatus(in.Status).
		SetUserID(in.UserId).
		SetCollectionTime(in.CollectionTime).
		SetEffectStartTime(in.EffectStartTime).
		SetEffectEndTime(in.EffectEndTime).
		SetUpdateTime(now).
		SetCreateTime(now).
		SaveX(ctx)
	return row.ID, nil
}
