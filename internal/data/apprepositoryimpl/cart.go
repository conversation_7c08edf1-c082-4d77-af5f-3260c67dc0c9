package apprepositoryimpl

import (
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/cartitem"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/jinzhu/copier"
	"time"
)

type CartRepoImpl struct {
	Base[ent.CartItem, do.CartItemDo, ent.CartItemQuery]
	data *data.Data
}

func NewCartRepoImpl(d *data.Data) repository.CartRepo {
	return &CartRepoImpl{data: d}
}
func (g *CartRepoImpl) CartItemList(ctx context.Context, in *bo.CartItemListBo) (int, []*do.CartItemDo, error) {
	sql := g.data.GetDb(ctx).CartItem.Query().Where(cartitem.UserIDEQ(in.UserId))
	if in.CartItemId > 0 {
		sql.Where(cartitem.IDLT(in.CartItemId))
	}
	count, err := sql.Clone().Count(ctx)
	if err != nil {
		return 0, nil, err
	}
	result := sql.Order(ent.Desc(cartitem.FieldID)).Limit(in.Size).AllX(ctx)
	if err != nil {
		return 0, nil, err
	}
	cartItemDos := make([]*do.CartItemDo, 0, len(result))
	err = copier.Copy(&cartItemDos, &result)
	if err != nil {
		return 0, nil, err
	}
	return count, cartItemDos, nil
}
func (g *CartRepoImpl) CartItemTotal(ctx context.Context, in *bo.CartItemListBo) (int, error) {
	sql := g.data.GetDb(ctx).CartItem.Query().Where(cartitem.UserIDEQ(in.UserId))
	count, err := sql.Count(ctx)
	if err != nil {
		return 0, err
	}
	return count, nil
}
func (g *CartRepoImpl) FindOneByWhere(ctx context.Context, in *bo.CartItemQueryBo) (*do.CartItemDo, error) {
	sql := g.data.GetDb(ctx).CartItem.Query()
	if in.UserId > 0 {
		sql.Where(cartitem.UserIDEQ(in.UserId))
	}
	if in.CartItemId > 0 {
		sql.Where(cartitem.IDEQ(in.CartItemId))
	}
	if in.GoodsId > 0 {
		sql.Where(cartitem.GoodsID(in.GoodsId))
	}
	if in.GoodsSkuNo != "" {
		sql.Where(cartitem.GoodsSkuNo(in.GoodsSkuNo))
	}
	result := sql.FirstX(ctx)
	cartItemDo := &do.CartItemDo{}
	if result == nil {
		return cartItemDo, nil
	}
	err := copier.Copy(cartItemDo, &result)
	if err != nil {
		return nil, err
	}
	return cartItemDo, nil
}
func (g *CartRepoImpl) AddQuantity(ctx context.Context, in *ent.CartItem) (lastInsertId, rowsAffected int64, err error) {

	err = g.data.GetDb(ctx).CartItem.Create().
		SetGoodsID(in.GoodsID).
		SetGoodsSkuID(in.GoodsSkuID).
		SetUserID(in.UserID).
		SetQuantity(in.Quantity).
		SetPrice(in.Price).
		SetGoodsSkuNo(in.GoodsSkuNo).
		SetCreateTime(time.Now().Unix()).
		SetUpdateTime(time.Now().Unix()).
		OnConflict().
		Update(func(u *ent.CartItemUpsert) {
			u.AddQuantity(in.Quantity)
			u.SetUpdateTime(time.Now().Unix())
		}).Exec(ctx)
	if err != nil {
		log.Errorf("namedExec in InsertOrUpdate(%v), error: %v", err)
		return
	}
	return

}
func (g *CartRepoImpl) UpdateQuantity(ctx context.Context, in *ent.CartItem) (int, error) {
	sql := g.data.GetDb(ctx).CartItem.Update().
		SetQuantity(in.Quantity).
		SetUpdateTime(time.Now().Unix()).
		Where(cartitem.IDEQ(in.ID)).
		Where(cartitem.UserID(in.UserID))
	return sql.Save(ctx)
}
func (g *CartRepoImpl) DeleteCartItem(ctx context.Context, in *bo.CartItemDeleteBo) (int, error) {
	sql := g.data.GetDb(ctx).CartItem.Delete()
	if in.UserId > 0 {
		sql.Where(cartitem.UserIDEQ(in.UserId))
	}
	if len(in.CartItemId) > 0 {
		sql.Where(cartitem.IDIn(in.CartItemId...))
	}
	if len(in.GoodsId) > 0 {
		sql.Where(cartitem.IDIn(in.GoodsId...))
	}
	if len(in.GoodsSkuId) > 0 {
		sql.Where(cartitem.IDIn(in.GoodsSkuId...))
	}
	if len(in.GoodsSkuNo) > 0 {
		sql.Where(cartitem.GoodsSkuNoIn(in.GoodsSkuNo...))
	}
	return sql.Exec(ctx)
}

func (g *CartRepoImpl) GetAll(ctx context.Context, in *bo.CartItemAllBo) ([]*do.CartItemDo, error) {
	sql := g.data.GetDb(ctx).CartItem.Query()
	if in.UserId > 0 {
		sql.Where(cartitem.UserIDEQ(in.UserId))
	}
	if len(in.CartItemId) > 0 {
		sql.Where(cartitem.IDIn(in.CartItemId...))
	}
	result := sql.AllX(ctx)
	return g.ToEntities(result), nil
}
