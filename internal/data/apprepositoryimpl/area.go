package apprepositoryimpl

import (
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/area"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type AreaRepoImpl struct {
	data *data.Data
	Base[ent.Area, do.AreaDo, ent.AreaQuery]
}

func NewAreaRepoImpl(d *data.Data) repository.AreaRepo {
	return &AreaRepoImpl{
		data: d,
	}
}

func (a *AreaRepoImpl) All(ctx context.Context, pid int) ([]*do.AreaDo, error) {
	return a.ToEntities(a.data.GetDb(ctx).Area.Query().Where(area.Pid(pid)).AllX(ctx)), nil
}
func (a *AreaRepoImpl) GetByIds(ctx context.Context, id []int) ([]*do.AreaDo, error) {
	d := a.data.GetDb(ctx).Area.Query().Where(area.IDIn(id...)).AllX(ctx)
	return a.ToEntities(d), nil
}

func (a *AreaRepoImpl) GetMapByIds(ctx context.Context, id []int) (map[int]*do.AreaDo, error) {
	d, _ := a.GetByIds(ctx, id)
	res := make(map[int]*do.AreaDo)
	for _, v := range d {
		res[v.Id] = v
	}
	return res, nil
}

func (a *AreaRepoImpl) FindById(ctx context.Context, id int) (*do.AreaDo, error) {
	d := a.data.GetDb(ctx).Area.Query().Where(area.IDEQ(id)).FirstX(ctx)
	return a.ToEntity(d), nil
}

func (a *AreaRepoImpl) FindByName(ctx context.Context, name string) (*do.AreaDo, error) {
	d := a.data.GetDb(ctx).Area.Query().Where(area.NameEQ(name)).FirstX(ctx)
	return a.ToEntity(d), nil
}
