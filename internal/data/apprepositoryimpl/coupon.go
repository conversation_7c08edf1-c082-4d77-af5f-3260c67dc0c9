package apprepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/coupon"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
	"time"
)

type CouponRepoImpl struct {
	Base[ent.Coupon, do.CouponListDo, ent.CouponQuery]
	data *data.Data
}

func NewCouponRepoImpl(d *data.Data) repository.CouponRepo {
	return &CouponRepoImpl{data: d}
}

func (c *CouponRepoImpl) List(ctx context.Context, in *bo.CouponListBo) (int, []*do.CouponListDo, error) {
	now := int(time.Now().Unix())
	sql := c.data.GetDb(ctx).Coupon.Query().
		Where(coupon.CollectionEndTimeGT(now)).
		Where(coupon.CollectionStartTimeLT(now)).
		Where(coupon.IsAbolishEQ(valobj.CouponIsAbolishNo)).
		Where(coupon.SurplusNumGT(0)).Where(coupon.CollectionTypeEQ(valobj.CouponCollectionTypeUser))
	countSql := sql.Clone()
	count := countSql.CountX(ctx)
	res := sql.Offset(in.GetOffset()).
		Limit(in.GetPageSize()).
		Order(ent.Desc(coupon.FieldDiscountAmount)).
		AllX(ctx)
	return count, c.ToEntities(res), nil
}

func (c *CouponRepoImpl) Find(ctx context.Context, in *bo.CouponQueryBo) (*do.CouponListDo, error) {
	sql := c.data.GetDb(ctx).Coupon.Query().Where(coupon.IDEQ(in.Id))
	if in.ForShare {
		//sql.ForShare()
	}
	res := sql.FirstX(ctx)
	return c.ToEntity(res), nil
}

func (c *CouponRepoImpl) Collection(ctx context.Context, in *bo.CouponCollectionBo) (int, error) {
	res := c.data.GetDb(ctx).Coupon.Update().
		Where(coupon.IDEQ(in.CouponId)).
		Where(coupon.SurplusNumGTE(in.Num)).
		SetUpdateTime(int(time.Now().Unix())).
		AddSurplusNum(-in.Num).
		AddCollectionNum(in.Num).
		SaveX(ctx)
	return res, nil
}

func (c *CouponRepoImpl) GetByIds(ctx context.Context, ids []int) ([]*do.CouponListDo, error) {
	result := c.data.GetDb(ctx).Coupon.Query().Where(coupon.IDIn(ids...)).AllX(ctx)
	return c.ToEntities(result), nil
}

// OnCodeUsed 优惠券使用时，修改已使用和已领取的数量
func (c *CouponRepoImpl) OnCodeUsed(ctx context.Context, id, num int) (int, error) {
	row := c.data.GetDb(ctx).Coupon.Update().Where(coupon.IDEQ(id)).AddCollectionNum(-num).AddUsedNum(num).SaveX(ctx)
	return row, nil
}

// OnCodeReturn 优惠券退还时，修改已使用和已领取的数量
func (c *CouponRepoImpl) OnCodeReturn(ctx context.Context, id, num int) (int, error) {
	row := c.data.GetDb(ctx).Coupon.Update().Where(coupon.IDEQ(id)).AddCollectionNum(num).AddUsedNum(-num).SaveX(ctx)
	return row, nil
}

// GetProductLimitNone 获取不限商品的可使用优惠券
func (c *CouponRepoImpl) GetProductLimitNone(ctx context.Context) ([]*do.CouponListDo, error) {
	res := c.data.GetDb(ctx).Coupon.Query().Where(coupon.ProductLimit(valobj.CouponProductLimitNone)).AllX(ctx)
	return c.ToEntities(res), nil
}
