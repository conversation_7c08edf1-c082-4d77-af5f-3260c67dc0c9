package apprepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/useraddress"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
	"time"
)

type UserAddressRepoImpl struct {
	Base[ent.UserAddress, do.UserAddressDo, ent.UserAddressQuery]
	data *data.Data
}

func NewUserAddressRepoImpl(d *data.Data) repository.UserAddressRepo {
	return &UserAddressRepoImpl{
		data: d,
	}
}

func (u *UserAddressRepoImpl) Get(ctx context.Context, in *bo.UserAddressQueryBo) ([]*do.UserAddressDo, error) {
	sql := u.data.GetDb(ctx).UserAddress.Query().Where(useraddress.UserID(in.UserId))
	if in.IsDefault != nil {
		sql.Where(useraddress.IsDefault(*in.IsDefault))
	}
	return u.ToEntities(sql.AllX(ctx)), nil
}

func (u *UserAddressRepoImpl) Add(ctx context.Context, in *bo.UserAddressAddBo) (int, error) {
	now := int(time.Now().Unix())
	if in.IsDefault == valobj.UserAddressDefaultYes {
		u.data.GetDb(ctx).UserAddress.Update().Where(useraddress.UserIDEQ(in.UserId)).SetIsDefault(valobj.UserAddressDefaultNo).SetUpdateTime(now).SaveX(ctx)
	}
	row := u.data.GetDb(ctx).UserAddress.Create().
		SetName(in.Name).
		SetPhone(in.Phone).
		SetProvinceID(in.ProvinceId).
		SetCityID(in.CityId).
		SetRegionID(in.RegionId).
		SetArea(in.Area).
		SetDetail(in.Detail).
		SetUserID(in.UserId).
		SetIsDefault(in.IsDefault).
		SetUpdateTime(now).
		SetCreateTime(now).
		SaveX(ctx)
	return row.ID, nil
}

func (u *UserAddressRepoImpl) Update(ctx context.Context, in *bo.UserAddressUpdateBo) (int, error) {
	now := int(time.Now().Unix())
	if in.IsDefault == valobj.UserAddressDefaultYes {
		u.data.GetDb(ctx).UserAddress.Update().Where(useraddress.UserIDEQ(in.UserId)).SetIsDefault(0).SetUpdateTime(now).SaveX(ctx)
	}
	row := u.data.GetDb(ctx).UserAddress.Update().Where(useraddress.ID(in.Id)).
		SetName(in.Name).
		SetPhone(in.Phone).
		SetProvinceID(in.ProvinceId).
		SetCityID(in.CityId).
		SetRegionID(in.RegionId).
		SetArea(in.Area).
		SetDetail(in.Detail).
		SetUserID(in.UserId).
		SetIsDefault(in.IsDefault).
		SetUpdateTime(now).
		SaveX(ctx)
	return row, nil
}

func (u *UserAddressRepoImpl) Del(ctx context.Context, in *bo.UserAddressDelBo) (int, error) {
	return u.data.GetDb(ctx).UserAddress.Delete().Where(useraddress.ID(in.Id)).Where(useraddress.UserID(in.UserId)).ExecX(ctx), nil
}

func (u *UserAddressRepoImpl) Find(ctx context.Context, in *bo.UserAddressQueryBo) (*do.UserAddressDo, error) {
	sql := u.data.GetDb(ctx).UserAddress.Query().Where(useraddress.UserID(in.UserId))
	if in.IsDefault != nil {
		sql.Where(useraddress.IsDefault(*in.IsDefault))
	}
	if in.Id > 0 {
		sql.Where(useraddress.ID(in.Id))
	}
	if in.ProvinceId > 0 {
		sql.Where(useraddress.ProvinceIDEQ(in.ProvinceId))
	}
	if in.CityId > 0 {
		sql.Where(useraddress.CityIDEQ(in.CityId))
	}
	if in.RegionId > 0 {
		sql.Where(useraddress.RegionIDEQ(in.RegionId))
	}
	if in.Detail != "" {
		sql.Where(useraddress.DetailEQ(in.Detail))
	}
	return u.ToEntity(sql.FirstX(ctx)), nil
}

func (u *UserAddressRepoImpl) GetOne(ctx context.Context, id int) (*do.UserAddressDo, error) {
	row, err := u.data.GetDb(ctx).UserAddress.Query().
		Where(useraddress.IDEQ(id)).
		First(ctx)
	if err != nil {
		return nil, err
	}

	return u.ToEntity(row), nil
}

func (u *UserAddressRepoImpl) SetDefault(ctx context.Context, in *bo.UserAddressSetDefaultBo) (int, error) {
	now := int(time.Now().Unix())
	if *in.IsDefault == valobj.UserAddressDefaultYes {
		u.data.GetDb(ctx).UserAddress.Update().Where(useraddress.UserIDEQ(in.UserId)).SetIsDefault(0).SetUpdateTime(now).SaveX(ctx)
		return u.data.GetDb(ctx).UserAddress.Update().
			Where(useraddress.ID(in.Id)).
			Where(useraddress.IDEQ(in.Id)).
			SetIsDefault(1).
			SetUpdateTime(now).
			SaveX(ctx), nil
	} else {
		return u.data.GetDb(ctx).UserAddress.Update().
			Where(useraddress.ID(in.Id)).
			Where(useraddress.IDEQ(in.Id)).
			SetIsDefault(valobj.UserAddressDefaultNo).
			SetUpdateTime(now).
			SaveX(ctx), nil
	}
}

func (u *UserAddressRepoImpl) Count(ctx context.Context, userId int) int {
	sql := u.data.GetDb(ctx).UserAddress.Query().Where(useraddress.UserID(userId))
	return sql.CountX(ctx)
}
