package apprepositoryimpl

import (
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderdelivergoods"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type OrderDeliverGoodsRepoImpl struct {
	data *data.Data
	Base[ent.OrderDeliverGoods, do.OrderDeliverGoodsDo, ent.OrderDeliverGoodsQuery]
}

func NewOrderDeliverGoodsRepoImpl(d *data.Data) repository.OrderDeliverGoodsRepo {
	return &OrderDeliverGoodsRepoImpl{
		data: d,
	}
}

func (o *OrderDeliverGoodsRepoImpl) GetByDeliverId(ctx context.Context, deliverId []int) ([]*do.OrderDeliverGoodsDo, error) {
	res := o.data.GetDb(ctx).OrderDeliverGoods.Query().Where(orderdelivergoods.OrderDeliverIDIn(deliverId...)).AllX(ctx)
	return o.ToEntities(res), nil
}

func (o *OrderDeliverGoodsRepoImpl) GetMapByDeliverId(ctx context.Context, deliverId []int) (map[string]*do.OrderDeliverGoodsDo, error) {
	res, err := o.GetByDeliverId(ctx, deliverId)
	if err != nil {
		return nil, err
	}
	m := make(map[string]*do.OrderDeliverGoodsDo, len(res))
	if len(res) > 0 {
		for _, v := range res {
			m[v.SkuNo] = v
		}
	}
	return m, nil
}
