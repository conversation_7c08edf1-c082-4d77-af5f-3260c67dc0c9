package apprepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/paymerchant"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type PayMerchantRepoImpl struct {
	data *data.Data
	Base[ent.PayMerchant, do.PayMerchantDo, ent.PayMerchantQuery]
}

func NewPayMerchantRepoImpl(d *data.Data) repository.PayMerchantRepo {
	return &PayMerchantRepoImpl{
		data: d,
	}
}

// Find 查询支付商户
func (p *PayMerchantRepoImpl) Find(ctx context.Context, payType valobj.PayMerchantPayTypeObj) (*do.PayMerchantDo, error) {
	if d, err := p.getCache(ctx, payType); err == nil {
		return d, nil
	}
	d := p.data.GetDb(ctx).PayMerchant.Query().
		Where(paymerchant.DeleteTimeEQ(0)).
		Where(paymerchant.PayTypeEQ(payType)).
		Order(ent.Desc(paymerchant.FieldID)).
		FirstX(ctx)
	_ = p.setCache(ctx, p.ToEntity(d))
	return p.ToEntity(d), nil
}

// FindByPayMerchantId 指定payMerchantId商户ID查询时，直接读库，并且不缓存
func (p *PayMerchantRepoImpl) FindByPayMerchantId(ctx context.Context, payMerchantId int) (*do.PayMerchantDo, error) {
	d := p.data.GetDb(ctx).PayMerchant.Query().
		Where(paymerchant.MerchantID(payMerchantId)).
		FirstX(ctx)
	return p.ToEntity(d), nil
}

// FindById 直接读库，并且不缓存
func (p *PayMerchantRepoImpl) FindById(ctx context.Context, id int) (*do.PayMerchantDo, error) {
	d := p.data.GetDb(ctx).PayMerchant.Query().
		Where(paymerchant.IDEQ(id)).
		FirstX(ctx)
	return p.ToEntity(d), nil
}

func (p *PayMerchantRepoImpl) setCache(ctx context.Context, in *do.PayMerchantDo) error {
	if in == nil {
		return nil
	}
	cacheKey := constants.PayMerchantCacheTpl.GetKey(ctx, in.PayType)
	return p.data.Rdb.Set(ctx, cacheKey, in.ToJson(), constants.PayMerchantCacheTpl.GetTTL()).Err()
}

func (p *PayMerchantRepoImpl) getCache(ctx context.Context, payType valobj.PayMerchantPayTypeObj) (*do.PayMerchantDo, error) {
	cacheKey := constants.PayMerchantCacheTpl.GetKey(ctx, payType)
	str, err := p.data.Rdb.Get(ctx, cacheKey).Result()
	if err != nil {
		return nil, err
	}
	res := &do.PayMerchantDo{}
	return res, res.FromJson(str)
}
