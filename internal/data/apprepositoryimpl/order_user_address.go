package apprepositoryimpl

import (
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderuseraddress"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
)

type OrderUserAddressRepoImpl struct {
	data *data.Data
	Base[ent.OrderUserAddress, do.OrderUserAddressDo, ent.OrderUserAddressQuery]
}

func NewOrderUserAddressRepoImpl(d *data.Data) repository.OrderUserAddressRepo {
	return &OrderUserAddressRepoImpl{
		data: d,
	}
}

func (o *OrderUserAddressRepoImpl) FindByOrderId(ctx context.Context, orderId int) (*do.OrderUserAddressDo, error) {
	return o.ToEntity(o.data.GetDb(ctx).OrderUserAddress.Query().Where(orderuseraddress.OrderIDEQ(orderId)).FirstX(ctx)), nil
}

func (o *OrderUserAddressRepoImpl) Update(ctx context.Context, in *do.OrderUserAddressDo) (int, error) {
	return o.data.GetDb(ctx).OrderUserAddress.Update().
		Where(orderuseraddress.IDEQ(in.ID)).
		SetName(in.Name).
		SetPhoneNumber(in.PhoneNumber).
		SetProvinceID(in.ProvinceID).
		SetCityID(in.CityID).
		SetRegionID(in.RegionID).
		SetArea(in.Area).
		SetDetail(in.Detail).
		AddEditTimes(1).
		SaveX(ctx), nil
}

func (o *OrderUserAddressRepoImpl) FindByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderUserAddressDo, error) {
	return o.ToEntity(o.data.GetDb(ctx).OrderUserAddress.Query().Where(orderuseraddress.OrderNumberEQ(orderNumber)).FirstX(ctx)), nil
}
