package apprepositoryimpl

import (
	bbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/activity"
	"cardMall/internal/data/ent/goods"
	"cardMall/internal/data/ent/goodssku"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"time"

	"cardMall/internal/data/ent/flashsalegoods"

	"entgo.io/ent/dialect/sql"
)

type GoodsSkuRepoImpl struct {
	Base[ent.GoodsSku, do.GoodsSkuDo, ent.GoodsSkuQuery]
	data *data.Data
}

func NewGoodsSkuRepoImpl(d *data.Data) repository.GoodsSkuRepo {
	return &GoodsSkuRepoImpl{data: d}
}

func (g *GoodsSkuRepoImpl) FindById(ctx context.Context, id int) (*do.GoodsSkuDo, error) {
	res := g.data.GetDb(ctx).GoodsSku.Query().Where(goodssku.IDEQ(id)).FirstX(ctx)
	return g.ToEntity(res), nil
}
func (g *GoodsSkuRepoImpl) FindByIdIn(ctx context.Context, ids []int) ([]*do.GoodsSkuDo, error) {
	result := g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.IDIn(ids...)).
		AllX(ctx)
	return g.ToEntities(result), nil
}

func (g *GoodsSkuRepoImpl) ReduceStock(ctx context.Context, in *bo.GoodsSkuReduceStockBo) (int, error) {
	return g.data.GetDb(ctx).GoodsSku.Update().
		Where(goodssku.IDEQ(in.SkuId)).
		Where(goodssku.GoodsIDEQ(in.GoodsId)).
		Where(goodssku.StockGTE(in.StockNum)).
		Where(goodssku.DeleteTimeEQ(0)).
		AddStock(-in.StockNum).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}

func (g *GoodsSkuRepoImpl) AddStock(ctx context.Context, in *bo.GoodsSkuReduceStockBo) (int, error) {
	return g.data.GetDb(ctx).GoodsSku.Update().
		Where(goodssku.IDEQ(in.SkuId)).
		Where(goodssku.GoodsIDEQ(in.GoodsId)).
		AddStock(in.StockNum).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}

func (g *GoodsSkuRepoImpl) FindOrderPrice(ctx context.Context, ids []int) ([]*do.GoodsSkuDo, error) {
	c := g.data.GetDb(ctx)
	goodsId := make([]any, 0, len(ids))
	for _, id := range ids {
		goodsId = append(goodsId, id)
	}

	skuTable := sql.Table(goodssku.Table)
	query := sql.Select(
		sql.As(goodssku.FieldID, "id"),
		sql.Distinct(sql.As(goodssku.FieldGoodsID, "goods_id")),
		sql.As(goodssku.FieldSalePrice, "sale_price"),
	).From(skuTable).
		Where(sql.EQ(goodssku.FieldDeleteTime, 0)).
		Where(sql.In(goodssku.FieldGoodsID, goodsId...)).
		Where(sql.EQ(goodssku.FieldStatus, valobj.GoodsSkuStatusEnable))
	q, args := query.Query()
	r, err := c.QueryContext(ctx, q, args...)
	if err != nil {
		return nil, err
	}
	res := make([]*do.GoodsSkuDo, 0)
	err = sql.ScanSlice(r, &res)
	return res, err
}

func (g *GoodsSkuRepoImpl) FindMapOrderPrice(ctx context.Context, ids []int) (map[int]*do.GoodsSkuDo, error) {
	d, _ := g.FindOrderPrice(ctx, ids)
	res := make(map[int]*do.GoodsSkuDo)
	for _, v := range d {
		res[v.GoodsID] = v
	}
	return res, nil
}

func (g *GoodsSkuRepoImpl) AddSalesVolume(ctx context.Context, skuNo string, num int) (int, error) {
	return g.data.GetDb(ctx).GoodsSku.Update().
		Where(goodssku.SkuNoEQ(skuNo)).
		AddSalesVolume(num).
		Save(ctx)
}

func (g *GoodsSkuRepoImpl) GetByGoodsId(ctx context.Context, goodsId int) ([]*do.GoodsSkuDo, error) {
	res := g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.GoodsIDEQ(goodsId)).
		Where(goodssku.DeleteTimeEQ(0)).
		Where(goodssku.StatusEQ(int(valobj.GoodsSkuStatusEnable))).
		Order(ent.Desc(goodssku.FieldSort)).
		AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsSkuRepoImpl) FindMapByIdIn(ctx context.Context, ids []int) (map[int]*do.GoodsSkuDo, error) {
	d, _ := g.FindByIdIn(ctx, ids)
	res := make(map[int]*do.GoodsSkuDo)
	for _, v := range d {
		res[v.ID] = v
	}
	return res, nil
}

func (g *GoodsSkuRepoImpl) GetDiscount(ctx context.Context, in *bo.GoodsDiscountQueryBo) ([]*do.GoodsSkuDo, error) {
	c := g.data.GetDb(ctx)
	skuTable := sql.Table(goodssku.Table)
	goodsTable := sql.Table(goods.Table)
	query := sql.Select(
		sql.Distinct(sql.As(skuTable.C(goodssku.FieldGoodsID), "goods_id")),
		sql.As(skuTable.C(goodssku.FieldID), "id"),
		sql.As(skuTable.C(goodssku.FieldName), "name"),
		sql.As(skuTable.C(goodssku.FieldDiscount), "discount"),
		sql.As(goodsTable.C(goods.FieldImage), "image"),
		sql.As(goodsTable.C(goods.FieldCategoryID), "category_id"),
		sql.As(goodsTable.C(goods.FieldName), "goods_name"),
	).From(skuTable).
		Join(goodsTable.As("goods")).
		On(skuTable.C(goodssku.FieldGoodsID), goodsTable.C(goods.FieldID))

	query.Where(sql.EQ(skuTable.C(goodssku.FieldStatus), valobj.GoodsSkuStatusEnable))
	query.Where(sql.EQ(goodsTable.C(goods.FieldStatus), valobj.GoodsStatusEnable))
	query.Where(sql.EQ(skuTable.C(goodssku.FieldDeleteTime), 0))
	query.Where(sql.GT(skuTable.C(goodssku.FieldDiscount), 0))
	query.Where(sql.LT(skuTable.C(goodssku.FieldDiscount), 100))
	query.Where(sql.EQ(goodsTable.C(goods.FieldCustomerID), isolationcustomer.GetCustomerIdZero(ctx)))
	query.Where(sql.EQ(goodsTable.C(goods.FieldShopID), isolationcustomer.GetShopIdZero(ctx)))
	query.Where(sql.EQ(skuTable.C(goods.FieldCustomerID), isolationcustomer.GetCustomerIdZero(ctx)))
	query.Where(sql.EQ(skuTable.C(goods.FieldShopID), isolationcustomer.GetShopIdZero(ctx)))

	if in.IsIntegralShop {
		query.Where(sql.In(skuTable.C(goodssku.FieldSettlementType), valobj.GoodsSkuSettlementTypeCashIntegral, valobj.GoodsSkuSettlementTypeIntegral))
	} else {
		if in.IntegralShopEnable {
			query.Where(sql.EQ(skuTable.C(goodssku.FieldSettlementType), valobj.GoodsSkuSettlementTypeCash))
		}
	}

	query.OrderBy(sql.Asc(skuTable.C(goodssku.FieldDiscount)))

	query.Limit(in.Size)

	s, args := query.Query()
	r, err := c.QueryContext(ctx, s, args...)
	if err != nil {
		return nil, err
	}
	res := make([]*do.GoodsSkuDo, 0)
	err = sql.ScanSlice(r, &res)
	if err != nil {
		return nil, err
	}
	return res, err
}

func (g *GoodsSkuRepoImpl) FindEnableBySkuNo(ctx context.Context, skuNo string) (*do.GoodsSkuDo, error) {
	res := g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.SkuNoEQ(skuNo)).
		Where(goodssku.DeleteTimeEQ(0)).
		Where(goodssku.StatusEQ(int(valobj.GoodsSkuStatusEnable))).
		FirstX(ctx)
	return g.ToEntity(res), nil
}

func (g *GoodsSkuRepoImpl) GetBySkuNo(ctx context.Context, skuNo []string) ([]*do.GoodsSkuDo, error) {
	res := g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.SkuNoIn(skuNo...)).
		Where(goodssku.DeleteTimeEQ(0)).
		//Where(goodssku.StatusEQ(int(valobj.GoodsSkuStatusEnable))).
		AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsSkuRepoImpl) GetMapBySkuNo(ctx context.Context, skuNo []string) (map[string]*do.GoodsSkuDo, error) {
	d, _ := g.GetBySkuNo(ctx, skuNo)
	res := make(map[string]*do.GoodsSkuDo)
	for _, v := range d {
		res[v.SkuNo] = v
	}
	return res, nil
}

func (g *GoodsSkuRepoImpl) FindBySkuNo(ctx context.Context, skuNo string) (*do.GoodsSkuDo, error) {
	res := g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.SkuNoEQ(skuNo)).
		FirstX(ctx)
	return g.ToEntity(res), nil
}

func (g *GoodsSkuRepoImpl) GroupByGoodsId(ctx context.Context, goodsIds ...int) ([]*do.GoodsSkuCountDo, error) {
	res := make([]*do.GoodsSkuCountDo, 0)
	g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.GoodsIDIn(goodsIds...)).
		GroupBy(goodssku.FieldGoodsID).Aggregate(ent.Count()).ScanX(ctx, &res)
	return res, nil
}

func (g *GoodsSkuRepoImpl) GetActivityGoodsSkuList(ctx context.Context, in *bo.GoodsSkuListByIdsPageBo) (dos []*do.GoodsSkuDo, pageRsp *bbo.RespPageBo, err error) {
	pageRsp = &bbo.RespPageBo{
		Page:     in.Page.GetPage(),
		PageSize: in.Page.GetPageSize(),
	}
	wrapper := g.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.HasActivityWith(activity.ID(in.ActivityID))).
		Where(goodssku.Status(1))
	if in.Page != nil {
		total, _ := wrapper.Clone().Count(ctx)
		pageRsp.Total = total
		wrapper.Offset(in.Page.GetOffset()).Limit(in.Page.GetPageSize())
	}
	rows, err := wrapper.WithGoods().All(ctx)
	if err != nil {
		return nil, nil, err
	}
	if rows == nil {
		return nil, pageRsp, nil
	}
	dos = g.ToEntities(rows)
	return dos, pageRsp, nil
}

func (g *GoodsSkuRepoImpl) ToEntity(row *ent.GoodsSku) *do.GoodsSkuDo {
	if row == nil {
		return nil
	}

	entity := g.Base.ToEntity(row)
	if row.Edges.Goods != nil {
		goodsItem := row.Edges.Goods
		entity.Goods = goodsRepoImpl.ToEntity(goodsItem)
	}
	return entity
}

func (g *GoodsSkuRepoImpl) ToEntities(rows []*ent.GoodsSku) []*do.GoodsSkuDo {
	if len(rows) == 0 {
		return nil
	}
	entities := make([]*do.GoodsSkuDo, 0, len(rows))
	for _, row := range rows {
		entities = append(entities, g.ToEntity(row))
	}
	return entities
}

func (g *GoodsSkuRepoImpl) GetFlashSaleList(ctx context.Context, in *bo.FlashSaleListBo) (int, []*do.FlashSaleGoodsSkuDo, error) {
	c := g.data.GetDb(ctx)
	skuTable := sql.Table(goodssku.Table)
	flashSaleTable := sql.Table(flashsalegoods.Table)
	query := sql.Select(
		sql.As(skuTable.C(goodssku.FieldID), "id"),
		sql.As(skuTable.C(goodssku.FieldGoodsID), "goods_id"),
		sql.As(skuTable.C(goodssku.FieldName), "name"),
		sql.As(skuTable.C(goodssku.FieldSkuNo), "sku_no"),
		sql.As(skuTable.C(goodssku.FieldImage), "image"),
		sql.As(skuTable.C(goodssku.FieldSalePrice), "sale_price"),
		sql.As(flashSaleTable.C(flashsalegoods.FieldPrice), "discount_price"),
		sql.As(flashSaleTable.C(flashsalegoods.FieldLimitNum), "limit_num"),
		sql.As(flashSaleTable.C(flashsalegoods.FieldStock), "activity_current_stock"),
		sql.As(flashSaleTable.C(flashsalegoods.FieldTotalStock), "activity_total_stock"),
	).From(skuTable).
		Join(flashSaleTable.As("flash_sale_goods")).
		On(skuTable.C(goodssku.FieldSkuNo), flashSaleTable.C(flashsalegoods.FieldSkuNo)).
		OnP(sql.EQ(flashSaleTable.C(flashsalegoods.FieldActivityID), in.ActivityId))

	query.Where(sql.EQ(skuTable.C(goodssku.FieldStatus), valobj.GoodsSkuStatusEnable))
	query.Where(sql.EQ(skuTable.C(goodssku.FieldDeleteTime), 0))
	query.Where(sql.EQ(skuTable.C(goodssku.FieldSettlementType), valobj.GoodsSkuSettlementTypeCash))
	query.Where(sql.EQ(skuTable.C(goods.FieldCustomerID), isolationcustomer.GetCustomerIdZero(ctx)))
	query.Where(sql.EQ(skuTable.C(goods.FieldShopID), isolationcustomer.GetShopIdZero(ctx)))
	query.Where(sql.EQ(flashSaleTable.C(flashsalegoods.FieldCustomerID), isolationcustomer.GetCustomerIdZero(ctx)))
	query.Where(sql.EQ(flashSaleTable.C(flashsalegoods.FieldShopID), isolationcustomer.GetShopIdZero(ctx)))

	var count int
	if in.Page != nil {
		countSql, countArgs := query.Clone().Count(skuTable.C(goodssku.FieldID)).Query()
		countRes, err := c.QueryContext(ctx, countSql, countArgs...)
		if err != nil {
			return 0, nil, err
		}
		count, err = sql.ScanInt(countRes)
		if err != nil {
			return 0, nil, err
		}
		if count == 0 {
			return 0, nil, nil
		}
		query.Offset(in.Page.GetOffset()).Limit(in.Page.GetPageSize())

	}

	query.OrderBy(sql.Desc(flashSaleTable.C(flashsalegoods.FieldSort)))
	s, args := query.Query()
	r, err := c.QueryContext(ctx, s, args...)
	if err != nil {
		return 0, nil, err
	}
	res := make([]*do.FlashSaleGoodsSkuDo, 0)
	err = sql.ScanSlice(r, &res)
	if err != nil {
		return 0, nil, err
	}
	return count, res, err
}
