package apprepositoryimpl

import (
	"github.com/google/wire"
)

var ProviderSetAppRepositoryImpl = wire.NewSet(
	NewUserRepoImpl,
	NewBannerRepoImpl,
	NewGoodsRepoImpl,
	NewGoodsCategoryRepoImpl,
	NewGoodsBrandRepoImpl,
	NewBaseGoodsRepoImpl,
	NewCouponCodeRepoImpl,
	NewUserIntegralLogRepoImpl,
	NewCouponRepoImpl,
	NewOrderRepoImpl,
	NewOrderAfterSaleRepoImpl,
	NewPayOrderRepoImpl,
	NewOrderRefundLogRepoImpl,
	NewGoodsCategoryBrandRepoImpl,
	NewCouponGoodsRepoImpl,
	NewCartRepoImpl,
	NewGoodsSkuRepoImpl,
	NewAreaRepoImpl,
	NewUserAddressRepoImpl,
	NewOrderLogisticsRepoImpl,
	NewOrderDeliverRepoImpl,
	NewOrderDeliverGoodsRepoImpl,
	NewOrderGoodsRepoImpl,
	NewKdRepoImpl,
	NewPayMerchantRepoImpl,
	NewPayConfigRepoImpl,
	NewOrderUserAddressRepoImpl,
)
