package apprepositoryimpl

import (
	bo2 "cardMall/internal/biz/bo"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/payorder"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/helper"
	"context"
	"time"
)

type PayOrderRepoImpl struct {
	Base[ent.PayOrder, do.PayOrderDo, ent.PayOrderQuery]
	data *data.Data
}

func NewPayOrderRepoImpl(d *data.Data) repository.PayOrderRepo {
	return &PayOrderRepoImpl{data: d}
}

func (p *PayOrderRepoImpl) Create(ctx context.Context, in *bo2.PayOrderCreateBo) (*do.PayOrderDo, error) {
	now := int(time.Now().Unix())
	sql := p.data.GetDb(ctx).PayOrder.Create().
		SetCreateTime(now).
		SetUpdateTime(now).
		SetOrderNumber(in.OrderNumber).
		SetThirdTradeNo(in.ThirdTradeNo).
		SetPayType(in.PayType).
		SetUserID(in.UserId).
		SetGoodsID(in.GoodsId).
		SetTotalAmount(in.TotalAmount).
		SetTotalPayAmount(in.TotalPayAmount).
		SetTotalPayIntegral(in.TotalPayIntegral).
		SetUserIntegralLogID(in.UserIntegralLogId).
		SetNum(in.Num).SetCouponCodeID(in.CouponCodeId).
		SetCouponDiscountAmount(in.CouponDiscountAmount).
		SetBrandID(in.BrandId).
		SetSiteID(in.SiteId).
		SetSettlementType(in.SettlementType).
		SetIntegralDiscountAmount(in.IntegralDiscountAmount).
		SetCouponID(in.CouponId).
		SetCardCouponNumber(in.CardCouponNumber).
		SetFreightFee(in.FreightFee).
		SetGoodsPayAmount(in.GoodsPayAmount).
		SetCardGiftAmount(in.CardGiftAmount).
		SetActivityID(in.ActivityId)
	//SetCustomerID(in.CustomerId).
	//SetShopID(in.ShopId)
	if in.ReturnUrl != "" {
		sql.SetReturnURL(in.ReturnUrl)
	}
	if in.ExpireTime > 0 {
		sql.SetExpireTime(in.ExpireTime)
	}
	if in.OrderType > 0 {
		sql.SetOrderType(in.OrderType)
	}
	if in.PayUrl != "" {
		sql.SetPayURL(in.PayUrl)
	}
	if in.Status != 0 {
		sql.SetStatus(in.Status)
	}
	if in.OutTradeNo != "" {
		sql.SetOutTradeNo(in.OutTradeNo)
	}
	if in.PayMerchantId != 0 {
		sql.SetPayMerchantID(in.PayMerchantId)
	}
	if in.PayeeType > 0 {
		sql.SetPayeeType(in.PayeeType.GetValue())
	}
	res, err := sql.Save(ctx)
	if err != nil {
		return nil, err
	}
	return p.ToEntity(res), nil
}

func (p *PayOrderRepoImpl) SetOutTradeNoByOrderNumber(ctx context.Context, orderNumber, outTradeNo string) (int, error) {
	return p.data.GetDb(ctx).
		PayOrder.
		Update().
		Where(payorder.OrderNumberEQ(orderNumber)).
		SetOutTradeNo(outTradeNo).
		SetUpdateTime(int(time.Now().Unix())).
		SaveX(ctx), nil
}

func (p *PayOrderRepoImpl) FindByOrderNumber(ctx context.Context, orderNumber string) (*do.PayOrderDo, error) {
	res := p.data.GetDb(ctx).PayOrder.Query().Where(payorder.OrderNumberEQ(orderNumber)).FirstX(ctx)
	return p.ToEntity(res), nil
}

func (p *PayOrderRepoImpl) RefundSuccess(ctx context.Context, in *bo.PayOrderRefundSuccessBo) (int, error) {
	now := int(time.Now().Unix())
	row := p.data.GetDb(ctx).PayOrder.
		Update().
		Where(payorder.IDEQ(in.OrderId)).
		SetRefundTime(now).
		SetUpdateTime(now).
		AddRefundAmount(in.RefundAmount).
		AddRefundIntegral(in.RefundIntegral).
		SetStatus(in.OrderStatus).
		SaveX(ctx)
	return row, nil
}

func (p *PayOrderRepoImpl) PaySuccess(ctx context.Context, orderNumber string) (int, error) {
	now := int(time.Now().Unix())
	row := p.data.GetDb(ctx).PayOrder.
		Update().
		Where(payorder.OrderNumberEQ(orderNumber)).
		SetStatus(valobj.PayOrderStatusPaid).
		SetPayTime(now).
		SetUpdateTime(now).
		SaveX(ctx)
	return row, nil
}

func (p *PayOrderRepoImpl) GetNeedCanceledOrder(ctx context.Context, timeOut int) ([]*do.PayOrderDo, error) {
	orders := p.data.GetDb(ctx).PayOrder.Query().
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		//Where(payorder.OrderTypeIn(valobj.PayOrderTypeEntity, valobj.PayOrderTypeRecharge)).
		Where(payorder.ExpireTimeLTE(helper.GetNow())).
		AllX(ctx)
	return p.ToEntities(orders), nil
}

func (p *PayOrderRepoImpl) Canceled(ctx context.Context, orderNumber string) (int, error) {
	now := int(time.Now().Unix())
	row := p.data.GetDb(ctx).PayOrder.
		Update().
		Where(payorder.OrderNumberEQ(orderNumber)).
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		SetStatus(valobj.PayOrderStatusCancel).
		SetUpdateTime(now).
		SaveX(ctx)
	return row, nil
}

func (p *PayOrderRepoImpl) GetUserUnPayOrder(ctx context.Context, in *bo.PayOrderUserUnpaidListBo) (int, []*do.PayOrderDo, error) {
	sql := p.data.GetDb(ctx).PayOrder.
		Query().
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		Where(payorder.UserIDEQ(in.UserId))
	countSql := sql.Clone()
	count := countSql.CountX(ctx)
	if count == 0 {
		return count, nil, nil
	}
	orders := sql.Offset(in.GetOffset()).
		Limit(in.GetPageSize()).
		Order(ent.Desc(payorder.FieldID)).
		AllX(ctx)
	return count, p.ToEntities(orders), nil
}

func (p *PayOrderRepoImpl) FindUserOrderDetail(ctx context.Context, id, userId int) (*do.PayOrderDo, error) {
	res := p.data.GetDb(ctx).PayOrder.Query().Where(payorder.IDEQ(id)).Where(payorder.UserIDEQ(userId)).FirstX(ctx)
	return p.ToEntity(res), nil
}

func (p *PayOrderRepoImpl) SetPayParams(ctx context.Context, id int, payParams string) (int, error) {
	row := p.data.GetDb(ctx).PayOrder.Update().Where(payorder.IDEQ(id)).SetPayParams(payParams).SaveX(ctx)
	return row, nil
}

func (p *PayOrderRepoImpl) GetByOrderNumber(ctx context.Context, orderNumber []string) ([]*do.PayOrderDo, error) {
	res := p.data.GetDb(ctx).PayOrder.Query().Where(payorder.OrderNumberIn(orderNumber...)).AllX(ctx)
	return p.ToEntities(res), nil
}

func (p *PayOrderRepoImpl) GetMapByOrderNumber(ctx context.Context, orderNumber []string) (map[string]*do.PayOrderDo, error) {
	d, _ := p.GetByOrderNumber(ctx, orderNumber)
	res := make(map[string]*do.PayOrderDo)
	for _, val := range d {
		res[val.OrderNumber] = val
	}
	return res, nil
}

func (p *PayOrderRepoImpl) UpdateByOrderNumber(ctx context.Context, in *bo.PayOrderUpdateBo) (int, error) {
	if err := in.Validate(); err != nil {
		return 0, err
	}

	sql := p.data.GetDb(ctx).PayOrder.Update().Where(payorder.IDEQ(in.OrderId))
	if in.PayParams != "" {
		sql.SetPayParams(in.PayParams)
	}
	if in.PayType > 0 {
		sql.SetPayType(in.PayType)
	}
	if in.OutTradeNo != "" {
		sql.SetOutTradeNo(in.OutTradeNo)
	}
	if in.ThirdTradeNo != "" {
		sql.SetThirdTradeNo(in.ThirdTradeNo)
	}
	if in.PayUrl != "" {
		sql.SetPayURL(in.PayUrl)
	}
	if in.MerchantId > 0 {
		sql.SetPayMerchantID(in.MerchantId)
	}
	if in.PaySerialNumber != "" {
		sql.SetPaySerialNumber(in.PaySerialNumber)
	}
	sql.SetUpdateTime(int(time.Now().Unix()))
	return sql.SaveX(ctx), nil
}

func (p *PayOrderRepoImpl) FindByThirdTradeNo(ctx context.Context, thirdTradeNo string) (*do.PayOrderDo, error) {
	result := p.data.GetDb(ctx).PayOrder.Query().Where(payorder.ThirdTradeNo(thirdTradeNo)).FirstX(ctx)
	return p.ToEntity(result), nil
}

func (p *PayOrderRepoImpl) FindById(ctx context.Context, id int) (*do.PayOrderDo, error) {
	result := p.data.GetDb(ctx).PayOrder.Query().Where(payorder.IDEQ(id)).FirstX(ctx)
	return p.ToEntity(result), nil
}

func (p *PayOrderRepoImpl) GetMeiTuanNeedCanceledOrder(ctx context.Context) ([]*do.PayOrderDo, error) {
	now := int(time.Now().Unix())
	res := p.data.GetDb(ctx).PayOrder.Query().
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		Where(payorder.OrderTypeEQ(valobj.PayOrderTypeMeiTuan)).
		Where(payorder.ExpireTimeLTE(now)).AllX(ctx)
	return p.ToEntities(res), nil
}

func (p *PayOrderRepoImpl) GetQianZhuNeedCanceledOrder(ctx context.Context) ([]*do.PayOrderDo, error) {
	now := int(time.Now().Unix())
	res := p.data.GetDb(ctx).PayOrder.Query().
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		Where(payorder.OrderTypeIn(valobj.PayOrderTypeQianZhuCinema, valobj.PayOrderTypeQianZhuKFC)).
		Where(payorder.ExpireTimeLTE(now)).AllX(ctx)
	return p.ToEntities(res), nil
}

func (p *PayOrderRepoImpl) GetUserQianZhuUnpaidOrder(ctx context.Context, userId int) ([]*do.PayOrderDo, error) {
	res := p.data.GetDb(ctx).PayOrder.Query().
		Where(payorder.UserIDEQ(userId)).
		Where(payorder.OrderTypeEQ(valobj.PayOrderTypeQianZhuCinema)).
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		AllX(ctx)
	return p.ToEntities(res), nil
}

func (p *PayOrderRepoImpl) GetUnpaidQueryOrder(ctx context.Context, timeOut int) ([]*do.PayOrderDo, error) {
	t := int(time.Now().Unix()) - timeOut
	if t <= 0 {
		return nil, nil
	}
	res := p.data.GetDb(ctx).PayOrder.Query().
		Where(payorder.UpdateTimeLTE(t)).
		Where(payorder.OrderTypeIn(valobj.PayOrderTypeQianZhuCinema, valobj.PayOrderTypeMeiTuan, valobj.PayOrderTypeQianZhuKFC, valobj.PayOrderTypeDGSS)).
		Where(payorder.StatusEQ(valobj.PayOrderStatusUnpaid)).
		AllX(ctx)
	return p.ToEntities(res), nil
}

func (p *PayOrderRepoImpl) UpdateOrderSite(ctx context.Context, in *bo.PayOrderUpdateSiteBo) (int, error) {
	return p.data.GetDb(ctx).PayOrder.Update().Where(payorder.OrderNumberEQ(in.OrderNumber)).Where(payorder.UserIDEQ(in.UserId)).SetSiteID(in.SiteId).SaveX(ctx), nil
}

func (p *PayOrderRepoImpl) UpdateAlipayMiniOrderJson(ctx context.Context, orderNumber string, jsonData string) (int, error) {
	return p.data.GetDb(ctx).PayOrder.Update().Where(payorder.OrderNumberEQ(orderNumber)).SetAlipayMiniOrderJSON(jsonData).SaveX(ctx), nil
}

func (p *PayOrderRepoImpl) UpdateDiscount(ctx context.Context, in *bo.PayOrderDiscountBo) (int, error) {
	return p.data.GetDb(ctx).PayOrder.Update().
		Where(payorder.OrderNumberEQ(in.OrderNumber)).
		Where(payorder.UserIDEQ(in.UserId)).
		SetTotalPayAmount(in.TotalPayAmount).
		SetCardGiftAmount(in.CardGiftAmount).
		SetSettlementType(in.SettlementType).
		//SetStatus(in.Status).
		SetUpdateTime(helper.GetNow()).
		SaveX(ctx), nil
}
