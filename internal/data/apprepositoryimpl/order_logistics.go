package apprepositoryimpl

import (
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderlogistics"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"context"
	"time"
)

type OrderLogisticsRepoImpl struct {
	data *data.Data
	Base[ent.OrderLogistics, do.OrderLogisticsDo, ent.OrderLogisticsQuery]
}

func NewOrderLogisticsRepoImpl(d *data.Data) repository.OrderLogisticsRepo {
	return &OrderLogisticsRepoImpl{
		data: d,
	}
}

func (o *OrderLogisticsRepoImpl) FindLast(ctx context.Context, in *bo.OrderLogisticsFindLastBo) (*do.OrderLogisticsDo, error) {
	query := o.data.GetDb(ctx).OrderLogistics.Query().
		Where(orderlogistics.OrderNumber(in.OrderNumber))
	if in.LogisticsNo != "" {
		query.Where(orderlogistics.LogisticsNo(in.LogisticsNo))
	}
	res := query.Order(ent.Desc(orderlogistics.FieldOpTime)).
		FirstX(ctx)
	return o.ToEntity(res), nil
}

func (o *OrderLogisticsRepoImpl) Get(ctx context.Context, in *bo.OrderLogisticsFindLastBo) ([]*do.OrderLogisticsDo, error) {
	res := o.data.GetDb(ctx).OrderLogistics.Query().
		Where(orderlogistics.OrderNumber(in.OrderNumber)).
		Where(orderlogistics.LogisticsNo(in.LogisticsNo)).
		Order(ent.Desc(orderlogistics.FieldOpTime)).
		AllX(ctx)
	return o.ToEntities(res), nil
}

func (o *OrderLogisticsRepoImpl) Create(ctx context.Context, in *bo.OrderLogisticsCreateBo) (int, error) {
	now := int(time.Now().Unix())
	row := o.data.GetDb(ctx).OrderLogistics.Create().
		SetOpMessage(in.OpMessage).
		SetKdCode(in.KdCode).
		SetOpTime(in.OpTime).
		SetOpDesc(in.OpDesc).
		SetLogisticsNo(in.LogisticsNo).
		SetOpCode(in.OpCode).
		SetAddressText(in.AddressText).
		SetFrom(in.From).
		SetOrderNumber(in.OrderNumber).
		SetCreateTime(now).
		SaveX(ctx)
	return row.ID, nil
}

func (o *OrderLogisticsRepoImpl) FindOne(ctx context.Context, in *bo.OrderLogisticsFindOneBo) (*do.OrderLogisticsDo, error) {
	res := o.data.GetDb(ctx).OrderLogistics.Query().
		Where(orderlogistics.OrderNumber(in.OrderNumber)).
		Where(orderlogistics.LogisticsNo(in.LogisticsNo)).
		Where(orderlogistics.KdCode(in.KdCode)).
		Where(orderlogistics.OpTime(in.OpTime)).
		FirstX(ctx)
	return o.ToEntity(res), nil
}

func (o *OrderLogisticsRepoImpl) Delete(ctx context.Context, in *bo.OrderLogisticsDeleteBo) (int, error) {
	return o.data.GetDb(ctx).OrderLogistics.Delete().
		Where(orderlogistics.OrderNumber(in.OrderNumber)).
		Where(orderlogistics.LogisticsNo(in.LogisticsNo)).
		Where(orderlogistics.KdCode(in.KdCode)).
		Exec(ctx)
}
