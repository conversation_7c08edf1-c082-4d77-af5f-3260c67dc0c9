package apprepositoryimpl

import (
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodsbrandcommon"
	"cardMall/internal/data/ent/goodsbrandextra"
	"cardMall/internal/module/appbiz/bo"
	"cardMall/internal/module/appbiz/do"
	"cardMall/internal/module/appbiz/repository"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"entgo.io/ent/dialect/sql"
)

type GoodsBrandRepoImpl struct {
	Base[ent.GoodsBrandCommon, do.GoodsBrandInfoDo, ent.GoodsBrandCommonQuery]
	data *data.Data
}

func NewGoodsBrandRepoImpl(d *data.Data) repository.GoodsBrandRepo {
	return &GoodsBrandRepoImpl{data: d}
}

func (g *GoodsBrandRepoImpl) All(ctx context.Context, in *bo.GoodsBrandQueryBo) ([]*do.GoodsBrandInfoDo, error) {
	//query := g.data.GetDb(ctx).GoodsBrandCommon.Query()
	//if in.Status > 0 {
	//	query.Where(goodsbrandcommon.StatusEQ(int(in.Status)))
	//}
	//if in.Recommend != nil {
	//	recommend := *in.Recommend
	//	query.Where(goodsbrandcommon.RecommendEQ(int(recommend)))
	//}
	//if in.Size > 0 {
	//	query.Limit(in.Size)
	//}
	//res := query.Order(ent.Desc(goodsbrandcommon.FieldSort)).
	//	AllX(ctx)
	//return g.ToEntities(res), nil

	extraTable := sql.Table(goodsbrandextra.Table).As("t1")
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	shopId := isolationcustomer.GetShopIdZero(ctx)
	query := g.data.GetDb(ctx).GoodsBrandCommon.Query().Modify(func(s *sql.Selector) {
		s.LeftJoin(extraTable).
			On(extraTable.C(goodsbrandextra.FieldCode), s.C(goodsbrandcommon.FieldCode)).
			OnP(sql.EQ(extraTable.C(goodsbrandextra.FieldCustomerID), customerId)).
			OnP(sql.EQ(extraTable.C(goodsbrandextra.FieldShopID), shopId))
		if in.Status.IsValid() {
			if in.Status.IsEnable() {
				s.Where(sql.Or(sql.EQ(extraTable.C(goodsbrandextra.FieldStatus), in.Status), sql.IsNull(extraTable.C(goodsbrandextra.FieldStatus))))
			} else {
				s.Where(sql.EQ(extraTable.C(goodsbrandextra.FieldStatus), in.Status))
			}
		}
		if in.Recommend != nil {
			recommend := *in.Recommend
			if recommend == valobj.GoodsBrandRecommendYes {
				s.Where(sql.EQ(extraTable.C(goodsbrandextra.FieldRecommend), recommend))
			} else {
				s.Where(sql.Or(sql.EQ(extraTable.C(goodsbrandextra.FieldRecommend), recommend), sql.IsNull(extraTable.C(goodsbrandextra.FieldRecommend))))
			}
		}
		s.OrderBy(sql.Desc(extraTable.C(goodsbrandextra.FieldSort)))
	})
	if in.Size > 0 {
		query.Limit(in.Size)
	}
	res := query.AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsBrandRepoImpl) FindById(ctx context.Context, id int) (*do.GoodsBrandInfoDo, error) {
	res := g.data.GetDb(ctx).GoodsBrandCommon.Query().
		Where(goodsbrandcommon.IDEQ(id)).
		FirstX(ctx)
	return g.ToEntity(res), nil
}

func (g *GoodsBrandRepoImpl) GetById(ctx context.Context, id []int) ([]*do.GoodsBrandInfoDo, error) {
	res := g.data.GetDb(ctx).GoodsBrandCommon.Query().
		Where(goodsbrandcommon.IDIn(id...)).
		Where(goodsbrandcommon.StatusEQ(valobj.GoodsBrandStatusEnable.GetInt())).
		AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsBrandRepoImpl) GetMapById(ctx context.Context, id []int) (map[int]*do.GoodsBrandInfoDo, error) {
	d, err := g.GetById(ctx, id)
	if err != nil {
		return nil, err
	}
	if d == nil {
		return nil, nil
	}

	res := make(map[int]*do.GoodsBrandInfoDo, len(d))
	for _, val := range d {
		res[val.Id] = val
	}
	return res, nil
}
