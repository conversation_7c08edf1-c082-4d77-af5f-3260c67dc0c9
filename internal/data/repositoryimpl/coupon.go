//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/coupon"
	"context"
	"errors"
)

type CouponRepoImpl struct {
	Base[ent2.Coupon, do.CouponDo, ent2.CouponQuery]
	data *data.Data
}

// NewCouponRepoImpl 创建 CouponRepo的实现者
func NewCouponRepoImpl(data *data.Data) repository.CouponRepo {
	return &CouponRepoImpl{data: data}
}

// ToEntity 转换成实体
func (cri *CouponRepoImpl) ToEntity(po *ent2.Coupon) *do.CouponDo {
	if po == nil {
		return nil
	}
	entity := cri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (cri *CouponRepoImpl) ToEntities(pos []*ent2.Coupon) []*do.CouponDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.CouponDo, len(pos))
	for k, p := range pos {
		entities[k] = cri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (cri *CouponRepoImpl) Get(ctx context.Context, id int) (*do.CouponDo, error) {
	row, err := cri.data.GetDb(ctx).Coupon.Query().Where(coupon.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return cri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (cri *CouponRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.CouponDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := cri.data.GetDb(ctx).Coupon.Query().Where(coupon.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return cri.ToEntities(rows), nil
}

// Create 创建数据
func (cri *CouponRepoImpl) Create(ctx context.Context, d *do.CouponDo) (*do.CouponDo, error) {
	row, err := cri.data.GetDb(ctx).Coupon.Create().
		SetTitle(d.Title).
		SetQuantity(d.Quantity).
		SetRemark(d.Remark).
		SetCollectionStartTime(d.CollectionStartTime).
		SetCollectionEndTime(d.CollectionEndTime).
		SetEffectTimeType(d.EffectTimeType).
		SetEffectStartTime(d.EffectStartTime).
		SetEffectEndTime(d.EffectEndTime).
		SetEffectStartDay(d.EffectStartDay).
		SetEffectEndDay(d.EffectEndDay).
		SetLimitAmount(d.LimitAmount).
		SetDiscountAmount(d.DiscountAmount).
		SetCollectionQuantity(d.CollectionQuantity).
		SetCollectionType(d.CollectionType).
		SetProductLimit(d.ProductLimit).
		SetCollectionNum(d.CollectionNum).
		SetUsedNum(d.UsedNum).
		SetAbolishNum(d.AbolishNum).
		SetSurplusNum(d.SurplusNum).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetIsAbolish(d.IsAbolish).
		SetCustomerID(d.CustomerID).
		SetShopID(d.ShopID).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return cri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (cri *CouponRepoImpl) CreateBulk(ctx context.Context, dos []*do.CouponDo) ([]*do.CouponDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.CouponCreate, len(dos))
	for i, d := range dos {
		values[i] = cri.data.GetDb(ctx).Coupon.Create().
			SetTitle(d.Title).
			SetQuantity(d.Quantity).
			SetRemark(d.Remark).
			SetCollectionStartTime(d.CollectionStartTime).
			SetCollectionEndTime(d.CollectionEndTime).
			SetEffectTimeType(d.EffectTimeType).
			SetEffectStartTime(d.EffectStartTime).
			SetEffectEndTime(d.EffectEndTime).
			SetEffectStartDay(d.EffectStartDay).
			SetEffectEndDay(d.EffectEndDay).
			SetLimitAmount(d.LimitAmount).
			SetDiscountAmount(d.DiscountAmount).
			SetCollectionQuantity(d.CollectionQuantity).
			SetCollectionType(d.CollectionType).
			SetProductLimit(d.ProductLimit).
			SetCollectionNum(d.CollectionNum).
			SetUsedNum(d.UsedNum).
			SetAbolishNum(d.AbolishNum).
			SetSurplusNum(d.SurplusNum).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime).
			SetIsAbolish(d.IsAbolish).
			SetCustomerID(d.CustomerID).
			SetShopID(d.ShopID)
	}
	rows, err := cri.data.GetDb(ctx).Coupon.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return cri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (cri *CouponRepoImpl) Update(ctx context.Context, d *do.CouponDo) (int, error) {
	return cri.data.GetDb(ctx).Coupon.Update().Where(coupon.ID(d.ID)).
		SetTitle(d.Title).
		SetQuantity(d.Quantity).
		SetRemark(d.Remark).
		SetCollectionStartTime(d.CollectionStartTime).
		SetCollectionEndTime(d.CollectionEndTime).
		SetEffectTimeType(d.EffectTimeType).
		SetEffectStartTime(d.EffectStartTime).
		SetEffectEndTime(d.EffectEndTime).
		SetEffectStartDay(d.EffectStartDay).
		SetEffectEndDay(d.EffectEndDay).
		SetLimitAmount(d.LimitAmount).
		SetDiscountAmount(d.DiscountAmount).
		SetCollectionQuantity(d.CollectionQuantity).
		SetCollectionType(d.CollectionType).
		SetProductLimit(d.ProductLimit).
		SetCollectionNum(d.CollectionNum).
		SetUsedNum(d.UsedNum).
		SetAbolishNum(d.AbolishNum).
		SetSurplusNum(d.SurplusNum).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetIsAbolish(d.IsAbolish).
		SetCustomerID(d.CustomerID).
		SetShopID(d.ShopID).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (cri *CouponRepoImpl) UpdateV2(ctx context.Context, d *do.CouponDo) (int, error) {
	cnt, err := cri.data.GetDb(ctx).Coupon.Update().Where(coupon.ID(d.ID)).
		SetTitle(d.Title).
		SetQuantity(d.Quantity).
		SetRemark(d.Remark).
		SetCollectionStartTime(d.CollectionStartTime).
		SetCollectionEndTime(d.CollectionEndTime).
		SetEffectTimeType(d.EffectTimeType).
		SetEffectStartTime(d.EffectStartTime).
		SetEffectEndTime(d.EffectEndTime).
		SetEffectStartDay(d.EffectStartDay).
		SetEffectEndDay(d.EffectEndDay).
		SetLimitAmount(d.LimitAmount).
		SetDiscountAmount(d.DiscountAmount).
		SetCollectionQuantity(d.CollectionQuantity).
		SetCollectionType(d.CollectionType).
		SetProductLimit(d.ProductLimit).
		SetCollectionNum(d.CollectionNum).
		SetUsedNum(d.UsedNum).
		SetAbolishNum(d.AbolishNum).
		SetSurplusNum(d.SurplusNum).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetIsAbolish(d.IsAbolish).
		SetCustomerID(d.CustomerID).
		SetShopID(d.ShopID).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (cri *CouponRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := cri.data.GetDb(ctx).Coupon.Delete().Where(coupon.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := cri.data.GetDb(ctx).Coupon.Update().
	// 	Where(coupon.IDIn(ids...), coupon.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (cri *CouponRepoImpl) SearchList(ctx context.Context, reqBo *bo.CouponSearchBo) (dos []*do.CouponDo, respPage *bo.RespPageBo) {
	q := cri.data.GetDb(ctx).Coupon.Query()
	if reqBo.Page != nil {
		cri.SetPageByBo(q, reqBo.Page)
		respPage = cri.QueryRespPage(ctx, q, reqBo.Page)
	}

	pos := q.AllX(ctx)
	dos = cri.ToEntities(pos)
	return
}
