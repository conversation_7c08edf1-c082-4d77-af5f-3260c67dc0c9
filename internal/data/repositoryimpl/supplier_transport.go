//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"context"
	"errors"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/suppliertransport"
	"cardMall/internal/server/middleware/authtools"
)

var supplierTransportRepoImpl = &SupplierTransportRepoImpl{}

// NewSupplierTransportRepoImpl 创建 SupplierTransportRepo的实现者
func NewSupplierTransportRepoImpl(data *data.Data) repository.SupplierTransportRepo {
	return &SupplierTransportRepoImpl{data: data}
}

type SupplierTransportRepoImpl struct {
	Base[ent2.SupplierTransport, do.SupplierTransportDo, ent2.SupplierTransportQuery]
	data *data.Data
}

func (stri *SupplierTransportRepoImpl) FindByNamesByLoginInfo(ctx context.Context, names []string) []*do.SupplierTransportDo {
	adminLoginInfoBo := authtools.GetLoginInfoX(ctx).ToLoginInfo()
	rows := stri.data.GetDb(ctx).SupplierTransport.Query().
		Where(suppliertransport.NameIn(names...)).
		Where(suppliertransport.CustomerIDEQ(adminLoginInfoBo.CustomerId)).
		Where(suppliertransport.ShopIDEQ(adminLoginInfoBo.ShopId)).
		Where(suppliertransport.SupplierIDEQ(adminLoginInfoBo.SupplierId)).
		AllX(ctx)
	return stri.ToEntities(rows)
}

// ToEntity 转换成实体
func (stri *SupplierTransportRepoImpl) ToEntity(po *ent2.SupplierTransport) *do.SupplierTransportDo {
	if po == nil {
		return nil
	}
	entity := stri.Base.ToEntity(po)
	entity.SupplierTransportItem = supplierTransportItemRepoImpl.ToEntities(po.Edges.SupplierTransportItem)
	// entity.SupplierTransportCity = supplierTransportCityRepoImpl.ToEntities(po.Edges.SupplierTransportCity)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (stri *SupplierTransportRepoImpl) ToEntities(pos []*ent2.SupplierTransport) []*do.SupplierTransportDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.SupplierTransportDo, len(pos))
	for k, p := range pos {
		entities[k] = stri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (stri *SupplierTransportRepoImpl) Get(ctx context.Context, id int) (*do.SupplierTransportDo, error) {
	row, err := stri.data.GetDb(ctx).SupplierTransport.Query().Where(suppliertransport.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return stri.ToEntity(row), nil
}

// Exist 判断数据是否存在
func (stri *SupplierTransportRepoImpl) Exist(ctx context.Context, id int) (bool, error) {
	ctn, err := stri.data.GetDb(ctx).SupplierTransport.Query().
		Where(suppliertransport.IDEQ(id)).
		Count(ctx)
	if err != nil {
		return false, err
	}
	return ctn > 0, err
}

func (stri *SupplierTransportRepoImpl) GetHytTransportId(ctx context.Context, name string, shopId, customerId, supplierId int) (int, error) {
	po, err := stri.data.GetDb(ctx).SupplierTransport.Query().
		Where(suppliertransport.NameEQ(name)).
		Where(suppliertransport.DescriptionEQ(name)).
		Where(suppliertransport.PricingModeEQ(valobj.TransportPricingModeFree)).
		Where(suppliertransport.SupplierIDEQ(supplierId)).
		Where(suppliertransport.CustomerIDEQ(customerId)).
		Where(suppliertransport.ShopIDEQ(shopId)).
		FirstID(ctx)
	if err != nil {
		return 0, err
	}
	return po, err
}

// Find 搜索列表
func (stri *SupplierTransportRepoImpl) Find(ctx context.Context, ids ...int) (dos []*do.SupplierTransportDo, err error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := stri.data.GetDb(ctx).SupplierTransport.Query().Where(suppliertransport.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return stri.ToEntities(rows), err
}

// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
func (stri *SupplierTransportRepoImpl) GetWithEdges(ctx context.Context, id int, reqBo *bo.SupplierTransportEdgesBo) (*do.SupplierTransportDo, error) {
	q := stri.data.GetDb(ctx).SupplierTransport.Query().Where(suppliertransport.ID(id))
	if reqBo.WithSupplierTransportItem {
		q.WithSupplierTransportItem(func(query *ent2.SupplierTransportItemQuery) {
			if reqBo.WithSupplierTransportCity {
				query.WithSupplierTransportCity()
			}
		})
	}

	row, err := q.First(ctx)
	if err != nil {
		return nil, err
	}
	return stri.ToEntity(row), nil
}

// Create 创建数据
func (stri *SupplierTransportRepoImpl) Create(ctx context.Context, d *do.SupplierTransportDo) (int, error) {
	row, err := stri.data.GetDb(ctx).SupplierTransport.Create().
		SetName(d.Name).
		SetDescription(d.Description).
		SetKdID(d.KdID).
		SetKdName(d.KdName).
		SetKdCode(d.KdCode).
		SetPricingMode(d.PricingMode).
		SetSupplierID(d.SupplierID).
		SetDefaultNum(d.DefaultNum).
		SetDefaultPrice(d.DefaultPrice).
		SetAddNum(d.AddNum).
		SetAddPrice(d.AddPrice).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if err != nil {
		return 0, err
	}
	return row.ID, nil
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (stri *SupplierTransportRepoImpl) UpdateV2(ctx context.Context, supplierId int, d *do.SupplierTransportDo) (int, error) {
	cnt, err := stri.data.GetDb(ctx).SupplierTransport.Update().
		Where(suppliertransport.IDEQ(d.ID)).
		Where(suppliertransport.SupplierIDEQ(supplierId)).
		SetName(d.Name).
		SetDescription(d.Description).
		SetKdID(d.KdID).
		SetKdName(d.KdName).
		SetKdCode(d.KdCode).
		SetPricingMode(d.PricingMode).
		SetDefaultNum(d.DefaultNum).
		SetDefaultPrice(d.DefaultPrice).
		SetAddNum(d.AddNum).
		SetAddPrice(d.AddPrice).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (stri *SupplierTransportRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := stri.data.GetDb(ctx).SupplierTransport.Delete().Where(suppliertransport.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := stri.data.GetDb(ctx).SupplierTransport.Update().
	// 	Where(suppliertransport.IDIn(ids...), suppliertransport.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (stri *SupplierTransportRepoImpl) SearchList(ctx context.Context, reqBo *bo.SupplierTransportSearchBo) (dos []*do.SupplierTransportDo, respPage *bo.RespPageBo) {
	q := stri.data.GetDb(ctx).SupplierTransport.Query()
	if reqBo.SupplierId > 0 {
		q.Where(suppliertransport.SupplierIDEQ(reqBo.SupplierId))
	}

	if reqBo.Name != "" {
		q.Where(suppliertransport.NameContains(reqBo.Name))
	}

	if reqBo.Page != nil {
		stri.SetPageByBo(q, reqBo.Page)
		respPage = stri.QueryRespPage(ctx, q, reqBo.Page)
	}
	if reqBo.Edges != nil {
		if reqBo.Edges.WithSupplierTransportItem {
			q.WithSupplierTransportItem()
		}
	}

	pos := q.AllX(ctx)
	dos = stri.ToEntities(pos)
	return
}
