//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/cardbatch"
	"cardMall/internal/data/ent/cardbatchcoupon"
	"cardMall/internal/pkg/helper"
	"context"
	"entgo.io/ent/dialect/sql"
	"errors"
	"github.com/duke-git/lancet/v2/mathutil"
	"time"
)

var cardBatchCouponRepoImpl = &CardBatchCouponRepoImpl{}

type CardBatchCouponRepoImpl struct {
	Base[ent2.CardBatchCoupon, do.CardBatchCouponDo, ent2.CardBatchCouponQuery]
	data *data.Data
}

// NewCardBatchCouponRepoImpl 创建 CardBatchCouponRepo的实现者
func NewCardBatchCouponRepoImpl(data *data.Data) repository.CardBatchCouponRepo {
	return &CardBatchCouponRepoImpl{data: data}
}

// ToEntity 转换成实体
func (cbcri *CardBatchCouponRepoImpl) ToEntity(po *ent2.CardBatchCoupon) *do.CardBatchCouponDo {
	if po == nil {
		return nil
	}
	entity := cbcri.Base.ToEntity(po)
	entity.CardBatch = cardBatchRepoImpl.ToEntity(po.Edges.CardBatch)
	entity.CardCouponOperatorLog = cardCouponOperatorLogRepoImpl.ToEntities(po.Edges.CardCouponOperatorLog)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (cbcri *CardBatchCouponRepoImpl) ToEntities(pos []*ent2.CardBatchCoupon) []*do.CardBatchCouponDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.CardBatchCouponDo, len(pos))
	for k, p := range pos {
		entities[k] = cbcri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (cbcri *CardBatchCouponRepoImpl) Get(ctx context.Context, id int) (*do.CardBatchCouponDo, error) {
	row, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Query().Where(cardbatchcoupon.ID(id)).WithCardBatch().First(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntity(row), nil
}

// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
func (cbcri *CardBatchCouponRepoImpl) GetWithEdges(ctx context.Context, id int, reqBo *bo.CardBatchCouponEdgesBo) (*do.CardBatchCouponDo, error) {
	q := cbcri.data.GetDb(ctx).CardBatchCoupon.Query().Where(cardbatchcoupon.ID(id))
	if reqBo.WithCardBatch {
		q.WithCardBatch()
	}
	if reqBo.WithCardCouponOperatorLog {
		q.WithCardCouponOperatorLog()
	}

	row, err := q.First(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (cbcri *CardBatchCouponRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.CardBatchCouponDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Query().Where(cardbatchcoupon.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntities(rows), nil
}

// Create 创建数据
func (cbcri *CardBatchCouponRepoImpl) Create(ctx context.Context, d *do.CardBatchCouponDo) (*do.CardBatchCouponDo, error) {
	row, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Create().
		SetCardCouponNumber(d.CardCouponNumber).
		SetCardBatchID(d.CardBatchID).
		SetCardBatchNumber(d.CardBatchNumber).
		SetCardBatchName(d.CardBatchName).
		SetExchangeGoodsRepeat(d.ExchangeGoodsRepeat).
		SetExchangeGoodsNum(d.ExchangeGoodsNum).
		SetCardNumberType(d.CardNumberType).
		SetUseExpireStart(d.UseExpireStart).
		SetUseExpireEnd(d.UseExpireEnd).
		SetUserID(d.UserID).
		SetUserName(d.UserName).
		SetBindTime(d.BindTime).
		SetBindStatus(d.BindStatus).
		SetCardCouponUseNum(d.CardCouponUseNum).
		SetStatus(d.Status).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (cbcri *CardBatchCouponRepoImpl) CreateBulk(ctx context.Context, dos []*do.CardBatchCouponDo) ([]*do.CardBatchCouponDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.CardBatchCouponCreate, len(dos))
	for i, d := range dos {
		values[i] = cbcri.data.GetDb(ctx).CardBatchCoupon.Create().
			SetCardCouponNumber(d.CardCouponNumber).
			SetCardBatchID(d.CardBatchID).
			SetCardBatchNumber(d.CardBatchNumber).
			SetCardBatchName(d.CardBatchName).
			SetExchangeGoodsRepeat(d.ExchangeGoodsRepeat).
			SetExchangeGoodsNum(d.ExchangeGoodsNum).
			SetCardNumberType(d.CardNumberType).
			SetUseExpireStart(d.UseExpireStart).
			SetUseExpireEnd(d.UseExpireEnd).
			SetUserID(d.UserID).
			SetUserName(d.UserName).
			SetBindTime(d.BindTime).
			SetBindStatus(d.BindStatus).
			SetCardCouponUseNum(d.CardCouponUseNum).
			SetStatus(d.Status).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime)
	}
	rows, err := cbcri.data.GetDb(ctx).CardBatchCoupon.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (cbcri *CardBatchCouponRepoImpl) Update(ctx context.Context, d *do.CardBatchCouponDo) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchCoupon.Update().Where(cardbatchcoupon.ID(d.ID)).
		SetCardCouponNumber(d.CardCouponNumber).
		SetCardBatchID(d.CardBatchID).
		SetCardBatchNumber(d.CardBatchNumber).
		SetCardBatchName(d.CardBatchName).
		SetExchangeGoodsRepeat(d.ExchangeGoodsRepeat).
		SetExchangeGoodsNum(d.ExchangeGoodsNum).
		SetCardNumberType(d.CardNumberType).
		SetUseExpireStart(d.UseExpireStart).
		SetUseExpireEnd(d.UseExpireEnd).
		SetUserID(d.UserID).
		SetUserName(d.UserName).
		SetCardCouponUseNum(d.CardCouponUseNum).
		SetStatus(d.Status).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (cbcri *CardBatchCouponRepoImpl) UpdateV2(ctx context.Context, d *do.CardBatchCouponDo) (int, error) {
	cnt, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Update().Where(cardbatchcoupon.ID(d.ID)).
		SetCardCouponNumber(d.CardCouponNumber).
		SetCardBatchID(d.CardBatchID).
		SetCardBatchNumber(d.CardBatchNumber).
		SetCardBatchName(d.CardBatchName).
		SetExchangeGoodsRepeat(d.ExchangeGoodsRepeat).
		SetExchangeGoodsNum(d.ExchangeGoodsNum).
		SetCardNumberType(d.CardNumberType).
		SetUseExpireStart(d.UseExpireStart).
		SetUseExpireEnd(d.UseExpireEnd).
		SetUserID(d.UserID).
		SetUserName(d.UserName).
		SetCardCouponUseNum(d.CardCouponUseNum).
		SetStatus(d.Status).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (cbcri *CardBatchCouponRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Delete().Where(cardbatchcoupon.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := cbcri.data.GetDb(ctx).CardBatchCoupon.Update().
	// 	Where(cardbatchcoupon.IDIn(ids...), cardbatchcoupon.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (cbcri *CardBatchCouponRepoImpl) SearchList(ctx context.Context, reqBo *bo.CardBatchCouponSearchBo) (dos []*do.CardBatchCouponDo, respPage *bo.RespPageBo) {
	q := cbcri.data.GetDb(ctx).CardBatchCoupon.Query()

	if reqBo.CreateTimeStart > 0 {
		q.Where(cardbatchcoupon.CreateTimeGTE(reqBo.CreateTimeStart))
	}
	if reqBo.CreateTimeEnd > 0 {
		q.Where(cardbatchcoupon.CreateTimeLTE(reqBo.CreateTimeEnd))
	}

	if reqBo.UseExpireStart > 0 {
		q.Where(cardbatchcoupon.UseExpireEndGTE(reqBo.UseExpireStart))
	}
	if reqBo.UseExpireEnd > 0 {
		q.Where(cardbatchcoupon.UseExpireEndLTE(reqBo.UseExpireEnd))
	}
	if reqBo.CardCouponNumber != "" {
		q.Where(cardbatchcoupon.CardCouponNumberEQ(reqBo.CardCouponNumber))
	}

	if reqBo.CouponStatus != 0 {
		q.Where(cardbatchcoupon.StatusEQ(reqBo.CouponStatus))
	}
	if reqBo.CardBatchNumber != "" {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchcoupon.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.CardBatchNumber != "" {
				s.Where(sql.Contains(cardBatchTable.C(cardbatch.FieldCardBatchNumber), reqBo.CardBatchNumber))
			}
		})
	}
	if reqBo.Status != 0 {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchcoupon.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.Status != 0 {
				s.Where(sql.EQ(cardBatchTable.C(cardbatch.FieldStatus), reqBo.Status))
			}
		})
	}
	if reqBo.ActivateTimeStart > 0 {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchcoupon.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.ActivateTimeStart > 0 {
				s.Where(sql.GTE(cardBatchTable.C(cardbatch.FieldActivateTime), reqBo.ActivateTimeStart))
			}
		})
	}
	if reqBo.ActivateTimeEnd > 0 {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchcoupon.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.ActivateTimeEnd > 0 {
				s.Where(sql.LTE(cardBatchTable.C(cardbatch.FieldActivateTime), reqBo.ActivateTimeEnd))
			}
		})
	}
	q.Order(ent2.Desc(cardbatch.FieldID))
	if reqBo.Page != nil {
		cbcri.SetPageByBo(q, reqBo.Page)
		respPage = cbcri.QueryRespPage(ctx, q, reqBo.Page)
	}
	if reqBo.Edges != nil {
		if reqBo.Edges.WithCardBatch {
			q.WithCardBatch()
		}
		if reqBo.Edges.WithCardCouponOperatorLog {
			q.WithCardCouponOperatorLog()
		}
	}

	pos := q.AllX(ctx)
	dos = cbcri.ToEntities(pos)
	return
}

// FindByUserId 通过UserId 获取多条数据
func (cbcri *CardBatchCouponRepoImpl) FindByUserId(ctx context.Context, userId int) ([]*do.CardBatchCouponDo, error) {

	rows, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Query().Where(cardbatchcoupon.UserIDEQ(userId)).WithCardBatch().All(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntities(rows), nil
}

// CancelByCardBatchID 作废卡券
func (cbcri *CardBatchCouponRepoImpl) CancelByCardBatchID(ctx context.Context, cardBatchIds []int) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchCoupon.Update().Where(cardbatchcoupon.CardBatchIDIn(cardBatchIds...)).Where(cardbatchcoupon.CardCouponUseNumEQ(0)).
		SetStatus(valobj.CardBatchCouponStatusCancel).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}

// DeleteByCardBatchID 删除卡券
func (cbcri *CardBatchCouponRepoImpl) DeleteByCardBatchID(ctx context.Context, cardBatchIds []int) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchCoupon.Delete().Where(cardbatchcoupon.CardBatchIDIn(cardBatchIds...)).Where(cardbatchcoupon.CardCouponUseNumEQ(0)).Exec(ctx)
}

// CancelByID 作废卡券
func (cbcri *CardBatchCouponRepoImpl) CancelByID(ctx context.Context, cardBatchCouponIds []int) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchCoupon.Update().Where(cardbatchcoupon.IDIn(cardBatchCouponIds...)).Where(cardbatchcoupon.CardCouponUseNumEQ(0)).
		SetStatus(valobj.CardBatchCouponStatusCancel).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}
func (r *CardBatchCouponRepoImpl) Num(ctx context.Context, in []int) ([]*do.BatchCouponNumResult, error) {
	var (
		wantArgs []any
	)

	t1 := sql.Table(cardbatchcoupon.Table).As("cbc")

	fieldIn := sql.FieldIn(t1.C(cardbatchcoupon.FieldID), in...)
	selector := sql.Select(
		t1.C(sql.As(cardbatchcoupon.FieldID, "card_batch_coupon_id")),
		"SUM(CASE WHEN (status = 1 or status = 2) AND use_expire_end > UNIX_TIMESTAMP() THEN exchange_goods_num-card_coupon_use_num ELSE 0 END) as surplus_num",
		"SUM(CASE WHEN status = 3 THEN exchange_goods_num-card_coupon_use_num ELSE 0 END) as cancel_num",
		"SUM(CASE WHEN (status = 1 or status = 2) AND use_expire_end < UNIX_TIMESTAMP() THEN exchange_goods_num-card_coupon_use_num ELSE 0 END) as expire_num",
	).From(t1).GroupBy(cardbatchcoupon.FieldID)
	fieldIn(selector)
	query, wantArgs := selector.Query()

	result, err := r.data.GetDb(ctx).QueryContext(ctx, query, wantArgs...)
	if err != nil {
		return nil, err
	}
	numResults := make([]*do.BatchCouponNumResult, 0)
	err = sql.ScanSlice(result, &numResults)
	if err != nil {
		return nil, err
	}
	return numResults, nil
}

// GetByCardCouponNumber 通过 cardCouponNumber 获取一条数据
func (cbcri *CardBatchCouponRepoImpl) GetByCardCouponNumber(ctx context.Context, cardCouponNumber string) (*do.CardBatchCouponDo, error) {
	row, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Query().Where(cardbatchcoupon.CardCouponNumberEQ(cardCouponNumber)).WithCardBatch().First(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntity(row), nil
}
func (cbcri *CardBatchCouponRepoImpl) Exchange(ctx context.Context, userId int, cardCouponNumber string) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchCoupon.Update().
		Where(cardbatchcoupon.CardCouponNumberEQ(cardCouponNumber)).
		Where(cardbatchcoupon.UserIDEQ(0)).
		SetBindStatus(valobj.CardBatchCouponBindStatusYes).
		SetUserID(userId).
		SetUpdateTime(int(time.Now().Unix())).
		SetBindTime(int(time.Now().Unix())).
		Save(ctx)
}

// FindByCardBatchNumber 通过cardBatchNumber 获取多条数据
func (cbcri *CardBatchCouponRepoImpl) FindByCardBatchNumber(ctx context.Context, cardBatchNumber string) ([]*do.CardBatchCouponDo, error) {

	rows, err := cbcri.data.GetDb(ctx).CardBatchCoupon.Query().Where(cardbatchcoupon.CardBatchNumber(cardBatchNumber)).WithCardBatch().All(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntities(rows), nil
}

// UpdateCardCouponUseNum 更新卡券使用数量
func (cbcri *CardBatchCouponRepoImpl) UpdateCardCouponUseNum(ctx context.Context, id int, reqBo *bo.UpdateCardBatchCouponUseInfoBo) error {
	query := cbcri.data.GetDb(ctx).CardBatchCoupon.Update().
		Where(cardbatchcoupon.ID(id)).
		AddCardCouponUseNum(mathutil.Abs(reqBo.UseNum)).
		SetUseSkuNos(reqBo.UseSkuNos).
		SetUpdateTime(helper.GetNow()).
		SetStatus(reqBo.Status)

	ctn, err := query.Save(ctx)
	if err != nil {
		return err
	}
	if ctn == 0 {
		return apierr.ErrorDbNotFound("更新卡券使用数量失败")
	}
	return nil
}

// RejectOrderCardCouponUseNum 更新卡券使用数量
func (cbcri *CardBatchCouponRepoImpl) RejectOrderCardCouponUseNum(ctx context.Context, id int, reqBo *bo.RejectOrderCardBatchCouponUseInfoBo) error {
	query := cbcri.data.GetDb(ctx).CardBatchCoupon.Update().
		Where(cardbatchcoupon.ID(id)).
		AddCardCouponUseNum(-mathutil.Abs(reqBo.UseNum)).
		SetUseSkuNos(reqBo.UseSkuNos).
		SetUpdateTime(helper.GetNow())

	ctn, err := query.Save(ctx)
	if err != nil {
		return err
	}
	if ctn == 0 {
		return apierr.ErrorDbNotFound("更新卡券使用数量失败")
	}
	return nil
}
