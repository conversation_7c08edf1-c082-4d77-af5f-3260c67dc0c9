package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodsspec"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"time"
)

type GoodsSpecRepoImpl struct {
	data *data.Data
	Base[ent.GoodsSpec, do.GoodsSpecDo, ent.GoodsSpecQuery]
}

func NewGoodsSpecRepoImpl(data *data.Data) repository.GoodsSpecRepo {
	return &GoodsSpecRepoImpl{
		data: data,
	}
}

func (g *GoodsSpecRepoImpl) FindByName(ctx context.Context, name string) (*do.GoodsSpecDo, error) {
	res := g.data.GetDb(ctx).GoodsSpec.Query().Where(goodsspec.Name(name)).FirstX(ctx)
	return g.To<PERSON>(res), nil
}

func (g *GoodsSpecRepoImpl) Add(ctx context.Context, in *bo.GoodsSpecAddBo) (*do.GoodsSpecDo, error) {
	now := int(time.Now().Unix())
	res := g.data.GetDb(ctx).GoodsSpec.Create().
		SetName(in.Name).
		SetRemark(in.Remark).
		SetFormat(in.Format).
		SetSort(in.Sort).
		SetType(in.Type).
		SetCreateTime(now).
		SetUpdateTime(now).
		SetCustomerID(isolationcustomer.GetCustomerIdZero(ctx)).
		SetShopID(in.ShopID).
		SaveX(ctx)
	return g.ToEntity(res), nil
}

func (g *GoodsSpecRepoImpl) Get(ctx context.Context, in *bo.GoodsSpecQueryBo) ([]*do.GoodsSpecDo, error) {
	sql := g.data.GetDb(ctx).GoodsSpec.Query()
	if in.Id > 0 {
		sql.Where(goodsspec.ID(in.Id))
	}
	if in.Name != "" {
		sql.Where(goodsspec.NameContains(in.Name))
	}
	if in.Type.IsValid() {
		sql.Where(goodsspec.Type(in.Type))
	}
	return g.ToEntities(sql.AllX(ctx)), nil
}
func (g *GoodsSpecRepoImpl) GetByIds(ctx context.Context, id []int) ([]*do.GoodsSpecDo, error) {
	return g.ToEntities(g.data.GetDb(ctx).GoodsSpec.Query().Where(goodsspec.IDIn(id...)).AllX(ctx)), nil
}

func (g *GoodsSpecRepoImpl) GetMapByIds(ctx context.Context, id []int) (map[int]*do.GoodsSpecDo, error) {
	d, _ := g.GetByIds(ctx, id)
	res := make(map[int]*do.GoodsSpecDo)
	for _, v := range d {
		res[v.Id] = v
	}
	return res, nil
}
