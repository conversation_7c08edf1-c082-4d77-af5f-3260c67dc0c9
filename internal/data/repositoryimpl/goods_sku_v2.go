//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodssku"
	"context"
)

var goodsSkuRepoImpl = &GoodsSkuV2RepoImpl{}

type GoodsSkuV2RepoImpl struct {
	Base[ent2.GoodsSku, do.GoodsSkuDo, ent2.GoodsSkuQuery]
	data *data.Data
}

// NewGoodsSkuV2RepoImpl 创建 GoodsSkuV2Repo的实现者
func NewGoodsSkuV2RepoImpl(data *data.Data) repository.GoodsSkuV2Repo {
	return &GoodsSkuV2RepoImpl{data: data}
}

// ToEntity 转换成实体
func (gsri *GoodsSkuV2RepoImpl) ToEntity(po *ent2.GoodsSku) *do.GoodsSkuDo {
	if po == nil {
		return nil
	}
	entity := gsri.Base.ToEntity(po)
	entity.Goods = goodsRepoImpl.ToEntity(po.Edges.Goods)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (gsri *GoodsSkuV2RepoImpl) ToEntities(pos []*ent2.GoodsSku) []*do.GoodsSkuDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.GoodsSkuDo, len(pos))
	for k, p := range pos {
		entities[k] = gsri.ToEntity(p)
	}
	return entities
}

// Find 通过多个 id 获取多条数据
func (gsri *GoodsSkuV2RepoImpl) Find(ctx context.Context, ids ...int) ([]*do.GoodsSkuDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := gsri.data.GetDb(ctx).GoodsSku.Query().Where(goodssku.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return gsri.ToEntities(rows), nil
}

// FindWithGoods 通过多个 id 获取多条数据
func (gsri *GoodsSkuV2RepoImpl) FindWithGoods(ctx context.Context, ids ...int) ([]*do.GoodsSkuDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := gsri.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.IDIn(ids...)).
		Where(goodssku.DeleteTimeEQ(0)).
		WithGoods().
		All(ctx)
	if err != nil {
		return nil, err
	}
	return gsri.ToEntities(rows), nil
}

// FindWithGoodsBySkuNo 通过多个 id 获取多条数据
func (gsri *GoodsSkuV2RepoImpl) FindWithGoodsBySkuNo(ctx context.Context, skuNos ...string) ([]*do.GoodsSkuDo, error) {
	if len(skuNos) == 0 {
		return nil, nil
	}
	rows, err := gsri.data.GetDb(ctx).GoodsSku.Query().
		Where(goodssku.SkuNoIn(skuNos...)).
		Where(goodssku.DeleteTimeEQ(0)).
		WithGoods().
		All(ctx)
	if err != nil {
		return nil, err
	}
	return gsri.ToEntities(rows), nil
}
