//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/kd"
	"context"
)

type KdRepoImpl struct {
	Base[ent2.Kd, do.KdDo, ent2.KdQuery]
	data *data.Data
}

// NewKdRepoImpl 创建 KdRepo的实现者
func NewKdRepoImpl(data *data.Data) repository.KdRepo {
	return &KdRepoImpl{data: data}
}

// ToEntity 转换成实体
func (kri *KdRepoImpl) ToEntity(po *ent2.Kd) *do.KdDo {
	if po == nil {
		return nil
	}
	entity := kri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (kri *KdRepoImpl) ToEntities(pos []*ent2.Kd) []*do.KdDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.KdDo, len(pos))
	for k, p := range pos {
		entities[k] = kri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (kri *KdRepoImpl) Get(ctx context.Context, id int) (*do.KdDo, error) {
	row, err := kri.data.GetDb(ctx).Kd.Query().
		Where(kd.ID(id)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return kri.ToEntity(row), nil
}

// GetByCode 通过 code 获取一条数据
func (kri *KdRepoImpl) GetByCode(ctx context.Context, code string) (*do.KdDo, error) {
	row, err := kri.data.GetDb(ctx).Kd.Query().
		Where(kd.CodeEQ(code)).
		Where(kd.StatusEQ(valobj.KdStatusEnable)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return kri.ToEntity(row), nil
}

func (kri *KdRepoImpl) FindByCodes(ctx context.Context, code ...string) ([]*do.KdDo, error) {
	rows, err := kri.data.GetDb(ctx).Kd.Query().
		Where(kd.CodeIn(code...)).
		Where(kd.StatusEQ(valobj.KdStatusEnable)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return kri.ToEntities(rows), nil
}

func (kri *KdRepoImpl) FindByNames(ctx context.Context, name ...string) ([]*do.KdDo, error) {
	rows, err := kri.data.GetDb(ctx).Kd.Query().
		Where(kd.NameIn(name...)).
		Where(kd.StatusEQ(valobj.KdStatusEnable)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return kri.ToEntities(rows), nil
}

// SearchByName 通过 name 搜索数据
func (kri *KdRepoImpl) SearchByName(ctx context.Context, name string) ([]*do.KdDo, error) {
	rows, err := kri.data.GetDb(ctx).Kd.Query().
		Where(kd.NameContains(name)).
		Limit(constants.SearchDefaultLimitNum).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return kri.ToEntities(rows), nil
}

func (kri *KdRepoImpl) GetByName(ctx context.Context, name string) (*do.KdDo, error) {
	row, err := kri.data.GetDb(ctx).Kd.Query().
		Where(kd.NameContains(name)).
		Limit(constants.SearchDefaultLimitNum).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return kri.ToEntity(row), nil
}

// SearchList 搜索列表
func (kri *KdRepoImpl) SearchList(ctx context.Context, reqBo *bo.KdSearchBo) (dos []*do.KdDo, respPage *bo.RespPageBo) {
	q := kri.data.GetDb(ctx).Kd.Query()

	if reqBo.Name != "" {
		q.Where(kd.NameContains(reqBo.Name))
	}
	if reqBo.Id != 0 {
		q.Where(kd.IDEQ(reqBo.Id))
	}
	if reqBo.Code != "" {
		q.Where(kd.CodeEQ(reqBo.Code))
	}

	if reqBo.Status != nil {
		q.Where(kd.StatusEQ(*reqBo.Status))
	}

	if reqBo.Page != nil {
		kri.SetPageByBo(q, reqBo.Page)
		respPage = kri.QueryRespPage(ctx, q, reqBo.Page)
	}
	for _, sort := range reqBo.SortData {
		q.Order(sort.GetOrderSelector())
	}

	pos := q.AllX(ctx)
	dos = kri.ToEntities(pos)
	return
}
