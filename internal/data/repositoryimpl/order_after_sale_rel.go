//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderaftersalerel"
	"cardMall/internal/pkg/hyt/hytvo"
	"context"
	"errors"
)

type OrderAfterSaleRelRepoImpl struct {
	Base[ent2.OrderAfterSaleRel, do.OrderAfterSaleRelDo, ent2.OrderAfterSaleRelQuery]
	data *data.Data
}

// NewOrderAfterSaleRelRepoImpl 创建 OrderAfterSaleRelRepo的实现者
func NewOrderAfterSaleRelRepoImpl(data *data.Data) repository.OrderAfterSaleRelRepo {
	return &OrderAfterSaleRelRepoImpl{data: data}
}

// ToEntity 转换成实体
func (oashrri *OrderAfterSaleRelRepoImpl) ToEntity(po *ent2.OrderAfterSaleRel) *do.OrderAfterSaleRelDo {
	if po == nil {
		return nil
	}
	entity := oashrri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (oashrri *OrderAfterSaleRelRepoImpl) ToEntities(pos []*ent2.OrderAfterSaleRel) []*do.OrderAfterSaleRelDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.OrderAfterSaleRelDo, len(pos))
	for k, p := range pos {
		entities[k] = oashrri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (oashrri *OrderAfterSaleRelRepoImpl) Get(ctx context.Context, id int) (*do.OrderAfterSaleRelDo, error) {
	row, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Query().Where(orderaftersalerel.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (oashrri *OrderAfterSaleRelRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleRelDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Query().Where(orderaftersalerel.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntities(rows), nil
}

// Create 创建数据
func (oashrri *OrderAfterSaleRelRepoImpl) Create(ctx context.Context, d *do.OrderAfterSaleRelDo) (*do.OrderAfterSaleRelDo, error) {
	row, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Create().
		SetAfterSaleID(d.AfterSaleID).
		SetHytNo(d.HytNo).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (oashrri *OrderAfterSaleRelRepoImpl) CreateBulk(ctx context.Context, dos []*do.OrderAfterSaleRelDo) ([]*do.OrderAfterSaleRelDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.OrderAfterSaleRelCreate, len(dos))
	for i, d := range dos {
		values[i] = oashrri.data.GetDb(ctx).OrderAfterSaleRel.Create().
			SetAfterSaleID(d.AfterSaleID).
			SetHytNo(d.HytNo).
			SetSubOrderNo(d.SubOrderNo).
			SetStatus(d.Status).
			SetType(d.Type).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime)
	}
	rows, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (oashrri *OrderAfterSaleRelRepoImpl) Update(ctx context.Context, d *do.OrderAfterSaleRelDo) (int, error) {
	return oashrri.data.GetDb(ctx).OrderAfterSaleRel.Update().Where(orderaftersalerel.ID(d.ID)).
		SetAfterSaleID(d.AfterSaleID).
		SetHytNo(d.HytNo).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (oashrri *OrderAfterSaleRelRepoImpl) UpdateV2(ctx context.Context, d *do.OrderAfterSaleRelDo) (int, error) {
	cnt, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Update().Where(orderaftersalerel.ID(d.ID)).
		SetAfterSaleID(d.AfterSaleID).
		SetHytNo(d.HytNo).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (oashrri *OrderAfterSaleRelRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Delete().Where(orderaftersalerel.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Update().
	// 	Where(OrderAfterSaleRel.IDIn(ids...), OrderAfterSaleRel.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (oashrri *OrderAfterSaleRelRepoImpl) SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleRelSearchBo) (dos []*do.OrderAfterSaleRelDo, respPage *bo.RespPageBo) {
	q := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Query()
	if reqBo.Page != nil {
		oashrri.SetPageByBo(q, reqBo.Page)
		respPage = oashrri.QueryRespPage(ctx, q, reqBo.Page)
	}

	pos := q.AllX(ctx)
	dos = oashrri.ToEntities(pos)
	return
}

func (oashrri *OrderAfterSaleRelRepoImpl) UpdateStatusByHytNo(ctx context.Context, hytNo string, status hytvo.AfterSaleStatusObj) (int, error) {
	return oashrri.data.GetDb(ctx).OrderAfterSaleRel.Update().
		Where(orderaftersalerel.HytNoEQ(hytNo)).
		Where(orderaftersalerel.TypeEQ(valobj.AfterSaleRelTypeHyt)).
		SetStatus(status).
		Save(ctx)
}

// FindHytByAfterSaleIds 通过 afterSaleId 查询多条数据
func (oashrri *OrderAfterSaleRelRepoImpl) FindHytByAfterSaleIds(ctx context.Context, afterSaleIds []int) ([]*do.OrderAfterSaleRelDo, error) {
	rows, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Query().
		Where(orderaftersalerel.AfterSaleIDIn(afterSaleIds...)).
		Where(orderaftersalerel.TypeEQ(valobj.AfterSaleRelTypeHyt)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntities(rows), nil
}

// FindByHytNo 通过 hytNo 查询多条数据
func (oashrri *OrderAfterSaleRelRepoImpl) FindByHytNo(ctx context.Context, hytNo string) (*do.OrderAfterSaleRelDo, error) {
	row, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Query().
		Where(orderaftersalerel.HytNoEQ(hytNo)).
		Where(orderaftersalerel.TypeEQ(valobj.AfterSaleRelTypeHyt)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntity(row), nil
}

func (oashrri *OrderAfterSaleRelRepoImpl) FindByAfterSaleId(ctx context.Context, afterSaleId int) (*do.OrderAfterSaleRelDo, error) {
	row, err := oashrri.data.GetDb(ctx).OrderAfterSaleRel.Query().
		Where(orderaftersalerel.AfterSaleIDEQ(afterSaleId)).
		Where(orderaftersalerel.TypeEQ(valobj.AfterSaleRelTypeHyt)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return oashrri.ToEntity(row), nil
}
