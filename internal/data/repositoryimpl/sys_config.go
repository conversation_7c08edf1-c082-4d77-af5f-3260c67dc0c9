//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/sysconfig"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/redis/go-redis/v9"
)

type SysConfigRepoImpl struct {
	Base[ent2.SysConfig, do.SysConfigDo, ent2.SysConfigQuery]
	data *data.Data
}

// NewSysConfigRepoImpl 创建 SysConfigRepo的实现者
func NewSysConfigRepoImpl(data *data.Data) repository.SysConfigRepo {
	return &SysConfigRepoImpl{data: data}
}

// ToEntity 转换成实体
func (scri *SysConfigRepoImpl) ToEntity(po *ent2.SysConfig) *do.SysConfigDo {
	if po == nil {
		return nil
	}
	entity := scri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (scri *SysConfigRepoImpl) ToEntities(pos []*ent2.SysConfig) []*do.SysConfigDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.SysConfigDo, len(pos))
	for k, p := range pos {
		entities[k] = scri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (scri *SysConfigRepoImpl) Get(ctx context.Context, id int) (*do.SysConfigDo, error) {
	row, err := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return scri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (scri *SysConfigRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.SysConfigDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return scri.ToEntities(rows), nil
}

// FindByConfigKey 通过configKey 获取多条数据
func (scri *SysConfigRepoImpl) FindByConfigKey(ctx context.Context, configKey string) (*do.SysConfigDo, error) {
	d, _ := scri.getCache(ctx, configKey)
	if len(d) > 0 {
		return d[0], nil
	}
	row := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.ConfigKeyEQ(configKey)).FirstX(ctx)
	res := scri.ToEntity(row)
	return res, nil
}

// FindByConfigKeys 通过多个 configKey 获取多条数据
func (scri *SysConfigRepoImpl) FindByConfigKeys(ctx context.Context, configKeys ...string) ([]*do.SysConfigDo, error) {
	if len(configKeys) == 0 {
		return nil, nil
	}
	d, _ := scri.getCache(ctx, configKeys...)
	if len(d) > 0 {
		return d, nil
	}
	rows, err := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.ConfigKeyIn(configKeys...)).All(ctx)
	if err != nil {
		return nil, err
	}
	res := scri.ToEntities(rows)
	return res, nil
}

// Create 创建数据
func (scri *SysConfigRepoImpl) Create(ctx context.Context, d *do.SysConfigDo) (*do.SysConfigDo, error) {
	row, err := scri.data.GetDb(ctx).SysConfig.Create().
		SetCreateUserID(d.CreateUserID).
		SetCreateUserName(d.CreateUserName).
		SetUpdateUserID(d.UpdateUserID).
		SetUpdateUserName(d.UpdateUserName).
		SetConfigKey(d.ConfigKey).
		SetConfigValue(d.ConfigValue).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetCustomerID(d.CustomerId).
		SetShopID(d.ShopId).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	_ = scri.deleteCache(isolationcustomer.WithCustomerAndShopCtx(ctx, d.CustomerId, d.ShopId))
	return scri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (scri *SysConfigRepoImpl) CreateBulk(ctx context.Context, dos []*do.SysConfigDo) ([]*do.SysConfigDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.SysConfigCreate, len(dos))
	for i, d := range dos {
		values[i] = scri.data.GetDb(ctx).SysConfig.Create().
			SetCreateUserID(d.CreateUserID).
			SetCreateUserName(d.CreateUserName).
			SetUpdateUserID(d.UpdateUserID).
			SetUpdateUserName(d.UpdateUserName).
			SetConfigKey(d.ConfigKey).
			SetConfigValue(d.ConfigValue).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime).
			SetCustomerID(d.CustomerId).
			SetShopID(d.ShopId)
	}
	rows, err := scri.data.GetDb(ctx).SysConfig.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	for _, configDo := range dos {
		_ = scri.deleteCache(isolationcustomer.WithCustomerAndShopCtx(ctx, configDo.CustomerId, configDo.ShopId))
	}
	return scri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (scri *SysConfigRepoImpl) Update(ctx context.Context, d *do.SysConfigDo) (int, error) {
	return scri.data.GetDb(ctx).SysConfig.Update().Where(sysconfig.ID(d.ID)).
		SetCreateUserID(d.CreateUserID).
		SetCreateUserName(d.CreateUserName).
		SetUpdateUserID(d.UpdateUserID).
		SetUpdateUserName(d.UpdateUserName).
		SetConfigKey(d.ConfigKey).
		SetConfigValue(d.ConfigValue).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SaveX(ctx), scri.deleteCache(isolationcustomer.WithCustomerAndShopCtx(ctx, d.CustomerId, d.ShopId))
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (scri *SysConfigRepoImpl) UpdateV2(ctx context.Context, d *do.SysConfigDo) (int, error) {
	cnt, err := scri.data.GetDb(ctx).SysConfig.Update().Where(sysconfig.ID(d.ID)).
		SetCreateUserID(d.CreateUserID).
		SetCreateUserName(d.CreateUserName).
		SetUpdateUserID(d.UpdateUserID).
		SetUpdateUserName(d.UpdateUserName).
		SetConfigKey(d.ConfigKey).
		SetConfigValue(d.ConfigValue).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	if err != nil {
		return cnt, err
	}
	err = scri.deleteCache(isolationcustomer.WithCustomerAndShopCtx(ctx, d.CustomerId, d.ShopId))
	return cnt, err
}

// Upsert 更新插入
func (scri *SysConfigRepoImpl) Upsert(ctx context.Context, d *do.SysConfigDo) error {
	err := scri.data.GetDb(ctx).SysConfig.Create().
		SetCreateUserID(d.CreateUserID).
		SetCreateUserName(d.CreateUserName).
		SetUpdateUserID(d.UpdateUserID).
		SetUpdateUserName(d.UpdateUserName).
		SetConfigKey(d.ConfigKey).
		SetConfigValue(d.ConfigValue).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		OnConflict().Update(func(u *ent2.SysConfigUpsert) {
		u.SetUpdateTime(d.UpdateTime)
		u.SetUpdateUserID(d.UpdateUserID)
		u.SetUpdateUserName(d.UpdateUserName)
		u.SetConfigValue(d.ConfigValue)
	}).
		Exec(ctx)
	if err != nil {
		return err
	}
	return scri.deleteCache(isolationcustomer.WithCustomerAndShopCtx(ctx, d.CustomerId, d.ShopId))
}

// Delete 删除数据
func (scri *SysConfigRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := scri.data.GetDb(ctx).SysConfig.Delete().Where(sysconfig.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := scri.data.GetDb(ctx).SysConfig.Update().
	// 	Where(sysconfig.IDIn(ids...), sysconfig.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (scri *SysConfigRepoImpl) SearchList(ctx context.Context, reqBo *bo.SysConfigSearchBo) (dos []*do.SysConfigDo, respPage *bo.RespPageBo) {
	q := scri.data.GetDb(ctx).SysConfig.Query()
	if reqBo.Page != nil {
		scri.SetPageByBo(q, reqBo.Page)
		respPage = scri.QueryRespPage(ctx, q, reqBo.Page)
	}

	pos := q.AllX(ctx)
	dos = scri.ToEntities(pos)
	return
}

func (scri *SysConfigRepoImpl) GetByShopId(ctx context.Context, ShopId int, keys ...string) ([]*do.SysConfigDo, error) {
	cacheCtx := isolationcustomer.WithShopIdCtx(ctx, &ShopId)
	res, _ := scri.getCache(cacheCtx, keys...)
	if len(res) > 0 {
		return res, nil
	}
	d := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.ShopID(ShopId)).AllX(ctx)
	res = scri.ToEntities(d)

	_ = scri.setCache(cacheCtx, res)
	return res, nil
}

func (scri *SysConfigRepoImpl) GetByShopIdByKeys(ctx context.Context, ShopId int, keys ...string) ([]*do.SysConfigDo, error) {
	q := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.ShopID(ShopId))
	if len(keys) > 0 {
		q.Where(sysconfig.ConfigKeyIn(keys...))
	}
	d := q.AllX(ctx)
	res := scri.ToEntities(d)
	return res, nil
}

func (scri *SysConfigRepoImpl) setCache(ctx context.Context, d []*do.SysConfigDo) error {
	cacheData := make(map[string]string, 0)
	for _, v := range d {
		cacheData[v.ConfigKey] = v.ToJson()
	}
	err := scri.data.Rdb.HMSet(ctx, constants.ShopSysConfigCachePrefixTpl.GetKey(ctx), cacheData).Err()
	if err != nil && err != redis.Nil {
		return err
	}
	scri.data.Rdb.Expire(ctx, constants.ShopSysConfigCachePrefixTpl.GetKey(ctx), constants.ShopSysConfigCachePrefixTpl.GetTTL())
	return nil
}

func (scri *SysConfigRepoImpl) getCache(ctx context.Context, configKey ...string) ([]*do.SysConfigDo, error) {
	res := make([]*do.SysConfigDo, 0)
	if len(configKey) == 0 {
		var cacheData map[string]string
		var err error
		cacheData, err = scri.data.Rdb.HGetAll(ctx, constants.ShopSysConfigCachePrefixTpl.GetKey(ctx)).Result()
		if err != nil && err != redis.Nil {
			return nil, err
		}
		for _, jsonStr := range cacheData {
			d := &do.SysConfigDo{}
			err = json.Unmarshal([]byte(jsonStr), &d)
			if err != nil {
				return nil, err
			}
			res = append(res, d)
		}
		return res, nil
	} else {
		var cacheData []interface{}
		var err error
		cacheData, err = scri.data.Rdb.HMGet(ctx, constants.ShopSysConfigCachePrefixTpl.GetKey(ctx), configKey...).Result()
		if err != nil && err != redis.Nil {
			return nil, err
		}

		for _, v := range cacheData {
			jsonStr, success := v.(string)
			if !success {
				return nil, fmt.Errorf("断言失败")
			}
			d := &do.SysConfigDo{}
			err = json.Unmarshal([]byte(jsonStr), &d)
			if err != nil {
				return nil, err
			}
			res = append(res, d)
		}
		return res, nil
	}
}

func (scri *SysConfigRepoImpl) deleteCache(ctx context.Context, configKey ...string) error {
	if len(configKey) == 0 {
		err := scri.data.Rdb.Del(ctx, constants.ShopSysConfigCachePrefixTpl.GetKey(ctx)).Err()
		if err != nil && err != redis.Nil {
			return err
		}
	} else {
		err := scri.data.Rdb.HDel(ctx, constants.ShopSysConfigCachePrefixTpl.GetKey(ctx), configKey...).Err()
		if err != nil && err != redis.Nil {
			return err
		}
	}
	return nil
}

func (scri *SysConfigRepoImpl) GetByShopIdsByKeys(ctx context.Context, ShopId []int, keys ...string) ([]*do.SysConfigDo, error) {
	q := scri.data.GetDb(ctx).SysConfig.Query().Where(sysconfig.ShopIDIn(ShopId...))
	if len(keys) > 0 {
		q.Where(sysconfig.ConfigKeyIn(keys...))
	}
	d := q.AllX(ctx)
	res := scri.ToEntities(d)
	return res, nil
}
