//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	ent "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/user"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"time"
)

var userRepoImpl = &UserRepoImpl{}

type UserRepoImpl struct {
	Base[ent.User, do.UserDo, ent.UserQuery]
	data *data.Data
}

// NewUserRepoImpl 创建 UserRepo的实现者
func NewUserRepoImpl(data *data.Data) repository.UserRepo {
	return &UserRepoImpl{data: data}
}

// ToEntity 转换成实体
func (uri *UserRepoImpl) ToEntity(po *ent.User) *do.UserDo {
	if po == nil {
		return nil
	}
	entity := uri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (uri *UserRepoImpl) ToEntities(pos []*ent.User) []*do.UserDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.UserDo, len(pos))
	for k, p := range pos {
		entities[k] = uri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (uri *UserRepoImpl) Get(ctx context.Context, id int) (*do.UserDo, error) {
	row, err := uri.data.GetDb(ctx).User.Query().Where(user.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return uri.ToEntity(row), nil
}

// SearchList 搜索列表
func (uri *UserRepoImpl) SearchList(ctx context.Context, reqBo *bo.UserSearchBo) (dos []*do.UserDo, respPage *bo.RespPageBo) {
	q := uri.data.GetDb(ctx).User.Query()
	if reqBo.Id > 0 {
		q.Where(user.IDEQ(reqBo.Id))
	}

	if reqBo.PhoneNumber != "" {
		q.Where(user.PhoneNumber(reqBo.PhoneNumber))
	}
	if reqBo.ShopID != 0 {
		q.Where(user.ShopIDEQ(reqBo.ShopID))
	}
	q.Order(ent.Desc(user.FieldID))
	if reqBo.Page != nil {
		uri.SetPageByBo(q, reqBo.Page)
		respPage = uri.QueryRespPage(ctx, q, reqBo.Page)
	}
	pos := q.AllX(ctx)
	dos = uri.ToEntities(pos)
	return
}

// ShopCount 统计商城用户数量
func (uri *UserRepoImpl) ShopCount(ctx context.Context, shopId ...int) ([]*do.ShopUserCountDo, error) {
	res := make([]*do.ShopUserCountDo, 0)
	uri.data.GetDb(ctx).User.Query().
		Where(user.ShopIDIn(shopId...)).
		GroupBy(user.FieldShopID).
		Aggregate(ent.Count()).
		ScanX(ctx, &res)
	return res, nil
}

// UpsertByUnionLogin 创建或更新联合登录用户
func (uri *UserRepoImpl) UpsertByUnionLogin(ctx context.Context, req *bo.RegisterUserUnionLoginBo) error {
	now := int(time.Now().Unix())
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	shopId := isolationcustomer.GetShopIdZero(ctx)
	err := uri.data.GetDb(ctx).User.Create().
		SetPhoneNumber(req.Mobile).
		SetNickName(req.NickName).
		SetCustomerID(customerId).
		SetShopID(shopId).
		SetNickName(req.NickName).
		SetAvatarURL(req.Avatar).
		SetUnionUserID(req.UnionUserId).
		SetCreateTime(now).
		SetUpdateTime(now).
		OnConflict().
		UpdatePhoneNumber().UpdateNickName().UpdateAvatarURL().SetUpdateTime(now).
		Exec(ctx)
	return err
}

func (uri *UserRepoImpl) GetByUnionId(ctx context.Context, unionUserId string) *do.UserDo {
	po := uri.data.GetDb(ctx).User.Query().Where(user.UnionUserIDEQ(unionUserId)).FirstX(ctx)
	return uri.ToEntity(po)
}
