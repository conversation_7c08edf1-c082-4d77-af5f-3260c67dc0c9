//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/saasshopapplet"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"errors"
)

type SaasShopAppletRepoImpl struct {
	Base[ent2.SaasShopApplet, do.SaasShopAppletDo, ent2.SaasShopAppletQuery]
	data *data.Data
}

// NewSaasShopAppletRepoImpl 创建 SaasShopAppletRepo的实现者
func NewSaasShopAppletRepoImpl(data *data.Data) repository.SaasShopAppletRepo {
	return &SaasShopAppletRepoImpl{data: data}
}

func (ssari *SaasShopAppletRepoImpl) getCtx(ctx context.Context) context.Context {
	ctx = isolationcustomer.WithSassDbCtx(ctx)
	return ctx
}

// ToEntity 转换成实体
func (ssari *SaasShopAppletRepoImpl) ToEntity(po *ent2.SaasShopApplet) *do.SaasShopAppletDo {
	if po == nil {
		return nil
	}
	entity := ssari.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (ssari *SaasShopAppletRepoImpl) ToEntities(pos []*ent2.SaasShopApplet) []*do.SaasShopAppletDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.SaasShopAppletDo, len(pos))
	for k, p := range pos {
		entities[k] = ssari.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (ssari *SaasShopAppletRepoImpl) Get(ctx context.Context, id int) (*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	row, err := ssari.data.GetDb(ctx).SaasShopApplet.Query().Where(saasshopapplet.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return ssari.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (ssari *SaasShopAppletRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := ssari.data.GetDb(ctx).SaasShopApplet.Query().Where(saasshopapplet.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return ssari.ToEntities(rows), nil
}

// Create 创建数据
func (ssari *SaasShopAppletRepoImpl) Create(ctx context.Context, d *do.SaasShopAppletDo) (*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	row, err := ssari.data.GetDb(ctx).SaasShopApplet.Create().
		SetCustomerID(d.CustomerID).
		SetShopID(d.ShopID).
		SetAppID(d.AppID).
		SetAppType(d.AppType).
		SetAppSecret(d.AppSecret).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return ssari.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (ssari *SaasShopAppletRepoImpl) CreateBulk(ctx context.Context, dos []*do.SaasShopAppletDo) ([]*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.SaasShopAppletCreate, len(dos))
	for i, d := range dos {
		values[i] = ssari.data.GetDb(ctx).SaasShopApplet.Create().
			SetShopID(d.ShopID).
			SetAppID(d.AppID).
			SetAppType(d.AppType).
			SetAppSecret(d.AppSecret).
			SetCustomerID(d.CustomerID)
	}
	rows, err := ssari.data.GetDb(ctx).SaasShopApplet.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return ssari.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (ssari *SaasShopAppletRepoImpl) Update(ctx context.Context, d *do.SaasShopAppletDo) (int, error) {
	ctx = ssari.getCtx(ctx)
	return ssari.data.GetDb(ctx).SaasShopApplet.Update().Where(saasshopapplet.ID(d.ID)).
		SetAppID(d.AppID).
		SetAppType(d.AppType).
		SetAppSecret(d.AppSecret).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (ssari *SaasShopAppletRepoImpl) UpdateV2(ctx context.Context, d *do.SaasShopAppletDo) (int, error) {
	ctx = ssari.getCtx(ctx)
	cnt, err := ssari.data.GetDb(ctx).SaasShopApplet.Update().Where(saasshopapplet.ID(d.ID)).
		SetAppID(d.AppID).
		SetAppType(d.AppType).
		SetAppSecret(d.AppSecret).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (ssari *SaasShopAppletRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	ctx = ssari.getCtx(ctx)
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := ssari.data.GetDb(ctx).SaasShopApplet.Delete().Where(saasshopapplet.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := ssari.data.GetDb(ctx).SaasShopApplet.Update().
	// 	Where(saasshopapplet.IDIn(ids...), saasshopapplet.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (ssari *SaasShopAppletRepoImpl) SearchList(ctx context.Context, reqBo *bo.SaasShopAppletSearchBo) (dos []*do.SaasShopAppletDo, respPage *bo.RespPageBo) {
	ctx = ssari.getCtx(ctx)
	q := ssari.data.GetDb(ctx).SaasShopApplet.Query()
	if reqBo.Page != nil {
		ssari.SetPageByBo(q, reqBo.Page)
		respPage = ssari.QueryRespPage(ctx, q, reqBo.Page)
	}

	pos := q.AllX(ctx)
	dos = ssari.ToEntities(pos)
	return
}

func (ssari *SaasShopAppletRepoImpl) FindByAppId(ctx context.Context, appId string) (*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	d := ssari.data.GetDb(ctx).SaasShopApplet.Query().Where(saasshopapplet.AppID(appId)).FirstX(ctx)
	return ssari.ToEntity(d), nil
}

func (ssari *SaasShopAppletRepoImpl) FindByShopId(ctx context.Context, customerId, shopId int) (*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	d := ssari.data.GetDb(ctx).SaasShopApplet.Query().Where(saasshopapplet.CustomerIDEQ(customerId)).Where(saasshopapplet.ShopIDEQ(shopId)).FirstX(ctx)
	return ssari.ToEntity(d), nil
}

func (ssari *SaasShopAppletRepoImpl) GetByShopId(ctx context.Context, customerId int, shopId ...int) ([]*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	d := ssari.data.GetDb(ctx).SaasShopApplet.Query().Where(saasshopapplet.CustomerIDEQ(customerId)).Where(saasshopapplet.ShopIDIn(shopId...)).AllX(ctx)
	return ssari.ToEntities(d), nil
}

func (ssari *SaasShopAppletRepoImpl) FindByType(ctx context.Context, customerId int, shopId int, appletType valobj.SaasShopAppletTypeObj) (*do.SaasShopAppletDo, error) {
	ctx = ssari.getCtx(ctx)
	d := ssari.data.GetDb(ctx).SaasShopApplet.Query().
		Where(saasshopapplet.CustomerIDEQ(customerId)).
		Where(saasshopapplet.ShopIDEQ(shopId)).
		Where(saasshopapplet.AppTypeEQ(appletType)).
		FirstX(ctx)
	return ssari.ToEntity(d), nil
}

func (ssari *SaasShopAppletRepoImpl) ExistsCheckByAppId(ctx context.Context, appId string, customerId int) (bool, error) {
	ctx = ssari.getCtx(ctx)
	query := ssari.data.GetDb(ctx).SaasShopApplet.Query().
		Where(saasshopapplet.AppIDEQ(appId))
	if customerId > 0 {
		query.Where(saasshopapplet.CustomerIDNEQ(customerId))
	}
	return query.ExistX(ctx), nil
}
