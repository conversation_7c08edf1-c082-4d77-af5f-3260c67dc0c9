package repositoryimpl

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/sysrolemenu"
	"context"
)

type SysRoleMenuRepoImpl struct {
	Base[ent.SysRoleMenu, do.SysRoleMenuDo, ent.SysRoleMenuQuery]
	data *data.Data
}

func NewSysRoleMenuRepoImpl(data *data.Data) repository.SysRoleMenuRepo {
	return &SysRoleMenuRepoImpl{
		data: data,
	}
}

func (g *SysRoleMenuRepoImpl) SysRoleMenuListByRoleId(ctx context.Context, roleId int) ([]*do.SysRoleMenuDo, error) {
	sql := g.data.GetDb(ctx).SysRoleMenu.Query().Where(sysrolemenu.RoleIDEQ(roleId))
	res := sql.AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *SysRoleMenuRepoImpl) AddSysRoleMenu(ctx context.Context, sysRoles []*ent.SysRoleMenu) (int, error) {

	values := make([]*ent.SysRoleMenuCreate, len(sysRoles))
	for i, d := range sysRoles {
		values[i] = g.data.GetDb(ctx).SysRoleMenu.Create().
			SetMenuID(d.MenuID).
			SetRoleID(d.RoleID)

	}

	save, err := g.data.GetDb(ctx).SysRoleMenu.CreateBulk(values...).Save(ctx)
	if err != nil {
		return 0, err
	}
	return len(save), nil
}

func (g *SysRoleMenuRepoImpl) DeleteSysRoleByRoleIds(ctx context.Context, roleIds []int) (int, error) {
	return g.data.GetDb(ctx).SysRoleMenu.Delete().Where(sysrolemenu.RoleIDIn(roleIds...)).Exec(ctx)
}

func (g *SysRoleMenuRepoImpl) DeleteSysRoleByMenuIds(ctx context.Context, menuIds []int) (int, error) {
	return g.data.GetDb(ctx).SysRoleMenu.Delete().Where(sysrolemenu.MenuIDIn(menuIds...)).Exec(ctx)
}
