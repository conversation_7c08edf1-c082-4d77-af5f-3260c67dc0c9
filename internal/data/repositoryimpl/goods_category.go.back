package repositoryimpl

import (
	"cardMall/internal/pkg/helper"
	"context"
	"time"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodscategory"
	"cardMall/internal/data/ent/predicate"
	"cardMall/internal/pkg/isolationcustomer"
	"cardMall/internal/server/middleware/authtools"
	"entgo.io/ent/dialect/sql"
)

var goodsCategoryRepoImpl = &GoodsCategoryRepoImpl{}

func NewGoodsCategoryRepoImpl(data *data.Data) repository.GoodsCategoryRepo {
	return &GoodsCategoryRepoImpl{data: data}
}

type GoodsCategoryRepoImpl struct {
	Base[ent.GoodsCategory, do.GoodsCategoryDo, ent.GoodsCategoryQuery]
	data *data.Data
}

func (g *GoodsCategoryRepoImpl) FindByNamesByLoginInfo(ctx context.Context, names []string) []*do.GoodsCategoryDo {
	adminLoginInfoBo := authtools.GetLoginInfoX(ctx).ToLoginInfo()
	row := g.data.GetDb(ctx).GoodsCategory.Query().
		Where(goodscategory.NameIn(names...)).
		Where(goodscategory.CustomerID(adminLoginInfoBo.CustomerId)).
		Where(goodscategory.ShopID(adminLoginInfoBo.ShopId)).
		WithChildren(func(query *ent2.GoodsCategoryQuery) {
			query.WithChildren(func(q *ent2.GoodsCategoryQuery) {
				q.WithChildren()
			})
		}).
		AllX(ctx)
	return g.ToEntities(row)
}

// ToEntity 转换成实体
func (g *GoodsCategoryRepoImpl) ToEntity(po *ent2.GoodsCategory) *do.GoodsCategoryDo {
	if po == nil {
		return nil
	}
	entity := g.Base.ToEntity(po)
	entity.Children = goodsCategoryRepoImpl.ToEntities(po.Edges.Children)
	entity.Parent = goodsCategoryRepoImpl.ToEntity(po.Edges.Parent)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (g *GoodsCategoryRepoImpl) ToEntities(pos []*ent2.GoodsCategory) []*do.GoodsCategoryDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.GoodsCategoryDo, len(pos))
	for k, p := range pos {
		entities[k] = g.ToEntity(p)
	}
	return entities
}

// Find 通过多个 id 获取多条数据
func (cbri *GoodsCategoryRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.GoodsCategoryDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := cbri.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return cbri.ToEntities(rows), nil
}

func (g *GoodsCategoryRepoImpl) Add(ctx context.Context, in *bo.GoodsCategoryAddBo) (*do.GoodsCategoryAddDo, error) {
	now := int(time.Now().Unix())
	q := g.data.GetDb(ctx).GoodsCategory.Create().
		SetName(in.Name).
		SetCreateTime(now).
		SetUpdateTime(now).
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetPid(in.Pid).
		SetLevel(in.Level).
		SetType(in.Type).
		SetImage(in.Image).
		SetRecommend(in.Recommend).
		SetCustomerID(isolationcustomer.GetCustomerIdZero(ctx)).
		SetShopID(isolationcustomer.GetShopIdZero(ctx))
	if in.IndexShow.Exists() {
		q.SetIndexShow(in.IndexShow)
	}
	res := q.SaveX(ctx)
	return &do.GoodsCategoryAddDo{Id: res.ID}, nil
}

func (g *GoodsCategoryRepoImpl) Query(ctx context.Context, in *bo.GoodsCategoryQueryBo) ([]*do.GoodsCategoryDo, error) {
	query := g.data.GetDb(ctx).GoodsCategory.Query()
	if in.Name != "" {
		query.Where(goodscategory.NameContains(in.Name))
	}
	if in.Pid > 0 {
		query.Where(goodscategory.PidEQ(in.Pid))
	}
	if in.Id > 0 {
		query.Where(goodscategory.IDEQ(in.Id))
	}
	if in.Type.IsValid() {
		query.Where(goodscategory.TypeEQ(in.Type))
	}
	if status := in.Status; status != nil && (*status).IsValid() {
		query.Where(goodscategory.StatusEQ(in.Status.GetInt()))
	}
	if in.Level > 0 {
		query.Where(goodscategory.LevelEQ(in.Level))
	}
	query.Order(ent.Desc(goodscategory.FieldSort))

	res := query.AllX(ctx)
	return g.ToEntities(res), nil
}

func (g *GoodsCategoryRepoImpl) Update(ctx context.Context, in *bo.GoodsCategoryUpdateBo) (int, error) {
	sql := g.data.GetDb(ctx).GoodsCategory.Update().
		Where(goodscategory.IDEQ(in.Id))
	if in.Name != "" {
		sql.SetName(in.Name)
	}

	if in.Status != nil {
		sql.SetStatus(*in.Status)
	}

	if in.Sort != nil {
		sql.SetSort(*in.Sort)
	}

	if in.Image != "" {
		sql.SetImage(in.Image)
	}

	if in.Recommend.IsValid() {
		sql.SetRecommend(in.Recommend)
	}
	if in.IndexShow.Exists() {
		sql.SetIndexShow(in.IndexShow)
	}

	sql.SetUpdateTime(int(time.Now().Unix()))

	return sql.SaveX(ctx), nil
}

// Del 通过ID删除
func (g *GoodsCategoryRepoImpl) Del(ctx context.Context, id int) (int, error) {
	return g.data.GetDb(ctx).GoodsCategory.Delete().Where(goodscategory.IDEQ(id)).ExecX(ctx), nil
}

func (g *GoodsCategoryRepoImpl) FindById(ctx context.Context, id int) (*do.GoodsCategoryDo, error) {
	return g.ToEntity(g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.IDEQ(id)).FirstX(ctx)), nil
}
func (g *GoodsCategoryRepoImpl) FindByIds(ctx context.Context, id []int) ([]*do.GoodsCategoryDo, error) {
	return g.ToEntities(g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.IDIn(id...)).AllX(ctx)), nil
}
func (g *GoodsCategoryRepoImpl) All(ctx context.Context) ([]*do.GoodsCategoryDo, error) {
	return g.ToEntities(g.data.GetDb(ctx).GoodsCategory.Query().AllX(ctx)), nil
}
func (g *GoodsCategoryRepoImpl) FindByIdsNotIn(ctx context.Context, id []int) ([]*do.GoodsCategoryDo, error) {
	return g.ToEntities(g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.IDNotIn(id...)).AllX(ctx)), nil
}

func (g *GoodsCategoryRepoImpl) FindByPid(ctx context.Context, pid int) ([]*do.GoodsCategoryDo, error) {
	return g.ToEntities(g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.PidEQ(pid)).AllX(ctx)), nil
}

func (g *GoodsCategoryRepoImpl) AddGoodsNum(ctx context.Context, in *bo.GoodsCategoryAddGoodsNumBo) (int, error) {
	return g.data.GetDb(ctx).GoodsCategory.Update().
		Where(goodscategory.IDEQ(in.Id)).
		Where(goodscategory.LevelEQ(constants.GoodsCategoryLevelMax)).
		AddGoodsNum(in.Num).
		SaveX(ctx), nil
}

// CategoryTree 获取分类树
func (g *GoodsCategoryRepoImpl) CategoryTree(ctx context.Context, typeSlice []valobj.GoodsCategoryTypeObj) ([]*do.GoodsCategoryDo, error) {
	cond := make([]predicate.GoodsCategory, 0)
	//cond = append(cond, goodscategory.StatusEQ(valobj.GoodsCategoryStatusEnable.GetInt()))
	if len(typeSlice) > 0 {
		cond = append(cond, goodscategory.TypeIn(typeSlice...))
	}
	query := g.data.GetDb(ctx).GoodsCategory.Query().
		Where(goodscategory.PidEQ(0)).
		Where(cond...).
		WithChildren(func(level2 *ent.GoodsCategoryQuery) {
			level2.Where(cond...).
				WithChildren(func(level3 *ent.GoodsCategoryQuery) {
					level3.Where(cond...)
				})
		})
	rows, err := query.All(ctx)
	if err != nil {
		return nil, err
	}
	return g.ToEntities(rows), nil
}

// GetCategoryWithParent  通过ID获取分类
func (g *GoodsCategoryRepoImpl) GetCategoryWithParent(ctx context.Context, id int) (*do.GoodsCategoryDo, error) {
	row, err := g.data.GetDb(ctx).GoodsCategory.Query().
		Where(goodscategory.IDEQ(id)).
		WithParent(func(level2 *ent2.GoodsCategoryQuery) {
			level2.WithParent(func(level3 *ent2.GoodsCategoryQuery) {
				level3.WithParent()
			})
		}).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return g.ToEntity(row), err
}

// UpdateCategoryOnMigrate 目前只在数据迁移时用到
func (g *GoodsCategoryRepoImpl) UpdateCategoryOnMigrate(ctx context.Context, id, pid, level int) (int, error) {
	return g.data.GetDb(ctx).GoodsCategory.Update().Where(goodscategory.IDEQ(id)).SetPid(pid).SetLevel(level).SaveX(ctx), nil
}

func (g *GoodsCategoryRepoImpl) FindByName(ctx context.Context, name string) (*do.GoodsCategoryDo, error) {
	query := g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.NameEQ(name))
	return g.ToEntity(query.FirstX(ctx)), nil
}

func (g *GoodsCategoryRepoImpl) UpdateStatusByIds(ctx context.Context, ids []int, status valobj.GoodsCategoryStatusObj) (int, error) {
	return g.data.GetDb(ctx).GoodsCategory.Update().Where(goodscategory.IDIn(ids...)).SetStatus(status.GetInt()).SaveX(ctx), nil
}

// NameExist 判断名字是否存在
func (a *GoodsCategoryRepoImpl) NameExist(ctx context.Context, name string, pid int, id int) (bool, error) {
	query := a.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.Name(name)).Where(goodscategory.PidEQ(pid))
	if id != 0 {
		query.Where(goodscategory.IDNEQ(id))
	}
	ctn, err := query.Count(ctx)
	return ctn > 0, err
}
func (g *GoodsCategoryRepoImpl) FindByNameAndLevel(ctx context.Context, name string, level int) (*do.GoodsCategoryDo, error) {
	query := g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.NameEQ(name)).Where(goodscategory.Level(level))
	return g.ToEntity(query.FirstX(ctx)), nil
}

// AllNameExist 判断名字是否存在
func (a *GoodsCategoryRepoImpl) AllNameExist(ctx context.Context, name3 string, name2 string, name1 string) (*do.GoodsCategoryDo, error) {
	query := a.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.Level(3))

	query.Modify(func(s *sql.Selector) {
		t2 := sql.Table(goodscategory.Table).As("t2")
		t3 := sql.Table(goodscategory.Table).As("t3")

		s.Join(t2).On(s.C(goodscategory.FieldPid), t2.C(goodscategory.FieldID)).Join(t3).On(t2.C(goodscategory.FieldPid), t3.C(goodscategory.FieldID))

		s.Where(sql.EQ(s.C(goodscategory.FieldName), name3))
		s.Where(sql.EQ(t2.C(goodscategory.FieldName), name2))
		s.Where(sql.EQ(t3.C(goodscategory.FieldName), name1))
	})
	return a.ToEntity(query.FirstX(ctx)), nil
}

func (g *GoodsCategoryRepoImpl) UpdateName(ctx context.Context, id int, name string) (int, error) {
	ctn, err := g.data.GetDb(ctx).GoodsCategory.Update().
		Where(goodscategory.IDEQ(id)).
		SetName(name).
		SetUpdateTime(helper.GetNow()).
		Save(ctx)
	if err != nil {
		return 0, err
	}
	return ctn, err
}

func (g *GoodsCategoryRepoImpl) FindIds(ctx context.Context, customerId, shopId int) ([]int, error) {
	return g.data.GetDb(ctx).GoodsCategory.Query().
		Where(goodscategory.CustomerIDEQ(customerId), goodscategory.ShopIDEQ(shopId)).
		Select(goodscategory.FieldID).
		Ints(ctx)

}
