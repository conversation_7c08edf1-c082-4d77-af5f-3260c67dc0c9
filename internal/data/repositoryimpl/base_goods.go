//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"context"

	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/basegoods"
	"cardMall/internal/server/middleware/authtools"
)

type BaseGoodsRepoImpl struct {
	Base[ent2.BaseGoods, do.BaseGoodsDo, ent2.BaseGoodsQuery]
	data *data.Data
}

// NewBaseGoodsRepoImpl 创建 BaseGoodsRepo的实现者
func NewBaseGoodsRepoImpl(data *data.Data) repository.BaseGoodsRepo {
	return &BaseGoodsRepoImpl{data: data}
}

// ToEntity 转换成实体
func (bgri *BaseGoodsRepoImpl) ToEntity(po *ent2.BaseGoods) *do.BaseGoodsDo {
	if po == nil {
		return nil
	}
	entity := bgri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (bgri *BaseGoodsRepoImpl) ToEntities(pos []*ent2.BaseGoods) []*do.BaseGoodsDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.BaseGoodsDo, len(pos))
	for k, p := range pos {
		entities[k] = bgri.ToEntity(p)
	}
	return entities
}

// Create 创建数据
func (bgri *BaseGoodsRepoImpl) Create(ctx context.Context, d *do.BaseGoodsDo) (*do.BaseGoodsDo, error) {
	row, err := bgri.data.GetDb(ctx).BaseGoods.Create().
		SetProductID(d.ProductID).
		SetChannelPrice(d.ChannelPrice).
		SetOriginalPrice(d.OriginalPrice).
		SetProductName(d.ProductName).
		SetCreateTime(d.CreateTime).
		SetStatus(d.Status).
		SetType(d.Type).
		SetCardExpireTime(d.CardExpireTime).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return bgri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (bgri *BaseGoodsRepoImpl) CreateBulk(ctx context.Context, dos []*do.BaseGoodsDo) ([]*do.BaseGoodsDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.BaseGoodsCreate, len(dos))
	for i, d := range dos {
		values[i] = bgri.data.GetDb(ctx).BaseGoods.Create().
			SetProductID(d.ProductID).
			SetChannelPrice(d.ChannelPrice).
			SetOriginalPrice(d.OriginalPrice).
			SetProductName(d.ProductName).
			SetCreateTime(d.CreateTime).
			SetStatus(d.Status).
			SetType(d.Type).
			SetCardExpireTime(d.CardExpireTime)
	}
	rows, err := bgri.data.GetDb(ctx).BaseGoods.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return bgri.ToEntities(rows), nil
}

// SearchList 搜索列表
func (bgri *BaseGoodsRepoImpl) SearchList(ctx context.Context, reqBo *bo.BaseGoodsSearchBo) (dos []*do.BaseGoodsDo, respPage *bo.RespPageBo) {
	q := bgri.data.GetDb(ctx).BaseGoods.Query()
	if reqBo.ProductName != "" {
		q.Where(basegoods.ProductNameContains(reqBo.ProductName))
	}
	if reqBo.ProductId != "" {
		q.Where(basegoods.ProductID(reqBo.ProductId))
	}
	if reqBo.Status != nil {
		q.Where(basegoods.StatusEQ(*reqBo.Status))
	}
	q.Where(basegoods.TypeGT(valobj.BaseGoodsTypeUnknown))

	if reqBo.Page != nil {
		bgri.SetPageByBo(q, reqBo.Page)
		respPage = bgri.QueryRespPage(ctx, q, reqBo.Page)
	}

	pos := q.AllX(ctx)
	dos = bgri.ToEntities(pos)
	return
}

// All 搜索全部
func (bgri *BaseGoodsRepoImpl) All(ctx context.Context) (dos []*do.BaseGoodsDo) {
	q := bgri.data.GetDb(ctx).BaseGoods.Query()
	pos := q.AllX(ctx)
	dos = bgri.ToEntities(pos)
	return
}
func (b *BaseGoodsRepoImpl) FindByProductId(ctx context.Context, productId string) *do.BaseGoodsDo {
	res := b.data.GetDb(ctx).BaseGoods.Query().Where(basegoods.ProductID(productId)).FirstX(ctx)
	return b.ToEntity(res)
}

func (bgri *BaseGoodsRepoImpl) FindByNamesByLoginInfo(ctx context.Context, names []string) []*do.BaseGoodsDo {
	adminLoginInfo := authtools.GetLoginInfo(ctx).ToLoginInfo()
	row := bgri.data.GetDb(ctx).BaseGoods.Query().
		Where(basegoods.ProductNameIn(names...)).
		Where(basegoods.CustomerID(adminLoginInfo.CustomerId)).
		AllX(ctx)
	return bgri.ToEntities(row)
}

func (bgri *BaseGoodsRepoImpl) FindByProductIdsByLoginInfo(ctx context.Context, productIds []string) []*do.BaseGoodsDo {
	adminLoginInfo := authtools.GetLoginInfo(ctx).ToLoginInfo()
	row := bgri.data.GetDb(ctx).BaseGoods.Query().
		Where(basegoods.ProductIDIn(productIds...)).
		Where(basegoods.CustomerID(adminLoginInfo.CustomerId)).
		AllX(ctx)
	return bgri.ToEntities(row)
}
