package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	"cardMall/internal/data/ent/payorderlog"
	"cardMall/internal/pkg/helper"
	"context"
)

type PayOrderLogRepoImpl struct {
	Base[ent.PayOrderLog, do.PayOrderLogDo, ent.PayOrderQuery]
	data *data.Data
}

func NewPayOrderLogRepoImpl(data *data.Data) repository.PayOrderLogRepo {
	return &PayOrderLogRepoImpl{
		data: data,
	}
}

func (p *PayOrderLogRepoImpl) Create(ctx context.Context, d *bo.PayOrderLogCreateBo) (*do.PayOrderLogDo, error) {
	now := helper.GetNow()
	res := p.data.GetDb(ctx).PayOrderLog.Create().
		SetOrderNumber(d.OrderNumber).
		SetOutTradeNo(d.OutTradeNo).
		SetPaySerialNumber(d.PaySerialNumber).
		SetPayStatus(valobj.PayOrderLogPayStatusPending).
		SetCreateTime(now).
		SetUpdateTime(now).
		SaveX(ctx)
	return p.ToEntity(res), nil
}

func (p *PayOrderLogRepoImpl) FindBySerialNumber(ctx context.Context, serialNo string) (*do.PayOrderLogDo, error) {
	res := p.data.GetDb(ctx).PayOrderLog.Query().
		Where(payorderlog.PaySerialNumberEQ(serialNo)).
		FirstX(ctx)
	return p.ToEntity(res), nil
}

func (p *PayOrderLogRepoImpl) PaySuccess(ctx context.Context, serialNo string) (int, error) {
	now := helper.GetNow()
	row := p.data.GetDb(ctx).PayOrderLog.
		Update().
		Where(payorderlog.PaySerialNumberEQ(serialNo)).
		SetPayStatus(valobj.PayOrderLogPayStatusSuccess).
		SetUpdateTime(now).
		SaveX(ctx)
	return row, nil
}
