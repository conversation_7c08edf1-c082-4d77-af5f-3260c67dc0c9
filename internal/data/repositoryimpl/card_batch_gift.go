//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/cardbatch"
	"cardMall/internal/data/ent/cardbatchcoupon"
	"cardMall/internal/data/ent/cardbatchgift"
	"cardMall/internal/pkg/helper"
	"context"
	"errors"
	"time"

	"entgo.io/ent/dialect/sql"
)

var cardBatchGiftRepoImpl = &CardBatchGiftRepoImpl{}

type CardBatchGiftRepoImpl struct {
	Base[ent2.CardBatchGift, do.CardBatchGiftDo, ent2.CardBatchGiftQuery]
	data *data.Data
}

// NewCardBatchGiftRepoImpl 创建 CardBatchGiftRepo的实现者
func NewCardBatchGiftRepoImpl(data *data.Data) repository.CardBatchGiftRepo {
	return &CardBatchGiftRepoImpl{data: data}
}

// ToEntity 转换成实体
func (cbgri *CardBatchGiftRepoImpl) ToEntity(po *ent2.CardBatchGift) *do.CardBatchGiftDo {
	if po == nil {
		return nil
	}
	entity := cbgri.Base.ToEntity(po)
	entity.CardBatch = cardBatchRepoImpl.ToEntity(po.Edges.CardBatch)
	entity.CardGiftOperatorLog = cardGiftOperatorLogRepoImpl.ToEntities(po.Edges.CardGiftOperatorLog)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (cbgri *CardBatchGiftRepoImpl) ToEntities(pos []*ent2.CardBatchGift) []*do.CardBatchGiftDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.CardBatchGiftDo, len(pos))
	for k, p := range pos {
		entities[k] = cbgri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (cbgri *CardBatchGiftRepoImpl) Get(ctx context.Context, id int) (*do.CardBatchGiftDo, error) {
	row, err := cbgri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.ID(id)).WithCardBatch().First(ctx)
	if err != nil {
		return nil, err
	}
	return cbgri.ToEntity(row), nil
}

// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
func (cbgri *CardBatchGiftRepoImpl) GetWithEdges(ctx context.Context, id int, reqBo *bo.CardBatchGiftEdgesBo) (*do.CardBatchGiftDo, error) {
	q := cbgri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.ID(id))
	if reqBo.WithCardBatch {
		q.WithCardBatch()
	}
	if reqBo.WithCardGiftOperatorLog {
		q.WithCardGiftOperatorLog()
	}

	row, err := q.First(ctx)
	if err != nil {
		return nil, err
	}
	return cbgri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (cbgri *CardBatchGiftRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.CardBatchGiftDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := cbgri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return cbgri.ToEntities(rows), nil
}

// Create 创建数据
func (cbgri *CardBatchGiftRepoImpl) Create(ctx context.Context, d *do.CardBatchGiftDo) (*do.CardBatchGiftDo, error) {
	row, err := cbgri.data.GetDb(ctx).CardBatchGift.Create().
		SetCardGiftNumber(d.CardGiftNumber).
		SetCardBatchID(d.CardBatchID).
		SetCardBatchNumber(d.CardBatchNumber).
		SetCardBatchName(d.CardBatchName).
		SetExchangeAmount(d.ExchangeAmount).
		SetCardNumberType(d.CardNumberType).
		SetUseExpireStart(d.UseExpireStart).
		SetUseExpireEnd(d.UseExpireEnd).
		SetUserID(d.UserID).
		SetUserName(d.UserName).
		SetBindStatus(d.BindStatus).
		SetBindTime(d.BindTime).
		SetCardGiftBalance(d.CardGiftBalance).
		SetStatus(d.Status).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return cbgri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (cbgri *CardBatchGiftRepoImpl) CreateBulk(ctx context.Context, dos []*do.CardBatchGiftDo) ([]*do.CardBatchGiftDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.CardBatchGiftCreate, len(dos))
	for i, d := range dos {
		values[i] = cbgri.data.GetDb(ctx).CardBatchGift.Create().
			SetCardGiftNumber(d.CardGiftNumber).
			SetCardBatchID(d.CardBatchID).
			SetCardBatchNumber(d.CardBatchNumber).
			SetCardBatchName(d.CardBatchName).
			SetExchangeAmount(d.ExchangeAmount).
			SetCardNumberType(d.CardNumberType).
			SetUseExpireStart(d.UseExpireStart).
			SetUseExpireEnd(d.UseExpireEnd).
			SetUserID(d.UserID).
			SetUserName(d.UserName).
			SetBindStatus(d.BindStatus).
			SetBindTime(d.BindTime).
			SetCardGiftBalance(d.CardGiftBalance).
			SetStatus(d.Status).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime)
	}
	rows, err := cbgri.data.GetDb(ctx).CardBatchGift.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return cbgri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (cbgri *CardBatchGiftRepoImpl) Update(ctx context.Context, d *do.CardBatchGiftDo) (int, error) {
	return cbgri.data.GetDb(ctx).CardBatchGift.Update().Where(cardbatchgift.ID(d.ID)).
		SetCardGiftNumber(d.CardGiftNumber).
		SetCardBatchID(d.CardBatchID).
		SetCardBatchNumber(d.CardBatchNumber).
		SetCardBatchName(d.CardBatchName).
		SetExchangeAmount(d.ExchangeAmount).
		SetCardNumberType(d.CardNumberType).
		SetUseExpireStart(d.UseExpireStart).
		SetUseExpireEnd(d.UseExpireEnd).
		SetUserID(d.UserID).
		SetUserName(d.UserName).
		SetBindStatus(d.BindStatus).
		SetBindTime(d.BindTime).
		SetCardGiftBalance(d.CardGiftBalance).
		SetStatus(d.Status).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (cbgri *CardBatchGiftRepoImpl) UpdateV2(ctx context.Context, d *do.CardBatchGiftDo) (int, error) {
	cnt, err := cbgri.data.GetDb(ctx).CardBatchGift.Update().Where(cardbatchgift.ID(d.ID)).
		SetCardGiftNumber(d.CardGiftNumber).
		SetCardBatchID(d.CardBatchID).
		SetCardBatchNumber(d.CardBatchNumber).
		SetCardBatchName(d.CardBatchName).
		SetExchangeAmount(d.ExchangeAmount).
		SetCardNumberType(d.CardNumberType).
		SetUseExpireStart(d.UseExpireStart).
		SetUseExpireEnd(d.UseExpireEnd).
		SetUserID(d.UserID).
		SetUserName(d.UserName).
		SetBindStatus(d.BindStatus).
		SetBindTime(d.BindTime).
		SetCardGiftBalance(d.CardGiftBalance).
		SetStatus(d.Status).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (cbgri *CardBatchGiftRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := cbgri.data.GetDb(ctx).CardBatchGift.Delete().Where(cardbatchgift.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := cbgri.data.GetDb(ctx).CardBatchGift.Update().
	// 	Where(cardbatchgift.IDIn(ids...), cardbatchgift.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (cbgri *CardBatchGiftRepoImpl) SearchList(ctx context.Context, reqBo *bo.CardBatchGiftSearchBo) (dos []*do.CardBatchGiftDo, respPage *bo.RespPageBo) {
	q := cbgri.data.GetDb(ctx).CardBatchGift.Query()

	if reqBo.CreateTimeStart > 0 {
		q.Where(cardbatchgift.CreateTimeGTE(reqBo.CreateTimeStart))
	}
	if reqBo.CreateTimeEnd > 0 {
		q.Where(cardbatchgift.CreateTimeLTE(reqBo.CreateTimeEnd))
	}

	if reqBo.UseExpireStart > 0 {
		q.Where(cardbatchgift.UseExpireEndGTE(reqBo.UseExpireStart))
	}
	if reqBo.UseExpireEnd > 0 {
		q.Where(cardbatchgift.UseExpireEndLTE(reqBo.UseExpireEnd))
	}
	if reqBo.CardGiftNumber != "" {
		q.Where(cardbatchgift.CardGiftNumberEQ(reqBo.CardGiftNumber))
	}
	if reqBo.BindStatus != 0 {
		q.Where(cardbatchgift.BindStatusEQ(reqBo.BindStatus))
	}

	if reqBo.GiftStatus != 0 {
		q.Where(cardbatchgift.StatusEQ(reqBo.GiftStatus))
	}
	if reqBo.CardBatchNumber != "" {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchgift.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.CardBatchNumber != "" {
				s.Where(sql.Contains(cardBatchTable.C(cardbatch.FieldCardBatchNumber), reqBo.CardBatchNumber))
			}
		})
	}
	if reqBo.Status != 0 {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchgift.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.Status != 0 {
				s.Where(sql.EQ(cardBatchTable.C(cardbatch.FieldStatus), reqBo.Status))
			}
		})
	}
	if reqBo.ActivateTimeStart > 0 {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchgift.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.ActivateTimeStart > 0 {
				s.Where(sql.GTE(cardBatchTable.C(cardbatch.FieldActivateTime), reqBo.ActivateTimeStart))
			}
		})
	}
	if reqBo.ActivateTimeEnd > 0 {
		q.Modify(func(s *sql.Selector) {
			cardBatchTable := sql.Table(cardbatch.Table)
			s.Join(cardBatchTable).On(s.C(cardbatchgift.FieldCardBatchID), cardBatchTable.C(cardbatch.FieldID))
			if reqBo.ActivateTimeEnd > 0 {
				s.Where(sql.LTE(cardBatchTable.C(cardbatch.FieldActivateTime), reqBo.ActivateTimeEnd))
			}
		})
	}
	q.Order(ent2.Desc(cardbatch.FieldID))

	if reqBo.Page != nil {
		cbgri.SetPageByBo(q, reqBo.Page)
		respPage = cbgri.QueryRespPage(ctx, q, reqBo.Page)
	}
	if reqBo.Edges != nil {
		if reqBo.Edges.WithCardBatch {
			q.WithCardBatch()
		}
		if reqBo.Edges.WithCardGiftOperatorLog {
			q.WithCardGiftOperatorLog()
		}
	}

	pos := q.AllX(ctx)
	dos = cbgri.ToEntities(pos)
	return
}

func (cbgri *CardBatchGiftRepoImpl) UseAble(ctx context.Context, userId int, id ...string) ([]*do.CardBatchGiftDo, error) {
	now := helper.GetNow()
	query := cbgri.data.GetDb(ctx).CardBatchGift.Query().
		Where(cardbatchgift.UserIDEQ(userId)).
		Where(cardbatchgift.StatusIn(valobj.CardBatchGiftStatusUnUsed, valobj.CardBatchGiftStatusUsed)).
		Where(cardbatchgift.BindStatusEQ(valobj.CardBatchCouponBindStatusYes)).
		Where(cardbatchgift.CardGiftBalanceGT(0)).
		Where(cardbatchgift.UseExpireStartLTE(now)).
		Where(cardbatchgift.UseExpireEndGTE(now))
	if len(id) > 0 {
		query = query.Where(cardbatchgift.CardGiftNumberIn(id...))
	}
	query.Order(ent2.Asc(cardbatchgift.FieldUseExpireEnd))
	res := query.AllX(ctx)
	return cbgri.ToEntities(res), nil
}

// CancelByID 作废卡券
func (cbcri *CardBatchGiftRepoImpl) CancelByID(ctx context.Context, cardBatchGiftIds []int) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchGift.Update().Where(cardbatchgift.IDIn(cardBatchGiftIds...)).
		SetStatus(valobj.CardBatchGiftStatusCancel).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}
func (r *CardBatchGiftRepoImpl) Num(ctx context.Context, in []int) ([]*do.BatchGiftNumResult, error) {
	var (
		wantArgs []any
	)

	t1 := sql.Table(cardbatchgift.Table).As("cbg")

	fieldIn := sql.FieldIn(t1.C(cardbatchgift.FieldID), in...)
	selector := sql.Select(
		t1.C(sql.As(cardbatchcoupon.FieldID, "card_batch_gift_id")),
		"SUM(CASE WHEN (status = 1 or status = 2) AND use_expire_end > UNIX_TIMESTAMP() THEN card_gift_balance ELSE 0 END) as surplus_amount",
		"SUM(CASE WHEN status = 3 THEN card_gift_balance ELSE 0 END) as cancel_amount",
		"SUM(CASE WHEN (status = 1 or status = 2) AND use_expire_end < UNIX_TIMESTAMP() THEN card_gift_balance ELSE 0 END) as expire_amount",
	).From(t1).GroupBy(cardbatchcoupon.FieldID)
	fieldIn(selector)
	query, wantArgs := selector.Query()

	result, err := r.data.GetDb(ctx).QueryContext(ctx, query, wantArgs...)
	if err != nil {
		return nil, err
	}
	numResults := make([]*do.BatchGiftNumResult, 0)
	err = sql.ScanSlice(result, &numResults)
	if err != nil {
		return nil, err
	}
	return numResults, nil
}

// FindByUserId 通过UserId 获取多条数据
func (cbcri *CardBatchGiftRepoImpl) FindByUserId(ctx context.Context, userId int) ([]*do.CardBatchGiftDo, error) {

	rows, err := cbcri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.UserIDEQ(userId)).WithCardBatch().All(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntities(rows), nil
}

// FindByCardGiftNumber 通过 cardGiftNumber 获取一条数据
func (cbcri *CardBatchGiftRepoImpl) FindByCardGiftNumber(ctx context.Context, cardGiftNumber string) (*do.CardBatchGiftDo, error) {
	row, err := cbcri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.CardGiftNumber(cardGiftNumber)).WithCardBatch().First(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntity(row), nil
}
func (cbcri *CardBatchGiftRepoImpl) Exchange(ctx context.Context, userId int, userName string, cardGiftNumber string) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchGift.Update().
		Where(cardbatchgift.CardGiftNumberEQ(cardGiftNumber)).
		Where(cardbatchgift.UserIDEQ(0)).
		SetBindStatus(valobj.CardBatchCouponBindStatusYes).
		SetUserID(userId).
		SetUserName(userName).
		SetUpdateTime(int(time.Now().Unix())).
		SetBindTime(int(time.Now().Unix())).
		Save(ctx)
}

// CancelByCardBatchID 作废卡券
func (cbcri *CardBatchGiftRepoImpl) CancelByCardBatchID(ctx context.Context, cardBatchIds []int) (int, error) {
	return cbcri.data.GetDb(ctx).CardBatchGift.Update().Where(cardbatchgift.CardBatchIDIn(cardBatchIds...)).
		SetStatus(valobj.CardBatchGiftStatusCancel).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}

func (cbcri *CardBatchGiftRepoImpl) GetByCardGiftNumber(ctx context.Context, cardGiftNumber ...string) ([]*do.CardBatchGiftDo, error) {
	rows := cbcri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.CardGiftNumberIn(cardGiftNumber...)).AllX(ctx)
	return cbcri.ToEntities(rows), nil
}

func (cbcri *CardBatchGiftRepoImpl) BalanceChange(ctx context.Context, cardGiftNumber string, amount float64, oldBalance float64) (int, error) {
	q := cbcri.data.GetDb(ctx).CardBatchGift.Update().
		Where(cardbatchgift.CardGiftNumberEQ(cardGiftNumber)).
		Where(func(s *sql.Selector) {
			s.Where(
				sql.ExprP("exchange_amount >= card_gift_balance + ?", amount),
			)
		})
	//退款操作只要余额大于等于当前余额就行
	if amount > 0 {
		q.Where(cardbatchgift.CardGiftBalanceGTE(oldBalance))
	} else {
		//扣款操作，必须要和当前余额相等
		q.Where(cardbatchgift.CardGiftBalanceEQ(oldBalance))
	}
	q.AddCardGiftBalance(amount).SetUpdateTime(int(time.Now().Unix()))
	if amount < 0 {
		q.SetStatus(valobj.CardBatchGiftStatusUsed)
	}
	return q.Save(ctx)
}

// FindByCardBatchNumber 通过cardBatchNumber 获取多条数据
func (cbcri *CardBatchGiftRepoImpl) FindByCardBatchNumber(ctx context.Context, cardBatchNumber string) ([]*do.CardBatchGiftDo, error) {

	rows, err := cbcri.data.GetDb(ctx).CardBatchGift.Query().Where(cardbatchgift.CardBatchNumber(cardBatchNumber)).WithCardBatch().All(ctx)
	if err != nil {
		return nil, err
	}
	return cbcri.ToEntities(rows), nil
}

// FindUseAbleOne 查询用户是否有未过期的卡券
func (cbgri *CardBatchGiftRepoImpl) FindUseAbleOne(ctx context.Context, userId int) (*do.CardBatchGiftDo, error) {
	now := helper.GetNow()
	query := cbgri.data.GetDb(ctx).CardBatchGift.Query().
		Where(cardbatchgift.UserIDEQ(userId)).
		Where(cardbatchgift.StatusIn(valobj.CardBatchGiftStatusUnUsed, valobj.CardBatchGiftStatusUsed)).
		Where(cardbatchgift.BindStatusEQ(valobj.CardBatchCouponBindStatusYes)).
		Where(cardbatchgift.UseExpireStartLTE(now)).
		Where(cardbatchgift.UseExpireEndGTE(now))
	res := query.FirstX(ctx)
	return cbgri.ToEntity(res), nil
}
