//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderoperatorlog"
	"context"
	"time"
)

var orderOperatorLogImpl = &OrderOperatorLogImpl{}

type OrderOperatorLogImpl struct {
	Base[ent2.OrderOperatorLog, do.OrderOperatorLogDo, ent2.OrderOperatorLogQuery]
	data *data.Data
}

// NewOrderOperatorLogImpl
func NewOrderOperatorLogImpl(data *data.Data) repository.OrderOperatorLogRepo {
	return &OrderOperatorLogImpl{data: data}
}

// ToEntity 转换成实体
func (ori *OrderOperatorLogImpl) ToEntity(po *ent2.OrderOperatorLog) *do.OrderOperatorLogDo {
	if po == nil {
		return nil
	}
	entity := ori.Base.ToEntity(po)
	entity.Order = orderRepoImpl.ToEntity(po.Edges.Order)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (ori *OrderOperatorLogImpl) ToEntities(pos []*ent2.OrderOperatorLog) []*do.OrderOperatorLogDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.OrderOperatorLogDo, len(pos))
	for k, p := range pos {
		entities[k] = ori.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (ori *OrderOperatorLogImpl) Get(ctx context.Context, id int) (*do.OrderOperatorLogDo, error) {
	row, err := ori.data.GetDb(ctx).OrderOperatorLog.Query().Where(orderoperatorlog.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return ori.ToEntity(row), nil
}

func (ori *OrderOperatorLogImpl) FindByOrderNumber(ctx context.Context, orderNumber string) ([]*ent2.OrderOperatorLog, error) {
	sql := ori.data.GetDb(ctx).OrderOperatorLog.Query().Where(orderoperatorlog.OrderNumberEQ(orderNumber))
	result := sql.Order(ent2.Desc(orderoperatorlog.FieldCreateTime)).AllX(ctx)
	return result, nil
}
func (ori *OrderOperatorLogImpl) FindByOrderIds(ctx context.Context, orderIds []int) ([]*do.OrderOperatorLogDo, error) {
	sql := ori.data.GetDb(ctx).OrderOperatorLog.Query().Where(orderoperatorlog.OrderIDIn(orderIds...))
	result, err := sql.Order(ent2.Desc(orderoperatorlog.FieldCreateTime)).All(ctx)
	if err != nil {
		return nil, err
	}
	return ori.ToEntities(result), err
}
func (ori *OrderOperatorLogImpl) FindByOrderNumberIn(ctx context.Context, orderNumbers []string) ([]*ent2.OrderOperatorLog, error) {
	sql := ori.data.GetDb(ctx).OrderOperatorLog.Query().Where(orderoperatorlog.OrderNumberIn(orderNumbers...))
	result := sql.Order(ent2.Desc(orderoperatorlog.FieldCreateTime)).AllX(ctx)
	return result, nil
}
func (ori *OrderOperatorLogImpl) Add(ctx context.Context, in *bo.OrderOperatorLogAddBo) (int, error) {
	now := time.Now().Unix()
	return ori.data.GetDb(ctx).OrderOperatorLog.Create().
		SetOrderID(in.OrderId).
		SetOrderNumber(in.OrderNumber).
		SetOrderStatus(int(in.OrderStatus)).
		SetContent(in.Content).
		SetOperatorUserType(int(in.OperatorUserType)).
		SetOperatorUserID(in.OperatorUserId).
		SetOperatorUserName(in.OperatorUserName).
		SetCreateTime(now).
		SetUpdateTime(now).SaveX(ctx).ID, nil
}
