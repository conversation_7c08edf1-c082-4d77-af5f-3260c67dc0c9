//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderlogistics"
	"context"
	"errors"
)

type OrderLogisticsRepoImpl struct {
	Base[ent2.OrderLogistics, do.OrderLogisticsDo, ent2.OrderLogisticsQuery]
	data *data.Data
}

// NewOrderLogisticsRepoImpl 创建 OrderLogisticsRepo的实现者
func NewOrderLogisticsRepoImpl(data *data.Data) repository.OrderLogisticsRepo {
	return &OrderLogisticsRepoImpl{data: data}
}

// ToEntity 转换成实体
func (olri *OrderLogisticsRepoImpl) ToEntity(po *ent2.OrderLogistics) *do.OrderLogisticsDo {
	if po == nil {
		return nil
	}
	entity := olri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (olri *OrderLogisticsRepoImpl) ToEntities(pos []*ent2.OrderLogistics) []*do.OrderLogisticsDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.OrderLogisticsDo, len(pos))
	for k, p := range pos {
		entities[k] = olri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (olri *OrderLogisticsRepoImpl) Get(ctx context.Context, id int) (*do.OrderLogisticsDo, error) {
	row, err := olri.data.GetDb(ctx).OrderLogistics.Query().Where(orderlogistics.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return olri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (olri *OrderLogisticsRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.OrderLogisticsDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := olri.data.GetDb(ctx).OrderLogistics.Query().Where(orderlogistics.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return olri.ToEntities(rows), nil
}

// Create 创建数据
func (olri *OrderLogisticsRepoImpl) Create(ctx context.Context, d *do.OrderLogisticsDo) (*do.OrderLogisticsDo, error) {
	row, err := olri.data.GetDb(ctx).OrderLogistics.Create().
		SetOrderNumber(d.OrderNumber).
		SetKdCode(d.KdCode).
		SetLogisticsNo(d.LogisticsNo).
		SetOpTime(d.OpTime).
		SetOpMessage(d.OpMessage).
		SetOpDesc(d.OpDesc).
		SetOpCode(d.OpCode).
		SetAddressText(d.AddressText).
		SetCreateTime(d.CreateTime).
		SetFrom(d.From).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return olri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (olri *OrderLogisticsRepoImpl) CreateBulk(ctx context.Context, dos []*do.OrderLogisticsDo) ([]*do.OrderLogisticsDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.OrderLogisticsCreate, len(dos))
	for i, d := range dos {
		values[i] = olri.data.GetDb(ctx).OrderLogistics.Create().
			SetOrderNumber(d.OrderNumber).
			SetKdCode(d.KdCode).
			SetLogisticsNo(d.LogisticsNo).
			SetOpTime(d.OpTime).
			SetOpMessage(d.OpMessage).
			SetOpDesc(d.OpDesc).
			SetOpCode(d.OpCode).
			SetAddressText(d.AddressText).
			SetCreateTime(d.CreateTime).
			SetFrom(d.From)
	}
	rows, err := olri.data.GetDb(ctx).OrderLogistics.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return olri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (olri *OrderLogisticsRepoImpl) Update(ctx context.Context, d *do.OrderLogisticsDo) (int, error) {
	return olri.data.GetDb(ctx).OrderLogistics.Update().Where(orderlogistics.ID(d.ID)).
		SetOrderNumber(d.OrderNumber).
		SetKdCode(d.KdCode).
		SetLogisticsNo(d.LogisticsNo).
		SetOpTime(d.OpTime).
		SetOpMessage(d.OpMessage).
		SetOpDesc(d.OpDesc).
		SetOpCode(d.OpCode).
		SetAddressText(d.AddressText).
		SetCreateTime(d.CreateTime).
		SetFrom(d.From).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (olri *OrderLogisticsRepoImpl) UpdateV2(ctx context.Context, d *do.OrderLogisticsDo) (int, error) {
	cnt, err := olri.data.GetDb(ctx).OrderLogistics.Update().Where(orderlogistics.ID(d.ID)).
		SetOrderNumber(d.OrderNumber).
		SetKdCode(d.KdCode).
		SetLogisticsNo(d.LogisticsNo).
		SetOpTime(d.OpTime).
		SetOpMessage(d.OpMessage).
		SetOpDesc(d.OpDesc).
		SetOpCode(d.OpCode).
		SetAddressText(d.AddressText).
		SetCreateTime(d.CreateTime).
		SetFrom(d.From).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (olri *OrderLogisticsRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := olri.data.GetDb(ctx).OrderLogistics.Delete().Where(orderlogistics.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := olri.data.GetDb(ctx).OrderLogistics.Update().
	// 	Where(orderlogistics.IDIn(ids...), orderlogistics.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (olri *OrderLogisticsRepoImpl) SearchList(ctx context.Context, reqBo *bo.OrderLogisticsSearchBo) (dos []*do.OrderLogisticsDo, respPage *bo.RespPageBo) {
	q := olri.data.GetDb(ctx).OrderLogistics.Query()
	if reqBo.Page != nil {
		olri.SetPageByBo(q, reqBo.Page)
		respPage = olri.QueryRespPage(ctx, q, reqBo.Page)
	}
	pos := q.AllX(ctx)
	dos = olri.ToEntities(pos)
	return
}

func (olri *OrderLogisticsRepoImpl) FindLast(ctx context.Context, in *bo.OrderLogisticsFindBo) (*do.OrderLogisticsDo, error) {
	query := olri.data.GetDb(ctx).OrderLogistics.Query().
		Where(orderlogistics.OrderNumber(in.OrderNumber))
	if in.LogisticsNo != "" {
		query.Where(orderlogistics.LogisticsNo(in.LogisticsNo))
	}
	res := query.Order(ent2.Desc(orderlogistics.FieldOpTime)).
		FirstX(ctx)
	return olri.ToEntity(res), nil
}
