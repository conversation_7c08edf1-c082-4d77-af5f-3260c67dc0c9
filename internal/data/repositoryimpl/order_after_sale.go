//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/order"
	"cardMall/internal/data/ent/orderaftersale"
	"cardMall/internal/data/ent/orderaftersalegoods"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"entgo.io/ent/dialect/sql"
	"errors"
	"time"
)

var orderAfterSaleRepoImpl = &OrderAfterSaleRepoImpl{}

type OrderAfterSaleRepoImpl struct {
	Base[ent2.OrderAfterSale, do.OrderAfterSaleDo, ent2.OrderAfterSaleQuery]
	data *data.Data
}

// NewOrderAfterSaleRepoImpl 创建 OrderAfterSaleRepo的实现者
func NewOrderAfterSaleRepoImpl(data *data.Data) repository.OrderAfterSaleRepo {
	return &OrderAfterSaleRepoImpl{data: data}
}

// ToEntity 转换成实体
func (oasri *OrderAfterSaleRepoImpl) ToEntity(po *ent2.OrderAfterSale) *do.OrderAfterSaleDo {
	if po == nil {
		return nil
	}
	entity := oasri.Base.ToEntity(po)
	entity.OrderAfterSaleDeliver = orderAfterSaleDeliverRepoImpl.ToEntity(po.Edges.OrderAfterSaleDeliver)
	entity.OrderAfterSaleGoods = orderAfterSaleGoodsRepoImpl.ToEntities(po.Edges.OrderAfterSaleGoods)
	entity.OrderAfterSaleLog = orderAfterSaleLogRepoImpl.ToEntities(po.Edges.OrderAfterSaleLog)
	entity.OrderAfterSaleDeal = orderAfterSaleDealRepoImpl.ToEntities(po.Edges.OrderAfterSaleDeal)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (oasri *OrderAfterSaleRepoImpl) ToEntities(pos []*ent2.OrderAfterSale) []*do.OrderAfterSaleDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.OrderAfterSaleDo, len(pos))
	for k, p := range pos {
		entities[k] = oasri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (oasri *OrderAfterSaleRepoImpl) Get(ctx context.Context, id int) (*do.OrderAfterSaleDo, error) {
	query := oasri.data.GetDb(ctx).OrderAfterSale.Query().Where(orderaftersale.ID(id))
	query.WithOrderAfterSaleDeliver()
	query.WithOrderAfterSaleGoods()
	query.WithOrderAfterSaleLog()
	row, err := query.First(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntity(row), nil
}

func (oasri *OrderAfterSaleRepoImpl) GetBySaas(ctx context.Context, id int, orderNo string) (*do.OrderAfterSaleDo, error) {
	query := oasri.data.GetDb(ctx).OrderAfterSale.Query().
		Where(orderaftersale.ID(id)).
		Where(orderaftersale.OrderNumberEQ(orderNo))

	row, err := query.First(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntity(row), nil
}

func (oasri *OrderAfterSaleRepoImpl) GetLastByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderAfterSaleDo, error) {
	query := oasri.data.GetDb(ctx).OrderAfterSale.Query().Where(orderaftersale.OrderNumberEQ(orderNumber))
	query.WithOrderAfterSaleDeliver()
	query.WithOrderAfterSaleGoods()
	query.WithOrderAfterSaleLog()
	row, err := query.Order(ent2.Desc(orderaftersale.FieldID)).First(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntity(row), nil
}
func (oasri *OrderAfterSaleRepoImpl) GetByOrderNumber(ctx context.Context, orderNumber string) ([]*do.OrderAfterSaleDo, error) {
	query := oasri.data.GetDb(ctx).OrderAfterSale.Query().Where(orderaftersale.OrderNumberEQ(orderNumber))
	row, err := query.Order(ent2.Desc(orderaftersale.FieldID)).All(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntities(row), nil
}
func (oasri *OrderAfterSaleRepoImpl) GetByOriginalOrderNo(ctx context.Context, originalOrderNo string) ([]*do.OrderAfterSaleDo, error) {
	query := oasri.data.GetDb(ctx).OrderAfterSale.Query().Where(orderaftersale.OriginalOrderNoEQ(originalOrderNo)).WithOrderAfterSaleGoods()
	row, err := query.Order(ent2.Desc(orderaftersale.FieldID)).All(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntities(row), nil
}

// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
func (oasri *OrderAfterSaleRepoImpl) GetWithEdges(ctx context.Context, id int, reqBo *bo.OrderAfterSaleEdgesBo) (*do.OrderAfterSaleDo, error) {
	q := oasri.data.GetDb(ctx).OrderAfterSale.Query().Where(orderaftersale.ID(id))
	if reqBo.WithOrderAfterSaleDeliver {
		q.WithOrderAfterSaleDeliver()
	}
	if reqBo.WithOrderAfterSaleGoods {
		q.WithOrderAfterSaleGoods()
	}
	if reqBo.WithOrderAfterSaleApplyGoods {
		q.WithOrderAfterSaleGoods(func(innerQuery *ent2.OrderAfterSaleGoodsQuery) {
			innerQuery.Where(orderaftersalegoods.Type(valobj.AfterSaleGoodsTypeApply))
		})
	}
	if reqBo.WithOrderAfterSaleExchangeGoods {
		q.WithOrderAfterSaleGoods(func(innerQuery *ent2.OrderAfterSaleGoodsQuery) {
			innerQuery.Where(orderaftersalegoods.Type(valobj.AfterSaleGoodsTypeExchange))
		})
	}

	if reqBo.WithOrderAfterSaleLog {
		q.WithOrderAfterSaleLog()
	}

	row, err := q.First(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (oasri *OrderAfterSaleRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := oasri.data.GetDb(ctx).OrderAfterSale.Query().Where(orderaftersale.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntities(rows), nil
}

// Create 创建数据
func (oasri *OrderAfterSaleRepoImpl) Create(ctx context.Context, d *do.OrderAfterSaleDo) (*do.OrderAfterSaleDo, error) {
	row, err := oasri.data.GetDb(ctx).OrderAfterSale.Create().
		SetSupplierID(d.SupplierID).
		SetOrderID(d.OrderID).
		SetOrderNumber(d.OrderNumber).
		SetReason(d.Reason).
		SetImages(d.Images).
		SetRemark(d.Remark).
		SetExchangeOrderNo(d.ExchangeOrderNo).
		SetRefundAmount(d.RefundAmount).
		SetRefundGoodsAmount(d.RefundGoodsAmount).
		SetRefundFreightFee(d.RefundFreightFee).
		SetReceiveStatus(d.ReceiveStatus).
		SetStatus(d.Status).
		SetPlatformStatus(d.PlatformStatus).
		SetType(d.Type).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetAfterSaleNo(d.AfterSaleNo).
		SetUserID(d.UserID).
		SetOriginalOrderNo(d.OriginalOrderNo).
		SetRefundCardGiftAmount(d.RefundCardGiftAmount).
		SetRefundCardGiftFreightFee(d.RefundCardGiftFreightFee).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (oasri *OrderAfterSaleRepoImpl) CreateBulk(ctx context.Context, dos []*do.OrderAfterSaleDo) ([]*do.OrderAfterSaleDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.OrderAfterSaleCreate, len(dos))
	for i, d := range dos {
		values[i] = oasri.data.GetDb(ctx).OrderAfterSale.Create().
			SetSupplierID(d.SupplierID).
			SetOrderID(d.OrderID).
			SetOrderNumber(d.OrderNumber).
			SetReason(d.Reason).
			SetImages(d.Images).
			SetRemark(d.Remark).
			SetExchangeOrderNo(d.ExchangeOrderNo).
			SetRefundAmount(d.RefundAmount).
			SetReceiveStatus(d.ReceiveStatus).
			SetStatus(d.Status).
			SetType(d.Type).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime)
	}
	rows, err := oasri.data.GetDb(ctx).OrderAfterSale.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return oasri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (oasri *OrderAfterSaleRepoImpl) Update(ctx context.Context, d *do.OrderAfterSaleDo) (int, error) {
	return oasri.data.GetDb(ctx).OrderAfterSale.Update().Where(orderaftersale.ID(d.ID)).
		SetSupplierID(d.SupplierID).
		SetOrderID(d.OrderID).
		SetOrderNumber(d.OrderNumber).
		SetReason(d.Reason).
		SetImages(d.Images).
		SetRemark(d.Remark).
		SetExchangeOrderNo(d.ExchangeOrderNo).
		SetRefundAmount(d.RefundAmount).
		SetReceiveStatus(d.ReceiveStatus).
		SetStatus(d.Status).
		SetType(d.Type).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (oasri *OrderAfterSaleRepoImpl) UpdateV2(ctx context.Context, d *do.OrderAfterSaleDo) (int, error) {
	cnt, err := oasri.data.GetDb(ctx).OrderAfterSale.Update().Where(orderaftersale.ID(d.ID)).
		SetSupplierID(d.SupplierID).
		SetOrderID(d.OrderID).
		SetOrderNumber(d.OrderNumber).
		SetReason(d.Reason).
		SetImages(d.Images).
		SetRemark(d.Remark).
		SetExchangeOrderNo(d.ExchangeOrderNo).
		SetRefundAmount(d.RefundAmount).
		SetReceiveStatus(d.ReceiveStatus).
		SetStatus(d.Status).
		SetType(d.Type).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// UpdateStatus 更新状态
func (oasri *OrderAfterSaleRepoImpl) UpdateStatus(ctx context.Context, id int, oldStatus valobj.AfterSaleStatus, newStatus valobj.AfterSaleStatus, exchangeOrderNumbers ...string) error {
	q := oasri.data.GetDb(ctx).OrderAfterSale.Update().
		Where(orderaftersale.IDEQ(id), orderaftersale.StatusEQ(oldStatus)).
		SetStatus(newStatus).
		SetUpdateTime(int(time.Now().Unix()))

	if len(exchangeOrderNumbers) > 0 {
		q.SetExchangeOrderNo(exchangeOrderNumbers[0])
	}

	cnt, err := q.Save(ctx)

	if cnt == 0 {
		return apierr.ErrorDbNotFound("更新失败")
	}
	return err
}

func (oasri *OrderAfterSaleRepoImpl) UpdatePlatformStatus(ctx context.Context, id int, oldStatus valobj.AfterSalePlatformStatus, newStatus valobj.AfterSalePlatformStatus, asType valobj.AfterSaleType) error {
	q := oasri.data.GetDb(ctx).OrderAfterSale.Update().
		Where(orderaftersale.IDEQ(id), orderaftersale.PlatformStatusEQ(oldStatus)).
		SetPlatformStatus(newStatus).
		SetUpdateTime(int(time.Now().Unix()))

	if newStatus.GetUpdateStatus(asType).Exists() {
		q.SetStatus(newStatus.GetUpdateStatus(asType))
	}

	cnt, err := q.Save(ctx)
	if cnt == 0 {
		return apierr.ErrorDbNotFound("更新失败")
	}
	return err
}

// Delete 删除数据
func (oasri *OrderAfterSaleRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := oasri.data.GetDb(ctx).OrderAfterSale.Delete().Where(orderaftersale.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := oasri.data.GetDb(ctx).OrderAfterSale.Update().
	// 	Where(orderaftersale.IDIn(ids...), orderaftersale.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (oasri *OrderAfterSaleRepoImpl) SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleSearchBo) (dos []*do.OrderAfterSaleDo, respPage *bo.RespPageBo) {
	q := oasri.data.GetDb(ctx).OrderAfterSale.Query()

	if reqBo.OrderNumber != "" {
		q.Where(orderaftersale.OrderNumberContains(reqBo.OrderNumber))
	}

	if reqBo.GoodsName != "" {
		q.Modify(func(s *sql.Selector) {
			afterGoodsTable := sql.Table(orderaftersalegoods.Table)
			innerSql := sql.Select(orderaftersalegoods.FieldPid).
				From(afterGoodsTable).
				Where(sql.EQ(afterGoodsTable.C(orderaftersalegoods.FieldType), valobj.AfterSaleGoodsTypeApply)).
				Where(sql.Contains(afterGoodsTable.C(orderaftersalegoods.FieldGoodsName), reqBo.GoodsName))

			s.Where(sql.In(orderaftersale.FieldID, innerSql))
		})
	}
	if reqBo.AfterSaleType != 0 {
		q.Where(orderaftersale.TypeEQ(reqBo.AfterSaleType))
	}
	if !reqBo.AfterSaleStatus.IsUnknown() {
		q.Where(orderaftersale.StatusIn(reqBo.AfterSaleStatus.ToAfterSaleStatus()...))
	}
	if reqBo.AfterSalePlatformStatus.Exists() {
		q.Where(orderaftersale.PlatformStatusEQ(reqBo.AfterSalePlatformStatus))
	}
	if reqBo.OrderDateStart != 0 && reqBo.OrderDateEnd != 0 {
		q.Where(orderaftersale.CreateTimeGTE(reqBo.OrderDateStart), orderaftersale.CreateTimeLT(reqBo.OrderDateEnd))
	}
	if reqBo.SupplierId != 0 {
		q.Where(orderaftersale.SupplierIDEQ(reqBo.SupplierId))
	}
	if reqBo.AfterSaleNo != "" {
		q.Where(orderaftersale.AfterSaleNoEQ(reqBo.AfterSaleNo))
	}
	if reqBo.UserId != 0 {
		q.Where(orderaftersale.UserID(reqBo.UserId))
	}

	if reqBo.Page != nil {
		oasri.SetPageByBo(q, reqBo.Page)
		respPage = oasri.QueryRespPage(ctx, q, reqBo.Page)
	}
	if reqBo.Edges != nil && !isolationcustomer.IsSassDbCtx(ctx) {
		if reqBo.Edges.WithOrderAfterSaleDeliver {
			q.WithOrderAfterSaleDeliver()
		}
		if reqBo.Edges.WithOrderAfterSaleGoods {
			q.WithOrderAfterSaleGoods()
		}
		if reqBo.Edges.WithOrderAfterSaleLog {
			q.WithOrderAfterSaleLog()
		}
	}

	for _, sort := range reqBo.SortData {
		q.Order(sort.GetOrderSelector())
	}

	pos := q.AllX(ctx)
	dos = oasri.ToEntities(pos)
	return
}

func (oasri *OrderAfterSaleRepoImpl) UserClose(ctx context.Context, id int) (int, error) {
	return oasri.data.GetDb(ctx).OrderAfterSale.Update().
		Where(orderaftersale.ID(id)).
		SetStatus(valobj.AfterSaleStatusBuyerClose).
		SetPlatformStatus(valobj.AfterSalePlatformStatusBuyerClose).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
}

func (oasri *OrderAfterSaleRepoImpl) HasHanding(ctx context.Context, orderId int) (bool, error) {
	return oasri.data.GetDb(ctx).OrderAfterSale.Query().
		Where(orderaftersale.OrderID(orderId),
			orderaftersale.StatusNotIn(
				valobj.AfterSaleStatusFinish,
				valobj.AfterSaleStatusSellerClose,
				valobj.AfterSaleStatusBuyerClose,
				valobj.AfterSaleStatusAuditRefuse,
			)).
		Exist(ctx)
}

func (oasri *OrderAfterSaleRepoImpl) FindLast(ctx context.Context, orderId int) (*do.OrderAfterSaleDo, error) {
	return oasri.ToEntity(oasri.data.GetDb(ctx).OrderAfterSale.Query().
		Where(orderaftersale.OrderID(orderId)).
		Order(ent2.Desc(orderaftersale.FieldID)).WithOrderAfterSaleGoods().WithOrderAfterSaleDeliver().WithOrderAfterSaleLog().
		FirstX(ctx)), nil
}

func (oasri *OrderAfterSaleRepoImpl) GetFinishAll(ctx context.Context, orderId int) ([]*do.OrderAfterSaleDo, error) {
	return oasri.ToEntities(oasri.data.GetDb(ctx).OrderAfterSale.Query().
		Where(orderaftersale.OrderID(orderId)).
		Where(orderaftersale.StatusEQ(valobj.AfterSaleStatusFinish)).WithOrderAfterSaleGoods().
		AllX(ctx)), nil
}
func (oasri *OrderAfterSaleRepoImpl) GetAll(ctx context.Context, orderId int) ([]*do.OrderAfterSaleDo, error) {
	return oasri.ToEntities(oasri.data.GetDb(ctx).OrderAfterSale.Query().
		Where(orderaftersale.OrderID(orderId)).WithOrderAfterSaleGoods().
		AllX(ctx)), nil
}
func (r *OrderAfterSaleRepoImpl) AfterSaleDayNum(ctx context.Context, startTime int) ([]*bo.AfterSaleDayResult, error) {
	var (
		wantArgs []any
	)

	t1 := sql.Table(orderaftersale.Table).As("o")

	selector := sql.Select(
		orderaftersale.FieldOrderNumber,
		"count(*) AS after_sale_num",
	).From(t1).GroupBy(orderaftersale.FieldOrderNumber)
	selector = selector.Where(sql.GTE(t1.C(order.FieldCreateTime), startTime))
	query, wantArgs := selector.Query()

	result, err := r.data.GetDb(ctx).QueryContext(ctx, query, wantArgs...)
	if err != nil {
		return nil, err
	}
	numResults := make([]*bo.AfterSaleDayResult, 0)
	err = sql.ScanSlice(result, &numResults)
	if err != nil {
		return nil, err
	}
	return numResults, nil
}
func (o *OrderAfterSaleRepoImpl) FindByOrderNumber(ctx context.Context, orderNumber string) (*do.OrderAfterSaleDo, error) {
	sql := o.data.GetDb(ctx).OrderAfterSale.Query()
	sql.Where(orderaftersale.OrderNumberEQ(orderNumber))
	sql.Order(ent2.Desc(orderaftersale.FieldID))
	res := sql.FirstX(ctx)
	return o.ToEntity(res), nil
}

func (o *OrderAfterSaleRepoImpl) ListWaitSupplierApprove(ctx context.Context, supplierId int) ([]*do.OrderAfterSaleDo, error) {
	sql := o.data.GetDb(ctx).OrderAfterSale.Query()
	sql.Where(orderaftersale.SupplierIDEQ(supplierId))
	sql.Where(orderaftersale.PlatformStatusIn(
		valobj.AfterSalePlatformStatusApproveToUser,
		valobj.AfterSalePlatformStatusRefuseByPlatformToSupplier,
	))
	sql.Order(ent2.Desc(orderaftersale.FieldID))
	res, err := sql.All(ctx)
	if err != nil {
		return nil, err
	}
	return o.ToEntities(res), nil
}
func (g *OrderAfterSaleRepoImpl) GetAllWaitAuditCount(ctx context.Context) (int, error) {
	return g.data.GetDb(ctx).OrderAfterSale.Query().
		Where(orderaftersale.StatusEQ(valobj.AfterSaleStatusWaitAudit)).
		Count(ctx)

}

// GetRefundOrdersByOrigin 根据原订单号获退款取售后单
func (o *OrderAfterSaleRepoImpl) GetRefundOrdersByOrigin(ctx context.Context, status valobj.AfterSaleStatus, orderNumbers ...string) ([]*do.OrderAfterSaleDo, error) {
	q := o.data.GetDb(ctx).OrderAfterSale.Query().
		Where(
			orderaftersale.OriginalOrderNoIn(orderNumbers...),
			orderaftersale.Status(status),
			orderaftersale.RefundAmountGT(0),
		).Order(ent2.Desc(orderaftersale.FieldID))
	res := q.AllX(ctx)
	return o.ToEntities(res), nil
}
