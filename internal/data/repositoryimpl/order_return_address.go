//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/api/apierr"
	commonbo "cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderreturnaddress"
	"cardMall/internal/module/supplierbiz/bo"
	"cardMall/internal/pkg/helper"
	"context"
)

type OrderReturnAddressRepoImpl struct {
	Base[ent2.OrderReturnAddress, do.OrderReturnAddressDo, ent2.OrderReturnAddressQuery]
	data *data.Data
}

// NewOrderReturnAddressRepoImpl 创建 OrderReturnAddressRepo的实现者
func NewOrderReturnAddressRepoImpl(data *data.Data) repository.OrderReturnAddressRepo {
	return &OrderReturnAddressRepoImpl{data: data}
}

// ToEntity 转换成实体
func (orari *OrderReturnAddressRepoImpl) ToEntity(po *ent2.OrderReturnAddress) *do.OrderReturnAddressDo {
	if po == nil {
		return nil
	}
	entity := orari.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (orari *OrderReturnAddressRepoImpl) ToEntities(pos []*ent2.OrderReturnAddress) []*do.OrderReturnAddressDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.OrderReturnAddressDo, len(pos))
	for k, p := range pos {
		entities[k] = orari.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (orari *OrderReturnAddressRepoImpl) Get(ctx context.Context, id int) (*do.OrderReturnAddressDo, error) {
	row, err := orari.data.GetDb(ctx).OrderReturnAddress.Query().Where(orderreturnaddress.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return orari.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (orari *OrderReturnAddressRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.OrderReturnAddressDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := orari.data.GetDb(ctx).OrderReturnAddress.Query().Where(orderreturnaddress.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return orari.ToEntities(rows), nil
}

// Create 创建数据
func (orari *OrderReturnAddressRepoImpl) Create(ctx context.Context, d *bo.OrderReturnAddressSaveBo) (*do.OrderReturnAddressDo, error) {
	now := helper.GetNow()
	row, err := orari.data.GetDb(ctx).OrderReturnAddress.Create().
		SetSupplierID(d.SupplierId).
		SetContactName(d.ContactName).
		SetContactPhone(d.ContactPhone).
		SetAddress(d.Address).
		SetTitle(d.Title).
		SetCreateTime(now).
		SetUpdateTime(now).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return orari.ToEntity(row), nil
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (orari *OrderReturnAddressRepoImpl) UpdateV2(ctx context.Context, d *bo.OrderReturnAddressSaveBo) (int, error) {
	now := helper.GetNow()
	cnt, err := orari.data.GetDb(ctx).OrderReturnAddress.Update().
		Where(orderreturnaddress.IDEQ(d.Id)).
		Where(orderreturnaddress.SupplierIDEQ(d.SupplierId)).
		SetContactName(d.ContactName).
		SetContactPhone(d.ContactPhone).
		SetAddress(d.Address).
		SetTitle(d.Title).
		SetUpdateTime(now).
		Save(ctx)
	if cnt == 0 {
		return cnt, apierr.ErrorDbNotFound("更新失败")
	}
	return cnt, err
}

// Delete 删除数据
func (orari *OrderReturnAddressRepoImpl) Delete(ctx context.Context, supplierId int, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := orari.data.GetDb(ctx).OrderReturnAddress.Delete().
		Where(orderreturnaddress.SupplierIDEQ(supplierId)).
		Where(orderreturnaddress.IDIn(ids...)).
		Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := orari.data.GetDb(ctx).OrderReturnAddress.Update().
	// 	Where(orderreturnaddress.IDIn(ids...), orderreturnaddress.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (orari *OrderReturnAddressRepoImpl) SearchList(ctx context.Context, reqBo *commonbo.OrderReturnAddressSearchBo) (dos []*do.OrderReturnAddressDo, respPage *commonbo.RespPageBo) {
	q := orari.data.GetDb(ctx).OrderReturnAddress.Query()
	if reqBo.Title != "" {
		q.Where(orderreturnaddress.TitleContains(reqBo.Title))
	}

	if reqBo.SupplierId > 0 {
		q.Where(orderreturnaddress.SupplierIDEQ(reqBo.SupplierId))
	}

	if reqBo.Page != nil {
		orari.SetPageByBo(q, reqBo.Page)
		respPage = orari.QueryRespPage(ctx, q, reqBo.Page)
	}
	for _, sort := range reqBo.SortData {
		q.Order(sort.GetOrderSelector())
	}

	pos := q.AllX(ctx)
	dos = orari.ToEntities(pos)
	return
}
