//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"context"
	"time"

	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/customershowsupplier"
	"cardMall/internal/data/ent/saascustomer"
	"cardMall/internal/data/ent/supplier"
	"cardMall/internal/data/ent/suppliergoods"
	"cardMall/internal/data/ent/suppliergoodssku"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"entgo.io/ent/dialect/sql"
	"github.com/duke-git/lancet/v2/mathutil"
	"github.com/duke-git/lancet/v2/slice"
)

var supplierGoodsSkuRepoImpl = &SupplierGoodsSkuRepoImpl{}

type SupplierGoodsSkuRepoImpl struct {
	Base[ent2.SupplierGoodsSku, do.SupplierGoodsSkuDo, ent2.SupplierGoodsSkuQuery]
	data *data.Data
}

// NewSupplierGoodsSkuRepoImpl 创建 SupplierGoodsSkuRepo的实现者
func NewSupplierGoodsSkuRepoImpl(data *data.Data) repository.SupplierGoodsSkuRepo {
	return &SupplierGoodsSkuRepoImpl{data: data}
}

// ToEntity 转换成实体
func (sgsri *SupplierGoodsSkuRepoImpl) ToEntity(po *ent2.SupplierGoodsSku) *do.SupplierGoodsSkuDo {
	if po == nil {
		return nil
	}
	entity := sgsri.Base.ToEntity(po)
	entity.Goods = supplierGoodsRepoImpl.ToEntity(po.Edges.Goods)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (sgsri *SupplierGoodsSkuRepoImpl) ToEntities(pos []*ent2.SupplierGoodsSku) []*do.SupplierGoodsSkuDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.SupplierGoodsSkuDo, len(pos))
	for k, p := range pos {
		entities[k] = sgsri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (sgsri *SupplierGoodsSkuRepoImpl) Get(ctx context.Context, id int) (*do.SupplierGoodsSkuDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.ID(id)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// GetNum 获取总数
func (sgsri *SupplierGoodsSkuRepoImpl) GetNum(ctx context.Context, supplierId int, status []valobj.SupplierGoodsStatusObj) (int, error) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		Modify(func(s *sql.Selector) {
			goodsTable := sql.Table(suppliergoods.Table)
			s.LeftJoin(goodsTable).On(s.C(suppliergoodssku.FieldGoodsID), goodsTable.C(suppliergoods.FieldID))
			s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldSupplierID), supplierId))
		})
	if len(status) > 0 {
		q.Where(suppliergoodssku.StatusIn(status...))
	}
	return q.Count(ctx)
}

// FindByIds 获取商品sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) FindByIds(ctx context.Context, ids ...int) ([]*do.SupplierGoodsSkuDo, error) {
	rows, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().WithGoods().
		Where(suppliergoodssku.IDIn(ids...)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(rows), nil
}

// FindWithGoods 获取商品sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) FindWithGoods(ctx context.Context, ids ...int) ([]*do.SupplierGoodsSkuDo, error) {
	rows, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.IDIn(ids...)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(rows), nil
}

// FindWithGoodsBySkuNo 获取商品sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) FindWithGoodsBySkuNo(ctx context.Context, skuNos ...string) ([]*do.SupplierGoodsSkuDo, error) {
	rows, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoIn(skuNos...)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(rows), nil
}

// IncrStock 增加库存
func (sgsri *SupplierGoodsSkuRepoImpl) IncrStock(ctx context.Context, id int, num int) error {
	now := int(time.Now().Unix())
	_, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.IDEQ(id)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		AddStock(num).
		SetUpdateTime(now).
		Save(ctx)
	return err
}

// Shelf .
func (sgsri *SupplierGoodsSkuRepoImpl) Shelf(ctx context.Context, goodsId int, status valobj.SupplierGoodsStatusObj) error {
	now := int(time.Now().Unix())
	_, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.GoodsIDEQ(goodsId)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		SetStatus(status).
		SetUpdateTime(now).
		Save(ctx)
	return err
}

// ListGoodsSku 通过 id 获取一条数据
func (sgsri *SupplierGoodsSkuRepoImpl) ListGoodsSku(ctx context.Context, id int) ([]*do.SupplierGoodsSkuDo, error) {
	rows, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.GoodsIDEQ(id)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(rows), nil
}

// Create 创建数据
func (sgsri *SupplierGoodsSkuRepoImpl) Create(ctx context.Context, goodsId int, d *do.SupplierGoodsSkuDo, status valobj.SupplierGoodsStatusObj) (*do.SupplierGoodsSkuDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Create().
		SetGoodsID(goodsId).
		SetSkuNo(d.SkuNo).
		SetName(d.Name).
		SetImage(d.Image).
		SetSpecItemIds(d.SpecItemIds).
		SetMarketPrice(d.MarketPrice).
		SetSalePrice(d.SalePrice).
		SetSupplierPrice(d.SupplierPrice).
		SetChannelPrice(d.ChannelPrice).
		SetFreePostagePrice(d.FreePostagePrice).
		SetBarcode(d.Barcode).
		SetSupplierBarcode(d.SupplierBarcode).
		SetProductID(d.ProductID).
		SetProductType(d.ProductType).
		SetProductName(d.ProductName).
		SetStock(d.Stock).
		SetStockAlarmNum(d.StockAlarmNum).
		SetStatus(status).
		SetSort(d.Sort).
		SetSalesVolume(d.SalesVolume).
		SetProfit(d.Profit).
		SetProfitRate(d.ProfitRate).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetShopID(d.ShopID).
		SetCustomerID(d.CustomerID).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// GetBySkuInfo 通过sku信息获取sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) GetBySkuInfo(ctx context.Context, goodsId int, specStr string) (*do.SupplierGoodsSkuDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.GoodsIDEQ(goodsId)).
		Where(suppliergoodssku.SpecItemIdsEQ(specStr)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// DeleteBySkuInfos 批量删除sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) DeleteBySkuInfos(ctx context.Context, goodsId int, specStr []string) ([]int, error) {
	ids, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.GoodsIDEQ(goodsId)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		Where(suppliergoodssku.SpecItemIdsNotIn(specStr...)).
		IDs(ctx)
	if err != nil {
		return nil, err
	}
	return ids, sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.IDIn(ids...)).
		SetDeleteTime(int(time.Now().Unix())).
		Exec(ctx)
}

// UpdateStatusBySkuInfos 批量更新sku状态信息
func (sgsri *SupplierGoodsSkuRepoImpl) UpdateStatusBySkuInfos(ctx context.Context, goodsId int, specStr []string, status valobj.SupplierGoodsStatusObj) ([]int, error) {
	ids, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.GoodsIDEQ(goodsId)).
		Where(suppliergoodssku.SpecItemIdsNotIn(specStr...)).
		IDs(ctx)
	if err != nil {
		return nil, err
	}
	return ids, sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.IDIn(ids...)).
		SetStatus(status).
		Exec(ctx)
}

func (sgsri *SupplierGoodsSkuRepoImpl) Update(ctx context.Context, id int, d *do.SupplierGoodsSkuDo, status valobj.SupplierGoodsStatusObj) error {
	_, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.IDEQ(id)).
		SetSkuNo(d.SkuNo).
		SetName(d.Name).
		SetImage(d.Image).
		SetSpecItemIds(d.SpecItemIds).
		SetMarketPrice(d.MarketPrice).
		SetSalePrice(d.SalePrice).
		SetSupplierPrice(d.SupplierPrice).
		SetChannelPrice(d.ChannelPrice).
		SetFreePostagePrice(d.FreePostagePrice).
		SetBarcode(d.Barcode).
		SetSupplierBarcode(d.SupplierBarcode).
		SetProductID(d.ProductID).
		SetProductType(d.ProductType).
		SetProductName(d.ProductName).
		SetStock(d.Stock).
		SetStockAlarmNum(d.StockAlarmNum).
		SetStatus(status).
		SetSort(d.Sort).
		SetSalesVolume(d.SalesVolume).
		SetProfit(d.Profit).
		SetProfitRate(d.ProfitRate).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	return err
}

// CreateBulk 批量创建数据
func (sgsri *SupplierGoodsSkuRepoImpl) CreateBulk(ctx context.Context, goodsId int, dos []*do.SupplierGoodsSkuDo, status valobj.SupplierGoodsStatusObj) ([]*do.SupplierGoodsSkuDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.SupplierGoodsSkuCreate, len(dos))
	for i, d := range dos {
		values[i] = sgsri.data.GetDb(ctx).SupplierGoodsSku.Create().
			SetGoodsID(goodsId).
			SetSkuNo(d.SkuNo).
			SetName(d.Name).
			SetImage(d.Image).
			SetSpecItemIds(d.SpecItemIds).
			SetMarketPrice(d.MarketPrice).
			SetSalePrice(d.SalePrice).
			SetSupplierPrice(d.SupplierPrice).
			SetChannelPrice(d.ChannelPrice).
			SetFreePostagePrice(d.FreePostagePrice).
			SetBarcode(d.Barcode).
			SetSupplierBarcode(d.SupplierBarcode).
			SetProductID(d.ProductID).
			SetProductType(d.ProductType).
			SetProductName(d.ProductName).
			SetStock(d.Stock).
			SetStockAlarmNum(d.StockAlarmNum).
			SetStatus(status).
			SetSort(d.Sort).
			SetSalesVolume(d.SalesVolume).
			SetProfit(d.Profit).
			SetProfitRate(d.ProfitRate).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime)
	}
	rows, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(rows), nil
}

// DeleteByGoodsId 通过 id 删除数据
func (sgsri *SupplierGoodsSkuRepoImpl) DeleteByGoodsId(ctx context.Context, goodsId int) (int, error) {
	return sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.GoodsIDEQ(goodsId)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		SetDeleteTime(int(time.Now().Unix())).
		Save(ctx)
}

// SearchList 搜索列表
func (sgsri *SupplierGoodsSkuRepoImpl) SearchList(ctx context.Context, reqBo *bo.SupplierGoodsSkuSearchBo) (dos []*do.SupplierGoodsSkuDo, respPage *bo.RespPageBo) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	if reqBo.IsDelete != nil {
		if *reqBo.IsDelete {
			q.Where(suppliergoodssku.DeleteTimeGT(0))
		} else {
			q.Where(suppliergoodssku.DeleteTimeEQ(0))
		}
	}
	if reqBo.GoodsName != "" || reqBo.SupplierId > 0 {
		q.Modify(func(s *sql.Selector) {
			spuTable := sql.Table(suppliergoods.Table)
			s.LeftJoin(spuTable).On(s.C(suppliergoodssku.FieldGoodsID), spuTable.C(suppliergoods.FieldID))
			if reqBo.GoodsName != "" {
				s.Where(sql.Contains(spuTable.C(suppliergoods.FieldName), reqBo.GoodsName))
			}
			if reqBo.SupplierId > 0 {
				s.Where(sql.EQ(spuTable.C(suppliergoods.FieldSupplierID), reqBo.SupplierId))
			}
		})
	}
	if reqBo.SkuNo != "" {
		q.Where(suppliergoodssku.SkuNoEQ(reqBo.SkuNo))
	}
	if reqBo.SkuStatus != nil {
		q.Where(suppliergoodssku.StatusEQ(*reqBo.SkuStatus))
	}
	if reqBo.StockAlarmNum > 0 {
		q.Where(suppliergoodssku.StockLT(reqBo.StockAlarmNum))
	}

	if reqBo.GoodsId > 0 {
		q.Where(suppliergoodssku.GoodsIDEQ(reqBo.GoodsId))
	}
	if reqBo.IsStockWarning != nil {
		if *reqBo.IsStockWarning {
			q.Where(suppliergoodssku.StockAlarmNumGT(0))
		} else {
			q.Where(suppliergoodssku.StockAlarmNumEQ(0))
		}
	}
	if reqBo.WithSpu {
		q.WithGoods()
	}

	if reqBo.Page != nil {
		sgsri.SetPageByBo(q, reqBo.Page)
		respPage = sgsri.QueryRespPage(ctx, q, reqBo.Page)
	}

	for _, sort := range reqBo.SortData {
		q.Order(sort.GetOrderSelector())
	}

	pos := q.AllX(ctx)
	dos = sgsri.ToEntities(pos)
	return
}

// SearchList 搜索列表
func (sgsri *SupplierGoodsSkuRepoImpl) ProductAuthorizeSkuSearch(ctx context.Context, reqBo *bo.ProductAuthorizeSkuSearchBo) (dos []*do.SupplierGoodsSkuDo, respPage *bo.RespPageBo) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Where(suppliergoodssku.DeleteTimeEQ(0))

	q.Modify(func(s *sql.Selector) {
		spuTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(spuTable).On(s.C(suppliergoodssku.FieldGoodsID), spuTable.C(suppliergoods.FieldID))
		if reqBo.GoodsName != "" {
			//s.Where(sql.Contains(spuTable.C(suppliergoods.FieldName), reqBo.GoodsName))
			s.Where(sql.Or(sql.Contains(s.C(suppliergoods.FieldName), reqBo.GoodsName), sql.Contains(spuTable.C(supplier.FieldName), reqBo.GoodsName)))
		}
		if len(reqBo.SupplierId) > 0 {
			supplierIds := make([]any, len(reqBo.SupplierId))
			for i, v := range reqBo.SupplierId {
				supplierIds[i] = v
			}
			s.Where(sql.In(spuTable.C(suppliergoods.FieldSupplierID), supplierIds...))
		} else {
			customerQueryCtx := isolationcustomer.WithSassDbCtx(ctx)
			customerQuery, err := sgsri.data.GetDb(customerQueryCtx).SaasCustomer.Query().
				Where(saascustomer.ID(isolationcustomer.GetCustomerIdZero(ctx))).First(customerQueryCtx)
			if err != nil {
				return
			}
			if customerQuery.CompanySupplierEnable == 1 {
				// 获取配置的商城供应商id列表
				showSuppliers := sgsri.data.GetDb(customerQueryCtx).CustomerShowSupplier.Query().
					Where(
						customershowsupplier.CustomerIDEQ(isolationcustomer.GetCustomerIdZero(ctx)),
						customershowsupplier.ShowType(1),
					).
					AllX(customerQueryCtx)
				supplierIds := slice.Map(showSuppliers, func(index int, item *ent2.CustomerShowSupplier) int {
					return item.SupplierID
				})
				if len(supplierIds) != 0 {
					ids := make([]any, len(supplierIds))
					for i, v := range reqBo.SupplierId {
						ids[i] = v
					}
					s.Where(sql.NotIn(spuTable.C(suppliergoods.FieldSupplierID), ids...))
				}
			}
		}
		if len(reqBo.CategoryId) > 0 {
			categoryIds := make([]any, len(reqBo.CategoryId))
			for i, v := range reqBo.CategoryId {
				categoryIds[i] = v
			}
			s.Where(sql.In(spuTable.C(suppliergoods.FieldCategoryID), categoryIds...))
		}

		if reqBo.Type != 0 {
			s.Where(sql.EQ(spuTable.C(suppliergoods.FieldType), reqBo.Type))
		}
		if reqBo.BrandId != 0 {
			s.Where(sql.EQ(spuTable.C(suppliergoods.FieldBrandID), reqBo.BrandId))
		}
		if reqBo.Status != 0 {
			s.Where(sql.EQ(spuTable.C(suppliergoods.FieldStatus), reqBo.Status))
			s.Where(sql.EQ(s.C(suppliergoodssku.FieldStatus), reqBo.Status))
		}
	})
	if reqBo.Stock != 0 {
		q.Where(suppliergoodssku.StockGT(0))
	}

	if len(reqBo.NotSkuNo) != 0 {
		q.Where(suppliergoodssku.SkuNoNotIn(reqBo.NotSkuNo...))
	}
	if len(reqBo.InSkuNo) != 0 {
		q.Where(suppliergoodssku.SkuNoIn(reqBo.InSkuNo...))
	}
	q.Order(ent2.Desc(suppliergoodssku.FieldID))
	if reqBo.Page != nil {
		sgsri.SetPageByBo(q, reqBo.Page)
		respPage = sgsri.QueryRespPage(ctx, q, reqBo.Page)
	}

	q.WithGoods()
	pos := q.AllX(ctx)
	dos = sgsri.ToEntities(pos)
	return
}

func (sgsri *SupplierGoodsSkuRepoImpl) FindById(ctx context.Context, id int) (*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntity(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().Where(suppliergoodssku.ID(id)).FirstX(ctx)), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) AddSalesVolume(ctx context.Context, skuNo string, num int) (int, error) {
	return sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.SkuNoEQ(skuNo)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		AddSalesVolume(num).
		SaveX(ctx), nil
}

// GetStockAlarmNum 获取库存低于预警值的sku数量
func (sgsri *SupplierGoodsSkuRepoImpl) GetStockAlarmNum(ctx context.Context, supplierId int, num int) (int, error) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.StockLTE(num), suppliergoodssku.StockGTE(0))

	q.Modify(func(s *sql.Selector) {
		goodsTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(goodsTable).On(s.C(suppliergoodssku.FieldGoodsID), goodsTable.C(suppliergoods.FieldID))
		s.Where(sql.EQ(s.C(suppliergoods.FieldStatus), valobj.SupplierGoodsStatusEnable))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldSupplierID), supplierId))
	})
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	q.Where(suppliergoodssku.StatusEQ(valobj.SupplierGoodsStatusEnable))

	return q.Count(ctx)
}

// GetOffShelfCount 获取下架商品数量
func (sgsri *SupplierGoodsSkuRepoImpl) GetOffShelfCount(ctx context.Context, supplierId int) (int, error) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Modify(func(s *sql.Selector) {
		goodsTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(goodsTable).On(s.C(suppliergoodssku.FieldGoodsID), goodsTable.C(suppliergoods.FieldID))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldStatus), valobj.SupplierGoodsStatusDisable))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldSupplierID), supplierId))
	})
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	return q.Count(ctx)
}

func (sgsri *SupplierGoodsSkuRepoImpl) GetOnSaleCount(ctx context.Context, supplierId int) (int, error) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Modify(func(s *sql.Selector) {
		goodsTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(goodsTable).On(s.C(suppliergoodssku.FieldGoodsID), goodsTable.C(suppliergoods.FieldID))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldStatus), valobj.SupplierGoodsStatusEnable))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldSupplierID), supplierId))
	})
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	return q.Count(ctx)
}

func (sgsri *SupplierGoodsSkuRepoImpl) GetAuditCount(ctx context.Context, supplierId int, status valobj.SupplierGoodsDraftStatusObj) (int, error) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Modify(func(s *sql.Selector) {
		goodsTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(goodsTable).On(s.C(suppliergoodssku.FieldGoodsID), goodsTable.C(suppliergoods.FieldID))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldLastAuditStatus), status))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldSupplierID), supplierId))
	})
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	return q.Count(ctx)
}

func (sgsri *SupplierGoodsSkuRepoImpl) GetAllAuditCount(ctx context.Context, supplierId int) (int, error) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Modify(func(s *sql.Selector) {
		goodsTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(goodsTable).On(s.C(suppliergoodssku.FieldGoodsID), goodsTable.C(suppliergoods.FieldID))
		s.Where(sql.NEQ(goodsTable.C(suppliergoods.FieldLastAuditStatus), valobj.SupplierGoodsDraftStatusNone))
		s.Where(sql.EQ(goodsTable.C(suppliergoods.FieldSupplierID), supplierId))
	})
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	return q.Count(ctx)
}

func (sgsri *SupplierGoodsSkuRepoImpl) FindBySkuNo(ctx context.Context, skuNo string) (*do.SupplierGoodsSkuDo, error) {
	po, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoEQ(skuNo)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(po), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) FindWithDeleteBySkuNo(ctx context.Context, skuNo string) (*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntity(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoEQ(skuNo)).
		Order(ent2.Desc(suppliergoodssku.FieldID)).
		FirstX(ctx)), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) GetWithGoodsBySkuNo(ctx context.Context, skuNo string) (*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntity(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoEQ(skuNo)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().
		FirstX(ctx)), nil
}
func (sgsri *SupplierGoodsSkuRepoImpl) GetWithGoodsBySkuNos(ctx context.Context, skuNo []string) ([]*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntities(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoIn(skuNo...)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().AllX(ctx)), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) GetBySkuNo(ctx context.Context, skuNo []string) ([]*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntities(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoIn(skuNo...)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().AllX(ctx)), nil
}
func (sgsri *SupplierGoodsSkuRepoImpl) GetByProductIds(ctx context.Context, productIds []string, customerID int) ([]*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntities(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.ProductIDIn(productIds...)).
		Where(suppliergoodssku.CustomerIDEQ(customerID)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		AllX(ctx)), nil
}
func (sgsri *SupplierGoodsSkuRepoImpl) GetMapBySkuNo(ctx context.Context, skuNo []string) (map[string]*do.SupplierGoodsSkuDo, error) {
	d, _ := sgsri.GetBySkuNo(ctx, skuNo)
	res := make(map[string]*do.SupplierGoodsSkuDo)
	for _, v := range d {
		res[v.SkuNo] = v
	}
	return res, nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) IncrStockBySkuNo(ctx context.Context, skuNo string, num int) error {
	now := int(time.Now().Unix())
	_, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.SkuNoEQ(skuNo)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		AddStock(num).
		SetUpdateTime(now).
		Save(ctx)
	return err
}

func (sgsri *SupplierGoodsSkuRepoImpl) DecrStockBySkuNo(ctx context.Context, skuNo string, num int) error {
	absNum := mathutil.Abs(num)
	ctn, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.SkuNoEQ(skuNo)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		Where(suppliergoodssku.StockGTE(absNum)).
		AddStock(-absNum).
		SetUpdateTime(helper.GetNow()).
		Save(ctx)
	if ctn <= 0 {
		return apierr.ErrorDbNotFound("库存不足")
	}
	return err
}
func (sgsri *SupplierGoodsSkuRepoImpl) SearchAll(ctx context.Context, reqBo *bo.ProductAuthorizeSearchBo) (dos []*do.SupplierGoodsSkuDo) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()

	//if reqBo.GoodsName != "" || len(reqBo.SupplierId) > 0 || len(reqBo.CategoryId) > 0 || reqBo.BrandId != 0 {
	q.Modify(func(s *sql.Selector) {
		spuTable := sql.Table(suppliergoods.Table)
		s.LeftJoin(spuTable).On(s.C(suppliergoodssku.FieldGoodsID), spuTable.C(suppliergoods.FieldID))
		if reqBo.GoodsName != "" {
			s.Where(sql.Contains(spuTable.C(suppliergoods.FieldName), reqBo.GoodsName))
		}
		if len(reqBo.SupplierId) > 0 {
			supplierIds := make([]any, len(reqBo.SupplierId))
			for i, v := range reqBo.SupplierId {
				supplierIds[i] = v
			}
			s.Where(sql.In(spuTable.C(suppliergoods.FieldSupplierID), supplierIds...))
		}
		if len(reqBo.CategoryId) > 0 {
			categoryIds := make([]any, len(reqBo.CategoryId))
			for i, v := range reqBo.CategoryId {
				categoryIds[i] = v
			}
			s.Where(sql.In(spuTable.C(suppliergoods.FieldCategoryID), categoryIds...))
		}

		if reqBo.Type != 0 {
			s.Where(sql.EQ(spuTable.C(suppliergoods.FieldType), reqBo.Type))
		}
		if reqBo.BrandId != 0 {
			s.Where(sql.EQ(spuTable.C(suppliergoods.FieldBrandID), reqBo.BrandId))
		}
	})
	//}
	if reqBo.SkuNo != "" {
		q.Where(suppliergoodssku.SkuNoEQ(reqBo.SkuNo))
	}
	q.WithGoods()
	q.Order(ent2.Desc(suppliergoodssku.FieldID))

	pos := q.AllX(ctx)
	dos = sgsri.ToEntities(pos)
	return
}

func (sgsri *SupplierGoodsSkuRepoImpl) OfficialList(ctx context.Context, in *bo.SupplierGoodsSkuBo) (int, []*do.SupplierGoodsSkuDo, error) {
	if err := in.Validate(); err != nil {
		return 0, nil, err
	}
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	q.Modify(func(query *sql.Selector) {
		supplierGoodsTable := sql.Table(suppliergoods.Table)

		query.Join(supplierGoodsTable).On(query.C(suppliergoodssku.FieldGoodsID), supplierGoodsTable.C(suppliergoods.FieldID))

		query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldStatus), valobj.SupplierGoodsStatusEnable))
		query.Where(sql.EQ(query.C(suppliergoodssku.FieldDeleteTime), 0))

		//if in.Selected == 2 {
		//	query.LeftJoin(goodsSkuTable).On(query.C(suppliergoodssku.FieldSkuNo), goodsSkuTable.C(goodssku.FieldSkuNo)).
		//		OnP(sql.EQ(goodsSkuTable.C(goodssku.FieldDeleteTime), 0))
		//	query.Where(sql.IsNull(goodsSkuTable.C(goodssku.FieldID)))
		//}
		if in.CategoryIds != nil && len(in.CategoryIds) > 0 {
			categoryIds := make([]any, len(in.CategoryIds))
			for i, v := range in.CategoryIds {
				categoryIds[i] = v
			}
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldCategoryID), categoryIds...))
		}
		if in.Name != "" {
			//query.Where(sql.Contains(supplierGoodsTable.C(suppliergoods.FieldName), in.Name))
			query.Where(sql.Or(sql.Contains(query.C(suppliergoods.FieldName), in.Name), sql.Contains(supplierGoodsTable.C(suppliergoodssku.FieldName), in.Name)))
		}

		//if in.SupplierId > 0 {
		//	query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldSupplierID), in.SupplierId))
		//}

		if len(in.SupplierGoodsType) > 0 {
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldType), helper.SliceConvertAnySlice(in.SupplierGoodsType)...))
		}

		if in.SupplierGoodsOne != 0 {
			query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldType), in.SupplierGoodsOne))
		}
	})
	//if in.ProfitRateMin > 0 {
	//	q = q.Where(suppliergoodssku.ProfitRateGTE(in.ProfitRateMin))
	//}
	//if in.ProfitRateMax > 0 {
	//	q = q.Where(suppliergoodssku.ProfitRateLTE(in.ProfitRateMax))
	//}
	//if in.SalePriceMin > 0 {
	//	q = q.Where(suppliergoodssku.SalePriceGTE(in.SalePriceMin))
	//}
	//if in.SalePriceMax > 0 {
	//	q = q.Where(suppliergoodssku.SalePriceLTE(in.SalePriceMax))
	//}
	if in.SkuNo != "" {
		q = q.Where(suppliergoodssku.SkuNoEQ(in.SkuNo))
	}
	if len(in.NotSkuNo) > 0 {
		q = q.Where(suppliergoodssku.SkuNoNotIn(in.NotSkuNo...))
	}
	if len(in.SkuNos) > 0 {
		q = q.Where(suppliergoodssku.SkuNoIn(in.SkuNos...))
	}
	//if in.HaveStock > 0 {
	//	q = q.Where(suppliergoodssku.StockGT(0))
	//}
	var count int
	//if in.ReqPageBo != nil {
	countSql := q.Clone()
	count = countSql.CountX(ctx)
	if count == 0 {
		return 0, nil, nil
	}
	//}

	//if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSaleVolume {
	//	q.Order(ent2.Desc(suppliergoodssku.FieldSalesVolume))
	//} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeNew {
	//	q.Order(ent2.Desc(suppliergoodssku.FieldCreateTime))
	//} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSalePrice {
	//	q.Order(ent2.Asc(suppliergoodssku.FieldSalePrice))
	//} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeProfit {
	//	q.Order(ent2.Desc(suppliergoodssku.FieldProfit))
	//}
	//if in.ReqPageBo != nil {
	//	q.Offset(in.ReqPageBo.GetOffset()).Limit(in.ReqPageBo.GetPageSize())
	//}
	q.WithGoods()
	res := q.AllX(ctx)
	return count, sgsri.ToEntities(res), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) CustomerList(ctx context.Context, in *bo.SupplierGoodsSkuBo) (int, []*do.SupplierGoodsSkuDo, error) {
	if err := in.Validate(); err != nil {
		return 0, nil, err
	}
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	q.Modify(func(query *sql.Selector) {
		supplierGoodsTable := sql.Table(suppliergoods.Table)

		query.Join(supplierGoodsTable).On(query.C(suppliergoodssku.FieldGoodsID), supplierGoodsTable.C(suppliergoods.FieldID))

		query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldStatus), valobj.SupplierGoodsStatusEnable))

		//if in.Selected == 2 {
		//	query.LeftJoin(goodsSkuTable).On(query.C(suppliergoodssku.FieldSkuNo), goodsSkuTable.C(goodssku.FieldSkuNo)).
		//		OnP(sql.EQ(goodsSkuTable.C(goodssku.FieldDeleteTime), 0))
		//	query.Where(sql.IsNull(goodsSkuTable.C(goodssku.FieldID)))
		//}
		if in.CategoryIds != nil && len(in.CategoryIds) > 0 {
			categoryIds := make([]any, len(in.CategoryIds))
			for i, v := range in.CategoryIds {
				categoryIds[i] = v
			}
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldCategoryID), categoryIds...))
		}
		if in.Name != "" {
			query.Where(sql.Or(sql.Contains(query.C(suppliergoods.FieldName), in.Name), sql.Contains(supplierGoodsTable.C(suppliergoodssku.FieldName), in.Name)))
		}

		//if in.SupplierId > 0 {
		//	query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldSupplierID), in.SupplierId))
		//}

		if len(in.SupplierGoodsType) > 0 {
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldType), helper.SliceConvertAnySlice(in.SupplierGoodsType)...))
		}
		if in.SupplierGoodsOne != 0 {
			query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldType), in.SupplierGoodsOne))
		}
	})
	//if in.ProfitRateMin > 0 {
	//	q = q.Where(suppliergoodssku.ProfitRateGTE(in.ProfitRateMin))
	//}
	//if in.ProfitRateMax > 0 {
	//	q = q.Where(suppliergoodssku.ProfitRateLTE(in.ProfitRateMax))
	//}
	//if in.SalePriceMin > 0 {
	//	q = q.Where(suppliergoodssku.SalePriceGTE(in.SalePriceMin))
	//}
	//if in.SalePriceMax > 0 {
	//	q = q.Where(suppliergoodssku.SalePriceLTE(in.SalePriceMax))
	//}
	if in.SkuNo != "" {
		q = q.Where(suppliergoodssku.SkuNoEQ(in.SkuNo))
	}
	if len(in.SkuNos) > 0 {
		q = q.Where(suppliergoodssku.SkuNoIn(in.SkuNos...))
	}
	//if in.HaveStock > 0 {
	//	q = q.Where(suppliergoodssku.StockGT(0))
	//}
	var count int
	//if in.ReqPageBo != nil {
	countSql := q.Clone()
	count = countSql.CountX(ctx)
	if count == 0 {
		return 0, nil, nil
	}
	//}
	//
	//if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSaleVolume {
	//	q.Order(ent2.Desc(suppliergoodssku.FieldSalesVolume))
	//} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeNew {
	//	q.Order(ent2.Desc(suppliergoodssku.FieldCreateTime))
	//} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSalePrice {
	//	q.Order(ent2.Asc(suppliergoodssku.FieldSalePrice))
	//} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeProfit {
	//	q.Order(ent2.Desc(suppliergoodssku.FieldProfit))
	//}
	//if in.ReqPageBo != nil {
	//	q.Offset(in.ReqPageBo.GetOffset()).Limit(in.ReqPageBo.GetPageSize())
	//}
	q.WithGoods()
	res := q.AllX(ctx)
	return count, sgsri.ToEntities(res), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) ShopList(ctx context.Context, in *bo.SupplierGoodsSkuBo) (int, []*do.SupplierGoodsSkuDo, error) {
	if err := in.Validate(); err != nil {
		return 0, nil, err
	}
	q := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query()
	q.Where(suppliergoodssku.DeleteTimeEQ(0))
	q.Modify(func(query *sql.Selector) {
		supplierGoodsTable := sql.Table(suppliergoods.Table)
		query.Join(supplierGoodsTable).On(query.C(suppliergoodssku.FieldGoodsID), supplierGoodsTable.C(suppliergoods.FieldID))
		query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldStatus), valobj.SupplierGoodsStatusEnable))
		if in.CategoryIds != nil && len(in.CategoryIds) > 0 {
			categoryIds := make([]any, len(in.CategoryIds))
			for i, v := range in.CategoryIds {
				categoryIds[i] = v
			}
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldCategoryID), categoryIds...))
		}
		if in.Name != "" {
			query.Where(sql.Or(sql.Contains(query.C(suppliergoods.FieldName), in.Name), sql.Contains(supplierGoodsTable.C(suppliergoodssku.FieldName), in.Name)))
		}

		if len(in.SupplierIds) > 0 {
			ids := make([]any, 0, len(in.SupplierIds))
			for _, v := range in.SupplierIds {
				ids = append(ids, v)
			}
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldSupplierID), ids...))
		}

		if len(in.SupplierGoodsType) > 0 {
			query.Where(sql.In(supplierGoodsTable.C(suppliergoods.FieldType), helper.SliceConvertAnySlice(in.SupplierGoodsType)...))
		}
		if in.SupplierGoodsOne != 0 {
			query.Where(sql.EQ(supplierGoodsTable.C(suppliergoods.FieldType), in.SupplierGoodsOne))
		}
	})
	if in.ProfitRateMin > 0 {
		q = q.Where(suppliergoodssku.ProfitRateGTE(in.ProfitRateMin))
	}
	if in.ProfitRateMax > 0 {
		q = q.Where(suppliergoodssku.ProfitRateLTE(in.ProfitRateMax))
	}
	if in.SalePriceMin > 0 {
		q = q.Where(suppliergoodssku.SalePriceGTE(in.SalePriceMin))
	}
	if in.SalePriceMax > 0 {
		q = q.Where(suppliergoodssku.SalePriceLTE(in.SalePriceMax))
	}
	if in.SkuNo != "" {
		q = q.Where(suppliergoodssku.SkuNoEQ(in.SkuNo))
	}
	if len(in.SkuNos) > 0 {
		q = q.Where(suppliergoodssku.SkuNoIn(in.SkuNos...))
	}
	if in.HaveStock > 0 {
		q = q.Where(suppliergoodssku.StockGT(0))
	}
	var count int
	if in.ReqPageBo != nil {
		countSql := q.Clone()
		count = countSql.CountX(ctx)
		if count == 0 {
			return 0, nil, nil
		}
	}

	if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSaleVolume {
		q.Order(ent2.Desc(suppliergoodssku.FieldSalesVolume))
	} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeNew {
		q.Order(ent2.Desc(suppliergoodssku.FieldCreateTime))
	} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeSalePrice {
		q.Order(ent2.Asc(suppliergoodssku.FieldSalePrice))
	} else if in.SearchType == valobj.SupplierGoodsSkuSearchTypeProfit {
		q.Order(ent2.Desc(suppliergoodssku.FieldProfit))
	}
	if in.ReqPageBo != nil {
		q.Offset(in.ReqPageBo.GetOffset()).Limit(in.ReqPageBo.GetPageSize())
	}
	q.WithGoods()
	res := q.AllX(ctx)
	return count, sgsri.ToEntities(res), nil
}

// All 获取所有商品sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) All(ctx context.Context) ([]*do.SupplierGoodsSkuDo, error) {
	rows, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().WithGoods().
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(rows), nil
}
func (sgsri *SupplierGoodsSkuRepoImpl) GetBySkuNoAndCustomerID(ctx context.Context, skuNo []string, customerID int) ([]*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntities(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.SkuNoIn(skuNo...)).
		Where(suppliergoodssku.CustomerIDEQ(customerID)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		AllX(ctx)), nil
}

// UpdateSupplierChannelPrice 更新供应商渠道价
func (sgsri *SupplierGoodsSkuRepoImpl) UpdateSupplierChannelPrice(ctx context.Context, productId string, originPrice, channelPrice float64) (int, error) {
	var (
		profit     float64
		profitRate float64
	)
	if originPrice > 0 && originPrice != channelPrice {
		profit = (originPrice*10000 - channelPrice*10000) / 10000
		profitRate = (profit * 10000) / (originPrice * 10000)
	}

	row := sgsri.data.GetDb(ctx).SupplierGoodsSku.Update().
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		Where(
			suppliergoodssku.ProductID(productId),
			suppliergoodssku.Or(suppliergoodssku.MarketPriceNEQ(originPrice), suppliergoodssku.SupplierPriceNEQ(channelPrice)),
		).
		SetMarketPrice(originPrice).
		SetSalePrice(originPrice).
		SetSupplierPrice(channelPrice).
		SetChannelPrice(channelPrice).
		SetProfit(profit).
		SetProfitRate(profitRate).
		SetUpdateTime(int(time.Now().Unix())).
		SaveX(ctx)
	return row, nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) FindByHytGoodsNum(ctx context.Context, productId string) ([]*do.SupplierGoodsSkuDo, error) {
	d, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.ProductIDEQ(productId)).
		Where(suppliergoodssku.ProductTypeEQ(valobj.VirtualProductTypeHuoYiTong)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(d), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) GetWithGoodsByGoodsId(ctx context.Context, goodsId int) ([]*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntities(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.GoodsIDEQ(goodsId)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().AllX(ctx)), nil
}

// GetWithGoodsByProductId 根据商品id获取商品sku信息
func (sgsri *SupplierGoodsSkuRepoImpl) GetWithGoodsByProductId(ctx context.Context, productId ...string) ([]*do.SupplierGoodsSkuDo, error) {
	return sgsri.ToEntities(sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.ProductIDIn(productId...)).
		Where(suppliergoodssku.DeleteTimeEQ(0)).
		WithGoods().AllX(ctx)), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) FindByHytGoodsNums(ctx context.Context, productId []string) ([]*do.SupplierGoodsSkuDo, error) {
	d, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.ProductIDIn(productId...)).
		Where(suppliergoodssku.ProductTypeEQ(valobj.VirtualProductTypeHuoYiTong)).
		All(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntities(d), nil
}

func (sgsri *SupplierGoodsSkuRepoImpl) FindHytWithSpu(ctx context.Context, productId string) (*do.SupplierGoodsSkuDo, error) {
	d, err := sgsri.data.GetDb(ctx).SupplierGoodsSku.Query().
		Where(suppliergoodssku.ProductIDEQ(productId)).
		Where(suppliergoodssku.ProductTypeEQ(valobj.VirtualProductTypeHuoYiTong)).
		WithGoods().
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(d), nil
}
