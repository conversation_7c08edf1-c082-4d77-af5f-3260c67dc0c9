//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/orderaftersalelog"
	"context"
	"entgo.io/ent/dialect/sql"
)

var orderAfterSaleLogRepoImpl = &OrderAfterSaleLogRepoImpl{}

type OrderAfterSaleLogRepoImpl struct {
	Base[ent2.OrderAfterSaleLog, do.OrderAfterSaleLogDo, ent2.OrderAfterSaleLogQuery]
	data *data.Data
}

// NewOrderAfterSaleLogRepoImpl 创建 OrderAfterSaleLogRepo的实现者
func NewOrderAfterSaleLogRepoImpl(data *data.Data) repository.OrderAfterSaleLogRepo {
	return &OrderAfterSaleLogRepoImpl{data: data}
}

// ToEntity 转换成实体
func (oaslri *OrderAfterSaleLogRepoImpl) ToEntity(po *ent2.OrderAfterSaleLog) *do.OrderAfterSaleLogDo {
	if po == nil {
		return nil
	}
	entity := oaslri.Base.ToEntity(po)
	entity.OrderAfterSale = orderAfterSaleRepoImpl.ToEntity(po.Edges.OrderAfterSale)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (oaslri *OrderAfterSaleLogRepoImpl) ToEntities(pos []*ent2.OrderAfterSaleLog) []*do.OrderAfterSaleLogDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.OrderAfterSaleLogDo, len(pos))
	for k, p := range pos {
		entities[k] = oaslri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (oaslri *OrderAfterSaleLogRepoImpl) Get(ctx context.Context, id int) (*do.OrderAfterSaleLogDo, error) {
	row, err := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query().Where(orderaftersalelog.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return oaslri.ToEntity(row), nil
}

// GetWithEdges 通过 id 获取一条数据，并获取其关联数据
func (oaslri *OrderAfterSaleLogRepoImpl) GetWithEdges(ctx context.Context, id int, reqBo *bo.OrderAfterSaleLogEdgesBo) (*do.OrderAfterSaleLogDo, error) {
	q := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query().Where(orderaftersalelog.ID(id))
	if reqBo.WithOrderAfterSale {
		q.WithOrderAfterSale()
	}

	row, err := q.First(ctx)
	if err != nil {
		return nil, err
	}
	return oaslri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (oaslri *OrderAfterSaleLogRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.OrderAfterSaleLogDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query().Where(orderaftersalelog.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return oaslri.ToEntities(rows), nil
}

func (oaslri *OrderAfterSaleLogRepoImpl) FindByPids(ctx context.Context, pids ...int) ([]*do.OrderAfterSaleLogDo, error) {
	if len(pids) == 0 {
		return nil, nil
	}
	rows, err := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query().Where(orderaftersalelog.PidIn(pids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return oaslri.ToEntities(rows), nil
}

// Create 创建数据
func (oaslri *OrderAfterSaleLogRepoImpl) Create(ctx context.Context, d *do.OrderAfterSaleLogDo) (*do.OrderAfterSaleLogDo, error) {
	row, err := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Create().
		SetOrderID(d.OrderID).
		SetOrderNumber(d.OrderNumber).
		SetPid(d.Pid).
		SetCreateTime(d.CreateTime).
		SetStatus(d.Status).
		SetPlatformStatus(d.PlatformStatus).
		SetUserID(d.UserID).
		SetUserType(d.UserType).
		SetUserName(d.UserName).
		SetContent(d.Content).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return oaslri.ToEntity(row), nil
}

// SearchList 搜索列表
func (oaslri *OrderAfterSaleLogRepoImpl) SearchList(ctx context.Context, reqBo *bo.OrderAfterSaleLogSearchBo) (dos []*do.OrderAfterSaleLogDo, respPage *bo.RespPageBo) {
	q := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query()
	if reqBo.Page != nil {
		oaslri.SetPageByBo(q, reqBo.Page)
		respPage = oaslri.QueryRespPage(ctx, q, reqBo.Page)
	}
	if reqBo.Edges != nil {
		if reqBo.Edges.WithOrderAfterSale {
			q.WithOrderAfterSale()
		}
	}

	pos := q.AllX(ctx)
	dos = oaslri.ToEntities(pos)
	return
}

func (oaslri *OrderAfterSaleLogRepoImpl) GetByPid(ctx context.Context, pid int) ([]*do.OrderAfterSaleLogDo, error) {
	row := oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query().Where(orderaftersalelog.PidEQ(pid)).Order(ent2.Desc(orderaftersalelog.FieldID)).AllX(ctx)
	return oaslri.ToEntities(row), nil
}

func (oaslri *OrderAfterSaleLogRepoImpl) FindRefuseOne(ctx context.Context, pid int) (*do.OrderAfterSaleLogDo, error) {
	return oaslri.ToEntity(oaslri.data.GetDb(ctx).OrderAfterSaleLog.Query().
		Order(orderaftersalelog.ByID(sql.OrderDesc())).
		Where(
			orderaftersalelog.PidEQ(pid),
			orderaftersalelog.PlatformStatusIn(valobj.AfterSalePlatformStatusRefuseToUser, valobj.AfterSalePlatformStatusApproveByPlatformToSupplier),
		).
		FirstX(ctx)), nil
}
