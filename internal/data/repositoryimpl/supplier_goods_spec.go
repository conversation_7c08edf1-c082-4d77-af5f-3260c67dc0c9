//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/suppliergoodsspec"
	"cardMall/internal/data/ent/suppliergoodsspecitem"
	"context"
	"time"
)

var supplierGoodsSpecRepoImpl = &SupplierGoodsSpecRepoImpl{}

type SupplierGoodsSpecRepoImpl struct {
	Base[ent2.SupplierGoodsSpec, do.SupplierGoodsSpecDo, ent2.SupplierGoodsSpecQuery]
	data *data.Data
}

// NewSupplierGoodsSpecRepoImpl 创建 SupplierGoodsSpecRepo的实现者
func NewSupplierGoodsSpecRepoImpl(data *data.Data) repository.SupplierGoodsSpecRepo {
	return &SupplierGoodsSpecRepoImpl{data: data}
}

// ToEntity 转换成实体
func (sgsri *SupplierGoodsSpecRepoImpl) ToEntity(po *ent2.SupplierGoodsSpec) *do.SupplierGoodsSpecDo {
	if po == nil {
		return nil
	}
	entity := sgsri.Base.ToEntity(po)
	entity.GoodsSpecItems = supplierGoodsSpecItemRepoImpl.ToEntities(po.Edges.GoodsSpecItems)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (sgsri *SupplierGoodsSpecRepoImpl) ToEntities(pos []*ent2.SupplierGoodsSpec) []*do.SupplierGoodsSpecDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.SupplierGoodsSpecDo, len(pos))
	for k, p := range pos {
		entities[k] = sgsri.ToEntity(p)
	}
	return entities
}

// GetByName 通过多个 id 获取多条数据
func (sgsri *SupplierGoodsSpecRepoImpl) GetByName(ctx context.Context, supplierId int, name string) (*do.SupplierGoodsSpecDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Query().
		Where(suppliergoodsspec.SupplierIDEQ(supplierId)).
		Where(suppliergoodsspec.IsTplEQ(valobj.SupplierGoodsSpecTplNo)).
		Where(suppliergoodsspec.NameEQ(name)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// GetTplByName 通过多个 id 获取多条数据
func (sgsri *SupplierGoodsSpecRepoImpl) GetTplByName(ctx context.Context, supplierId int, name string) (*do.SupplierGoodsSpecDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Query().
		Where(suppliergoodsspec.SupplierIDEQ(supplierId)).
		Where(suppliergoodsspec.IsTplEQ(valobj.SupplierGoodsSpecTplYes)).
		Where(suppliergoodsspec.NameEQ(name)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// Create 创建数据
func (sgsri *SupplierGoodsSpecRepoImpl) Create(ctx context.Context, d *do.SupplierGoodsSpecDo) (*do.SupplierGoodsSpecDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Create().
		SetSupplierID(d.SupplierID).
		SetName(d.Name).
		SetRemark(d.Remark).
		SetIsTpl(d.IsTpl).
		SetSort(d.Sort).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// GetTpl 通过多个 id 获取多条数据
func (sgsri *SupplierGoodsSpecRepoImpl) GetTpl(ctx context.Context, supplierId int, id int) (*do.SupplierGoodsSpecDo, error) {
	row, err := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Query().
		Where(suppliergoodsspec.SupplierIDEQ(supplierId)).
		Where(suppliergoodsspec.IDEQ(id)).
		Where(suppliergoodsspec.IsTplEQ(valobj.SupplierGoodsSpecTplYes)).
		First(ctx)
	if err != nil {
		return nil, err
	}
	return sgsri.ToEntity(row), nil
}

// UpdateTpl 更新模版
func (sgsri *SupplierGoodsSpecRepoImpl) UpdateTpl(ctx context.Context, supplierId int, id int, name string) error {
	ctn, err := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Update().
		Where(suppliergoodsspec.SupplierIDEQ(supplierId)).
		Where(suppliergoodsspec.IDEQ(id)).
		SetIsTpl(valobj.SupplierGoodsSpecTplYes).
		SetName(name).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
	if err != nil {
		return err
	}
	if ctn == 0 {
		return apierr.ErrorDbNotFound("更新规格模板失败")
	}
	return nil
}

// DelTpl 删除模版
func (sgsri *SupplierGoodsSpecRepoImpl) DelTpl(ctx context.Context, supplierId int, id int) error {
	_, err := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Delete().
		Where(suppliergoodsspec.SupplierIDEQ(supplierId)).
		Where(suppliergoodsspec.IDEQ(id)).
		Where(suppliergoodsspec.IsTplEQ(valobj.SupplierGoodsSpecTplYes)).Exec(ctx)
	if err != nil {
		return err
	}
	return nil
}

// SearchList 搜索列表
func (sgsri *SupplierGoodsSpecRepoImpl) SearchList(ctx context.Context, reqBo *bo.SupplierGoodsSpecSearchBo) (dos []*do.SupplierGoodsSpecDo, respPage *bo.RespPageBo) {
	q := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Query()
	if reqBo.Name != "" {
		q.Where(suppliergoodsspec.NameContains(reqBo.Name))
	}

	if reqBo.IsTpl != nil {
		if *reqBo.IsTpl {
			q.Where(suppliergoodsspec.IsTplEQ(valobj.SupplierGoodsSpecTplYes))
		} else {
			q.Where(suppliergoodsspec.IsTplEQ(valobj.SupplierGoodsSpecTplNo))
		}
	}

	if reqBo.SupplierId > 0 {
		q.Where(suppliergoodsspec.SupplierIDEQ(reqBo.SupplierId))
	}

	if reqBo.WithItem {
		q.WithGoodsSpecItems(func(itemQuery *ent2.SupplierGoodsSpecItemQuery) {
			if *reqBo.IsTpl {
				itemQuery.Where(suppliergoodsspecitem.IsTplEQ(valobj.SupplierGoodsSpecTplYes))
			} else {
				itemQuery.Where(suppliergoodsspecitem.IsTplEQ(valobj.SupplierGoodsSpecTplNo))
			}
		})
	}

	if reqBo.Page != nil {
		sgsri.SetPageByBo(q, reqBo.Page)
		respPage = sgsri.QueryRespPage(ctx, q, reqBo.Page)
	}
	pos := q.AllX(ctx)
	dos = sgsri.ToEntities(pos)
	return
}
func (sgsri *SupplierGoodsSpecRepoImpl) FindById(ctx context.Context, id int) (*do.SupplierGoodsSpecDo, error) {
	d := sgsri.data.GetDb(ctx).SupplierGoodsSpec.Query().Where(suppliergoodsspec.ID(id)).FirstX(ctx)
	return sgsri.ToEntity(d), nil
}
