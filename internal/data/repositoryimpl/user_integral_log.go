//   ██████╗ ██████╗ ██████╗ ███████╗ ██████╗ ███████╗███╗   ██╗
//  ██╔════╝██╔═══██╗██╔══██╗██╔════╝██╔════╝ ██╔════╝████╗  ██║
//  ██║     ██║   ██║██║  ██║█████╗  ██║  ███╗█████╗  ██╔██╗ ██║
//  ██║     ██║   ██║██║  ██║██╔══╝  ██║   ██║██╔══╝  ██║╚██╗██║
//  ╚██████╗╚██████╔╝██████╔╝███████╗╚██████╔╝███████╗██║ ╚████║
//   ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝ ╚═════╝ ╚══════╝╚═╝  ╚═══╝

package repositoryimpl

import (
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/repository"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/userintegrallog"
	"context"
	"entgo.io/ent/dialect/sql"
	"errors"
	"time"
)

type UserIntegralLogRepoImpl struct {
	Base[ent2.UserIntegralLog, do.UserIntegralLogDo, ent2.UserIntegralLogQuery]
	data *data.Data
}

// NewUserIntegralLogRepoImpl 创建 UserIntegralLogRepo的实现者
func NewUserIntegralLogRepoImpl(data *data.Data) repository.UserIntegralLogRepo {
	return &UserIntegralLogRepoImpl{data: data}
}

// ToEntity 转换成实体
func (uilri *UserIntegralLogRepoImpl) ToEntity(po *ent2.UserIntegralLog) *do.UserIntegralLogDo {
	if po == nil {
		return nil
	}
	entity := uilri.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (uilri *UserIntegralLogRepoImpl) ToEntities(pos []*ent2.UserIntegralLog) []*do.UserIntegralLogDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.UserIntegralLogDo, len(pos))
	for k, p := range pos {
		entities[k] = uilri.ToEntity(p)
	}
	return entities
}

// Get 通过 id 获取一条数据
func (uilri *UserIntegralLogRepoImpl) Get(ctx context.Context, id int) (*do.UserIntegralLogDo, error) {
	row, err := uilri.data.GetDb(ctx).UserIntegralLog.Query().Where(userintegrallog.ID(id)).First(ctx)
	if err != nil {
		return nil, err
	}
	return uilri.ToEntity(row), nil
}

// Find 通过多个 id 获取多条数据
func (uilri *UserIntegralLogRepoImpl) Find(ctx context.Context, ids ...int) ([]*do.UserIntegralLogDo, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	rows, err := uilri.data.GetDb(ctx).UserIntegralLog.Query().Where(userintegrallog.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}
	return uilri.ToEntities(rows), nil
}

// Create 创建数据
func (uilri *UserIntegralLogRepoImpl) Create(ctx context.Context, d *do.UserIntegralLogDo) (*do.UserIntegralLogDo, error) {
	row, err := uilri.data.GetDb(ctx).UserIntegralLog.Create().
		SetUserID(d.UserID).
		SetRemark(d.Remark).
		SetType(d.Type).
		SetIntegral(d.Integral).
		SetStatus(d.Status).
		SetIncrSource(d.IncrSource).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		SetExpireTime(d.ExpireTime).
		SetExpireTaskStatus(d.ExpireTaskStatus).
		Save(ctx)
	if err != nil {
		return nil, err
	}
	return uilri.ToEntity(row), nil
}

// CreateBulk 批量创建数据
func (uilri *UserIntegralLogRepoImpl) CreateBulk(ctx context.Context, dos []*do.UserIntegralLogDo) ([]*do.UserIntegralLogDo, error) {
	if len(dos) == 0 {
		return nil, nil
	}
	values := make([]*ent2.UserIntegralLogCreate, len(dos))
	for i, d := range dos {
		values[i] = uilri.data.GetDb(ctx).UserIntegralLog.Create().
			SetUserID(d.UserID).
			SetRemark(d.Remark).
			SetType(d.Type).
			SetIntegral(d.Integral).
			SetStatus(d.Status).
			SetIncrSource(d.IncrSource).
			SetCreateTime(d.CreateTime).
			SetUpdateTime(d.UpdateTime)
	}
	rows, err := uilri.data.GetDb(ctx).UserIntegralLog.CreateBulk(values...).Save(ctx)
	if err != nil {
		return nil, err
	}
	return uilri.ToEntities(rows), nil
}

// Update 更新数据，如果没有更新到数据，返回 0, nil
func (uilri *UserIntegralLogRepoImpl) Update(ctx context.Context, d *do.UserIntegralLogDo) (int, error) {
	return uilri.data.GetDb(ctx).UserIntegralLog.Update().Where(userintegrallog.ID(d.ID)).
		SetUserID(d.UserID).
		SetRemark(d.Remark).
		SetType(d.Type).
		SetIntegral(d.Integral).
		SetStatus(d.Status).
		SetIncrSource(d.IncrSource).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
}

// UpdateV2 更新数据，如果没有更新到数据，返回 0, errors.New("update failed")
func (uilri *UserIntegralLogRepoImpl) UpdateV2(ctx context.Context, d *do.UserIntegralLogDo) (int, error) {
	cnt, err := uilri.data.GetDb(ctx).UserIntegralLog.Update().Where(userintegrallog.ID(d.ID)).
		SetUserID(d.UserID).
		SetRemark(d.Remark).
		SetType(d.Type).
		SetIntegral(d.Integral).
		SetStatus(d.Status).
		SetIncrSource(d.IncrSource).
		SetCreateTime(d.CreateTime).
		SetUpdateTime(d.UpdateTime).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// UpdateByUserId
func (uilri *UserIntegralLogRepoImpl) UpdateByUserId(ctx context.Context, expireTaskStatus valobj.UserIntegralLogExpireTaskStatus, ids []int, userId int) (int, error) {
	cnt, err := uilri.data.GetDb(ctx).UserIntegralLog.Update().Where(userintegrallog.IDIn(ids...)).Where(userintegrallog.UserIDEQ(userId)).
		SetExpireTaskStatus(expireTaskStatus).
		SetUpdateTime(int(time.Now().Unix())).
		Save(ctx)
	if cnt == 0 {
		return cnt, errors.New("update failed")
	}
	return cnt, err
}

// Delete 删除数据
func (uilri *UserIntegralLogRepoImpl) Delete(ctx context.Context, ids ...int) (int, error) {
	if len(ids) == 0 {
		return 0, nil
	}
	//物理删除
	effectCnt, err := uilri.data.GetDb(ctx).UserIntegralLog.Delete().Where(userintegrallog.IDIn(ids...)).Exec(ctx)

	//软件删除
	// nowTime := int(time.Now().Unix())
	// deleteVal := -1
	// effectCnt := uilri.data.GetDb(ctx).UserIntegralLog.Update().
	// 	Where(userintegrallog.IDIn(ids...), userintegrallog.StatusNEQ(deleteVal)).
	//	SetStatus(deleteVal).
	//	SetUpdateTime(nowTime).
	//	SaveX(ctx)
	return effectCnt, err
}

// SearchList 搜索列表
func (uilri *UserIntegralLogRepoImpl) SearchList(ctx context.Context, reqBo *bo.UserIntegralLogSearchBo) (dos []*do.UserIntegralLogDo, respPage *bo.RespPageBo) {
	q := uilri.data.GetDb(ctx).UserIntegralLog.Query()
	if reqBo.Page != nil {
		uilri.SetPageByBo(q, reqBo.Page)
		respPage = uilri.QueryRespPage(ctx, q, reqBo.Page)
	}

	pos := q.AllX(ctx)
	dos = uilri.ToEntities(pos)
	return
}
func (g *UserIntegralLogRepoImpl) UserIntegralCount(ctx context.Context, userIds []int, timeEnd int) ([]*do.UserIntegralNumResult, error) {
	var (
		wantArgs []any
	)

	t1 := sql.Table(userintegrallog.Table).As("uil")

	fieldIn := sql.FieldIn(t1.C(userintegrallog.FieldUserID), userIds...)
	selector := sql.Select(
		t1.C(sql.As(userintegrallog.FieldUserID, "user_id")),
		"SUM(CASE WHEN (CASE WHEN type=1 THEN integral ELSE -integral END) as total_num",
	).From(t1).GroupBy(userintegrallog.FieldUserID)
	fieldIn(selector)
	selector = selector.Where(sql.LTE(t1.C(userintegrallog.FieldCreateTime), timeEnd))
	query, wantArgs := selector.Query()

	result, err := g.data.GetDb(ctx).QueryContext(ctx, query, wantArgs...)
	if err != nil {
		return nil, err
	}
	numResults := make([]*do.UserIntegralNumResult, 0)
	err = sql.ScanSlice(result, &numResults)
	if err != nil {
		return nil, err
	}
	return numResults, nil
}

// FindByExpireTaskStatus 通过多个 id 获取多条数据
func (uilri *UserIntegralLogRepoImpl) FindByExpireTaskStatus(ctx context.Context, expireTaskStatus valobj.UserIntegralLogExpireTaskStatus, userIds []int) ([]*do.UserIntegralLogDo, error) {

	rows, err := uilri.data.GetDb(ctx).UserIntegralLog.Query().Where(userintegrallog.UserIDIn(userIds...)).
		Where(userintegrallog.ExpireTaskStatus(expireTaskStatus)).All(ctx)
	if err != nil {
		return nil, err
	}
	return uilri.ToEntities(rows), nil
}
