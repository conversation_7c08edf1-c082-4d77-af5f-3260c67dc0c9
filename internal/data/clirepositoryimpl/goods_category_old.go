package clirepositoryimpl

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodscategory"
	"cardMall/internal/module/clibiz/repository"
	"context"
)

var goodsCategoryRepoImpl = &GoodsCategoryRepoImpl{}

func NewGoodsCategoryRepoImpl(data *data.Data) repository.GoodsCategoryRepo {
	return &GoodsCategoryRepoImpl{data: data}
}

type GoodsCategoryRepoImpl struct {
	Base[ent.GoodsCategory, do.GoodsCategoryDo, ent.GoodsCategoryQuery]
	data *data.Data
}

// ToEntity 转换成实体
func (g *GoodsCategoryRepoImpl) ToEntity(po *ent2.GoodsCategory) *do.GoodsCategoryDo {
	if po == nil {
		return nil
	}
	entity := g.Base.ToEntity(po)
	entity.Children = goodsCategoryRepoImpl.ToEntities(po.Edges.Children)
	entity.Parent = goodsCategoryRepoImpl.ToEntity(po.Edges.Parent)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (g *GoodsCategoryRepoImpl) ToEntities(pos []*ent2.GoodsCategory) []*do.GoodsCategoryDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.GoodsCategoryDo, len(pos))
	for k, p := range pos {
		entities[k] = g.ToEntity(p)
	}
	return entities
}

func (g *GoodsCategoryRepoImpl) SettingAll(ctx context.Context) ([]*do.GoodsCategoryDo, error) {
	return g.ToEntities(g.data.GetDb(ctx).GoodsCategory.Query().Where(
		goodscategory.Or(
			goodscategory.StatusEQ(valobj.GoodsCategoryStatusDisable.GetInt()),
			goodscategory.RecommendEQ(valobj.GoodsCategoryRecommendYes),
			goodscategory.IndexShowEQ(valobj.GoodsCategoryIndexShowYes),
			goodscategory.SortGT(0),
		),
	).Where(goodscategory.ShopIDGT(0)).AllX(ctx)), nil
}

func (g *GoodsCategoryRepoImpl) FindById(ctx context.Context, id int) (*do.GoodsCategoryDo, error) {
	return g.ToEntity(g.data.GetDb(ctx).GoodsCategory.Query().Where(goodscategory.IDEQ(id)).FirstX(ctx)), nil
}
