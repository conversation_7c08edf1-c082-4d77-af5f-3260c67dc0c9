package clirepositoryimpl

import (
	"cardMall/internal/biz/do"
	"cardMall/internal/biz/valobj"
	"cardMall/internal/data"
	"cardMall/internal/data/ent"
	ent2 "cardMall/internal/data/ent"
	"cardMall/internal/data/ent/goodsbrand"
	"cardMall/internal/module/clibiz/repository"
	"cardMall/internal/server/middleware/authtools"
	"context"
)

func NewGoodsBrandRepoImpl(data *data.Data) repository.GoodsBrandRepo {
	return &GoodsBrandRepoImpl{data: data}
}

type GoodsBrandRepoImpl struct {
	Base[ent.GoodsBrand, do.GoodsBrandDo, ent.GoodsBrandQuery]
	data *data.Data
}

func (g *GoodsBrandRepoImpl) FindByNamesByLoginInfo(ctx context.Context, names []string) []*do.GoodsBrandDo {
	adminLoginInfoBo := authtools.GetLoginInfoX(ctx).ToLoginInfo()
	row := g.data.GetDb(ctx).GoodsBrand.Query().
		Where(goodsbrand.NameIn(names...)).
		Where(goodsbrand.CustomerID(adminLoginInfoBo.CustomerId)).
		Where(goodsbrand.ShopID(adminLoginInfoBo.ShopId)).
		AllX(ctx)
	return g.ToEntities(row)
}

// ToEntity 转换成实体
func (g *GoodsBrandRepoImpl) ToEntity(po *ent2.GoodsBrand) *do.GoodsBrandDo {
	if po == nil {
		return nil
	}
	entity := g.Base.ToEntity(po)
	return entity
}

// ToEntities 转换成实体
// 支持基本类型的值对象
func (g *GoodsBrandRepoImpl) ToEntities(pos []*ent2.GoodsBrand) []*do.GoodsBrandDo {
	if pos == nil {
		return nil
	}
	// 使用循环，以免单个转换有特殊处理，要修改两个地方
	entities := make([]*do.GoodsBrandDo, len(pos))
	for k, p := range pos {
		entities[k] = g.ToEntity(p)
	}
	return entities
}

func (g *GoodsBrandRepoImpl) SettingAll(ctx context.Context) ([]*do.GoodsBrandDo, error) {
	query := g.data.GetDb(ctx).GoodsBrand.Query().
		Where(
			goodsbrand.Or(
				goodsbrand.StatusEQ(valobj.GoodsCategoryStatusDisable.GetInt()),
				goodsbrand.RecommendEQ(valobj.GoodsBrandRecommendYes.GetInt()),
				goodsbrand.SortGT(0),
			),
		).Where(goodsbrand.ShopIDGT(0))
	res := query.AllX(ctx)
	return g.ToEntities(res), nil
}
