package rpcimpl

import (
	"cardMall/internal/biz/rpc"
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"cardMall/internal/data"
	"cardMall/internal/pkg/helper"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"time"
)

type LoginCacheImpl struct {
	log  *log.Helper
	conf *conf.Bootstrap
	data *data.Data
}

func NewLoginCacheImpl(log *log.Helper, conf *conf.Bootstrap, data *data.Data) rpc.LoginCache {
	return &LoginCacheImpl{log: log, conf: conf, data: data}
}

func (l *LoginCacheImpl) SmsErrorCountOverLimit(ctx context.Context, phone string) (bool, error) {
	cacheKey := constants.AdminAuthSmsErrorPrefixTpl.GetKey(ctx, phone, time.Now().Format("20060102"))
	count, err := l.data.Rdb.Exists(ctx, cacheKey).Result()
	if err != nil {
		return false, err
	}
	if count > 0 {
		ctn, err := l.data.Rdb.Get(ctx, cacheKey).Int()
		if err != nil {
			return false, err
		}
		return ctn >= constants.DailySmsMaxErrorCount, nil
	}
	return false, nil
}

func (l *LoginCacheImpl) IncrSmsErrorCount(ctx context.Context, phone string) error {
	cacheKey := constants.AdminAuthSmsErrorPrefixTpl.GetKey(ctx, phone, time.Now().Format("20060102"))
	ctn, err := l.data.Rdb.Incr(ctx, cacheKey).Uint64()
	if err != nil {
		return err
	}
	if ctn == 1 {
		// 设置过期时间
		ttl := helper.TomorrowStartTime() - time.Now().Unix()
		_ = l.data.Rdb.Expire(ctx, cacheKey, time.Duration(ttl)*time.Second).Err()
	}
	return nil
}
