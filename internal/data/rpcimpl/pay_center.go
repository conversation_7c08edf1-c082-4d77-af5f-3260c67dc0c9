package rpcimpl

import (
	"cardMall/internal/biz/bo"
	bizRepo "cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/conf"
	"context"
	"encoding/json"
	"github.com/go-kratos/kratos/v2/log"
	"io"
	"net/http"
	"strings"
)

const PayCenterAddPayConfigUrl = "/api/v1/merchant/create"

type PayCenterRepoImpl struct {
	log  *log.Helper
	conf *conf.Bootstrap
}

func NewPayCenterRepoImpl(conf *conf.Bootstrap, log *log.Helper) bizRepo.PayCenterRepo {
	return &PayCenterRepoImpl{
		conf: conf,
		log:  log,
	}
}

func (p *PayCenterRepoImpl) PayConfigAdd(ctx context.Context, in *bo.PayCenterConfigAddBo) (*acldo.PayConfigAddDo, error) {
	reqData, err := json.Marshal(in)
	if err != nil {
		return nil, err
	}
	rspData, err := p.send(ctx, PayCenterAddPayConfigUrl, string(reqData))
	if err != nil {
		return nil, err
	}

	rsp := &acldo.PayConfigAddDo{}
	err = json.Unmarshal([]byte(rspData), &rsp)
	return rsp, err
}

func (p *PayCenterRepoImpl) send(ctx context.Context, url string, body string) (string, error) {
	u := strings.TrimRight(p.conf.PayCenter.GetDomain(), "/") + url
	p.log.Infof("请求支付中心，地址:%s;参数:%s", u, body)
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, u, strings.NewReader(body))
	if err != nil {
		return "", err
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Id", p.conf.GetRecharge().GetMerchantId())
	req.Header.Set("User-Name", "分销商"+p.conf.GetRecharge().GetMerchantId())
	req.Header.Set("Real-Name", "分销商"+p.conf.GetRecharge().GetMerchantId())

	rsp, err := http.DefaultClient.Do(req)
	if err != nil {
		return "", err
	}
	defer rsp.Body.Close()

	content, err := io.ReadAll(rsp.Body)
	if err != nil {
		return "", err
	}
	return string(content), nil
}
