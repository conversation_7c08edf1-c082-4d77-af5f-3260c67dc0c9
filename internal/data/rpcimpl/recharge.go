package rpcimpl

import (
	"cardMall/api/apierr"
	"cardMall/internal/biz/bo"
	"cardMall/internal/biz/rpc"
	"cardMall/internal/biz/rpc/acldo"
	"cardMall/internal/biz/rpc/calvalobj"
	"cardMall/internal/conf"
	"cardMall/internal/pkg/helper"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	v2api "gitee.com/chengdu_blue_brothers/openapi-go-sdk-v2/api"
	v2notify "gitee.com/chengdu_blue_brothers/openapi-go-sdk-v2/notify"
	v2sdk "gitee.com/chengdu_blue_brothers/openapi-go-sdk-v2/sdk"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk-v2/sdk/requests"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/api"
	"gitee.com/chengdu_blue_brothers/openapi-go-sdk/notify"
	"github.com/go-kratos/kratos/v2/log"
	"net/http"
	"strconv"
	"time"
)

type RechargeAccountType int

var RechargeOrderAccountTypeOther RechargeAccountType = 0
var RechargeOrderAccountTypePhone RechargeAccountType = 1
var RechargeOrderAccountTypeQQ RechargeAccountType = 2

const CodeCreateOrderSuccess = "2000"

type RechargeRepoImpl struct {
	isProd      bool
	timeout     time.Duration
	notifyUrl   string
	notifyUrlV2 string
}

func NewRechargeRepoImpl(conf *conf.Bootstrap) rpc.RechargeRepo {
	return &RechargeRepoImpl{
		isProd:      conf.GetRecharge().GetIsProd(),
		timeout:     conf.GetRecharge().GetTimeout().AsDuration(),
		notifyUrl:   conf.GetRecharge().GetNotifyUrl(),
		notifyUrlV2: conf.GetRecharge().GetNotifyUrlV2(),
	}
}

func (r *RechargeRepoImpl) getAccountType(account string) RechargeAccountType {
	if helper.IsAccountQQ(account) {
		return RechargeOrderAccountTypeQQ
	} else if helper.IsAccountPhone(account) {
		return RechargeOrderAccountTypePhone
	}
	return RechargeOrderAccountTypeOther
}
func (r *RechargeRepoImpl) Recharge(ctx context.Context, in *bo.RechargeOrderBo, reseller *acldo.ResellerDo) error {
	notifyUrl, err := r.getNotifyUrl(ctx)
	if err != nil {
		return err
	}

	client, err := api.NewClient(reseller.GetIdToString(), reseller.SecretKey, r.isProd, r.timeout)
	if err != nil {
		return err
	}
	req := &api.RechargeOrderReq{
		OutTradeNo:      in.OutTradeNo,
		ProductId:       in.ProductId,
		RechargeAccount: in.RechargeAccount,
		AccountType:     int(r.getAccountType(in.RechargeAccount)),
		Number:          1,
		NotifyUrl:       notifyUrl,
		ExtendParameter: "",
	}
	rsp, err := client.RechargeOrder(ctx, req)
	if err != nil {
		return err
	}

	if rsp.Code != CodeCreateOrderSuccess && rsp.Code != "1005" {
		return errors.New(rsp.Message)
	}
	return nil
}

func (r *RechargeRepoImpl) RechargeCard(ctx context.Context, in *bo.RechargeOrderBo, reseller *acldo.ResellerDo) error {
	notifyUrl, err := r.getNotifyUrl(ctx)
	if err != nil {
		return err
	}

	client, err := api.NewClient(reseller.GetIdToString(), reseller.SecretKey, r.isProd, r.timeout)
	if err != nil {
		return err
	}
	req := &api.CardOrderReq{
		OutTradeNo:      in.OutTradeNo,
		ProductId:       in.ProductId,
		Mobile:          "",
		AccountType:     0,
		Number:          1,
		NotifyUrl:       notifyUrl,
		ExtendParameter: "",
	}
	rsp, err := client.CardOrder(ctx, req)
	if err != nil {
		return err
	}

	if rsp.Code != CodeCreateOrderSuccess && rsp.Code != "1005" {
		return errors.New(rsp.Message)
	}
	return nil
}
func (r *RechargeRepoImpl) getNotifyUrl(ctx context.Context) (string, error) {
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	if customerId == 0 {
		return "", apierr.ErrorSystemPanic("获取企业信息失败")
	}
	shopId := isolationcustomer.GetShopIdZero(ctx)
	if shopId == 0 {
		return "", apierr.ErrorSystemPanic("获取商城信息失败")
	}
	if r.notifyUrl == "" {
		return "", apierr.ErrorParam("未配置回调地址")
	}
	return fmt.Sprintf("%s?customerId=%d&shopId=%d", r.notifyUrl, customerId, shopId), nil
}

func (r *RechargeRepoImpl) getNotifyUrlV2(ctx context.Context) (string, error) {
	customerId := isolationcustomer.GetCustomerIdZero(ctx)
	if customerId == 0 {
		return "", apierr.ErrorSystemPanic("获取企业信息失败")
	}
	shopId := isolationcustomer.GetShopIdZero(ctx)
	if shopId == 0 {
		return "", apierr.ErrorSystemPanic("获取商城信息失败")
	}
	if r.notifyUrlV2 == "" {
		return "", apierr.ErrorParam("未配置回调地址")
	}
	return fmt.Sprintf("%s?customerId=%d&shopId=%d", r.notifyUrlV2, customerId, shopId), nil
}

func (r *RechargeRepoImpl) Notify(_ context.Context, req *http.Request, reseller *acldo.ResellerDo) (*acldo.RechargeNotifyDo, error) {
	client := notify.NewNotify(reseller.GetIdToString(), reseller.SecretKey)
	rsp, err := client.ParseAndVerify(req)
	if err != nil {
		return nil, err
	}
	return &acldo.RechargeNotifyDo{
		OutTradeNo:      rsp.OutTradeNo,
		Status:          calvalobj.RechargeOrderStatusObj(rsp.Status),
		RechargeAccount: rsp.RechargeAccount,
		CardCode:        rsp.CardCode.Value(),
	}, nil
}

func (r *RechargeRepoImpl) NotifyV2(_ context.Context, req *http.Request, reseller *acldo.ResellerDo) (*acldo.RechargeNotifyV2Do, error) {
	//client, err := r.getV2Client(reseller.SecretKey)
	notifyObj := v2notify.NewNotify(reseller.GetIdToString(), reseller.SecretKey)
	rsp, err := notifyObj.ParseAndVerify(req)
	if err != nil {
		return nil, err
	}
	res := &acldo.RechargeNotifyV2Do{
		TradeStatus:     rsp.TradeStatus,
		OrderNo:         rsp.OrderNo,
		TradeStateDesc:  rsp.TradeStateDesc,
		MchId:           rsp.MchId,
		OutTradeNo:      rsp.OutTradeNo,
		RechargeAccount: rsp.RechargeAccount,
		UnitPrice:       rsp.UnitPrice,
	}
	for _, card := range rsp.Cards {
		res.Cards = append(res.Cards, &acldo.RechargeNotifyCardDo{
			No:       card.No,
			Pwd:      card.Pwd,
			Deadline: card.Deadline,
			CardType: card.CardType,
			Url:      card.Url,
		})
	}
	return res, nil
}

// getV2Client 获取v2客户端
const openApiV2BaseUrlSandbox = "117.175.169.61:17100"
const openApiV2BaseUrl = "openapi.22233.cn"

func (r *RechargeRepoImpl) getV2Client(secretKey string) (*v2sdk.Client, error) {
	ht := requests.HTTP
	uri := openApiV2BaseUrlSandbox
	if r.isProd {
		ht = requests.HTTPS
		uri = openApiV2BaseUrl
	}
	return v2sdk.NewClientWithAccessKey(secretKey, ht, uri)
}

func (r *RechargeRepoImpl) RechargeV2(ctx context.Context, in *bo.RechargeOrderBoV2, platformReseller *acldo.ResellerDo, fromResellerId int, supplierPrice float64) error {
	notifyUrl, err := r.getNotifyUrlV2(ctx)
	if err != nil {
		return err
	}

	client, err := r.getV2Client(platformReseller.SecretKey)

	if err != nil {
		return err
	}

	attach := map[string]string{
		"from":          strconv.Itoa(fromResellerId),
		"merchantPrice": fmt.Sprintf("%.2f", supplierPrice),
	}

	createReq := &v2api.OrderCreateReq{
		OutTradeNo:      in.OutTradeNo,
		ProductId:       in.ProductId,
		Number:          1,
		NotifyUrl:       notifyUrl,
		RechargeAccount: in.RechargeAccount,
		Extends:         "",
		Attach:          string(helper.IgnoreErr(json.Marshal(attach))),
		// UnitPrice:       0,
		AccountType: int64(r.getAccountType(in.RechargeAccount)),
	}

	resp, errResp, err := v2api.OrderCreate(ctx, client, platformReseller.GetIdToString(), createReq)
	log.Infof("虚拟商品充值结果:%v %v, %v", resp, err, errResp)

	if err != nil || errResp != nil {
		return apierr.ErrorException("充值失败[%v]", createReq)
	}

	return nil
}

// GetProductsV2 获取虚拟商品列表
func (r *RechargeRepoImpl) GetProductsV2(ctx context.Context, platformReseller *acldo.ResellerDo) (*v2api.ProductQueryResp, *v2api.ErrorResp, error) {
	client, err := r.getV2Client(platformReseller.SecretKey)
	if err != nil {
		return nil, nil, err
	}
	return v2api.ProductQuery(ctx, client, platformReseller.GetIdToString())
}
