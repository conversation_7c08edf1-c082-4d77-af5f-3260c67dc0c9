package product

import (
	"cardMall/internal/conf"
	"cardMall/internal/constants"
	"context"
	"github.com/go-kratos/kratos/v2/log"
	"github.com/go-kratos/kratos/v2/middleware/auth/jwt"
	mmd "github.com/go-kratos/kratos/v2/middleware/metadata"
	"github.com/go-kratos/kratos/v2/transport/grpc"
	jwt2 "github.com/golang-jwt/jwt/v4"
	grpc2 "google.golang.org/grpc"
)

type Client struct {
	GrpcClient *grpc2.ClientConn
}

func NewProductClientImpl(c *conf.Bootstrap, log *log.Helper) (*Client, func(), error) {
	serviceConf := c.MicroService.GetProduct()
	serviceName := "product"

	conn, err := grpc.DialInsecure(
		context.Background(),
		grpc.WithMiddleware(
			mmd.Client(),
			jwt.Client(func(token *jwt2.Token) (interface{}, error) {
				return []byte(serviceConf.GetToken()), nil
			}),
		),
		grpc.WithEndpoint(serviceConf.GetAddr()),
		grpc.WithTimeout(serviceConf.Timeout.AsDuration()),
		grpc.WithOptions(grpc2.WithConnectParams(constants.DefaultGrpcConnectParam)),
	)
	//退出时清理资源
	cleanup := func() {
		if conn != nil {
			if err = conn.Close(); err != nil {
				log.Errorf("关闭 %s rpc 连接失败：%+v", serviceName, err)
				return
			}
		}
		log.Infof("关闭 %s rpc连接已完成", serviceName)
	}
	return &Client{GrpcClient: conn}, cleanup, err
}
