package data

import (
	"cardMall/internal/constants"
	"cardMall/internal/pkg/isolationcustomer"
	"context"
	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"fmt"
	"strings"
)

type WherePSetter interface {
	WhereP(ps ...func(*sql.Selector))
}

type CustomerIdSetter interface {
	SetCustomerID(i int)
}

type ShopIdSetter interface {
	SetShopID(i int)
}

func CreateHandler(ctx context.Context, m ent.Mutation, field isolationcustomer.FieldColumn) int {
	// 1. 如果设置了值，则不处理
	if setVal, isSet := m.Field(field.String()); isSet {
		val, ok := setVal.(int)
		if ok {
			return val
		}
		return 0
	}
	// 2. 没有，根据disable来判断
	// 3. 如果禁止，则不处理
	if field.IsDisableByContent(ctx) {
		return 0
	}

	// 4. Set 上 GetIdZero 的值
	var setVal int
	switch field {
	case isolationcustomer.CustomerFieldName:
		setter, haveCustomer := m.(CustomerIdSetter)
		if haveCustomer {
			setVal = isolationcustomer.GetCustomerIdZero(ctx)
			setter.SetCustomerID(setVal)
		}
	case isolationcustomer.ShopFieldName:
		setter, haveShop := m.(ShopIdSetter)
		if haveShop {
			setVal = isolationcustomer.GetShopIdZero(ctx)
			setter.SetShopID(setVal)
		}
	default:
	}
	return setVal
}

func HaveCondition(s *sql.Selector, field isolationcustomer.FieldColumn) bool {
	predicate := s.P()
	if predicate == nil {
		return false
	}
	conditionStr, _ := predicate.Query()

	if strings.Contains(conditionStr, field.ToQueryString()) {
		return true
	}

	if strings.Contains(conditionStr, "."+field.String()) {
		return true
	}

	if !strings.Contains(conditionStr, "_"+field.String()) && strings.Contains(conditionStr, field.String()) {
		return true
	}

	return false
}

func ConditionWhereP(ctx context.Context, s *sql.Selector, field isolationcustomer.FieldColumn) bool {
	// 1. 是否包含字段
	hasField := true
	switch field {
	case isolationcustomer.CustomerFieldName:
		hasField = isolationcustomer.IsCustomerTable(s.TableName())
	case isolationcustomer.ShopFieldName:
		hasField = isolationcustomer.IsShopTable(s.TableName())
	default:
		hasField = false
	}

	if !hasField {
		return true
	}

	// 2. 已经有条件
	if HaveCondition(s, field) {
		return true
	}

	// 3. context是否包含条件
	if !field.IsDisableByContent(ctx) {
		idVal := 0
		switch field {
		case isolationcustomer.CustomerFieldName:
			idVal = isolationcustomer.GetCustomerIdZero(ctx)
			if idVal == constants.DisableCustomerId {
				return true
			}
		case isolationcustomer.ShopFieldName:
			idVal = isolationcustomer.GetShopIdZero(ctx)
		default:
		}
		s.Where(sql.ExprP(fmt.Sprintf("%s.%s=?", s.TableName(), field), idVal))
	} else {
		return true
	}
	return true
}
