server:
  debug: true
  http:
    customerAdmin:
      addr: 0.0.0.0:8200
      timeout: 10s
    applet:
      addr: 0.0.0.0:9600
      timeout: 10s
    admin:
      addr: 0.0.0.0:8000
      timeout: 10s
    supplier:
      addr: 0.0.0.0:8001
      timeout: 10s
    openapi:
      addr: 0.0.0.0:8002
      timeout: 10s
  grpc:
    addr: 0.0.0.0:9000
    timeout: 1s
  env: LOCAL
data:
  database:
    driver: mysql
    source: root:lansexiongdi@tcp(*************:3307)/saas_mall_manager?parseTime=True&charset=utf8mb4&loc=Asia%2FShanghai
    #source: root:root@tcp(127.0.0.1:3306)/card_mall_sass?charset=utf8mb4
    debug: true
  redis:
    addr: *************:6380
    password: "lansexiongdi"
#    password: ""
    select_db: 6
    read_timeout: 10s
    write_timeout: 10s

jwt:
  key: "123456"
  timeout: 86400s #24*60*60分钟
oss:
  access_key: "LTAI5tGGZzjf3tvqWk8SQj2G"
  secret_key: "******************************"
  bucket: "suzhoudao"
  domain: "https://suzhoudao.oss-cn-hangzhou.aliyuncs.com"
  endpoint: "https://oss-cn-hangzhou.aliyuncs.com"
recharge:
  is_prod : false
  merchant_id: "23329"
  secret_key: "8db16e8cc8363ed4eb4c14f9520bcc32"
  timeout: 10s
  api_url: "http://test.openapi.1688sup.cn"
  notify_url: "http://**************:8818/app/v1/notify/recharge"
  notify_url_v2: "http://**************:8818/app/v2/notify/recharge"
payCenter:
  private_key: "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDS41uBRQoFXLCUcgrYLwuC7xMQyfblg05P0Kymo2qlq9pmqZJ9WxDRqaj7TOSHjZTrh6Dt0g+bI40yH/G665W27tr1Pa84Sk1MSsBFa3aD2QWdWEQhp5TdSzA5FSg3SyUlwEJtJnnEhdLKKzRcUv1pnAy4QcL0EgALcl21ipOVHPL/utjV9giZCqMDaXdJS77E0GA1SpHsSlfsdIri7G76N+y6fRYbn+5cSSrvX8SuNzEL60wTsgTRiv2kyf1Ja1G8FXixRapYkLuweSCIO/FZVIu82MVLpKJTQpa9XFYblTi7bE8YtH9A3oVMdnrToVWNiWNRgVyzjvBCyIDtcu4lAgMBAAECggEBALLpkEgrekTyThyiY/DkeK+shFBKB9zPeTDdqJv3KBNFqRRP6KfRCoPVnDcXFQqrYxwvBKi+/d5wWumbkEk0dN+t5LUKkA0BfHKtOFGsye8xv0GuEJ88vV7A2ziSanv+lJVidviDG+tOXXaHEPUCs1tVuLTZlbvbSY5siHPsDSkCy6fRQvSL4Eu0RudyTyo6wqcRelRvtXKP6h1+pmkhVf5t9nbnYghjFuBYHPKvOho94fvfPhbU+j3oCFE5nnPNmjC45RGG7vyRvW6NC9xBTiB9xJRdsXpuIAi7dkYueKGKTGCVUD3e7ESxAI5OH/MEIdDrDTo0kqqSdfN2PsErakECgYEA9EBVNdY25X7Jf4Y+z2124/DAUY37FIIdX+JkE7ehvhAf0BYaYRYJKLGgDnSpsfPXJLhZUJtC1wC9JJDdQnyRnOIOQN/U5JJmpgCn5rkCCxGinvqmUy+xvqHoIsrGoVLDMo/yDmNBgV8l0BrdjL0LXQwGfmiE/FFAxWIiVAcKHD8CgYEA3QgyPWHz73/SslpaNyk2QnjqQjHV9s+X4MWV3LkRrN0cNv5FkRKH99HIlJV4gCga9sUpjCDmvSmQma4ID/k3pMyuDUNZ7sqnpPjGbBUmsqE4yh/R6ZAgcUDOx+w6NvUn26+0qyVbOT4KmodasA26C9uMoIVggdVtLgSd/OhHLJsCgYEAq/PmiUJH34VE4gxmh61ut8Yz/rZP/v4jI+/DuMXcjmvXma0V4SecRxPVtJeZJ/ici/NknYTQp4CTp/x8aGQ+Nw5GLpHl1QzkSxXHHn083QILVJTtJz86iRR+0o+jCsuzNBN2u35RDX0op7tiIcStDVJ7994pUWD6knsl47YY/g0CgYABuuuxwU+Ig2ah0MU+A0uSb6HdzMgQ+4UD1Ggc5zH1OXHcGFdxEEUSMokwaI8d6bJ0VgzCNQu5VVb1bQBRJ7FAz/1Yyzsr/FgdmXjsJao84bgxFNiuzFsEp+42cGnwPjEGrjRq1osFQrAQj5itIh5fs2NBamnb+lC1eIHIFgVgmwKBgHsOaYuBE//jZNQSLutDz+m5saL8qSy/sNDcfwALP52pgv9XHzQDR2/286DQw8r0sHAmREBB+SShceXn+PU7UpIowyFRjzG34OY1RMRs9lS4XsM8ueOOIIf3yelAKTfJCJofw+BdDy53Wj0GNQl5d9j9mNQN+mKgFlyacNvwApSF"
  my_public_key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0uNbgUUKBVywlHIK2C8Lgu8TEMn25YNOT9CspqNqpavaZqmSfVsQ0amo+0zkh42U64eg7dIPmyONMh/xuuuVtu7a9T2vOEpNTErARWt2g9kFnVhEIaeU3UswORUoN0slJcBCbSZ5xIXSyis0XFL9aZwMuEHC9BIAC3JdtYqTlRzy/7rY1fYImQqjA2l3SUu+xNBgNUqR7EpX7HSK4uxu+jfsun0WG5/uXEkq71/ErjcxC+tME7IE0Yr9pMn9SWtRvBV4sUWqWJC7sHkgiDvxWVSLvNjFS6SiU0KWvVxWG5U4u2xPGLR/QN6FTHZ606FVjYljUYFcs47wQsiA7XLuJQIDAQAB"
  public_key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2ZTfQYRAul0eW1XDHL8wwhWGOkAcNnCl1/00nAeTVXcuqjK8afZrayjuOWn1lWcH4A5TODSGujf79MHDoYUONtIrpP22EWfWVRa1KHiw9ds4Pex5ZWlw8DLXBPtr0QE6pNRjL5Q67gEcipSPC5RVTFyng3gRGFWWxaEcmHN87pTHh58PlngVeoFJugfOxMwrS8GHAucA5oHh6io6jeR8CgNhDWy1WYe/+KVU063yPyw2OV1BLYnbXlUDFbcRNZxL5lm51UkQFidDLWkNaZndvOUq+vyDlIsdEJprnz+XgHGGo/SQB20DH9k60913GfDkPLv32f6HE7ROdNt0Z3rFawIDAQAB"
  merchant_id: 6
  domain: "http://**************:11000"
  #domain: "http://127.0.0.1:8100"
  return_url: "/subPackageA/order/index?a=1"
  notify_url: "http://**************:8818/app/v1/notify/pay"
  #微信h5支付统一支付中间页
  wx_h5_pay_url: "http://zy.1688sup.com/proxyPay.html?payUrl="
  #客户端支付页面
  pay_path: "/subPackageA/order/gopay"
wechatApplet:
  app_id: "wx7091092a0e460bab"
  mch_id: "**********"
  secret: "b7e67fa476918e1236a481c408ad69db"
  pay_merchant_id: 9
wechatOfficialAccount:
  appId: "wxe458535b85108afc"
  secret: "21da41caf6604d7d3085f431b2eb8334"
  auth_redirect_uri: "https://cardmallweb.85938.cn/wxAuth.html"
keFu:
  secret_key: "9b100756aee0d26d01ab6a9485ab3cea453a23dc370dee90453964bc930ce5ad"
  base_url: "http://*************:8081/chatIndex"
  backend_url: "http://*************:8081/login"
meiTuan:
  base_url: "https://meituan.openapi.22233.cn"
  auth_token: "Basic dGVzdDoxMjM0NTY="
  applet_pay_path: "applet/pay"
qianZhu:
  base_url: "http://*************:8101"
  auth_token: "Basic dGVzdDoxMjM0NTY="
sms:
  accessKeyId: "LTAI5tGGZzjf3tvqWk8SQj2G" #阿里云短信的accessKeyId
  accessKeySecret: "******************************" #阿里云短信的accessKeySecret
  endpoint: "dysmsapi.aliyuncs.com" #阿里云短信的endpoint
  sign: "蓝色兄弟" #阿里云短信的sign
  loginTemplateId: "SMS_274900579"  #黑名单提醒短信模板id （暂时放这儿，后面东哥会迁走的）
  goodsWarnTemplateId: "SMS_472330184"
site:
  domain: "http://test.sass.1688sup.com/"
  length: 10
  customer_admin: "http://test.sass.1688sup.com"
  saas_supplier: "http://test.sasssupplier.1688sup.com"
  saas_admin: "http://test.sassplatform.1688sup.com"
# 菜鸟物流
caiNiao:
  source_code: "7c36cfc13a1c49073a0d60d1aac79b79"
  secret_key: "We4x6QC944S9rGao90T18CJFYHH7N3j6"
  app_code: "BBrothersCardMall"
  base_url: "https://link.cainiao.com/gateway/link.do"

fileServer:
  attachment: #附件服务
    addr: "http://*************:8004" #地址

aesKey:
  key: 8LcegFCMqJ2GGdUatMsLHiA0I7DJSfyK
  iv:

alipayMiniProgram:
  app_id: "2021004177654013"
  secret: "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQClAiPLiBxIPmDtsJg5IcNh1Gq2qkb4PzsJoEsKAdNKxI8m8DE3TR4iVjtQEjeBmCpJHkx5/HunoEIOLkk36sLxgMqBQEfAv7sE79A0q3dPhJf/RR/UmwVY9hHRCy8dhoILhtFfUjZMGhKaB1PAgwVJYhIGnnFJjvHTSt2DGhrs0qD/sZoSVt8rUDV5HuCXZohsaQA9vHOq8nFAf9Nfjk/oTRiUc7UUZ4zdZnxAZB5wV9dkAdKoRPZCeiJc/SvMgoRTUb9axulLhv9K3oVkvkAlwLVDYT7Ge9+E2Ak1aAr+9ctXLqmUtlDfRvBlZwMWVi2kmCoQVd4ofgzWoWtyhJ7LAgMBAAECggEAIqb/Y2fTIKzQ6Dm0zhK0GGBmhLPfsZR9zni2KYTWnZYHxhKdE1MDqAxZ6mfY9kNBZheR0fBe+4XhOV8pCzAm/lagsGISb30URmYiYF098hoTXY1jsN9ZUSRj+3WQ0G+F8eu/HniCZJr5GxW81ent+0o4JOGzQtJdIJVRTY3255LBWlQjxI0iP0tRsR9VqSVOszYQKlxkp8bA8ihx/bq1vjEyz6sU5lNRTrrwf+TSPZZ+XesbZK2vAm+y5i1UQ6of7RInpAinCt/nflk58L9R3bpdkckD1HVlM74AxrTRMXMs9kVbaCFt3vSuPQSpr/S/IbjDTCKiaU1FEZPXx6UQ+QKBgQDlYMwRFJlkipbuYt4M7CZoLV6obC1hmh2AcGVOGt4Fx8gEJRGbQkJV5+JGpKdMmuEB/xx6aWTDan5bCOU/pkMOqIUfOIW4I3LvsaPX6LHJK9k8vOOzv0jpbNKUv8aKPOAwz7c3SDFeEg+P6ZykHcyDCmf64hD8nQF2qR+AIR3ZnQKBgQC4KM+VqfcDsApD0tgcCPE5oFz1utGMfeEl2EfUL/ke5qxO9H22UZe1SxyeUG+3usb8AFUN2OMkr7f5JKhd0Vj3+rS6p52PqAho/QINVXwc2vxVy5SruBrqw3WNEtA/hVi5u4+YlqVQPm2JyFCw/Otfq55ywjNsawjYyhIC6b5BhwKBgQC8cwlVIxpfGumjksCh10Y6osO5FZ83J9oDEtR4FeTA1QEEPrw3VXmQgIFLzqzQaBJihIiOHZa2YgDLpARMhblkCpnMK7DYtu8P9F8K3Ndr0sWYfAuLOqWZeVmM8f4YxqjeEVmSZpKAmdAFSUXzI7RJGLySxzZzhD6WkZ4as5qFlQKBgFsTO84dd17ogbsXG++JcJr6Nt8D3DwpyN4pfyRt8TsaA18sxLsU0P1Ev0uAJi/r/BqGo4msmHD9QfPB8E4tiV1iLF0fJen0xOv/LDOh8jZnCmH3w3fFHfK6ZXk2M60makG4g4SIBAuSRCPe4CEXg+QVgM/qQPo+WOq1zICKTqAjAoGAMrVpKiTO7qiN1DixiBNYV/ffSvjG5BqHd3tlTCOK67qaK7rAFa/3InQYmKMVsFjzTHePT0SxO6ZMqO6ibpVjV7ZTyKgpCnKGn9uDlPtwYzI6ZvOQPG0ZhsqeZQkNcb/ME39B2n/SXOTVfOO69c9dn6DwUj0v0+XSlktVfAWUCjI="
  pay_merchant_id: 340
  seller_id: "2088031697375328"

sharding:
  - source: root:lansexiongdi@tcp(*************:3307)/saas_mall_official?parseTime=True&charset=utf8mb4&loc=Asia%2FShanghai
    debug: true
    poolSize: 10
    customerId:
      min: 1
      max: 1

#  - source: root:lansexiongdi@tcp(*************:3307)/card_mall_saas?parseTime=True&charset=utf8mb4
#    debug: true
#    poolSize: 10
#    customerId:
#      min: 2
#      max: 4
      
#  - source: root:lansexiongdi@tcp(*************:3307)/saas_mall_cloud?charset=utf8mb4
#    debug: true
#    poolSize: 10
#    customerId:
#      min: 5
#      max: 6
#
#  - source: root:lansexiongdi@tcp(*************:3307)/saas_mall_cloud2?charset=utf8mb4
#    debug: true
#    poolSize: 10
#    customerId:
#      min: 7
#      max: 10

  - source: root:lansexiongdi@tcp(*************:3307)/card_mall_saas?parseTime=True&charset=utf8mb4&loc=Asia%2FShanghai
    debug: true
    poolSize: 11
    customerId:
      min: 11
      max: 0
      
microService:
  reseller:
    addr: 192.168.6.93:30402
    #    addr: 127.0.0.1:9002
    token: D#W@gdS@
    timeout: 8s
  product:
    addr: 192.168.6.93:30406
    timeout: 8s
    token:
  card:
    addr: *************:18001
    token: D#W@gdS@
    timeout: 50s

# 物流服务
logisticsService:
  addr: "http://*************:17300"
  notify_url: "http://**************:8818/app/v1/logistics/notify"

orderTcc:
  payQueryUrl: http://*************:9600/app/v1/order/pay/query
  refundQueryUrl:

#//  string base_url = 1;
#  string appId = 2;
#  string rsaPublicKey = 3;
#  string ourRsaPublicKey = 4;
#  string ourRsaPrivateKey = 5;
hyt:
  base_url: "http://*************:8109"
  appId: "a7bbacbd42c3469ead735c00dc1bb510"
  rsaPublicKey: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA0+1aaSbKipRk3Z3I7o5lTgy3DPyy6cvTK9S44wnEK0V3UFThxDz5GcUyHnsPutkRQWyB+YUXKEiRle47Mqi2Cr4l//K2JkXg0hlZpgOL06d9EHK9XydAVDDg0kNmAJBR7w+I4aGbKtMef+wtcTtvBX2Wv7U762PhKyS8s273Us0+Hisj34LWKiIGPG7Ijo78eSR65V2HfxSZNh7j4hzCE2BokVrK7oUh3irCM2Piz41Nb/CH8wA9Kw6w9TwuuDatMd7XdV8oreEbLF6zVcr/D22/Bs/O9IHV9iUbwpuq2PWLgTNyVB3kjGUyA028MoQXyhlU9fR+xrYgn0SszbvKJQIDAQAB"
  ourRsaPublicKey: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA047tRkLeOjsMSDonGbfUTbkYWi7OySFsc3o+lmE+a3Cd2lVHOZUnN6XqMC2T7BpPkRuw/lm6r3fQkrOb7tFWqv9zylVzl9wrPvtTFBWLHZu/DdSSQQI/tHoAZt8mfeB6kYOH/JPj2Caf/3bW12BairEt+009hteUssm3ab49ezyoyR3xr7NGthDUXR9LELkd//1fcChwlUzci2snoXygNSS32QJ6xo3M38Qjef8T1l8EXwjFIkZbabkiaHhY3vHdJ9tAchTh9aq8tywW3wyNtGIlZQZjjZ2FzJIwOzxRk3+X3GONRx3KNuHM/+N872Lmunuv5Z0Om0ZEyKIssWIhnwIDAQAB"
  ourRsaPrivateKey: "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDTju1GQt46OwxIOicZt9RNuRhaLs7JIWxzej6WYT5rcJ3aVUc5lSc3peowLZPsGk+RG7D+Wbqvd9CSs5vu0Vaq/3PKVXOX3Cs++1MUFYsdm78N1JJBAj+0egBm3yZ94HqRg4f8k+PYJp//dtbXYFqKsS37TT2G15Syybdpvj17PKjJHfGvs0a2ENRdH0sQuR3//V9wKHCVTNyLayehfKA1JLfZAnrGjczfxCN5/xPWXwRfCMUiRltpuSJoeFje8d0n20ByFOH1qry3LBbfDI20YiVlBmONnYXMkjA7PFGTf5fcY41HHco24cz/43zvYua6e6/lnQ6bRkTIoiyxYiGfAgMBAAECggEALUEeqm/1X5lv+CdQY+T56iJU/j2TCF04+L7Gkp58nrj5W1HwqXWpbPL11JZ7VP8P1CeLRe1lzNEhcbAwA/cNxghc+YQr9Oko6xER5XLyTkQZq1i0bGmRvQZ3/Ylwj26saWIsfbAZBgzFHv2J2YqH87EQ9/e+2JBXA6O4s0r5ldmxXb0op1YfAiLhlJoro/+rZ2W8ccTrZaqvqJZ4/HAmJFvMbplZ8MZ5T8toOzxv/01qVT58KQKLWPn2R3m99U6H6pLR1ezhQPpnYzt7j2ieCHROhk0mFSFk6ydH3U7KS/mQg63pMA61oaYJpf6EnKiWSptjjuM93+Mgzqli80H32QKBgQDuUC3h2EHMVOpPoniQDn1FziCbBgKR8JnJl3p4QeLI7JAfkCtkLu25mzJwNFMrO2rlgWf+DMqHS1EcFE9orYxi0DpXLbQtZvdtxfRWMyKuiQLqYP9zQzH9BEucJXTBdX7cHXfdf/I3VACvy/c2Y2fJ3SUOsFjnhVXhycJRv26XBQKBgQDjQmtVki8FLRcvOPwmlK5HRlk8lKJsqoqewOqvW1f7P4+Fq1yji82LHARJgugOTPQgkfnwPCpu3uOg2EJUz07ZOWSt3nNPVvS/tcyQkwCd506rEmw07a6tyYg1OiAGsJFQg10uRaW1ZJMUZoVdOUFYX5l2MtKqUxzy7p5g7pxvUwKBgCw/JsWv6UeO3JI8kDXu+sNjKhqeWwvNqEANp+d8FCdKBNhXZDcNn4W+kvbSQD7eoVJLXIuGxHOqQDz+aF//GPmfv7U//xwO6hGvl1YJ2H6fYbMynzs/VqCrk35CVhM1RYCI2CCACPDM3/PD4vpuKm8IaED57QfPccqiMzoKWPYhAoGAJwyZntmAhNYrnqye/w4bKBoZaURs6FkheofRi5IZew1/3ssEHwH9roKA5hS9RP/KDvKmTLY54FUiLdgbBbGdbcvj1dBcZeyeH3bgkJMBzyAYr2J5/1o74pa3ddXn/N5ICdqYn0POZm3CrAW1ssAMz1+V4e53rlabqc6Ank1BygsCgYEAgUFen4/gGbWkzQmCUfb7ub9BPR5uvlkS3jTghY1/A+mLAT/uWVWm08pyedD79r8xxs/49FTd4hRovadl9KlhxKqw+8IhKO0VpJ/cuib/dWy4zEm+75LUW7B36Ty6r4GimR4YtvWq9/MxUYp3nQe1Dg/twSQRLSPV7diU0w1fork="
  customerId: 1
  shopId: 0
  supplierId: 3

danGaoShuShu:
  base_url: "http://*************:8102"
  auth_token: "Basic dGVzdDoxMjM0NTY="
  h5_pay_path: "subPackageA/goods/sceneConfirm"

uploadExcel:
  supplierGoodsMaxUploadNum: 1000

openApi:
  whitelistSign: "123456"
  timestampDuration: 86400 #请求时间戳有效秒数
  shopSecretDataSecret: "dY2Nv9SlpmTeBjYMxUi14dxGfuVHcqlp" #店铺解密密钥

#活动配置
activity:
  skuDetailAccess: 10 #sku详情访问流量阈值
  skuDetailAccessTimeOut: 10s #sku详情请求超时时间
  orderCreateAccess: 10 #订单创建访问流量阈值
  orderCreateAccessTimeOut: 10s #订单创建请求超时时间