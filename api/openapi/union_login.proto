syntax = "proto3";

package api.openapi;

import "google/api/annotations.proto";
//import "validate/validate.proto";
import "buf/validate/validate.proto";

option go_package = "cardMall/api/openapi;openapi";
option java_multiple_files = true;
option java_package = "api.openapi";

// UnionLogin 联合登录
service UnionLogin {
  //获取各级地区
  rpc Get(GetUnionLoginReq) returns (GetUnionLoginResp) {
    option (google.api.http) = {
      post: "/openapi/v1/user/unionLogin======"
      body: "*"
    };
  }
}

// GetUnionLoginReq 联合登录请求
message GetUnionLoginReq {
  // 页面路径
  string path = 1 [(buf.validate.field).cel = {message: "path长度不能超过1024个字符",expression: "this.size() <= 1024"}];

  // 用户id
  string uniqueUserId = 2 [(buf.validate.field).cel = {message: "第三方唯一用户ID不能为空，或超过128个字符",expression: "this.size() > 0 && this.size() <= 128"}];

  // 用户昵称
  string nickName = 3 [(buf.validate.field).cel = {message: "用户昵称不能为空，或超过36个字符",expression: "this.size() > 0 && this.size() <= 36"}];

  // 用户手机号
  string mobile = 4;

  // 用户头像
  string avatar = 5 [(buf.validate.field).cel = {message: "头像链接不能超过255个字符",expression: "this.size() >= 0 && this.size() <= 255"}];
}

// GetUnionLoginResp 联合登录响应
message GetUnionLoginResp {
  // url 跳转地址
  string url = 1;
}
