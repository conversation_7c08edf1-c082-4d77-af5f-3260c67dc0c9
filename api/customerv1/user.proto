syntax = "proto3";

package api.customerv1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/customerv1;customerv1";

//用户-B端
service User {
  //列表-分页
  rpc List(UserListReq) returns (UserListRsp) {
    option (google.api.http) = {get: "/customer/v1/user/list"};
  }

}

message UserListReq {
  int32 page = 1;
  int32 page_size = 2;
  //用户ID
  int32 id = 4;
  //手机号
  string phone_number = 6;

  string username = 10;
  // 商城id
  int32 shop_id = 17;
}

message UserListRsp {
  message user {
    //用户ID
    int32 id = 1;
    //微信openid
    string wx_open_id = 2;
    //昵称
    string nick_name = 3;
    //头像
    string avatar_url = 4;
    //用户积分
    int32 integral = 5;
    //冻结积分
    int32 freeze_integral = 6;
    //注册时间
    string create_time = 7;
    //手机号
    string phone_number = 8;
    string username = 10;

    // 商城id
    int32 shop_id = 17;
    // 商城名称
    string shop_name = 18;

    string   wx_official_open_id = 27;

    string     alipay_open_id = 28;
  }
  repeated user list = 1;
  int32 count = 2;
  int32 page = 3;
}
