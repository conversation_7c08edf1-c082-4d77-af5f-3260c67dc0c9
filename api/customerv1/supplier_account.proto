syntax = "proto3";


package api.customerv1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/customerv1;customerv1";

//供应商管理-B端
service SupplierAccount {
  //供应商账号列表-分页
  rpc AccountList(SupplierAccountListReq) returns (SupplierAccountListRsp) {
    option (google.api.http) = {get: "/customer/v1/supplierAccount/list"};
  }

  //新增供应商账号
  rpc AccountAdd(SupplierAccountAddReq) returns (SupplierAccountAddRsp) {
    option (google.api.http) = {
      post: "/customer/v1/supplierAccount/add"
      body: "*"
    };
  }

  //修改供应商账号
  rpc AccountUpdate(SupplierAccountUpdateReq) returns (SupplierAccountUpdateRsp) {
    option (google.api.http) = {
      post: "/customer/v1/supplierAccount/update"
      body: "*"
    };
  }

  //删除供应商账号
  rpc AccountDel(SupplierAccountDelReq) returns (SupplierAccountDelRsp) {
    option (google.api.http) = {
      post: "/customer/v1/supplierAccount/del"
      body: "*"
    };
  }
}

message SupplierAccountListReq {
  //列表页码
  int32 page = 1;
  //每页条数
  int32 page_size = 2;
  //供应商ID
  int32 supplier_id = 3;
  //账号
  string account = 4;
  //状态:1=启用,2=禁用
  int32 status = 5;
  //手机号
  string phone_number = 6;
}

message SupplierAccountListRsp {
  message SupplierAccountItem {
    //供应商账号ID
    int32 id = 1;
    //供应商ID
    int32 supplier_id = 2;
    //供应商名称
    string supplier_name = 3;
    //账号
    string account = 4;
    //手机号
    string phone_number = 5;
    //状态:1=启用,2=禁用
    int32 status = 6;
    //创建时间
    string create_time = 7;
    string admin_url = 8;
  }
  repeated SupplierAccountItem list = 1;
  int32 count = 2;
  int32 page = 3;
}

message SupplierAccountAddReq {
  //供应商ID
  int32 supplier_id = 1;
  //账号
  string account = 2;
  //密码
  string password = 3;
  //手机号
  string phone_number = 4;
  //状态:1=启用,2=禁用
  int32 status = 5;
  //确认密码
  string confirm_password = 6;
}

message SupplierAccountAddRsp {
  //数据ID
  int32 id = 1;
}

message SupplierAccountUpdateReq {
  //供应商账号ID
  int32 id = 1;
  //账号
  string account = 2;
  //密码
  string password = 3;
  //手机号
  string phone_number = 4;
  //状态:1=启用,2=禁用
  int32 status = 5;
  //确认密码
  string confirm_password = 6;
}

message SupplierAccountUpdateRsp {
  //影响数据条数
  int32 effect_row = 1;
}

message SupplierAccountDelReq {
  int32 id = 1;
}

message SupplierAccountDelRsp {
  //影响数据条数
  int32 effect_row = 1;
}
