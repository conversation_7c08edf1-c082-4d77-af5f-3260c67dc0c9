syntax = "proto3";

package api.common;

import "validate/validate.proto";

option go_package = "cardMall/api/common;common";

// 空请求或返回
message Empty {}

// 空请求
message ReqEmpty {}

// 空返回
message RespEmpty {}

// ReqPage 分页请求参数
message ReqPage {
  int32 page = 1 [(validate.rules).int32 = {
    gte: 1
    lte: 100
  }]; //页码
  int32 page_size = 2 [(validate.rules).int32 = {
    gte: 1
    lte: 10000
  }]; //每页数量
}

// ReqPage 分页请求参数
message RespPage {
  int32 page = 1; //页码
  int32 page_size = 2; //每页数量
  int32 total = 3; //总数
}
