syntax = "proto3";

package api.common;

import "google/api/annotations.proto";
import "common/common.proto";

option go_package = "cardMall/api/common;common";

//登录-B端
service BackendLoginSrv {
  //登录
  rpc BackendLogin(BackendLoginReq) returns (BackendLoginRsp) {
    option (google.api.http) = {
      post: "/admin/v1/auth/login"
      body: "*"
    };
  }
  // sendSms
  rpc SendSms(SendSmsReq) returns (SendSmsRsp) {
    option (google.api.http) = {
      post: "/admin/v1/auth/sendSms"
      body: "*"
    };
  }
  //登录
  rpc BackendSmsLogin(BackendSmsLoginReq) returns (BackendSmsLoginRsp) {
    option (google.api.http) = {
      post: "/admin/v1/auth/smsLogin"
      body: "*"
    };
  }
  //登录
  rpc BackendUserInfo(BackendUserInfoReq) returns (BackendUserInfoRsp) {
    option (google.api.http) = {get: "/admin/v1/admin/info"};
  }
  rpc UpdatePwd(UpdatePwdReq) returns (RespEmpty) {
    option (google.api.http) = {
      post: "/admin/v1/admin/updatePwd"
      body: "*"
    };
  }

  rpc BackendLogout(BackendLogoutReq) returns (BackendLogoutResp) {
    option (google.api.http) = {
      post: "/admin/v1/auth/logout"
      body: "*"
    };
  }

  rpc LoginPageSetting(LoginPageSettingReq) returns (LoginPageSettingResp) {
    option (google.api.http) = {
      get: "/admin/v1/login/setting"
    };
  }

  rpc NeeModifyPwdSetting(ReqEmpty) returns (RespEmpty) {
    option (google.api.http) = {
      get: "/admin/v1/admin/neeModifyPwdSetting"
    };
  }
}

message BackendLoginReq {
  //账号
  string account = 1;
  //密码
  string password = 2;
  //client
  int32 client = 3;
}


message LoginSuccessData {
  //用户ID
  int32 admin_id = 1;
  //用户名
  string name = 2;
  //登录token
  string token = 3;
  // 1.管理员2.供应商3.企业管理员4.企业供应商5.平台管理员6.平台供应商
  int32 admin_type = 4;
  // shop_id
  int32 shop_id = 5;
  // customer_id
  int32 customer_id = 6;
  // sms_key 登录不成功时需要验证码
  string sms_key = 7;
  // sms_phone 接受短信的手机号，加密的
  string sms_phone = 8;
  // url_suffix
  string url_suffix = 9;
  // disable 1.启用 2.禁用
  int32 status = 10;
  // account 账号
  string account = 11;
  string admin_type_text = 12;
  string show_name = 13;
  int32 supplier_id = 14;
}

message BackendLoginRsp {
  repeated LoginSuccessData items = 1;
}
message BackendUserInfoReq {}

message SendSmsReq{
  // 手机号登录时传递
  string phone = 1;
  // 账号密码登录不成功后传递
  string sms_key = 2;
  int32 client = 3;
}
message SendSmsRsp{
  string sms_key = 1;
}

message BackendSmsLoginReq{
  // sms_key 登录不成功时需要验证码
  string sms_key = 7;
  // 验证码
  string sms_code = 8;
  //client
  int32 client = 3;
}
message BackendSmsLoginRsp {
  repeated LoginSuccessData items = 1;
}

message BackendUserInfoRsp {
  string name = 2;
  // 权限列表
  repeated MenuTree list_menu_tree = 1;
  // 权限项
  message MenuTree {
    int32 id = 14; //
    string menu_name = 1; // '菜单名称',
    int32 parent_id = 2; //'父菜单ID，一级菜单为0',
    string menu_path = 3; // '前端路由',
    string menu_icon = 4; //'菜单图标',
    int32 menu_sort = 5; //'菜单排序',
    int32 menu_type = 6; //'类型 1：顶部菜单,2：二级菜单,3：按钮',
    string api_path = 7; //'后端api的path地址',
    int32 api_method = 8; // '后端 api请求的 http method:1-GET,2-POST,3-PUT,4-DELETE',
    string front_component = 9; //'前端组件',
    string front_url = 10; //'前端路由',
    int32 process_type = 11; //'处理类型，按二进制位表示，第1位-需要登录，2-需要鉴权，3-需要日志，4-需要显示',
    // 权限 处理类型值
    repeated int32 processTypeValues = 13;
  }
  // userId
  int32 id = 3;
  // userName
  string username = 4;
  // 1.管理员2.供应商3.企业管理员4.企业供应商5.平台管理员6.平台供应商
  int32 admin_type = 7;
  // shop_id
  int32 shop_id = 5;
  // customer_id
  int32 customer_id = 6;
  int32 is_root = 8;
  bool need_modify_pwd = 9;
  int32 supplier_id = 10;
  string settingName = 11;
  string icon = 12;
  string logo = 13;
  map<string,string> shop_open_scene= 14;
}

message BackendLogoutReq {}
message BackendLogoutResp {}

message LoginPageSettingReq {
}
message LoginPageSettingResp {
  string icon = 1;
  string logo = 2;
  string bg_image = 3;
  string name = 4;
  int32 customer_id = 5;
  string prompt = 6;
  string name_color = 7;
}

message UpdatePwdReq {
  string pwd = 1;
}