syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/adminv1;adminv1";

//积分配置-B端
service IntegralConfig {
  //获取配置
  rpc Query(IntegralConfigQueryReq) returns (IntegralConfigQueryRsp) {
    option (google.api.http) = {get: "/admin/v1/integral/config"};
  }

  //新增
  rpc Add(IntegralConfigAddReq) returns (IntegralConfigAddRsp) {
    option (google.api.http) = {
      post: "/admin/v1/integral/config/add"
      body: "*"
    };
  }

  //修改
  rpc Update(IntegralConfigUpdateReq) returns (IntegralConfigUpdateRsp) {
    option (google.api.http) = {
      post: "/admin/v1/integral/config/update"
      body: "*"
    };
  }
}

message IntegralConfigQueryReq {}

message IntegralConfigQueryRsp {
  //ID
  int32 id = 1;
  // 汇率，xx积分=1元
  int32 exchange_rate = 2;
  // 积分名称
  string name = 3;
  // 抵扣类型：1=订单金额抵扣，2=利润部分抵扣
  int32 deduction_type = 4;
  // 最大抵扣百分比
  double deduction_rate = 5;
  // 过期策略类型：1=永不过期，2=相对年逐笔过期，3=相对年所有清零
  int32 expire_type = 6;
  // 过期策略：xxx年后过期
  int32 expire_year = 7;
  // 是否开启积分+钱购
  int32 integral_cash = 8;
  // 是否开启积分兑换
  int32 integral_exchange = 9;
  // 是否展示积分商城入口
  int32 integral_shop_status = 10;
}

message IntegralConfigAddReq {
  // 汇率，xx积分=1元
  int32 exchange_rate = 1;
  // 积分名称
  string name = 2;
  // 抵扣类型：1=订单金额抵扣，2=利润部分抵扣
  int32 deduction_type = 3;
  // 最大抵扣百分比
  double deduction_rate = 4;
  // 过期策略类型：1=永不过期，2=相对年逐笔过期，3=相对年所有清零
  int32 expire_type = 5;
  // 过期策略：xxx年后过期
  int32 expire_year = 6;
  // 是否开启积分+钱购
  int32 integral_cash = 7;
  // 是否开启积分兑换
  int32 integral_exchange = 8;
  // 是否展示积分商城入口
  int32 integral_shop_status = 9;
}

message IntegralConfigAddRsp {
  //ID
  int32 id = 1;
}

message IntegralConfigUpdateReq {
  //ID
  int32 id = 1;
  // 汇率，xx积分=1元
  int32 exchange_rate = 2;
  // 积分名称
  string name = 3;
  // 抵扣类型：1=订单金额抵扣，2=利润部分抵扣
  int32 deduction_type = 4;
  // 最大抵扣百分比
  double deduction_rate = 5;
  // 过期策略类型：1=永不过期，2=相对年逐笔过期，3=相对年所有清零
  int32 expire_type = 6;
  // 过期策略：xxx年后过期
  int32 expire_year = 7;
  // 是否开启积分+钱购
  int32 integral_cash = 8;
  // 是否开启积分兑换
  int32 integral_exchange = 9;
  // 是否展示积分商城入口
  int32 integral_shop_status = 10;
}

message IntegralConfigUpdateRsp {
  //影响行数
  int32 effect_row = 1;
}
