syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/adminv1;adminv1";

//轮播图-B端
service Banner {
  //列表
  rpc List(BannerListReq) returns (BannerListRsp) {
    option (google.api.http) = {get: "/admin/v1/banner/list"};
  }

  //新增
  rpc Add(BannerAddReq) returns (BannerAddRsp) {
    option (google.api.http) = {
      post: "/admin/v1/banner/add"
      body: "*"
    };
  }

  //编辑
  rpc Update(BannerUpdateReq) returns (BannerUpdateRsp) {
    option (google.api.http) = {
      post: "/admin/v1/banner/update"
      body: "*"
    };
  }

  //删除
  rpc Del(BannerDelReq) returns (BannerDelRsp) {
    option (google.api.http) = {
      post: "/admin/v1/banner/del"
      body: "*"
    };
  }

  //启用禁用
  rpc UpdateStatus(BannerUpdateStatusReq) returns (BannerUpdateStatusRsp) {
    option (google.api.http) = {
      post: "/admin/v1/banner/updateStatus"
      body: "*"
    };
  }

  //详情
  rpc Detail(BannerDetailReq) returns (BannerDetailRsp) {
    option (google.api.http) = {get: "/admin/v1/banner/detail"};
  }
}

message BannerListReq {
  int32 page = 1;
  int32 page_size = 2;
}

message BannerListRsp {
  message BannerListItem {
    //数据ID
    int32 id = 1;
    //名称
    string name = 2;
    string image = 3;
    //排序
    int32 sort = 4;
    //状态:0=禁用,1=启用
    int32 status = 5;
    //跳转地址
    string url = 6;
    //创建时间
    string create_time = 7;
    //修改时间
    string update_time = 8;
    //关联类型
    int32 relation_type = 9;
    //关联节点
    string relation_value = 10;
  }
  repeated BannerListItem list = 1;
  int32 page = 2;
  int32 count = 3;
}

message BannerAddReq {
  //名称
  string name = 1;
  //图片
  string image = 2;
  //排序
  int32 sort = 3;
  //状态:0=禁用,1=启用
  optional int32 status = 4;
  //关联类型 0=无，1=类目，2=品牌，2=积分商城，4=链接
  int32 relation_type = 5;
  //关联节点
  string relation_value = 6;
  //关联节点ID 关联类型是 1=类目，2=品牌时必传
  repeated int32 relation_value_ids = 7;
}

message BannerAddRsp {
  int32 id = 1;
}

message BannerUpdateReq {
  //数据ID
  int32 id = 1;
  //名称
  string name = 2;
  //图片
  string image = 3;
  //排序
  int32 sort = 4;
  //状态:0=禁用,1=启用
  optional int32 status = 5;
  //关联类型
  int32 relation_type = 6;
  //关联节点
  string relation_value = 7;
  //关联节点ID 关联类型是 1=类目，2=品牌时必传
  repeated int32 relation_value_ids = 8;
}

message BannerUpdateRsp {
  int32 effect_row = 1;
}

message BannerDelReq {
  int32 id = 1;
}

message BannerDelRsp {
  int32 effect_row = 1;
}

message BannerUpdateStatusReq {
  //数据ID
  int32 id = 1;
  //状态:0=禁用,1=启用
  optional int32 status = 2;
}

message BannerUpdateStatusRsp {
  int32 effect_row = 1;
}


message BannerDetailReq {
  int32 id = 1;
}

message BannerDetailRsp {
  //数据ID
  int32 id = 1;
  //名称
  string name = 2;
  string image = 3;
  //排序
  int32 sort = 4;
  //状态:0=禁用,1=启用
  int32 status = 5;
  //跳转地址
  string url = 6;
  //创建时间
  string create_time = 7;
  //修改时间
  string update_time = 8;
  //关联类型
  int32 relation_type = 9;
  //关联节点
  string relation_value = 10;
}