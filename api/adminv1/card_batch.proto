syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/adminv1;adminv1";


service CardBatch {
  //卡券批次-查询-分页
  rpc CardBatchList(CardBatchListReq) returns (CardBatchListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatch/list"};
  }
  //卡券批次礼包-新增
  rpc CardBatchAdd(CardBatchReq) returns (CardBatchRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatch/add"
      body: "*"
    };
  }

  //卡券批次详情
  rpc CardBatchDetail(CardBatchDetailReq) returns (CardBatchDetailRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatch/detail"};
  }

  //卡券批次礼包-更新
  rpc CardBatchUpdate(CardBatchReq) returns (CardBatchRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatch/update"
      body: "*"
    };
  }
  //卡券批次礼包-修改状态
  rpc CardBatchChangeStatus(CardBatchChangeStatusReq) returns (CardBatchChangeStatusRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatch/changeStatus"
      body: "*"
    };
  }

  //卡券批次礼包-删除
  rpc CardBatchDelete(CardBatchDeleteReq) returns (CardBatchDeleteRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatch/delete"
      body: "*"
    };
  }


  //卡券批次礼包-下载券码
  rpc CardBatchCouponDownload(CardBatchCouponDownloadReq) returns (CardBatchCouponDownloadRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatch/couponDownload"};
  }

  //卡券批次-作废
  rpc CardBatchCancel(CardBatchCancelReq) returns (CardBatchCancelRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatch/cancel"
      body: "*"
    };
  }
  //卡券批次商品查询供应商
  rpc CardBatchGoodsSupplierList(CardBatchGoodsSupplierListReq) returns (CardBatchGoodsSupplierListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatch/supplierList"};
  }

  //卡券批次商品查询-分页
  rpc CardBatchGoodsList(CardBatchGoodsListReq) returns (CardBatchGoodsListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatch/goodsList"};
  }

  //卡券批次商品导入校验
  rpc CardBatchGoodsSkuCheck(CardBatchGoodsSkuCheckReq) returns (CardBatchGoodsSkuCheckRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatch/skuCheck"
      body: "*"
    };
  }


  //卡券查询-查询-分页
  rpc CardBatchCouponList(CardBatchCouponListReq) returns (CardBatchCouponListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatchCoupon/list"};
  }

  //卡券-作废
  rpc CardBatchCouponCancel(CardBatchCouponCancelReq) returns (CardBatchCouponCancelRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatchCoupon/cancel"
      body: "*"
    };
  }


  //卡券-日志
  rpc CardBatchCouponLogList(CardBatchCouponLogListReq) returns (CardBatchCouponLogListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatchCoupon/logList"};
  }




  //礼品卡券查询-查询-分页
  rpc CardBatchGiftList(CardBatchGiftListReq) returns (CardBatchGiftListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatchGift/list"};
  }

  //礼品卡券-作废
  rpc CardBatchGiftCancel(CardBatchGiftCancelReq) returns (CardBatchGiftCancelRsp) {
    option (google.api.http) = {
      post: "/admin/v1/cardBatchGift/cancel"
      body: "*"
    };
  }

  //礼品卡券-日志
  rpc CardBatchGiftLogList(CardBatchGiftLogListReq) returns (CardBatchGiftLogListRsp) {
    option (google.api.http) = {get: "/admin/v1/cardBatchGift/logList"};
  }



}

message CardBatchGoodsSupplierListReq {

}
message CardBatchGoodsSupplierListRsp {
  message SupplierGoodsSupplierItem {
    //供应商ID
    int32 id = 1;
    //供应商名称
    string name = 2;
  }
  repeated SupplierGoodsSupplierItem list = 1;
}


message CardBatchListReq {
  //创建时间-起始
  string create_time_start = 1;
  //创建时间-结束
  string create_time_end = 2;

  //激活时间-起始
  string activate_time_start = 3;
  //激活时间-结束
  string activate_time_end = 4;

  //到期时间-起始
  string use_expire_start = 5;
  //到期时间-结束
  string use_expire_end = 6;

  //卡券类型 1礼包
  int32 card_batch_type = 7;
  //激活状态1=未激活，2=已激活
  int32 status = 8;
  //卡券批次号
  string card_batch_number = 9;
  //备注
  string remark = 10;


  int32 page = 11 ; //页码
  int32 page_size = 12 ; //每页数量


}

message CardBatchListRsp {
  message CardBatchListItem {
    // 卡券批次id,
    int32  id = 1;
    //卡券批次号,
    string card_batch_number = 2;
    // 卡券批次名称,
    string card_batch_name = 3;
    //卡券类型 1礼包,
    int32 card_batch_type = 4;
    // 是否可重复领取商品 1.单个商品仅可领取1个 2.单个商品可领取多个,
    int32 exchange_goods_repeat = 5;
    // 可领取商品数量,
    int32 exchange_goods_num = 6;
    // 兑换码样式 1.卡密 2.链接 3.二维码 4.白名单,
    int32 card_number_type = 7;
    // 使用规则,
    string use_rule = 9;
    // 激活状态1=未激活，2=已激活,
    int32  status = 10;
    //备注,
    string remark = 11;
    //激活时间,
    string activate_time = 12;
    // 到期时间开始,
    string use_expire_start = 13;
    //到期时间结束,
    string use_expire_end = 14;
    // 创建时间,
    string create_time = 15;
    // 修改时间,
    string update_time = 16;
    // 作废状态1=未作废，2=已作废
    int32  cancel_status = 20;
    // 作废时间
    string  cancel_time = 21;
    // 卡券生成状态1=未生成，2=生成中，3=已生成
    int32  generate_status = 22;


    // 总数量,
    int32 card_batch_num = 8;
    //已绑定
    int32 bind_num = 24;
    //已使用
    int32 used_num = 17;
    //已完结
    int32 finish_num = 18;
    //已过期
    int32 expire_num = 19;
    //作废数量
    int32 cancel_num = 23;


    // 礼品卡兑换金额
    string exchange_amount = 25;
  }
  repeated CardBatchListItem list = 1;
  //页码
  int32 page = 2;
  //数据总数
  int32 count = 3;
}
message CardBatchDetailReq {
  // 卡券批次id,
  int32  id = 1;
}


message CardBatchDetailRsp {
  // 卡券批次id,
  int32  id = 1;
  //卡券批次号,
  string card_batch_number = 2;
  // 卡券批次名称,
  string card_batch_name = 3;
  //卡券类型 1礼包,
  int32 card_batch_type = 4;
  // 是否可重复领取商品 1.单个商品仅可领取1个 2.单个商品可领取多个,
  int32 exchange_goods_repeat = 5;
  // 可领取商品数量,
  int32 exchange_goods_num = 6;
  // 兑换码样式 1.卡密 2.链接 3.二维码 4.白名单,
  int32 card_number_type = 7;
  // 总数量,
  int32 card_batch_num = 8;
  // 使用规则,
  string use_rule = 9;
  // 激活状态1=未激活，2=已激活,
  int32  status = 10;
  //备注,
  string remark = 11;
  //激活时间,
  string activate_time = 12;
  // 到期时间开始,
  string use_expire_start = 13;
  //到期时间结束,
  string use_expire_end = 14;
  // 创建时间,
  string create_time = 15;
  // 修改时间,
  string update_time = 16;
  // 作废状态1=未作废，2=已作废
  int32  cancel_status = 20;
  // 作废时间
  string  cancel_time = 21;
  // 卡券生成状态1=未生成，2=生成中，3=已生成
  int32  generate_status = 22;
  // 礼品卡兑换金额
  string exchange_amount = 25;

  message CardBatchGoodsListItem {
    //商品名称
    string goods_name = 11;
    // 商品类型:1=虚拟，2=实物
    int32 type = 14;
    //商品skuno
    string sku_no = 1;
    //skuId
    int32 sku_id = 2;
    //商品名称
    string goods_sku_name = 3;
    // 商品图片
    string image = 18;
    // 规格图片商品图片
    string goods_sku_image = 21;
    //售价
    string sale_price = 5;
    //销量
    int32 sale_volume = 6;
    //品牌ID
    int32 brand_id = 7;
    //类目ID
    int32 category_id = 8;
    //原价 【供应价】
    string  supplier_price = 9;
    // 库存
    int32 stock = 10;
    // 1 手机号  2 邮箱 4 QQ号  8  微信
    repeated int32  account_type = 12;

    int32 supplier_id = 22; // 分销商ID
    string supplier_name = 23; //供应商名称
  }
  repeated CardBatchGoodsListItem goods_list = 23;
}



message CardBatchReq {
  // 卡券批次id,
  int32  id = 1;
  //  //卡券批次号,
  //  string card_batch_number = 2;
  // 卡券批次名称,
  string card_batch_name = 3;
  //卡券类型 1礼包,
  int32 card_batch_type = 4;
  // 是否可重复领取商品 1.单个商品仅可领取1个 2.单个商品可领取多个,
  int32 exchange_goods_repeat = 5;
  // 可领取商品数量,
  int32 exchange_goods_num = 6;
  // 兑换码样式 1.卡密 2.链接 3.二维码 4.白名单,
  int32 card_number_type = 7;
  // 白名单
  repeated string white_list = 17;
  // 总数量,
  int32 card_batch_num = 8;
  // 使用规则,
  string use_rule = 9;
  // 激活状态1=未激活，2=已激活,
  int32  status = 10;
  //备注,
  string remark = 11;

  // 到期时间开始,
  string use_expire_start = 13;
  //到期时间结束,
  string use_expire_end = 14;

  message goodsInfo {
    string sku_no = 1;
  }
  // 白名单
  repeated goodsInfo card_batch_goods = 18;
  // 单个金额
  string exchange_amount = 19;
}
message CardBatchRsp{
  //影响数据行数
  int64 effect_row = 1;
}
// 激活状态设置
message CardBatchChangeStatusReq {
  repeated int64 card_batch_ids = 1 ;
  // 激活状态1=未激活，2=已激活
  int32 status = 2 ;
}

message CardBatchChangeStatusRsp {
  //影响数据行数
  int64 effect_row = 1;
}

// 删除批次
message CardBatchDeleteReq {
  repeated int64 card_batch_ids = 1 ;

}

message CardBatchDeleteRsp {
  //影响数据行数
  int64 effect_row = 1;
}

// 激活状态设置
message CardBatchCouponDownloadReq {
  //卡券批次号
  string card_batch_number = 13;
}

message CardBatchCouponDownloadRsp {
  // 任务号
  string sys_task_number = 1;
}



// 作废
message CardBatchCancelReq {
  repeated int64 card_batch_ids = 1 ;
}

message CardBatchCancelRsp {
  //影响数据行数
  int64 effect_row = 1;
}

//
message CardBatchGoodsListReq {
  int32 page = 1 ; //页码
  int32 page_size = 2 ; //每页数量


  // 市场价
  string market_price_start = 5;
  string market_price_end = 6;
  // 售价
  string sale_price_start = 7;
  string sale_price_end = 8;

  // 商品  名称或SKU
  string keyword = 9;

  //状态:0=下架,1=上架
  optional int32 status = 10;

  //商品类目
  int64 category_id = 11;
  //地区 省
  int64 area_id = 12;

  //仅看有货 0否 1是
  int32 stock = 13;
  //供应商id
  int32 supplier_id = 14;
  repeated int32 supplier_ids = 15;
}

message CardBatchGoodsListRsp {
  message CardBatchGoodsListItem {
    // 商品类型:1=虚拟，2=实物
    int32 type = 14;
    // sku编码
    string sku_no = 25;
    //商品名称
    string goods_name = 1;
    //商品编号
    int64 goods_id = 2;
    // 市场价
    string market_price = 3;
    // 售价【建议零售价】
    string sale_price = 4;
    // 原价 【供应价】
    string supplier_price = 5;
    // 库存
    int32 stock = 7;
    //商品sku编号
    int32 goods_sku_id = 12;
    //商品sku名称
    string goods_sku_name = 13;
    // 商品图片
    string image = 18;
    // 规格图片商品图片
    string goods_sku_image = 21;
    // 1 手机号  2 邮箱 4 QQ号  8  微信
    repeated int32  account_type = 10;

    int32 supplier_id = 22; // 分销商ID
    string supplier_name = 23; //供应商名称
  }
  repeated CardBatchGoodsListItem list = 1;
  //页码
  int32 page = 2;
  //数据总数
  int32 count = 3;
}

message CardBatchCouponListReq {
  int32 page = 1 ; //页码
  int32 page_size = 2 ; //每页数量


  //创建时间-起始
  string create_time_start = 5;
  //创建时间-结束
  string create_time_end = 6;

  //激活时间-起始
  string activate_time_start = 7;
  //激活时间-结束
  string activate_time_end = 8;

  //到期时间-起始
  string use_expire_start = 9;
  //到期时间-结束
  string use_expire_end = 10;

  //卡券类型 1礼包
  int32 card_batch_type = 11;
  //激活状态1=未激活，2=已激活
  int32 status = 12;
  //卡券批次号
  string card_batch_number = 13;
  //券码/兑换码
  string card_coupon_number = 14;

  //兑换状态 卡券状态 1 待使用 2 已使用 3 已作废
  int32 coupon_status = 15;
}

message CardBatchCouponListRsp {
  message CardBatchCouponListItem {
    // 卡券批次id,
    int32  id = 1;
    //卡券批次号,
    string card_batch_number = 2;
    //券码/兑换码
    string card_coupon_number = 17;
    // 卡券批次名称,
    string card_batch_name = 3;
    //卡券类型 1礼包,
    int32 card_batch_type = 4;
    // 是否可重复领取商品 1.单个商品仅可领取1个 2.单个商品可领取多个,
    int32 exchange_goods_repeat = 5;

    // 兑换码样式 1.卡密 2.链接 3.二维码 4.白名单,
    int32 card_number_type = 7;
    // 使用规则,
    //    string use_rule = 9;
    // 绑定状态 1.未绑定 2已绑定
    int32  bind_status = 10;

    //兑换状态 卡券状态 1 待使用 2 已使用 3 已作废
    int32  status = 18;
    //备注,
    //    string remark = 11;
    //激活时间,
    //    string activate_time = 12;
    // 到期时间开始,
    string use_expire_start = 13;
    //到期时间结束,
    string use_expire_end = 14;
    // 创建时间,
    string create_time = 15;
    // 修改时间,
    string update_time = 16;

    // 可领取商品数量/总次数
    int32 exchange_goods_num = 23;
    // 剩余次数
    int32 surplus_num = 19;
    //已使用次数
    int32 card_coupon_use_num = 20;
    //作废次数
    int32 cancel_num = 21;
    //过期次数
    int32 expire_num = 22;

  }
  repeated CardBatchCouponListItem list = 1;
  //页码
  int32 page = 2;
  //数据总数
  int32 count = 3;
}

message CardBatchCouponCancelReq {
  repeated int64 card_batch_coupon_ids = 1 ;

}

message CardBatchCouponCancelRsp {
  //影响数据行数
  int64 effect_row = 1;
}



message CardBatchCouponLogListReq {
  int32 card_batch_coupon_id = 1 ;
}

message CardBatchCouponLogListRsp {
  message CardBatchCouponLogListItem {
    int32 card_batch_coupon_id = 1 ;
    string user_name = 2 ;
    int32 user_id = 3 ;
    string content = 4 ;
    string create_time = 5;
  }
  repeated CardBatchCouponLogListItem list = 1;
}
message CardBatchGoodsSkuCheckReq {
  repeated string sku_nos = 1 ;
}

message CardBatchGoodsSkuCheckRsp {
  message SkuCheckItem {
    string sku_no = 1;
    string  err_msg = 2;


    int32 supplier_id = 22; // 分销商ID
    string supplier_name = 23; //供应商名称
    //商品名称
    string goods_name = 11;
    // 商品类型:1=虚拟，2=实物
    int32 type = 14;
    //skuId
    int32 sku_id = 15;
    //商品名称
    string goods_sku_name = 3;
    // 商品图片
    string image = 18;
    // 规格图片商品图片
    string goods_sku_image = 21;
    //售价
    string sale_price = 5;
    //销量
    int32 sale_volume = 6;
    //品牌ID
    int32 brand_id = 7;
    //类目ID
    int32 category_id = 8;
    //原价 【供应价】
    string  supplier_price = 9;
    // 库存
    int32 stock = 10;
    // 1 手机号  2 邮箱 4 QQ号  8  微信
    repeated int32  account_type = 12;

  }
  repeated SkuCheckItem list = 1;
  int32 success = 2;
  int32 fail = 3;
}


message CardBatchGiftListReq {
  int32 page = 1 ; //页码
  int32 page_size = 2 ; //每页数量


  //创建时间-起始
  string create_time_start = 5;
  //创建时间-结束
  string create_time_end = 6;

  //激活时间-起始
  string activate_time_start = 7;
  //激活时间-结束
  string activate_time_end = 8;

  //到期时间-起始
  string use_expire_start = 9;
  //到期时间-结束
  string use_expire_end = 10;
  //激活状态1=未激活，2=已激活
  int32 status = 12;
  //卡券批次号
  string card_batch_number = 13;
  //券码/兑换码
  string card_gift_number = 14;

  //兑换状态 卡券状态 1 待使用 2 已使用 3 已作废
  int32 gift_status = 15;
  //绑定状态 1.未绑定 2已绑定
  int32 bind_status = 16;
}

message CardBatchGiftListRsp {
  message CardBatchGiftListItem {
    // 卡券批次id,
    int32  id = 1;
    //卡券批次号,
    string card_batch_number = 2;
    //券码/兑换码
    string card_gift_number = 17;
    // 卡券批次名称,
    string card_batch_name = 3;
    // 礼品卡兑换金额
    string exchange_amount = 5;
    // 绑定状态 1.未绑定 2已绑定
    int32  bind_status = 10;

    //激活时间,
    string activate_time = 12;

    //兑换状态 卡券状态 1 待使用 2 已使用 3 已作废
    int32  status = 18;
    // 到期时间开始,
    string use_expire_start = 13;
    //到期时间结束,
    string use_expire_end = 14;
    // 创建时间,
    string create_time = 15;
    // 修改时间,
    string update_time = 16;

    // 剩余金额
    string surplus_amount = 19;
    //已使用金额
    string card_gift_use_amount = 20;
    //作废金额
    string cancel_amount = 21;
    //过期金额
    string expire_amount = 22;

  }
  repeated CardBatchGiftListItem list = 1;
  //页码
  int32 page = 2;
  //数据总数
  int32 count = 3;
}

message CardBatchGiftCancelReq {
  repeated int64 card_batch_gift_ids = 1 ;

}

message CardBatchGiftCancelRsp {
  //影响数据行数
  int64 effect_row = 1;
}



message CardBatchGiftLogListReq {
  int32 card_batch_gift_id = 1 ;
}

message CardBatchGiftLogListRsp {
  message CardBatchGiftLogListItem {
    int32 card_batch_gift_id = 1 ;
    string user_name = 2 ;
    int32 user_id = 3 ;
    string content = 4 ;
    string create_time = 5;
  }
  repeated CardBatchGiftLogListItem list = 1;
}
