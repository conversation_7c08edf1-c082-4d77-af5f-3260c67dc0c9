syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/adminv1;adminv1";

// 用户组（角色）
service SysRole {
  // 列表
  rpc list(ReqSysRoleList) returns (RespSysRoleList) {
    option (google.api.http) = {get: "/admin/v1/sysRole/list"};
  }

  // 详情
  rpc info(ReqSysRoleInfo) returns (RespSysRoleInfo) {
    option (google.api.http) = {get: "/admin/v1/sysRole/info"};
  }

  // 新增
  rpc create(ReqSysRoleSave) returns (RespSysRoleInfo) {
    option (google.api.http) = {
      post: "/admin/v1/sysRole/create"
      body: "*"
    };
  }

  // 更新
  rpc update(ReqSysRoleSave) returns (RespSysRoleInfo) {
    option (google.api.http) = {
      post: "/admin/v1/sysRole/update"
      body: "*"
    };
  }

  // 删除
  rpc delete(ReqSysRoleChange) returns (RespSysRoleEmpty) {
    option (google.api.http) = {
      post: "/admin/v1/sysRole/delete"
      body: "*"
    };
  }

  rpc Select(ReqSysRoleEmpty) returns (RespSysRoleSelectList) {
    option (google.api.http) = {get: "/admin/v1/sysRole/select"};
  }
}

// 返回角色列表
message RespSysRoleSelectList {
  repeated RespSysRoleSelectInfo list = 1;

  // 角色详情
  message RespSysRoleSelectInfo {
    //角色 id
    int32 id = 1;
    // 角色名称
    string role_name = 4;
  }
}

// 空返回
message RespSysRoleEmpty {
  //影响数据行数
  int32 effect_row = 1;
}

// 空返回
message ReqSysRoleEmpty {}

message ReqSysRoleList {
  string role_name = 1;
  int32 page = 8; //页码
  int32 page_size = 9;
}

// 返回角色列表
message RespSysRoleList {
  //页码
  int32 page = 2;
  //数据总数
  int32 count = 3;
  repeated RespSysRoleInfo list = 1;
}

// 角色详情
message RespSysRoleInfo {
  //角色 id
  int32 id = 1;
  // 角色规则 id
  repeated int32 menuIds = 3;
  // 角色名称
  string role_name = 4;
  //创建时间
  string update_time = 6;


  //创建时间
  string create_time = 7;
}

// 获取详情
message ReqSysRoleInfo {
  //角色 id
  int32 id = 1;
}

// 保存角色
message ReqSysRoleSave {
  //角色 id
  int32 id = 1;
  // 角色规则 id
  repeated int32 menuIds = 3;
  // 角色名称
  string role_name = 4;
}

// 更新角色
message ReqSysRoleChange {
  //角色 id
  int32 id = 1;
}
