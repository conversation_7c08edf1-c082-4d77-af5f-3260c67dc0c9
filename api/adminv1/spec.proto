syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";
import "supplierv1/spec.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/adminv1;adminv1";

service SpecSrv {
  // 添加规格
  rpc CreateSpec(api.supplierv1.CreateSpecReq) returns (api.supplierv1.CreateSpecResp) {
    option (google.api.http) = {
      post: "/admin/v1/spec/create"
      body: "*"
    };
  }

  rpc SaveSpecTpl(api.supplierv1.SaveSpecTplReq) returns (api.supplierv1.SaveSpecTplResp) {
    option (google.api.http) = {
      post: "/admin/v1/spec/saveTpl"
      body: "*"
    };
  }

  rpc ListSpecTpl(api.supplierv1.ListSpecTplReq) returns (api.supplierv1.ListSpecTplResp) {
    option (google.api.http) = {
      post: "/admin/v1/spec/listTpl"
      body: "*"
    };
  }

  rpc DelSpecTpl(api.supplierv1.DelSpecTplReq) returns (api.supplierv1.DelSpecTplResp) {
    option (google.api.http) = {
      post: "/admin/v1/spec/delTpl"
      body: "*"
    };
  }
}
