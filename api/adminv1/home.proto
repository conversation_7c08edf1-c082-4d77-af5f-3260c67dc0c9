syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/adminv1;adminv1";

//首页大屏统计
service Home {
  //统计
  rpc HomeStatistics(HomeStatisticsReq) returns (HomeStatisticsRsp) {
    option (google.api.http) = {get: "/admin/v1/home/<USER>"};
  }

  //销售趋势
  rpc HomeSalesTrend(SalesTrendReq) returns (SalesTrendRsp) {
    option (google.api.http) = {get: "/admin/v1/home/<USER>"};
  }
  //销售占比
  rpc HomeSalesProportion(HomeSalesProportionReq) returns (HomeSalesProportionRsp) {
    option (google.api.http) = {get: "/admin/v1/home/<USER>"};
  }
  //销量排行
  rpc HomeSalesTop(HomeSalesTopReq) returns (HomeSalesTopRsp) {
    option (google.api.http) = {get: "/admin/v1/home/<USER>"};
  }
}

message HomeSalesTopReq {
  // 1今日2近一周3近一月4全年
  int32 sales_trend_type = 2;
  // 1销量2销售额
  int32 top_type = 1;
}

message HomeSalesTopRsp {
  message HomeSalesTopItem {
    //商品名称
    string goods_name = 1;
    //商品sku名称
    string goods_sku_name = 2;
    // sku编码
    string sku_no = 3;
    int32 sale_number = 4;
    //销售金额
    string  sale_amount = 5;
  }
  repeated HomeSalesTopItem list = 1;


}

message HomeStatisticsReq {

}

message HomeStatisticsRsp {
  //销售金额
  string  sale_amount = 2;
  //虚拟销售金额
  string virtual_sale_amount = 3;
  //实物销售金额
  string  real_sale_amount = 4;
  //场景销售金额
  string third_sale_amount = 5;


  //退款金额
  string refund_amount = 6;
  //虚拟退款金额
  string virtual_refund_amount = 7;
  //实物退款金额
  string  real_refund_amount = 8;
  //场景退款金额
  string third_refund_amount = 9;


  //上架商品数量
  int32 goods_num = 10;
  //虚拟上架商品数量
  int32 virtual_goods_num = 11;
  //实物上架商品数量
  int32  real_goods_num = 12;

  // 下架商品数量
  int32 goods_disable_num = 13;
  //虚拟下架商品数量
  int32 virtual_goods_disable_num = 14;
  //实物下架商品数量
  int32  real_goods_disable_num = 15;


  //待发货
  int32  pending_ship = 16;
  //待审核
  int32 pending_audit = 17;
  //待售后
  int32 pending_after_sale = 18;

  message ResellerBalance {
    //是否展示分销商余额
    bool is_show = 1;
    //分销商余额
    string reseller_balance = 2;
  }
  //分销商余额
  ResellerBalance reseller_balance = 19;
}
message SalesTrendReq {
  // 1今日2近一周3近一月4全年
  int32 sales_trend_type = 1;
}

message SalesTrendRsp {
  message SalesTrendItem {
    //日期
    string sales_trend_date = 1;
    //金额
    string sale_amount = 2;
  }
  repeated SalesTrendItem real_list = 1;
  repeated SalesTrendItem third_list = 2;
  repeated SalesTrendItem virtual_list = 3;
}
message HomeSalesProportionReq {
  // 1今日2近一周3近一月4全年
  int32 sales_trend_type = 2;
  // 1全部2实物3虚拟4场景
  int32 product_type = 1;
}

message HomeSalesProportionRsp {
  message SalesProportionItem {
    //商品类目
    int32 category_id = 1;
    string category_name = 2; // 类目
    //金额
    string sale_amount = 3;
    //金额占比
    int32 sale_amount_proportion = 4;
  }
  repeated SalesProportionItem list = 3;
}
