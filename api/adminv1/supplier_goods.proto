syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";
import "supplierv1/goods.proto";
import "validate/validate.proto";
import "common/common.proto";


option go_package = "cardMall/api/adminv1;adminv1";

//商品-B端
service SupplierGoods {

  //选品列表供应商
  rpc SelectSupplierList(SupplierGoodsSupplierListReq) returns (SupplierGoodsSupplierListRsp) {
    option (google.api.http) = {get: "/admin/v1/supplierGoods/supplierList"};
  }
  //授权商品-更新数据
  rpc SkuChangeBrandAndCategory(SkuChangeBrandAndCategoryReq) returns (SkuChangeBrandAndCategoryRsp) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/changeSku"
      body: "*"
    };
  }

  //选品列表-分页
  rpc SelectList(SupplierGoodsSelectListReq) returns (SupplierGoodsSelectListRsp) {
    option (google.api.http) = {get: "/admin/v1/supplierGoods/selectList"};
  }

  //商品入库-选品
  rpc Selected(SupplierGoodsSelectedReq) returns (SupplierGoodsSelectedRsp) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/selected"
      body: "*"
    };
  }

  //选品入库-通过搜索条件入库
  rpc SelectedBySearch(SupplierGoodsSelectedBySearchReq) returns (SupplierGoodsSelectedRsp) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/selectedBySearch"
      body: "*"
    };
  }

  // 商品管理
  rpc SaveGoods(api.supplierv1.GoodsDetailData) returns (api.supplierv1.SaveGoodsResp) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/save"
      body: "*"
    };
  }

  // 上/下架
  rpc ShelfGoods(api.supplierv1.ShelfGoodsReq) returns (api.supplierv1.ShelfGoodsResp) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/shelf"
      body: "*"
    };
  }

  // 商品详情
  rpc GoodsDetail(api.supplierv1.GoodsDetailReq) returns (api.supplierv1.GoodsDetailData) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/detail"
      body: "*"
    };
  }

  // 商品列表
  rpc ListGoods(api.supplierv1.ListGoodsReq) returns (api.supplierv1.ListGoodsResp) {
    option (google.api.http) = {
      post: "/admin/v1/supplierGoods/list"
      body: "*"
    };
  }

  rpc ListGoodsStatistics(common.ReqEmpty) returns (api.supplierv1.ListGoodsStatisticsResp) {
    option (google.api.http) = {
      post: "/admin/v1/goods/statistics"
      body: "*"
    };
  }

  // 分类列表
  rpc CategoryTree(api.supplierv1.CategoryTreeReq) returns (api.supplierv1.CategoryTreeResp) {
    option (google.api.http) = {
      post: "/admin/v1/category/tree"
      body: "*"
    };
  }

  // 品牌搜索
  rpc BrandList(api.supplierv1.BrandListReq) returns (api.supplierv1.BrandListResp) {
    option (google.api.http) = {
      post: "/admin/v1/brand/list"
      body: "*"
    };
  }
}

message SkuChangeBrandAndCategoryReq {
  repeated string sku_nos = 3; // 供应商商品sku_no
  int32 category_id = 6;  //商品类目
  int32 brand_id = 7 ;  //品牌名称
  //供应商id 1 为官方供应商 2 企业供应商  
  int32  supplier_id = 10;
}


message SkuChangeBrandAndCategoryRsp {
  //影响数据行数
  int32 effect_row = 1;
}


message SupplierGoodsSupplierListReq {

}
message SupplierGoodsSupplierListRsp {
  message SupplierGoodsSupplierItem {
    //供应商ID
    int32 id = 1;
    //供应商名称
    string name = 2;
  }
  repeated SupplierGoodsSupplierItem list = 1;
}


message SupplierGoodsSelectListReq {
  //列表页码
  int32 page = 1;
  //每页条数
  int32 page_size = 2;
  //商品名称
  string name = 3;
  //商品分类ID--传分类的最下级ID，目前是第三级
  repeated int32 category_id = 4;
  //利润率下限(%)
  double profit_rate_min = 5;
  //利润率上限(%)
  double profit_rate_max = 6;
  //在售价下限
  double sale_price_min = 7;
  //在售价上限
  double sale_price_max = 8;
  //搜索类型:1=热销,2=新品,3=价格最低,4=收益最高
  int32 search_type = 9;
  //供应商id 1 为官方供应商
  int32  supplier_id = 10;
  //品牌名称
  int32 brand_id = 15 ;
  //是否只查未选 1 否 2 是
  int32 selected = 12;
  //是否只看有库存 1是
  int32 have_stock = 13;
  //类型, 1 = 虚拟, 2 = 实物
  int32 product_type = 14;
  repeated int32 supplier_ids = 11;
}

message SupplierGoodsSelectListRsp {
  message SupplierGoodsSelectListItem {
    //商品SkuID
    int32 id = 1;
    //商品名称
    string name = 2;
    //供货价
    string supplier_price = 3;
    //售价【建议零售价】
    string sale_price = 4;
    //利润=市场价-供应价
    string profit = 5;
    //商品图
    string image = 6;
    //商品编码
    string sku_no = 7;
    //销量
    int32 sales_volume = 8;
    //市场价
    string market_price = 9;
    //库存
    int32 stock = 11;
    int32 selected = 12;

    int32 brand_id = 13; // 品牌名称
    string brand_name = 14; // 品牌名称

    int32 category_id = 15; // 所属分类
    string category_name = 16; // 类目


    int32 supplier_id = 18; // 分销商ID
    string supplier_name = 19; //供应商名称

  }
  repeated SupplierGoodsSelectListItem list = 1;
  int32 count = 2;
  int32 page = 3;
}

message SupplierGoodsSelectedReq {
  //商品skuNo
  repeated string sku_no = 1;

  //状态:0=下架,1=上架
  optional int32 status = 2;

  //调整类型:1=上下调整，2=等于某个价格
  int32 type = 3;

  //价格类型:1=供应价,2=建议零售价(type=1时有效),3=市场价,4=固定价(type=2时有效), 5= 固定价+积分(type=2时有效)， 6= 积分(type=2时有效)
  int32 price_type = 4;

  //调价类型:1=上调，2=下调, type=1时必填
  int32 change_type = 5;

  //调价值,type=1时必填，50%传50， 1元传100
  double change_value = 6;

  //调价单位:1=百分比(%),2=金额(分), type=1时必填
  int32 change_unit = 7;

  //固定金额,单位分，type=2，price_type=4时必填
  int32 price = 8;

  // 积分 type=2，price_type=5, 6时有效
  int32 integral = 9;

  //供应商id 1 为官方供应商
  int32  supplier_id = 10;
}

message SupplierGoodsSelectedRsp {
  //错误信息
  repeated string msg = 1;
  // 任务编号
  string task_no = 2;
}

message SupplierGoodsSelectedBySearchReq {
  message SearchParams {
    //商品名称
    string name = 1;
    //商品分类ID--传分类的最下级ID，目前是第三级
    repeated int32 category_id = 2;
    //利润率下限(%)
    double profit_rate_min = 3;
    //利润率上限(%)
    double profit_rate_max = 4;
    //在售价下限
    double sale_price_min = 5;
    //在售价上限
    double sale_price_max = 6;
    //搜索类型:1=热销,2=新品,3=价格最低,4=收益最高
    int32 search_type = 7;

    //是否只查未选 1 否 2 是
    int32 selected = 12;
    //是否只看有库存 1是
    int32 have_stock = 13;
    //类型, 1 = 虚拟, 2 = 实物
    int32 product_type = 14;

    //品牌名称
    int32 brand_id = 15 ;

  }
  message SelectedParams {
    //状态:0=下架,1=上架
    optional int32 status = 1;

    //调整类型:1=上下调整，2=等于某个价格
    int32 type = 2;

    //价格类型:1=供应价,2=建议零售价(type=1时有效),3=市场价,4=固定价(type=2时有效), 5= 固定价+积分(type=2时有效)， 6= 积分(type=2时有效)
    int32 price_type = 3;

    //调价类型:1=上调，2=下调, type=1时必填
    int32 change_type = 4;

    //调价值,type=1时必填，50%传50， 1元传100
    double change_value = 5;

    //调价单位:1=百分比(%),2=金额(分), type=1时必填
    int32 change_unit = 6;

    //固定金额,单位分，type=2，price_type=4时必填
    int32 price = 7;

    // 积分 type=2，price_type=5, 6时有效
    int32 integral = 8;
  }
  SearchParams search_params = 1;
  SelectedParams selected_params = 2;
  //供应商id
  int32  supplier_id = 10;
  repeated int32 supplier_ids = 11;
}
