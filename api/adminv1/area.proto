syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";
import "supplierv1/area.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/adminv1;adminv1";

service AreaSrv {
  rpc AllProvince(api.supplierv1.AllProvinceReq) returns (api.supplierv1.AllProvinceResp) {
    option (google.api.http) = {
      post: "/admin/v1/area/province"
      body: "*"
    };
  }

  rpc ListProvince(api.supplierv1.ListProvinceReq) returns (api.supplierv1.ListProvinceResp) {
    option (google.api.http) = {
      post: "/admin/v1/area/regions"
      body: "*"
    };
  }
}
