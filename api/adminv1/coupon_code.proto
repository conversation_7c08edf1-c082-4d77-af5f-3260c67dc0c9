syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/adminv1;adminv1";

//优惠券码
service CouponCode {
  //列表
  rpc CodeList(CouponCodeListReq) returns (CouponCodeListRsp) {
    option (google.api.http) = {get: "/admin/v1/coupon/code/list"};
  }
}

message CouponCodeListReq {
  //优惠券ID
  int32 coupon_id = 1;
  //状态:2=已领取，3=已使用，4=使用中,5=已作废
  int32 status = 2;
  //领取时间-起始
  string collection_time_start = 3;
  //领取时间-结束
  string collection_time_end = 4;
  //页码
  int32 page = 5;
  //每页数据量
  int32 page_size = 6;
}

message CouponCodeListRsp {
  message CouponCodeListItem {
    //数据ID
    int32 id = 1;
    //券码
    string code = 2;
    //用户ID
    int32 user_id = 3;
    //用户昵称
    string nick_name = 4;
    //领取时间
    string collection_time = 5;
    //状态:1=未领取，2=已领取，3=已使用，4=使用中,5=已作废
    int32 status = 6;
    //使用时间
    string use_time = 7;
    //关联订单号
    string order_number = 8;
  }
  repeated CouponCodeListItem list = 9;
  //页码
  int32 page = 10;
  //总数据量
  int32 count = 11;
}
