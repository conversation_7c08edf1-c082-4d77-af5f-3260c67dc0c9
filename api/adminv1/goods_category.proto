syntax = "proto3";

package api.adminv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/adminv1;adminv1";

//商品分类-B端
service GoodsCategory {
  //新增
  rpc Add(GoodsCategoryAddReq) returns (GoodsCategoryAddRsp) {
    option (google.api.http) = {
      post: "/admin/v1/goodsCategory/add"
      body: "*"
    };
  }
  //列表
  rpc List(GoodsCategoryQueryReq) returns (GoodsCategoryListRsp) {
    option (google.api.http) = {get: "/admin/v1/goodsCategory/list"};
  }
  //修改
  rpc Update(GoodsCategoryUpdateReq) returns (GoodsCategoryUpdateRsp) {
    option (google.api.http) = {
      post: "/admin/v1/goodsCategory/update"
      body: "*"
    };
  }
  //删除
  rpc Del(GoodsCategoryDelReq) returns (GoodsCategoryDelRsp) {
    option (google.api.http) = {
      post: "/admin/v1/goodsCategory/del"
      body: "*"
    };
  }

  //列表
  rpc Select(GoodsCategorySelectReq) returns (GoodsCategorySelectRsp) {
    option (google.api.http) = {get: "/admin/v1/goodsCategory/select"};
  }
  rpc Sync(GoodsCategorySyncReq) returns (GoodsCategorySyncRsp) {
    option (google.api.http) = {
      post: "/admin/v1/goodsCategory/sync"
      body: "*"
    };
  }
  //待同步类目查询
  rpc SyncCategory(SyncCategoryReq) returns (SyncCategoryRsp) {
    option (google.api.http) = {get: "/admin/v1/goodsCategory/syncCategory"};
  }
}

message SyncCategoryReq {
  int32  source_type = 1;
}

message SyncCategoryRsp {
  message GoodsCategorySelectItem {
    //分类ID
    int32 id = 1;
    //名称
    string name = 2;
    // 来源1=官方 2=企业
    int32  source_type =4;
    repeated GoodsCategorySelectItem children = 3;
  }
  repeated GoodsCategorySelectItem list = 1;
}
message GoodsCategorySyncReq {
  int32  source_type = 2;
  repeated int32 ids = 1;
}

message GoodsCategorySyncRsp {
  //影像数据行数
  int32 effect_rows = 1;
}
message GoodsCategorySelectReq {}
message GoodsCategorySelectRsp {
  message GoodsCategorySelectItem {
    //分类ID
    int32 id = 1;
    //名称
    string name = 2;
    //状态:1=启用，0=禁用
    int32 status = 4;
    // 来源1=官方 2=企业
    int32  source_type =15;
    repeated GoodsCategorySelectItem children = 3;
  }
  repeated GoodsCategorySelectItem list = 1;
}
message GoodsCategoryAddReq {
  //分类名称
  string name = 1;
  //排序
  int32 sort = 2;
  //状态:1=启用，0=禁用
  int32 status = 4;
  //父级ID
  int32 pid = 5;
  //类目类型:1=虚拟商品,2=实物
  int32 type = 6;
  //是否推荐:1=推荐，2=不推荐
  int32 recommend = 7;
  //图片
  string image = 8;
  //是否首页展示，1=否，2=是
  int32 index_show = 9;
}

message GoodsCategoryAddRsp {
  //分类ID
  int32 id = 1;
}

message GoodsCategoryListRsp {
  message GoodsCategoryListItem {
    //分类ID
    int32 id = 1;
    //名称
    string name = 2;
    //排序
    int32 sort = 3;
    //状态:1=启用，0=禁用
    int32 status = 4;
    //创建时间
    int32 create_time = 5;
    //修改时间
    int32 update_time = 6;
    //品牌ID集合
    repeated int32 brand_id = 7;
    //父级ID
    int32 pid = 8;
    //层级
    int32 level = 9;
    //类目类型:1=虚拟商品,2=实物
    int32 type = 10;
    //是否推荐:1=推荐，2=不推荐
    int32 recommend = 11;
    //图片
    string image = 12;
    //商品数量
    int32 goods_num = 13;
    // 来源1=官方 2=企业
    int32  source_type =15;
    //是否首页展示，1=否，2=是
    int32 index_show = 16;
    //子级列表
    repeated GoodsCategoryListItem children = 14;
  }
  repeated GoodsCategoryListItem list = 1;
}

message GoodsCategoryUpdateReq {
  //分类ID
  int32 id = 1;
  //品类名称
  string name = 2;
  //状态:1=启用，0=禁用
  optional int32 status = 3;
  //排序
  optional int32 sort = 4;
  //是否推荐:1=推荐，2=不推荐
  int32 recommend = 5;
  //图片
  string image = 8;
  //是否首页展示，1=否，2=是
  int32 index_show = 9;
}

message GoodsCategoryUpdateRsp {
  //影响行数
  int32 effect_rows = 1;
}

message GoodsCategoryQueryReq {
  //品类名称
  string name = 1;
  // 状态 1=启用，0=禁用
  optional int32 status = 2;
}

message GoodsCategoryDelReq {
  //分类ID
  int32 id = 1;
}

message GoodsCategoryDelRsp {
  //影响行数
  int32 effect_rows = 1;
}
