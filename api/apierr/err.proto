syntax = "proto3";
package cardMall.apierr;

import "errors/errors.proto";

option go_package = "cardMall/api/apierr;apierr";

enum Err {
  // 设置缺省错误码，正常情况，所有业务错误都默认用此状态码
  option (errors.default_code) = 500;

  // 系统panic错误
  SYSTEM_PANIC = 0 [(errors.code) = 599];

  // 未登录，401不能滥用，客户端会拉起登录
  NOT_LOGIN = 1 [(errors.code) = 401];

  // DB数据未找到
  DB_NOT_FOUND = 2 [(errors.code) = 404];

  // 参数错误
  PARAM = 3 [(errors.code) = 400];

  // 不允许
  NOT_ALLOW = 4 [(errors.code) = 403];

  // 参数错误
  EXCEPTION = 5 [(errors.code) = 400];

  DB_NOT_FUND_PHONE = 6 [(errors.code) = 405];

  DB_NOT_FUND_NICK_NAME = 7 [(errors.code) = 406];

  // 没有访问权限
  FORBIDDEN = 8 [(errors.code) = 403];

  ORDER_REFUNDED_ALL = 9;
  ORDER_CLOSED = 10;

  ORDER_PAID_NOTIFY_REPEATED = 11;

  // 签名错误
  SIGN_ERROR = 12 [(errors.code) = 401];

  // 参数错误
  PARAM_ERROR = 13 [(errors.code) = 400];

  // 余额不足
  BALANCE_NOT_ENOUGH = 14 [(errors.code) = 403];

  // 系统异常，与微信保持一致
	SYSTEM_ERROR = 20 [(errors.code) = 500];

  // 订单不存在
  ORDER_NOT_FOUND = 21 [(errors.code) = 404];

  // 订单已支付
  ORDER_PAID = 22 [(errors.code) = 403];

  // 并发锁
  CONCURRENCE_LOCK = 23 [(errors.code) = 403];

  // HTTP 请求不符合微信支付 APIv3 接口规则
  INVALID_REQUEST = 24 [(errors.code) = 400];
}
