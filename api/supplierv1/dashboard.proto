syntax = "proto3";

package api.supplierv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/supplierv1;supplierv1";

// 首页面板
service DashboardSrv {
  // 首页面板
  rpc Dashboard(DashboardReq) returns (DashboardResp) {
    option (google.api.http) = {get: "/supplier/v1/dashboard/home"};
  }
}

message DashboardReq {}
message DashboardResp {
  // 待发货订单数
  int32 wait_deliver = 1;
  // 超时订单数
  int32 timeout_order = 2;
  // 待处理售后订单
  int32 after_sale = 3;
  // 待上架商品
  int32 off_shelf = 4;
  // 待审核商品
  int32 wait_audit = 5;
  // 库存预警
  int32 stock_warning = 6;
}
