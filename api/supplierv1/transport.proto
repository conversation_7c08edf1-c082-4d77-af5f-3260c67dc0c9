syntax = "proto3";

package api.supplierv1;

import "google/api/annotations.proto";
import "common/common.proto";

option go_package = "cardMall/api/supplierv1;supplierv1";

service TransportSrv {
  // 保存运费模版
  rpc SaveTransport(SaveTransportReq) returns (SaveTransportResp) {
    option (google.api.http) = {
      post: "/supplier/v1/transport/save"
      body: "*"
    };
  }
  // 删除运费模版
  rpc DelTransport(DelTransportReq) returns (DelTransportResp) {
    option (google.api.http) = {
      post: "/supplier/v1/transport/del"
      body: "*"
    };
  }

  // 运费模版列表
  rpc ListTransport(ListTransportReq) returns (ListTransportResp) {
    option (google.api.http) = {
      post: "/supplier/v1/transport/list"
      body: "*"
    };
  }

  // 运费模版详情
  rpc GetTransport(GetTransportReq) returns (GetTransportResp) {
    option (google.api.http) = {get: "/supplier/v1/transport/{id}"};
  }
}

message SaveTransportReq {
  // 运费模版id，修改时必传
  int32 id = 1;
  // 运费模版名称
  string name = 2;
  // 运费模版描述
  string description = 3;
  // 快递公司ID
  int32 kd_id = 4;
  // 计费模式
  int32 pricing_mode = 5;
  // 默认数量
  double default_num = 6;
  // 默认运费
  double default_price = 7;
  // 增加数量
  double add_num = 8;
  // 增加运费
  double add_price = 9;
  // 运费模板详情
  repeated TransportItem items = 10;
}
message SaveTransportResp {}
message TransportItem {
  // 默认数量
  double default_num = 1;
  // 默认运费
  double default_price = 2;
  // 增加数量
  double add_num = 3;
  // 增加运费
  double add_price = 4;
  // 省份
  repeated TransportCity items = 5;
}
message TransportCity {
  // 省份ID
  int32 city_id = 1;
  // 省份名称
  string city_name = 2;
}

message DelTransportReq {
  // 运费模版id
  int32 id = 1;
}
message DelTransportResp {}

message ListTransportReq {
  // 可用于模糊搜索
  string name = 1;
  // 分页
  common.ReqPage page = 2;
}
message ListTransportResp {
  repeated TransportData list = 1;
  common.RespPage page = 2;
}
message TransportData {
  // ID
  int32 id = 1;
  // 运费模版名称
  string name = 2;
  // 运费模版描述
  string description = 3;
  // 最近更新时间
  string update_time = 4;
  // 创建时间
  string create_time = 5;
}

message GetTransportReq {
  int32 id = 1;
}
message GetTransportResp {
  // 运费模版id
  int32 id = 1;
  // 运费模版名称
  string name = 2;
  // 运费模版描述
  string description = 3;
  // 快递公司ID
  int32 kd_id = 4;
  // 快递公司名称
  string kd_name = 5;
  // 快递公司编码
  string kd_code = 6;
  // 计费模式
  int32 pricing_mode = 7;
  // 默认数量
  double default_num = 8;
  // 默认运费
  double default_price = 9;
  // 增加数量
  double add_num = 10;
  // 增加运费
  double add_price = 11;
  // 运费模板详情
  repeated TransportItem items = 12;
}
