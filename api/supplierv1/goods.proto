syntax = "proto3";

package api.supplierv1;

import "google/api/annotations.proto";
import "common/common.proto";

option go_package = "cardMall/api/supplierv1;supplierv1";

// 商品服务
service GoodsSrv {
  // 商品管理
  rpc SaveGoods(GoodsDetailData) returns (SaveGoodsResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/save"
      body: "*"
    };
  }

  // 批量上传商品-解析excel
  rpc UploadGoods(UploadGoodsReq) returns (UploadGoodsResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/upload"
      body: "*"
    };
  }

  // 批量保存商品-商品列表
  rpc SaveGoodsList(SaveGoodsListReq) returns (SaveGoodsListResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/saveList"
      body: "*"
    };
  }

  // 上/下架
  rpc ShelfGoods(ShelfGoodsReq) returns (ShelfGoodsResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/shelf"
      body: "*"
    };
  }

  // 商品详情
  rpc GoodsDetail(GoodsDetailReq) returns (GoodsDetailData) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/detail"
      body: "*"
    };
  }

  // 商品列表
  rpc ListGoods(ListGoodsReq) returns (ListGoodsResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/list"
      body: "*"
    };
  }

  rpc ListGoodsStatistics(common.ReqEmpty) returns (ListGoodsStatisticsResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/statistics"
      body: "*"
    };
  }

  // 商品审核列表
  rpc ListGoodsAudit(ListGoodsAuditReq) returns (ListGoodsAuditResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/listAudit"
      body: "*"
    };
  }

  //  // 商品列表
  rpc InvalidWaitForAuditRecord(InvalidWaitForAuditRecordReq) returns (InvalidWaitForAuditRecordResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/invalidAudit"
      body: "*"
    };
  }

  // sku 库存列表
  rpc ListGoodsStock(ListGoodsStockReq) returns (ListGoodsStockResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/listStock"
      body: "*"
    };
  }

  // 设置库存预警
  rpc SetStockAlarm(SetStockAlarmReq) returns (SetStockAlarmResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/setStockAlarm"
      body: "*"
    };
  }

  // 设置库存预警
  rpc GetStockAlarm(GetStockAlarmReq) returns (GetStockAlarmResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/getStockAlarm"
      body: "*"
    };
  }

  // sku 调整库存
  rpc ModifyGoodsStock(ModifyGoodsStockReq) returns (ModifyGoodsStockResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/modifyStock"
      body: "*"
    };
  }

  // sku 调整库存
  rpc StockLog(StockLogReq) returns (StockLogResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/stockLog"
      body: "*"
    };
  }

  // 商品审核详情
  rpc GoodsAuditDetail(GoodsAuditDetailReq) returns (GoodsAuditDetailResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/auditDetail"
      body: "*"
    };
  }

  // 保存商品草稿
  rpc SaveGoodsDraft(GoodsDetailData) returns (SaveGoodsDraftResp) {
    option (google.api.http) = {
      post: "/supplier/v1/goods/draft"
      body: "*"
    };
  }

  // 分类列表
  rpc CategoryTree(CategoryTreeReq) returns (CategoryTreeResp) {
    option (google.api.http) = {
      post: "/supplier/v1/category/tree"
      body: "*"
    };
  }

  // 品牌搜索
  rpc BrandList(BrandListReq) returns (BrandListResp) {
    option (google.api.http) = {
      post: "/supplier/v1/brand/list"
      body: "*"
    };
  }
}

message Spec {
  // 规格ID
  int32 id = 1;
  // 规格名称
  string name = 2;
  // 只能有一个规格是 true
  bool addImage = 4;
  // 规格值
  repeated SpecItems items = 3;
}

message SpecItems {
  // 规格项ID
  int32 id = 1;
  // 规格项名称
  string name = 2;
  // 图片
  string image = 3;
  // 规格ID
  int32 specId = 4;
  // 规格名称
  string specName = 5;
}
message SkuItems {
  // 规格项ID
  repeated int32 spec_item_ids = 1;
  // 商品条码
  string barcode = 2;
  // 供应商商品条码
  string supplier_barcode = 3;
  // 市场价
  double market_price = 4;
  // 建议零售价
  // double sale_price = 5;
  // 免邮费价格
  //double free_postage_price = 6;
  // 库存
  int32 stock = 7;
  // 虚拟商品关联的商品ID
  string product_id = 8;
  // 供应价
  double supplier_price = 9;
  // sku id
  int32 id = 10;
  // 规格ID
  repeated int32 spec_ids = 11;
  // product_type 虚拟商品类型 1-直冲 2-卡密
  int32 product_type = 12;
  // product_type_text
  string product_type_text = 13;
  // 虚拟商品名称
  string product_name = 14;
  // SkuNo sku编号/第三方编码
  string sku_no = 15;
}
message GoodsDetailData {
  // 商品类型 1-实物 2-虚拟
  int32 type = 1;
  // 商品名称
  string name = 2;
  // 商品分类ID
  int32 category_id = 3;
  // 品牌ID
  int32 brand_id = 4;
  // 商品图片，第一张默作为商品主图
  repeated string images = 5;
  // 商品规格
  repeated Spec spec = 6;
  // 商品SKU
  repeated SkuItems sku_items = 7;
  // 商品税率
  double tax_rate = 8;
  // 商品税备注
  string tax_remark = 9;
  // 销售区域，城市ID
  // repeated string sale_area = 10;
  repeated SkuItems ban_sku_items = 10;
  // 商品详情
  string detail = 11;
  // 是否上架
  bool online = 12;
  // 商品ID
  int32 id = 13;
  // 限制销售区域，城市ID
  repeated int32 not_sale_area = 14;
  // 发货地址
  // repeated int32 deliver_addr = 15;

  // google.protobuf.Value spec_json = 16;
  repeated int32 category_ids = 17;
  repeated AreaItem not_sale_area_json = 18;

  // 通过草稿进行编辑时传递
  int32 draft_id = 19;
  // 运费模版ID
  int32 transport_id = 20;
  // 发货时效 0-未设置 1-当日 2-24h 3-48h
  int32 deliver_timeline = 21;
}
message AreaItem {
  int32 id = 1;
  string name = 2;
}
message SaveGoodsResp {}

message ShelfGoodsReq {
  // 商品 ID
  int32 goods_id = 1;
  // 是否上架
  bool online = 3;
}
message ShelfGoodsResp {}

message GoodsDetailReq {
  // 商品 ID
  int32 id = 1;
}

message SaveGoodsDraftResp {}

message ListGoodsReq {
  // 商品名称
  string name = 1;
  // 商品类型 1-实物 2-虚拟
  int32 type = 2;
  // 商品分类ID
  int32 categoryId = 3;
  // 品牌ID
  int32 brandId = 4;
  // 是否上架 选传
  optional bool online = 5;
  // id 商品编号
  int32 id = 6;
  // supplier_barcode 供应商商品编码【第三方编码】
  string supplierBarcode = 7;
  // barcode 商品条码
  string barcode = 8;
  // 分页信息
  common.ReqPage page = 9;
  // sku no
  string skuNo = 10;
  // tab 1-全部商品 2-售卖中 3-已下架  商品审核 10-全部 11-待审核 12-审核通过 13-审核驳回 14-已撤销  20-商品草稿
  int32 tab = 11;
}
message ListGoodsResp {
  repeated ListGoodsData list = 1;
  common.RespPage page = 2;
  // 总数
  int32 total = 3;
  // 下架商品数
  int32 offShelf = 4;
}

message ListGoodsStatisticsResp {
  // 总数
  int32 total = 3;
  // 下架商品数
  int32 offShelf = 4;
  // 上架商品数
  int32 onSale = 5;
  // 待审核商品数
  Audit audit = 6;
  // 待上架商品数
  int32 draft = 7;

  message Audit {
    // 全部
    int32 all = 1;
    // 待审核
    int32 wait = 2;
    // 通过
    int32 pass = 3;
    // 拒绝
    int32 fail = 4;
    // 取消
    int32 cancel = 5;
  }
}


message ListGoodsData {
  // 商品名称
  string name = 1;
  // 编号
  int32 id = 2;
  // 供应价 数组长度为1时只有一个价，长度为2时 返回最低和最高价
  repeated double supplier_price = 3;
  // 市场价 数组长度为1时只有一个价，长度为2时 返回最低和最高价
  repeated double market_price = 4;
  // 库存
  int32 stock = 5;
  // 创建时间
  string create_time = 6;
  // 状态
  int32 status = 7;
  // 状态text
  string status_text = 8;
  // sku no
  repeated string sku_no = 9;
  // 审核状态
  int32 audit_status = 10;
  // 审核状态文本
  string audit_status_text = 11;
  // transport_id 运费模版ID
  int32 transport_id = 12;
  // 商品分类ID
  string transport_name = 13;
  // 分类
  string category_name = 14;
  // 品牌
  string brand_name = 15;
  // 分类ID
  int32 category_id = 16;
  // 品牌ID
  int32 brand_id = 17;
  // 商品图片
  string image = 18;
}

message ListGoodsAuditReq {
  // 商品名称
  string name = 1;
  // 商品编号
  int32 goods_id = 2;
  // 操作类型 1-上架 2-修改信息 3-新增 4-草稿
  int32 op_type = 4;
  // 状态  1-待审核 2-审核通过 4-审核驳回 9-保存为草稿 10-已作废
  int32 status = 5;
  // 分页信息
  common.ReqPage page = 6;
  // 选传
  optional bool sort_by_submit_time = 7;
  // sku no
  string sku_no = 8;
}
message ListGoodsAuditResp {
  repeated GoodsDraft list = 1;
  common.RespPage page = 2;
}

message InvalidWaitForAuditRecordReq {
  // 商品编号
  int32 id = 2;
}
message InvalidWaitForAuditRecordResp {}

message GoodsDraft {
  // 商品名称
  string name = 1;
  // 编号
  int32 id = 2;
  // 审核状态 1-待审核 2-审核通过 4-审核驳回 9-保存为草稿 10-已作废
  int32 status = 3;
  // 审核状态text
  string status_text = 4;
  // 提交时间
  string submit_time = 5;
  // 类型 1-上架 2-修改信息 3-新增 4-草稿
  int32 op_type = 6;
  // 类型text
  string op_type_text = 7;
  // 商品编号
  int32 goods_id = 8;
}

message GoodsAuditDetailReq {
  int32 id = 1;
}
message GoodsAuditDetailResp {
  // 审核状态text
  string status_text = 1;
  // 审核状态 1-审核通过 2-审核驳回
  int32 status = 2;
  // 备注
  string remark = 3;
  // 审核人员
  string handler_user = 4;
  // 审核时间
  string handler_time = 5;
  // detail
  GoodsDetailData detail = 6;
}

message ListGoodsStockReq {
  // 商品名称
  string goods_name = 1;
  // 商品编号
  int32 goods_id = 2;
  // 是否库存预警 选传
  optional bool is_stock_warning = 3;
  // 分页信息
  common.ReqPage page = 4;
  // 库存排序
  bool stock_sort = 5;
  // 库存排序是否升序
  bool stock_sort_asc = 6;
  // sku no
  string sku_no = 7;
  // sku status
  optional int32 sku_status = 8;
}
message ListGoodsStockResp {
  repeated GoodsStock list = 1;
  common.RespPage page = 2;
}
message GoodsStock {
  // 商品名称
  string goods_name = 1;
  // 商品编号
  int32 goods_id = 2;
  // 库存
  int32 stock = 3;
  // 预警库存
  int32 stock_alarm_num = 4;
  // sku_id
  int32 sku_id = 5;
  // sku name
  string sku_name = 6;
  // sku 图片
  string sku_image = 7;
  // sku no
  string sku_no = 8;
  // sku 上/下架状态
  int32 sku_status = 9;
  // sku 上/下架状态 text
  string sku_status_text = 10;
}

message SetStockAlarmReq {
  // 库存预警数量
  int32 num = 1;
  // 手机号
  string mobile = 2;
}
message SetStockAlarmResp {}

message GetStockAlarmReq {}
message GetStockAlarmResp {
  // 库存预警数量
  int32 num = 1;
  // 手机号
  string mobile = 2;
  // 是否有设置
  bool is_set = 3;
  // 商品总数
  int32 goods_num = 4;
}

message ModifyGoodsStockReq {
  // sku_id
  int32 sku_id = 1;
  // 增加或减少库存数量
  int32 num = 2;
  // 操作类型 1-增加库存 2-减少库存
  int32 type = 3;
}
message ModifyGoodsStockResp {}

message StockLogReq {
  // sku_id
  int32 sku_id = 1;
  // page
  common.ReqPage page = 2;
}
message StockLogResp {
  repeated StockLog list = 1;
  common.RespPage page = 2;
}
message StockLog {
  // 操作前数量
  int32 old_num = 1;
  // 数量
  int32 num = 2;
  // 操作时间
  string create_time = 3;
  // 操作类型 1-增加库存 2-减少库存
  int32 type = 4;
  // 操作人
  string operator = 5;
  // 备注
  string type_text = 6;
}

message CategoryTreeReq {
  //类目类型: 0-全部 1=虚拟商品 2=实物
  int32 type = 1;
}
message CategoryTreeResp {
  repeated CategoryTree data = 1;
}

message CategoryTree {
  // 分类ID
  int32 id = 1;
  // 分类名称
  string name = 2;
  // 父级分类ID
  int32 pid = 3;
  // 子级分类
  repeated CategoryTree children = 4;
  int32 status = 5;
}

message BrandListReq {
  // 品牌名称
  string name = 1;
  // id
  int32 id = 2;
  common.ReqPage page = 3;
  // 类型 0-全部 1-实物 2-虚拟
  int32 type = 4;
}
message BrandListResp {
  repeated BrandData list = 1;
  common.RespPage page = 2;
}
message BrandData {
  // 品牌ID
  int32 id = 1;
  // 品牌名称
  string name = 2;
  // status
  int32 status = 3;
}

message UploadGoodsReq {
  // 文件地址
  string fileUrl = 1;
  // 动作 2-实物商品 1-虚拟商品
  int32 action = 2;
}
message UploadGoodsResp {
  repeated UploadGoodsData list = 1;
  // 总数
  int64 total = 2;
  // 错误数
  int64 errorTotal = 3;
  // json文件oss地址
  string jsonUrl = 4;
}
// 解析后的数据模型
// 序号	品牌	商品类目	商品名称	商品图片 是否上架	税率	税率备注	规格	规格图片	原厂编码	第三方编码	供应价	市场价	库存	物流模板	禁售地区	描述信息
message UploadGoodsData {
  // 序号
  string index = 1;
  // 品牌
  string brand = 2;
  // 商品类目 类目一/类目二/类目三
  repeated string category = 3;
  // 商品名称
  string goodsName = 4;
  // 商品图片
  string goodsImage = 5;
  // 税率
  double taxRate = 6;
  // 税率备注
  string taxRateRemark = 7;
  // 规格 颜色：颜色1；尺寸：尺寸1；内存：内存1；
  string spec = 8;
  // 规格图片
  string specImage = 9;
  // 原厂编码
  string originalCode = 10;
  // 第三方编码
  string thirdCode = 11;
  // 供应价
  double supplyPrice = 12;
  // 市场价
  double marketPrice = 13;
  // 库存
  int32 stock = 14;
  // 物流模板
  string logisticsTemplate = 15;
  // 禁售地区
  repeated string banSaleAreas = 16;
  // 描述信息
  string remark = 17;
  // 是否上架
  bool isOnSale = 18;
  // 错误信息
  repeated string errors = 19;
  // 商品OSS图片
  string goodsOssImage = 20;
  // 商品规则oss图片
  string specOssImage = 21;
  // 商品图片bytes base64
  string goodsImageBytesB64 = 22;
  string goodsImageBytesB64Ext = 23;
  // 商品规则图片bytes base64
  string specImageBytesB64 = 24;
  string specImageBytesB64Ext = 25;
  // 关联商品名称
  string goodsNameRelation = 26;
  string relationGoodsId = 27;
  // 商品类型
  int32 goodsType = 28;

}

message SaveGoodsListReq {
  repeated UploadGoodsData list = 1;
  // json文件oss地址
  string jsonUrl = 4;
}
message SaveGoodsListResp {}