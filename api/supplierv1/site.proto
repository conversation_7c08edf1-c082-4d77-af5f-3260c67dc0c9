syntax = "proto3";

package api.supplierv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/supplierv1;supplierv1";

//站点管理-B端
service Site {
  rpc All(SiteAllReq) returns (SiteAllRsp) {
    option (google.api.http) = {get: "/supplier/v1/site/all"};
  }
}



message SiteAllReq {}

message SiteAllRsp {
  message site {
    //站点ID
    int32 id = 1;
    //站点名称
    string name = 2;
    //站点域名
    string domain = 3;
    //状态:1=禁用,2=启用
    int32 status = 6;
    //是否是主站点 1=否，2=是
    int32 is_default = 7;
  }
  repeated site list = 1;
}
