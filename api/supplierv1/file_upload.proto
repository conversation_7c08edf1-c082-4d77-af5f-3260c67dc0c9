syntax = "proto3";

package api.supplierv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/supplierv1;supplierv1";

//文件上传
service FileUpload {
  //获取文件配置
  rpc Token(FileUploadTokenReq) returns (FileUploadTokenRsp) {
    option (google.api.http) = {get: "/supplier/v1/upload/token"};
  }
}

message FileUploadTokenReq{
  string file_name = 1;
}

message FileUploadTokenRsp {
  string access_id = 1;
  string host = 2;
  string policy = 3;
  string signature = 4;
  int64 expire = 5;
  string dir = 6;
  string file_name = 7;
  string url = 8;
  string preview_url = 9;
  string key = 10;
}