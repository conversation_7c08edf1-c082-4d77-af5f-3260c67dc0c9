syntax = "proto3";

package api.supplierv1;

import "google/api/annotations.proto";

option go_package = "cardMall/api/supplierv1;supplierv1";

service AreaSrv {
  rpc AllProvince(AllProvinceReq) returns (AllProvinceResp) {
    option (google.api.http) = {
      post: "/supplier/v1/area/province"
      body: "*"
    };
  }

  rpc ListProvince(ListProvinceReq) returns (ListProvinceResp) {
    option (google.api.http) = {
      post: "/supplier/v1/area/regions"
      body: "*"
    };
  }
}

message AllProvinceReq {}
message AllProvinceResp {
  repeated ProvinceNode data = 1;
}
message ProvinceNode {
  int32 id = 1;
  string name = 2;
  string short_name = 3;
}

message ListProvinceReq{}
message ListProvinceResp{
  repeated ProvinceAreaNode regions = 1;
}
message ProvinceAreaNode {
  string title = 1;
  repeated ProvinceNode items = 2;
}
