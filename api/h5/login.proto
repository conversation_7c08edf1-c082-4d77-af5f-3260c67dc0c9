syntax = "proto3";

package api.h5;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/h5;h5";
option java_multiple_files = true;
option java_package = "api.h5";

// 登录-h5
service Login {
  // h5登录
  rpc Login(LoginReq) returns (LoginRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/login"
      body: "*"
    };
  }

  // h5登录发送短信验证码
  rpc SendSMS(LoginSendSMSReq) returns (LoginSendSMSRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/login/sendSMS"
      body: "*"
    };
  }

  // 微信内获取授权url
  rpc WxAuthUrl(LoginWxAuthUrlReq) returns (LoginWxAuthUrlRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/login/wxAuthUrl"
      body: "*"
    };
  }

  // 微信内获取用户openId
  rpc WxOpenId(LoginWxOpenIdReq) returns (LoginWxOpenIdRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/login/wxOpenId"
      body: "*"
    };
  }
}

message LoginReq {
  //手机号
  string phone_number = 1;
  //验证码
  string verify_code = 2;
  //验证码唯一标识
  string verify_id = 3;
  //客户端类型: 1=微信小程序，2=h5，3=微信公众号， 4=支付宝小程序
  int32 client_type = 4;
}

message LoginRsp {
  // 登录token
  string token = 1;
  // 用户ID
  int32 user_id = 4;
  //手机号
  string phone_number = 5;
  //头像
  string avatar_url = 6;
  //昵称
  string nick_name = 7;
  //微信公众号openid
  string wx_official_open_id = 8;
}

message LoginSendSMSReq {
  // 手机号
  string phone_number = 1;
}

message LoginSendSMSRsp {
  // 验证码唯一标识
  string verify_id = 1;
  // 有效期
  int32 expire_time = 2;
}

message LoginWxAuthUrlReq {
  // 授权后回调地址
  string return_url = 1;
}

message LoginWxAuthUrlRsp {
  string url = 1;
}

message LoginWxOpenIdReq {
  string code = 1;
}

message LoginWxOpenIdRsp {}
