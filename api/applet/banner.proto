syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";

//轮播图-C端
service Banner {
  //列表
  rpc All(BannerAllReq) returns (BannerAllRsp) {
    option (google.api.http) = {get: "/app/v1/banner"};
  }
  //列表
  rpc Check(BannerCheckReq) returns (BannerCheckRsp) {
    option (google.api.http) = {get: "/app/v1/banner/check"};
  }
}

message BannerAllReq {}

message BannerAllRsp {
  message BannerListItem {
    //数据ID
    int32 id = 1;
    //名称
    string name = 2;
    //图片
    string image = 3;
    //排序
    int32 sort = 4;
    //关联类型
    int32 relation_type = 5;
    //关联节点
    string relation_value = 6;
    //关联节点ID
    repeated int32 relation_value_ids = 7;
  }
  repeated BannerListItem list = 1;
}

message BannerCheckReq {
  //数据id
  int32 id = 1;
}

message BannerCheckRsp {}
