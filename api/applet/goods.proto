syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";

//商品-C端
service Goods {
  //推荐商品
  rpc Recommend(GoodsRecommendReq) returns (GoodsRecommendRsp) {
    option (google.api.http) = {get: "/app/v1/goods/recommend"};
  }
  //商品列表
  rpc List(GoodsListReq) returns (GoodsListRsp) {
    option (google.api.http) = {
      post: "/app/v1/goods/list"
      body: "*",
    };
  }
  //商品详情
  rpc Detail(GoodsDetailReq) returns (GoodsDetailRsp) {
    option (google.api.http) = {get: "/app/v1/goods/detail"};
  }
  //商品运费
  rpc GoodsFreightFee(GoodsFreightFeeReq) returns (GoodsFreightFeeResp) {
    option (google.api.http) = {
      post: "/app/v1/goods/goodsFreightFee"
      body: "*"
    };
  }
  //商品搜索
  rpc Search(GoodsSearchReq) returns (GoodsSearchRsp) {
    option (google.api.http) = {get: "/app/v1/goods/search"};
  }
  //获取跳转商品详情页面参数
  rpc DetailParams(GoodsDetailParamsReq) returns (GoodsDetailParamsRsp) {
    option (google.api.http) = {get: "/app/v1/goods/detailParams"};
  }

  //获取商品sku信息--下单页面获取sku信息
  rpc SkuInfo(GoodsSkuInfoReq) returns (GoodsSkuInfoRsp) {
    option (google.api.http) = {
      post: "/app/v1/goodsSku/info"
      body: "*"
    };
  }

  //折扣商品
  rpc Discount(GoodsDiscountReq) returns (GoodsDiscountRsp) {
    option (google.api.http) = {get: "/app/v1/goods/discount"};
  }

  // GetGoodsSkuListByActivityId 获取活动页商品列表
  rpc GetGoodsSkuListByActivityId(GetGoodsSkuListByActivityIdReq) returns (GetGoodsSkuListByActivityIdRsp) {
    option (google.api.http) = {
      post: "/app/v1/goods/activity/goodsSkuList"
      body: "*"
    };
  }

  // 获取限时折扣商品列表
  rpc FlashSaleSkuList(FlashSaleSkuListReq) returns (FlashSaleSkuListRsp) {
    option (google.api.http) = {
      get: "/app/v1/goods/flashSale/goodsSkuList"
    };
  }

  // 获取限时折扣商品详情
  rpc FlashSaleSkuDetail(FlashSaleSkuDetailReq) returns (FlashSaleSkuDetailRsp) {
    option (google.api.http) = {
      get: "/app/v1/goods/flashSale/goodsSkuDetail"
    };
  }
}

message GoodsRecommendReq {
  //数量
  int32 size = 1;
  //类型:1=虚拟商品,2=实物
  int32 type = 2;
  //分类ID
  int32 category_id = 3;
  //品牌ID
  int32 brand_id = 4;
  //页码
  int32 page = 5;
  //每页数量
  int32 page_size = 6;
  //是否是积分商品
  bool is_integral = 7;
  // 积分-下限
  int32 pay_integral_min = 8;
  // 积分-上限
  int32 pay_integral_max = 9;
  // 商品展示方式 1=SPU，2=SKU
  int32 goods_show_type = 10;
  // 排序 0 默认，1=按销量，2=价格升序，3=价格降序
  int32 order_by = 11;
}

message GoodsRecommendRsp {
  message GoodsRecommendItem {
    //商品ID
    int32 id = 1;
    //商品编码
    string sku_no = 2;
    //商品名称
    string name = 3;
    //图片
    string image = 4;
    //售价
    string sale_price = 5;
    //销量
    int32 sale_volume = 6;
    //品牌ID
    int32 brand_id = 7;
    //类目ID
    int32 category_id = 8;
    //积分
    int32 sale_integral = 9;
    //市场价
    string market_price = 10;
  }
  repeated GoodsRecommendItem list = 1;
  int32 count = 2;
  int32 page = 3;
}

message GoodsListReq {
  //品牌ID
  int32 brand_id = 1;
  //分类ID
  int32 category_id = 2;
  //页码
  int32 page = 3;
  //每页数量
  int32 page_size = 4;
  // 积分-下限
  int32 pay_integral_min = 5;
  // 积分-上限
  int32 pay_integral_max = 6;
  //是否是积分商品
  bool is_integral = 7;
  // 商品展示方式 1=SPU，2=SKU
  int32 goods_show_type = 8;
  // 排序 0 默认，1=按销量，2=价格升序，3=价格降序
  int32 order_by = 9;
  // 品牌ID列表
  repeated int32 brand_ids = 10;
  // 分类ID列表
  repeated int32 category_ids = 11;
}

message GoodsListRsp {
  message GoodsListItem {
    //商品ID
    int32 id = 1;
    //商品编码
    string sku_no = 2;
    //商品名称
    string name = 3;
    //图片
    string image = 4;
    //售价
    string sale_price = 5;
    //销量
    int32 sale_volume = 6;
    //品牌ID
    int32 brand_id = 7;
    //类目ID
    int32 category_id = 8;
    //积分
    int32 sale_integral = 10;
    //市场价
    string market_price = 11;
  }
  repeated GoodsListItem list = 1;
  int32 count = 2;
  int32 page = 3;
}

message GoodsFreightFeeReq{
  // sku_no
  string sku_no = 1;
  // 省份ID
  int32 area_id = 2;
  // 数量
  int32 num = 3;
}
message GoodsFreightFeeResp {
  // 运费
  double freight_fee = 1;
  // 是否包邮
  bool is_free = 2;
}

message GoodsDetailReq {
  //商品ID
  int32 id = 1;
}

message GoodsDetailRsp {
  message GoodsSkuItem {
    //skuId
    int32 id = 1;
    //sku名称
    string name = 2;
    //图片
    string image = 3;
    //售价
    string sale_price = 4;
    //库存
    int32 stock = 5;
    //虚拟商品类型:1=直充,2=卡密
    int32 base_goods_type = 6;
    //sku编码
    string sku_no = 7;
    //积分
    int32 sale_integral = 8;
    //市场价
    string market_price = 9;
    string fullName = 10;
  }
  //商品ID
  int32 id = 1;
  //商品名称
  string name = 2;
  //类型:1=虚拟商品,2=实物
  int32 type = 3;
  //图片
  repeated string images = 4;
  //详情
  string detail = 5;
  //销量
  int32 sale_volume = 6;
  //sku
  repeated GoodsSkuItem sku = 7;
  //规格
  repeated string spec = 8;
}

message GoodsSearchReq {
  //名称
  string name = 1;
  int32 page = 2;
  int32 page_size = 3;
  // 商品展示方式 1=SPU，2=SKU
  int32 goods_show_type = 4;
  // 排序 0 默认，1=按销量，2=价格升序，3=价格降序
  int32 order_by = 5;
}

message GoodsSearchRsp {
  message GoodsSearchItem {
    //商品ID
    int32 id = 1;
    //sku编码
    string sku_no = 2;
    //商品名称
    string name = 3;
    //图片
    string image = 4;
    //售价
    string sale_price = 5;
    //销量
    int32 sale_volume = 6;
    //品牌ID
    int32 brand_id = 7;
    //类目ID
    int32 category_id = 8;
    //积分
    int32 sale_integral = 9;
    //市场价
    string market_price = 10;
  }
  repeated GoodsSearchItem list = 1;
  int32 count = 2;
  int32 page = 3;
}

message GoodsDetailParamsReq {
  //商品ID
  int32 goods_id = 1;
}

message GoodsDetailParamsRsp {
  //商品ID
  int32 goods_id = 1;
  //品牌ID
  int32 brand_id = 2;
  //类目ID
  int32 goods_category_id = 3;
}

message GoodsSkuInfoReq {
  //sku编码 source=2时必填
  string sku_no = 1;
  //购车数据ID
  repeated int32 cart_id = 2;
  //订单ID
  int32 order_id = 3;
  //下单来源：1=购物车，2=商品详情页，3=订单再次购买
  int32 source = 4;
  //sku编码 source=3时必传 选填，售后详情页面再次购买使用
  repeated string order_sku_no = 5;
  message Goods {
    //sku编码
    string sku_no = 1;
    //购买数量
    int32 num = 2;
  }
  repeated Goods goods = 6;
  //活动ID
  int32 activity_id = 7;
}

message GoodsSkuInfoRsp {
  message GoodsSkuInfoItem {
    //skuID
    int32 id = 1;
    //商品名称
    string goods_name = 2;
    //图片
    string image = 3;
    //售价
    string sale_price = 4;
    //库存
    int32 stock = 5;
    //状态：1=上架,0=下架
    int32 status = 6;
    //商品ID
    int32 goods_id = 7;
    //限购数量
    int32 num_limit = 8;
    //虚拟商品类型:1=直充,2=卡密
    int32 base_goods_type = 9;
    //sku名称
    string sku_name = 10;
    //商品类型:1=虚拟商品,2=实物
    int32 goods_type = 11;
    //购买数量
    int32 quantity = 12;
    //sku
    string sku_no = 13;
    //积分
    int32 sale_integral = 14;
  }
  repeated GoodsSkuInfoItem list = 1;
  double freight_fee = 2;
}

message GoodsDiscountReq {
  //数量
  int32 size = 1;
  //是否是积分商品
  bool is_integral = 2;
}

message GoodsDiscountRsp {
  message GoodsDiscountItem {
    //商品ID
    int32 id = 1;
    //skuId
    int32 sku_id = 2;
    //商品名称
    string name = 3;
    //图片
    string image = 4;
    //折扣
    string discount = 5;
    //类目ID
    int32 category_id = 6;
    //市场价
    string market_price = 7;
  }
  repeated GoodsDiscountItem list = 1;
}

message GetGoodsSkuListByActivityIdReq {
  //活动ID
  int32 activity_id = 1;
  int32 page = 2;
  int32 page_size = 3;
}

message GetGoodsSkuListByActivityIdRsp {
  message GoodsSkuInfoItem {
    //skuID
    int32 id = 1;
    //商品名称
    string goods_name = 2;
    //图片
    string image = 3;
    //售价
    string sale_price = 4;
    //库存
    int32 stock = 5;
    //状态：1=上架,0=下架
    int32 status = 6;
    //商品ID
    int32 goods_id = 7;
    //限购数量
    int32 num_limit = 8;
    //虚拟商品类型:1=直充,2=卡密
    int32 base_goods_type = 9;
    //sku名称
    string sku_name = 10;
    //商品类型:1=虚拟商品,2=实物
    int32 goods_type = 11;
    //购买数量
    int32 quantity = 12;
    //sku
    string sku_no = 13;
    //积分
    int32 sale_integral = 14;
    //销售量
    int32 sale_volume = 15;
    // 类目ID
    int32 category_id = 16;
  }
  repeated GoodsSkuInfoItem list = 1;
  int32 page = 2;
  int32 page_size = 3;
  int32 total = 4;
}

message FlashSaleSkuListReq {
  //活动ID
  int32 activity_id = 1;
  //页码
  int32 page = 2;
  //每页数量
  int32 page_size = 3;
}

message FlashSaleSkuListRsp {
  message FlashSaleSkuListItem {
    //sku编码
    string sku_no = 1;
    //商品名称
    string name = 2;
    //图片
    string image = 3;
    //售价
    string sale_price = 4;
    //限时折扣价
    string discount_price = 5;
    //库存
    int32 stock = 6;
    //活动总库存
    int32 total_stock = 7;
    //限购数量
    int32 limit_num = 8;
  }
  repeated FlashSaleSkuListItem list = 1;
  int32 count = 2;
  int32 page = 3;
}

message FlashSaleSkuDetailReq {
  //活动ID
  int32 activity_id = 1;
  //sku编码
  string sku_no = 2;
}

message FlashSaleSkuDetailRsp {
  message GoodsSkuItem {
    //skuId
    int32 id = 1;
    //sku名称
    string name = 2;
    //图片
    string image = 3;
    //售价
    string sale_price = 4;
    //库存
    int32 stock = 5;
    //虚拟商品类型:1=直充,2=卡密
    int32 base_goods_type = 6;
    //sku编码
    string sku_no = 7;
    //积分
    int32 sale_integral = 8;
    //市场价
    string market_price = 9;
    //商品名称
    string fullName = 10;
    //限购数量
    int32 num_limit = 11;
  }
  //商品ID
  int32 id = 1;
  //商品名称
  string name = 2;
  //类型:1=虚拟商品,2=实物
  int32 type = 3;
  //图片
  repeated string images = 4;
  //详情
  string detail = 5;
  //销量
  int32 sale_volume = 6;
  //sku
  repeated GoodsSkuItem sku = 7;
}