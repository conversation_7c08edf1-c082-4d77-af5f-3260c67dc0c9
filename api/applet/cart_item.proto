syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/applet;applet";

//购物车-H5
service Cart {
  //购物车列表
  rpc GetCart(GetCartReq) returns (GetCartRsp) {
    option (google.api.http) = {get: "/app/h5/v1/getCart"};
  }
  //清空购物车
  rpc DeleteCart(DeleteCartReq) returns (DeleteCartRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/deleteCart"
      body: "*"
    };
  }
  //添加购物车
  rpc AddCartItem(AddCartItemReq) returns (AddCartItemRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/addCartItem"
      body: "*"
    };
  }
  //修改购物车
  rpc UpdateCartItem(UpdateCartItemReq) returns (UpdateCartItemRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/updateCartItem"
      body: "*"
    };
  }
  //删除购物车其中一个
  rpc DeleteCartItem(DeleteCartItemReq) returns (DeleteCartItemRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/deleteCartItem"
      body: "*"
    };
  }
  //修改购物车
  rpc UpdateCartQuantity(UpdateCartQuantityReq) returns (UpdateCartQuantityRsp) {
    option (google.api.http) = {
      post: "/app/h5/v1/updateCartQuantity"
      body: "*"
    };
  }
}

message GetCartReq {
  // 购物车id  分页用
  int64 cart_item_id = 1;
  // 购物车查询个数 必填
  int64 size = 2 [(validate.rules).int64 = {gt: 0}];
}

message CartItemInfo {
  // 购物车id
  int64 cart_item_id = 1;
  // 商品ID
  int64 goods_id = 2;
  // 商品SKUID
  int64 goods_sku_id = 5;
  // 购买数量
  int32 quantity = 7;
  // 添加到购物车的价格
  string price = 8;
  // 创建时间
  string create_time = 13;
  // 修改时间
  string update_time = 14;
  //sku编码
  string goods_sku_no = 15;
  CartGoodsInfo cartGoodsInfo = 16;
}

message CartGoodsInfo {
  // 商品类型:1=虚拟，2=实物
  int32 type = 22;
  // 虚拟商品类型:1=直充,2=卡密
  int32 base_goods_type = 24;
  //商品名称
  string goods_name = 1;
  //商品编号
  int64 goods_id = 2;
  // 市场价
  string market_price = 3;
  // 售价【建议零售价】
  string sale_price = 4;
  // 状态:0=下架,1=上架
  int32 goods_sku_status = 5;
  // 状态:0=下架,1=上架
  int32 status = 9;
  //商品sku编号
  int64 goods_sku_id = 12;
  //商品sku名称
  string goods_sku_name = 13;
  // 所属分类
  int64 category_id = 14;
  // 类目
  string category_name = 15;
  // 品牌
  int64 brand_id = 16;
  // 品牌名称
  string brand_name = 17;
  // 商品图片
  string image = 18;
  // 规格图片商品图片
  string goods_sku_image = 21;

  string goods_sku_no = 26;

  // 库存
  int32 stock = 23;
  // 限购数量
  int32 num_limit = 25;
  int32  sale_integral = 27;
}

message GetCartRsp {
  repeated CartItemInfo cartItems = 1;
  bool has_more = 2;
  // 总数
  int64 total = 3;
}

message DeleteCartReq {}

message DeleteCartRsp {
  //影响数据行数
  int64 effect_row = 1;
}

message AddCartItemReq {
  int64 goods_id = 1 [(validate.rules).int64 = {gt: 0}]; // 商品ID
  string goods_sku_no = 2; // 商品SKUno
  int64 quantity = 3 [(validate.rules).int64 = {
    gt: 0
    lte: 100
  }]; // 数量
}

message AddCartItemRsp {
  //影响数据行数
  int64 effect_row = 1;
}

message UpdateCartItemReq {
  int64 cart_item_id = 1 [(validate.rules).int64 = {gt: 0}];
  int64 goods_id = 2 [(validate.rules).int64 = {gt: 0}]; // 商品ID
  string goods_sku_no = 3; // 商品SKUno
  int64 quantity = 4 [(validate.rules).int64 = {
    gt: 0
    lte: 100
  }];
}

message UpdateCartItemRsp {
  //影响数据行数
  int64 effect_row = 1;
}

message DeleteCartItemReq {
  repeated int64 cart_item_ids = 2 [(validate.rules).repeated.min_items = 1];
}

message DeleteCartItemRsp {
  //影响数据行数
  int64 effect_row = 1;
}

message UpdateCartQuantityReq {
  int64 cart_item_id = 1 [(validate.rules).int64 = {gt: 0}];
  int64 quantity = 4 [(validate.rules).int64 = {
    gt: 0
    lte: 100
  }];
}

message UpdateCartQuantityRsp {
  //影响数据行数
  int64 effect_row = 1;
}
