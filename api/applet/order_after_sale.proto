syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//实物订单售后-C端
service OrderAfterSale {
  // 售后列表
  rpc EntityList(OrderAfterSaleEntityListReq) returns (OrderAfterSaleEntityListRsp) {
    option (google.api.http) = {get: "/app/v1/order/afterSale/entityList"};
  }

  //用户申请售后
  rpc EntityCreate(OrderAfterSaleEntityCreateReq) returns (OrderAfterSaleEntityCreateRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/entity"
      body: "*"
    };
  }
  //用户撤销售后申请
  rpc EntityCancel(OrderAfterSaleEntityCancelReq) returns (OrderAfterSaleEntityCancelRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/entity/cancel"
      body: "*"
    };
  }

  //用户提交退货物流信息
  rpc EntityDeliver(OrderAfterSaleEntityDeliverReq) returns (OrderAfterSaleEntityDeliverRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/entity/deliver"
      body: "*"
    };
  }

  // 售后详情
  rpc EntityDetail(OrderAfterSaleEntityDetailReq) returns (OrderAfterSaleEntityDetailRsp) {
    option (google.api.http) = {get: "/app/v1/order/afterSale/entity/detail"};
  }

  // 售后日志
  rpc EntityLog(OrderAfterSaleEntityLogReq) returns (OrderAfterSaleEntityLogRsp) {
    option (google.api.http) = {get: "/app/v1/order/afterSale/entity/log"};
  }

  // 售后物流--用户退货物流
  rpc EntityLogistic(OrderAfterSaleEntityLogisticReq) returns (OrderAfterSaleEntityLogisticRsp) {
    option (google.api.http) = {get: "/app/v1/order/afterSale/entity/logistic"};
  }

  // 修改运单号 -- 仅用户已发货状态可调用
  rpc EntityUpdateDeliver(OrderAfterSaleEntityUpdateDeliverReq) returns (OrderAfterSaleEntityUpdateDeliverRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/entity/updateDeliver"
      body: "*"
    };
  }

  // 修改申请 -- 仅待审核状态可调用
  rpc EntityUpdate(OrderAfterSaleEntityUpdateReq) returns (OrderAfterSaleEntityUpdateRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/entity/update"
      body: "*"
    };
  }

  // 获取订单可已申请售后的商品
  rpc EntityGoods(OrderAfterSaleEntityGoodsReq) returns (OrderAfterSaleEntityGoodsRsp) {
    option (google.api.http) = {get: "/app/v1/order/afterSale/entity/goods"};
  }

  // 获取预计商品可退款金额
  rpc EntityRefundAmount(OrderAfterSaleEntityRefundAmountReq) returns (OrderAfterSaleEntityRefundAmountRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/entity/refundAmount"
      body: "*"
    };
  }

  rpc EntityRefundAmountByUpdate(EntityRefundAmountByUpdateReq) returns (OrderAfterSaleEntityRefundAmountRsp) {
    option (google.api.http) = {
      get: "/app/v1/order/afterSale/entity/refundAmountByUpdate"
    };
  }

  // 退货详情
  rpc EntityDeliverDetail(OrderAfterSaleEntityDeliverDetailReq) returns (OrderAfterSaleEntityDeliverDetailRsp) {
    option (google.api.http) = {get: "/app/v1/order/afterSale/entity/deliverDetail"};
  }
}

message OrderAfterSaleEntityCreateReq {
  message Skus {
    // sku编码
    string sku_no = 1;
    // 源skuNo
    string original_sku_no = 2;
    // 数量
    int32 num = 3;
    // afterSale/entity/goods 中的订单号
    string source_order_no = 4;
    //换货sku编码-申请换货时必传
    string exchange_sku_no = 5;
    //换货数量-申请换货时必传
    int32 exchange_num = 6;
  }
  //订单号
  string order_no = 1;
  //售后方式 1=仅退款,2=退货退款, 3=换货, 4=补发
  int32 type = 2;
  //售后商品
  repeated Skus skus = 3;
  // 收货状态 1=未收货,2=已收货
  int32 receive_status = 5;
  // 退货原因
  string reason = 6;
  // 退货原因图片
  repeated string images = 7;
  // 退货原因描述
  string remark = 8;
  // 退款商品金额 type=1、2时必填
  double refund_goods_amount = 9;
  // 退款运费 type=1、2时必填
  double refund_freight_fee = 10;
}

message OrderAfterSaleEntityCreateRsp {
  // 售后ID
  int32 id = 1;
}

message OrderAfterSaleEntityCancelReq {
  //售后ID
  int32 id = 1;
}

message OrderAfterSaleEntityCancelRsp {}

message OrderAfterSaleEntityDeliverReq {
  //售后ID
  int32 id = 1;
  // 退货物流公司ID
  int32 kd_id = 2;
  // 退货物流单号
  string express_no = 3;
  // 退货物流备注
  string remark = 4;
  // 退货物流凭证
  repeated string images = 5;
  // 联系电话
  string contact_phone = 6;
}

message OrderAfterSaleEntityDeliverRsp {}

message OrderAfterSaleEntityListReq {
  int32 page = 2;
  int32 page_size = 3;
}

message OrderAfterSaleEntityListRsp {
  int32 page = 2;
  int32 count = 3;
  // 售后商品
  repeated SaleEntityListItem list = 1;
  message SaleEntityListItem {
    message Skus {
      // 商品名称
      string name = 1;
      // 数量
      int32 num = 2;
      // 售价
      string sale_price = 3;
      // 商品图片
      string image = 4;
      // sku ID
      int32 sku_id = 5;
      // sku编号
      string sku_no = 6;
      // 商品
      int32 goods_id = 7;
      // 使用积分
      int32 sale_integral = 8;
    }
    // 售后ID
    int32 id = 1;
    // 订单id
    int32 order_id = 15;

    // 订单号
    string order_number = 14;
    // 退款金额
    string refund_amount = 2;
    // 售后原因
    string reason = 3;
    // 售后服务单状态售后状态
    // 退款退货：21-退货物流中 22-商家/供应商确认收货
    // 换货：31-退货物流中 32-商家/供应商确认收货
    // 公共状态：1-待处理 2-审核通过3-审核拒绝 7-买家主动关闭 8-卖家关闭 9-退款/换货完成
    int32 status = 4;
    //售后类型 1-仅退款 2-退款退货 3-换货
    int32 type = 5;
    //申请时间
    string create_time = 6;
    //售后单号
    string after_sale_no = 7;
    // 换货订单号 仅换货时存在
    string exchange_order_no = 8;
    // 售后商品
    repeated Skus skus = 9;
    // 换货商品
    repeated Skus exchange_skus = 17;
    // 收货状态 1-未收到货 2-已收货
    int32 receive_status = 11;
    // 补充描述
    string remark = 12;
    // 凭证
    repeated string images = 13;
    //订单状态:0=待支付，1=待充值，2=充值中,3=充值完成,4=充值失败,5=退款中,6=部分退款中,7=部分退款,8=已退款,9=已收货,10=售后中,11=售后完成,12=已取消,13=主订单待支付，14=主订单已取消,15=主订单已支付,20=待确认，21=供应商取消
    int32 order_status = 16;
    //源订单号
    string original_order_no = 18;
    // 售后状态 0-无售后 1-售后中 2-售后完成 3-售后关闭
    int32 after_sale_status = 19;
    // 可申请换货
    bool can_after_sale_exchange = 20;
    // 可申请退款 / 退货退款
    bool can_after_sale_refund = 21;
    // 正在售后中
    bool after_sale_is_dealing = 22;
    // 能申请全部 仅退款/退货退款
    bool can_after_sale_refund_all = 23;
    // 订单扩展类型
    int32 ext_type = 24;
  }
}

message OrderAfterSaleEntityDetailReq {
  //订单id
  int32 order_id = 1;
  //售后id
  int32 after_sale_id = 2;
}

message OrderAfterSaleEntityDetailRsp {
  message Skus {
    // 商品名称
    string name = 1;
    // 数量
    int32 num = 2;
    // 售价
    string sale_price = 3;
    // 商品图片
    string image = 4;
    // sku ID
    int32 sku_id = 5;
    // sku编号
    string sku_no = 6;
    // 商品
    int32 goods_id = 7;
    // 使用积分
    int32 sale_integral = 25;
  }
  message TipMsg {
    string title = 1;
    string msg = 2;
    string extra = 3;
  }
  // 售后ID
  int32 id = 1;
  // 订单id
  int32 order_id = 15;
  // 退款金额
  double refund_amount = 2;
  // 售后原因
  string reason = 3;
  // 售后服务单状态售后状态
  // 退款退货：21-退货物流中 22-商家/供应商确认收货
  // 换货：31-退货物流中 32-商家/供应商确认收货
  // 公共状态：1-待处理 2-审核通过3-审核拒绝 7-买家主动关闭 8-卖家关闭 9-退款/换货完成
  int32 status = 4;
  //售后类型 1-仅退款 2-退款退货 3-换货
  int32 type = 5;
  //申请时间
  string create_time = 6;
  //售后单号
  string after_sale_no = 7;
  // 换货订单号 仅换货时存在
  string exchange_order_no = 8;
  // 售后商品
  repeated Skus skus = 9;

  // 换货商品
  repeated Skus exchange_skus = 17;
  // 售后流程提示
  repeated TipMsg tip_msg = 10;
  // 收货状态 1-未收到货 2-已收货
  int32 receive_status = 11;
  // 补充描述
  string remark = 12;
  // 凭证
  repeated string images = 13;
  //所有商品都已经售后了 0/1
  int32 after_sale_all = 14;

  //订单状态:0=待支付，1=待充值，2=充值中,3=充值完成,4=充值失败,5=退款中,6=部分退款中,7=部分退款,8=已退款,9=已收货,10=售后中,11=售后完成,12=已取消,13=主订单待支付，14=主订单已取消,15=主订单已支付,20=待确认，21=供应商取消
  int32 order_status = 16;

  //源订单号
  string original_order_no = 18;
  // 售后状态 0-无售后 1-售后中 2-售后完成 3-售后关闭
  int32 after_sale_status = 27;
  // order_number 订单号
  string order_number = 28;
  // 可申请换货
  bool can_after_sale_exchange = 29;
  // 可申请退款 / 退货退款
  bool can_after_sale_refund = 30;
  // 正在售后中
  bool after_sale_is_dealing = 31;
  // 能申请全部 仅退款/退货退款
  bool can_after_sale_refund_all = 23;
  // 退款商品金额
  double refund_goods_amount = 32;
  // 运费
  double refund_freight_fee = 33;
  // 订单扩展类型
  int32 ext_type = 24;
  // 礼品卡抵扣金额
  double refund_card_gift_amount = 34;
  bool afterSaleOnlyOne = 35;
}

message OrderAfterSaleEntityLogReq {
  // 售后ID
  int32 id = 1;
}

message OrderAfterSaleEntityLogRsp {
  message Logs {
    // 操作状态
    // 退款退货：21-退货物流中 22-商家/供应商确认收货
    // 换货：31-退货物流中 32-商家/供应商确认收货
    // 公共状态：1-待处理 2-审核通过3-审核拒绝 7-买家主动关闭 8-卖家关闭 9-退款/换货完成
    int32 status = 1;
    // 操作时间
    string create_time = 2;
    // 操作内容
    string content = 3;
    // 操作用户类型 1-系统 2-用户 3-商家 4-供应商
    int32 user_type = 4;
  }
  repeated Logs logs = 1;
}

message OrderAfterSaleEntityLogisticReq {
  // 售后ID
  int32 id = 1;
}

message OrderAfterSaleEntityLogisticRsp {
  message KdInfo {
    // 快递公司名称
    string name = 1;
    // 物流单号
    string express_no = 2;
  }
  message Skus {
    // 商品名称
    string name = 1;
    // 数量
    int32 num = 2;
    // 商品图片
    string image = 4;
  }
  message Address {
    // 收货人
    string contact_name = 1;
    // 联系电话
    string contact_phone = 2;
    // 收货地址
    string address = 3;
  }
  message AfterSaleLogisticsItem {
    //订单号
    string order_number = 1;
    //快递平台编号
    string kd_code = 2;
    //快递单号
    string logistics_no = 3;
    //物流状态
    string op_desc = 4;
    //所在城市-可能为空
    string address_text = 5;
    //物流信息
    string op_message = 6;
    //物流时间
    string op_time = 7;
  }
  //物流数据
  repeated AfterSaleLogisticsItem list = 1;
  //售后商品
  Skus skus = 2;
  //收货地址
  Address address = 3;
  //快递公司信息
  KdInfo kd_info = 4;
}

message OrderAfterSaleEntityUpdateDeliverReq {
  // 售后ID
  int32 id = 1;
  // 退货物流公司ID
  int32 kd_id = 2;
  // 退货物流单号
  string express_no = 3;
  // 联系电话
  string contact_phone = 4;
  // 退货物流备注
  string remark = 5;
  // 退货物流凭证
  repeated string images = 6;
}

message OrderAfterSaleEntityUpdateDeliverRsp {}

message OrderAfterSaleEntityUpdateReq {
  // 售后ID
  int32 id = 1;
  // 退款金额-仅退款、退货退款时必填
  // double refund_amount = 2;
  // 收货状态 1=未收货,2=已收货
  int32 receive_status = 3;
  // 退货原因
  string reason = 4;
  // 退货原因图片
  repeated string images = 5;
  // 退货原因描述
  string remark = 6;
  // 运费
  double refund_freight_fee = 7;
  // double refund_goods_amount = 3;
  double refund_goods_amount = 8;
}

message OrderAfterSaleEntityUpdateRsp {}

message OrderAfterSaleEntityGoodsReq {
  string order_no = 1;
}

message OrderAfterSaleEntityGoodsRsp {
  message Skus {
    // 订单号
    string order_no = 1;
    // 父订单号
    string parent_order_no = 2;
    // 最原始的skuNo
    string original_sku_no = 3;
    // sku编号
    string sku_no = 4;
//    // 商品数量
//    int32 goods_num = 5;
//    // 已换货数量
//    int32 exchange_num = 6;
//    // 已退款数量
//    int32 refund_num = 7;
    // 商品名称
    string goods_name = 8;
    // 商品图片
    string main_image = 9;
    // tags
    repeated string tags = 10;
    // salePrice
    double sale_price = 11;
    // can_apply_num
    int32 can_apply_num = 12;
    // 是否可退款
    bool can_refund = 13;
    // 是否可以换货
    bool can_exchange = 14;
    // goods_id
    int32 goods_id = 15;
    // is_dealing
    bool order_is_complete = 16;
    // 积分售价
    double sale_integral = 17;
  }
  repeated Skus skus = 1;
  // 可申请换货
  bool can_after_sale_exchange = 2;
  // 可申请退款 / 退货退款
  bool can_after_sale_refund = 3;
  // 正在售后中
  bool after_sale_is_dealing = 4;
  // 能申请全部 仅退款/退货退款
  bool can_after_sale_refund_all = 5;
  // 是否为换货订单
  bool is_exchange_order = 6;
}

message OrderAfterSaleEntityRefundAmountReq {
  message Skus {
    // sku编码
    string sku_no = 1;
    // 源skuNo
    string original_sku_no = 2;
    // 数量
    int32 num = 3;
    // afterSale/entity/goods 中的订单号
    string source_order_no = 4;
    //换货sku编码-申请换货时必传
    string exchange_sku_no = 5;
    //换货数量-申请换货时必传
    int32 exchange_num = 6;
  }
  // 订单号
  string order_no = 1;
  //售后方式 1=仅退款,2=退货退款, 3=换货
  int32 type = 2;
  repeated Skus skus = 3;
}

message EntityRefundAmountByUpdateReq {
  //售后id
  int32 id = 2;
}

message OrderAfterSaleEntityRefundAmountRsp {
  // 退款金额
  double refund_amount = 1;
  // 运费
  double freight_fee = 2;
  // 商品金额
  double refund_goods_amount = 3;
  // 礼品卡退款金额
  double refund_card_gift_amount = 4;
  // 是否使用了礼品卡抵扣
  bool is_card_gift_discount = 5;
}

message OrderAfterSaleEntityDeliverDetailReq {
  // 售后ID
  int32 id = 1;
}

message OrderAfterSaleEntityDeliverDetailRsp {
  // 售后ID
  int32 id = 1;
  // 退货物流公司ID
  int32 kd_id = 2;
  // 退货物流单号
  string express_no = 3;
  // 联系电话
  string contact_phone = 4;
  // 退货物流备注
  string remark = 5;
  // 退货物流凭证
  repeated string images = 6;
  // 物流公司名称
  string kd_name = 7;
}
