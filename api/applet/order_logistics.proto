syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//订单物流-C端
service OrderLogistics {
  //获取物流详情信息
  rpc List(OrderLogisticsListReq) returns (OrderLogisticsListRsp) {
    option (google.api.http) = {get: "/app/v1/logistics/list"};
  }
  //获取订单最新的一条物流信息
  rpc Last(OrderLogisticsLastReq) returns (OrderLogisticsLastRsp) {
    option (google.api.http) = {get: "/app/v1/logistics/last"};
  }
}

message OrderLogisticsListReq {
  string order_number = 1;
  string logistics_no = 2;
}

message OrderLogisticsListRsp {
  message OrderLogisticsListItem {
    //订单号
    string order_number = 1;
    //快递平台编号
    string kd_code = 2;
    //快递单号
    string logistics_no = 3;
    //物流状态
    string op_desc = 4;
    //所在城市-可能为空
    string address_text = 5;
    //物流信息
    string op_message = 6;
    //物流时间
    string op_time = 7;
  }
  //同个订单有多个物流单信息，使用二维数组
  repeated OrderLogisticsListItem list = 1;
}

message OrderLogisticsLastReq {
  string order_number = 1;
}

message OrderLogisticsLastRsp {
  //订单号
  string order_number = 1;
  //快递平台编号
  string kd_code = 2;
  //快递单号
  string logistics_no = 3;
  //物流状态
  string op_desc = 4;
  //所在城市
  string address_text = 5;
  //物流信息
  string op_message = 6;
  //物流时间
  string op_time = 7;
}
