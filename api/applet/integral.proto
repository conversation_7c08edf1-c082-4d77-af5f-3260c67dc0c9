syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//积分-C端
service Integral {
  //用户下单可使用积分列表
  rpc UseList(IntegralUseListReq) returns (IntegralUseListRsp) {
    option (google.api.http) = {
      post: "/app/v1/integral/useList"
      body: "*"
    };
  }
  //积分配置
  rpc Config(IntegralConfigReq) returns (IntegralConfigRsp) {
    option (google.api.http) = {
      get: "/app/v1/integral/config"
    };
  }
  //下单页面校验用户输入积分
  rpc Calculate(IntegralCalculateReq) returns (IntegralCalculateRsp) {
    option (google.api.http) = {
      post: "/app/v1/integral/calculate"
      body: "*"
    };
  }
}

message IntegralUseListReq {
  message SkuItem {
    //商品ID
    int32 goods_id = 1;
    //sku编码
    string sku_no = 2;
    //商品数量
    int32 num = 3;
  }
  // 优惠券ID
  int32 coupon_code_id = 2;
  // 商品编码
  repeated SkuItem sku_list = 1;
  // 礼品卡编码
  repeated string card_gift_no = 3;
}

message IntegralUseListRsp {
  message UseItem {
    //使用积分
    int32 integral = 1;
    //抵扣金额
    string amount = 2;
    // 实际抵扣金额
    string actual_amount = 3;
  }
  message TipsItem {
    string tips = 1;
  }
  repeated UseItem use_list = 1;
  string  title_tips = 2;
  repeated TipsItem tips_list = 3;
}

message IntegralConfigReq {}

message IntegralConfigRsp {
  // 汇率，xx积分=1元
  int32 exchange_rate = 1;
  // 积分名称
  string name = 2;
  // 是否开启积分+钱购
  int32 integral_cash = 3;
  // 是否开启积分兑换
  int32 integral_exchange = 4;
  // 是否展示积分商城入口：1=否，2=是
  int32 integral_shop_status = 5;
}

message IntegralCalculateReq {
  message SkuItem {
    //商品ID
    int32 goods_id = 1;
    //sku编码
    string sku_no = 2;
    //商品数量
    int32 num = 3;
  }
  repeated SkuItem sku_list = 1;
  //积分
  int32 integral = 2;
  // 优惠券ID
  int32 coupon_code_id = 3;
  // 礼品卡编码
  repeated string card_gift_no = 4;
}

message IntegralCalculateRsp {
  //使用积分
  int32 integral = 1;
  //抵扣金额
  string amount = 2;
}
