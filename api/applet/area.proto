syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

// 地区-C端
service Area {
  //获取各级地区
  rpc All(AreaAllReq) returns (AreaAllRsp) {
    option (google.api.http) = {get: "/app/v1/area/all"};
  }
}

message AreaAllReq {
  int32 pid = 1;
}

message AreaAllRsp {
  message AreaListItem {
    int32 id = 1;
    string name = 2;
    int32 pid = 3;
  }
  repeated AreaListItem list = 1;
}
