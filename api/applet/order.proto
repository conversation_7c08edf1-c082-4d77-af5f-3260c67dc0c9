syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//订单-C端
service Order {
  //列表
  rpc List(OrderListReq) returns (OrderListRsp) {
    option (google.api.http) = {get: "/app/v1/order/list"};
  }
  //详情
  rpc Detail(OrderDetailReq) returns (OrderDetailRsp) {
    option (google.api.http) = {get: "/app/v1/order/detail"};
  }


  rpc OrderAddress(OrderAddressReq) returns (OrderAddressResp) {
    option (google.api.http) = {get: "/app/v1/order/orderAddress"};
  }

  rpc ModifyAddress(ModifyAddressReq) returns (ModifyAddressResp) {
    option (google.api.http) = {
      post: "/app/v1/order/modifyAddress"
      body: "*"
    };
  }

  //虚拟商品申请售后（已弃用）
  rpc AfterSale(OrderAfterSaleReq) returns (OrderAfterSaleRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale"
      body: "*"
    };
  }
  //虚拟商品售后取消（已弃用）
  rpc AfterSaleCancel(OrderAfterSaleCancelReq) returns (OrderAfterSaleCancelRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/afterSale/cancel"
      body: "*"
    };
  }

  //确认收货
  rpc Received(OrderReceivedReq) returns (OrderReceivedRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/received"
      body: "*"
    };
  }

  //待支付订单取消
  rpc UnpaidCanceled(OrderUnpaidCancelReq) returns (OrderUnpaidCancelRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/unpaid/cancel"
      body: "*"
    };
  }

  // 创建订单
  // @deprecate 前端没有调用了
  rpc Create(OrderCreateReq) returns (OrderCreateRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/create"
      body: "*"
    };
  }
  //创建订单-多商品下单
  rpc CreateNew(OrderCreateNewReq) returns (OrderCreateNewRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/createNew"
      body: "*"
    };
  }
  //创建订单-积分、积分+钱购商品下单
  rpc CreateCashAnIntegral(OrderCreateNewReq) returns (OrderCreateNewRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/creteCashAndIntegral"
      body: "*"
    };
  }
  //退款
  rpc Refund(OrderRefundReq) returns (OrderRefundRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/refund"
      body: "*"
    };
  }
  //修改订单收货地址
  rpc UpdateAddress(OrderUpdateAddressReq) returns (OrderUpdateAddressRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/updateAddress"
      body: "*"
    };
  }
  //获取下单支付金额
  rpc PayAmount(OrderPayAmountReq) returns (OrderPayAmountRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/payAmount"
      body: "*"
    };
  }
  //获取用户各状态订单数量
  rpc StatusCount(OrderStatusCountReq) returns (OrderStatusCountRsp) {
    option (google.api.http) = {get: "/app/v1/order/statusCount"};
  }

  rpc TccPay(TccPayReq) returns (TccPayRsp) {
    option (google.api.http) = {get: "/app/v1/order/pay/query"};
  }

  //获取场景订单实际支付金额
  rpc ThirdPayAmount(OrderThirdPayAmountReq) returns (OrderThirdPayAmountRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/third/payAmount"
      body: "*"
    };
  }

  //场景订单使用礼品卡抵扣订单
  rpc ThirdDiscount(OrderThirdDispountReq) returns (OrderThirdDispountRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/third/discount"
      body: "*"
    };
  }

  //上传订单站点来源
  rpc UpdateSite(UpdateOrderSiteReq) returns (UpdateOrderSiteRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/updateSite"
      body: "*"
    };
  }

  //订单详情中间页
  rpc DetailMiddlePage(OrderDetailMiddlePageReq) returns (OrderDetailMiddlePageRsp) {
    option (google.api.http) = {
      get: "/app/v1/order/detailMiddle"
    };
  }

  //秒杀创建订单
  rpc FalseSale(OrderCreateNewReq) returns (OrderCreateNewRsp) {
    option (google.api.http) = {
      post: "/app/v1/order/createFalseSale"
      body: "*"
    };
  }
}

// 请求参数
message TccPayReq {
  // 原子请求id，结构为："业务标识-业务数据id-雪花ID"，如：yum-123-123456789012345678，有新业务接入时，请联系相关负责人分配业务标识
  string request_id = 1;
}

message TccPayRsp {
  // 查询结果，1未知，2成功，3失败，当返回失败时，会回滚余额的操作
  int32 status = 1;
}

message OrderListReq {
  //前端传值:待支付=1; 待收货=2; 交易完成=3; 已取消=4; 售后订单=5;
  int32 status = 1;
  int32 page = 2;
  int32 page_size = 3;
}

message OrderListRsp {
  message TipMsg {
    //状态-文案
    string status = 2;
    //提示信息
    string msg = 3;
    //时间戳--售后中的订单，提示信息是动态倒计时，已此字段返回的时间戳为准
    int32 timestamp = 4;
  }
  message OrderGoodsItem {
    //商品ID
    int32 goods_id = 1;
    //类目ID
    int32 category_id = 2;
    //商品名称
    string goods_name = 3;
    //商品图片
    string goods_image = 4;
    //商品规格
    string goods_sku_name = 5;
    //价格
    string sale_price = 6;
    //数量
    int32 quantity = 7;
    //skuID
    int32 sku_id = 8;
    //sku编码
    string sku_no = 9;
  }
  message OrderListItem {
    //订单ID
    int32 id = 1;
    //充值账号
    string account = 2;
    //支付金额
    string pay_amount = 3;
    //订单状态:0=待支付，1=待充值，2=充值中,3=充值完成,4=充值失败,5=退款中,6=部分退款中,7=部分退款,8=已退款,9=已收货,10=售后中,11=售后完成,12=已取消,13=主订单待支付，14=主订单已取消,15=主订单已支付,20=待确认，21=供应商取消
    int32 status = 4;
    //订单状态文案
    string status_text = 5;
    //下单时间
    string create_time = 6;
    //订单类型:1=直充,2=卡密
    int32 order_type = 7;
    //订单号
    string order_number = 8;
    //订单商品
    repeated OrderGoodsItem goods_list = 9;
    //订单是否可确认收货
    bool can_received = 10;
    //额外信息,比如 物流信息、退款售后信息等
    TipMsg tip_msg = 11;
    //订单商品总数
    int32 num = 12;
    //发货状态 0-未发货 1-部分发货 2-全部发货
    int32 deliver_status = 13;
    // 售后状态 0-无售后 1-售后中 2-售后完成 3-售后关闭
    int32 after_sale_status = 14;
    //所有商品都已经售后了 0/1
    int32 after_sale_all = 15;
    //源订单号
    string original_order_no = 16;
    // 可申请换货
    bool can_after_sale_exchange = 17;
    // 可申请退款 / 退货退款
    bool can_after_sale_refund = 18;
    // 正在售后中
    bool after_sale_is_dealing = 19;
    // 能申请全部 仅退款/退货退款
    bool can_after_sale_refund_all = 20;
    // 订单扩展类型
    int32 ext_type = 21;
    // 使用积分
    int32 pay_integral = 22;
    // afterSaleOne 售后单
    bool afterSaleOnlyOne = 23;
  }
  repeated OrderListItem list = 1;
  int32 page = 2;
  int32 count = 3;
}

message OrderDetailReq {
  //订单ID
  int32 order_id = 1;
  //订单号
  string order_number = 2;
  // 支付单ID
  int32 pay_order_id = 3;
}

message OrderDetailRsp {
  message AfterSale {
    //数据ID
    int32 id = 1;
    //订单ID
    int32 order_id = 2;
    //订单号
    string order_number = 3;
    //申请原因
    string reason = 4;
    //图片
    repeated string images = 5;
  }
  message OrderGoodsItem {
    //商品ID
    int32 goods_id = 1;
    //类目ID
    int32 category_id = 2;
    //商品名称
    string goods_name = 3;
    //商品图片
    string goods_image = 4;
    //商品规格
    string goods_sku_name = 5;
    //价格
    string sale_price = 6;
    //数量
    int32 quantity = 7;
    //skuID
    int32 sku_id = 8;
    //sku编码
    string sku_no = 9;
    // 支付积分
    int32 pay_integral = 10;
    // 原价
    string origin_price = 11;
  }
  message UserAddress {
    //收货人
    string name = 1;
    //收货人电话
    string phone = 2;
    //收货地址
    string address = 3;
  }
  message TipMsg {
    //提示信息
    string msg = 1;
    //时间戳--售后中的订单，提示信息是动态倒计时，已此字段返回的时间戳为准
    int32 timestamp = 4;
  }
  message Refund {
    //退款金额
    string amount = 1;
    //退款时间
    string refund_time = 2;
    //退款单号
    string refund_No = 3;
  }
  message AlipayMiniOrderInfo {
    //交易组件订单号
    string mini_order_no = 1;
    //使用积分
    int32 integral = 2;
  }
  //订单ID
  int32 id = 1;
  //订单号
  string order_number = 2;
  //充值账号
  string account = 3;
  //积分抵扣金额
  string pay_integral_amount = 4;
  //实付金额
  string pay_amount = 5;
  //下单时间
  string create_time = 6;
  //订单状态:0=待支付，1=待充值，2=充值中,3=充值完成,4=充值失败,5=退款中,6=部分退款中,7=部分退款,8=已退款,9=已收货,10=售后中,11=售后完成,12=已取消,13=主订单待支付，14=主订单已取消,15=主订单已支付
  int32 status = 7;
  //卡密
  string card_code = 8;
  //卡密账号
  string card_account = 9;
  //卡密有效期
  string card_expire_time = 10;
  //订单类型:1=直充,2=卡密，3=美团，4=千猪电影票，5=千猪肯德基
  int32 order_type = 11;
  //售后信息
  AfterSale after_sale = 12;
  //卡密使用须知
  string card_goods_detail = 13;
  //优惠券优惠金额
  string coupon_discount_amount = 14;
  //订单总金额
  string total_amount = 15;
  //美团待支付订单有效期,orderType=3时有效
  string expire_time = 16;
  //美团待支付订单支付成功后的同步跳转地址,orderType=3时有效
  string return_url = 17;
  //发货时间
  string deliver_time = 18;
  //支付时间
  string pay_time = 19;
  //订单商品
  repeated OrderGoodsItem goods_list = 20;
  //收货地址
  UserAddress user_address = 21;
  //额外信息,比如 物流信息、退款售后信息等
  TipMsg tip_msg = 22;
  //总发货包裹数量
  int32 deliver_count = 23;
  //退款信息
  Refund refund = 24;
  //发货状态 0-未发货 1-部分发货 2-全部发货
  int32 deliver_status = 25;
  // 订单状态文案
  string status_text = 26;
  // 售后状态 0-无售后 1-售后中 2-售后完成 3-售后关闭
  int32 after_sale_status = 27;
  //所有商品都已经售后了 0/1
  int32 after_sale_all = 28;
  //源订单号
  string original_order_no = 29;
  // 可申请换货
  bool can_after_sale_exchange = 30;
  // 可申请退款 / 退货退款
  bool can_after_sale_refund = 31;
  // 正在售后中
  bool after_sale_is_dealing = 32;
  // 支付宝交易组件订单信息
  AlipayMiniOrderInfo alipay_mini_order_info = 33;
  // 能申请全部 仅退款/退货退款
  bool can_after_sale_refund_all = 34;
  // 订单扩展类型
  int32 ext_type = 35;
  CardBatchCouponInfo cardBatchCouponInfo = 36;
  // 运费
  double freight_fee = 37;
  // 商品总金额
  double goods_amount = 38;
  // 支付积分
  int32 pay_integral = 39;

  message CardBatchCouponInfo {
    // 卡券批次名称,
    string card_batch_name = 3;
    //券码/兑换码
    string card_coupon_number = 2;
  }

  message CardGiftInfo {
    // 总抵扣金额
    double total_amount = 1;
    // 商品抵扣金额
    double goods_amount = 2;
    // 运费抵扣金额
    double freight_fee_amount = 3;
    // 退款金额
    double total_refund_amount = 4;
    // 商品退款金额
    double goods_refund_amount = 5;
    // 运费退款金额
    double freight_fee_refund_amount = 6;
  }

  CardGiftInfo card_gift_info = 41;

  bool afterSaleOnlyOne = 42;

}

message OrderAddressReq {
  //订单ID
  string order_no = 1;
}
message OrderAddressResp {
  //收货人
  string name = 2;
  //收货人电话
  string phone = 3;
  //省一级ID
  int32 province_id = 4;
  //省一级名称
  string province = 5;
  //市一级ID
  int32 city_id = 6;
  //市一级名称
  string city = 7;
  //区一级ID
  int32 region_id = 8;
  //区一级名称
  string region = 9;
  //区域
  string area = 10;
  //详细地址
  string detail = 11;
}

message ModifyAddressReq {
  //订单no
  string order_no = 1;
  // 收货人
  string name = 3;
  // 收货人电话
  string phone = 4;
  // 省一级ID
  int32 province_id = 5;
  // 市一级ID
  int32 city_id = 6;
  // 区一级ID
  int32 region_id = 7;
  // 省一级名称
  string province = 8;
  // 市一级名称
  string city = 9;
  // 区一级名称
  string region = 10;
  // 地址详情
  string detail = 11;
}
message ModifyAddressResp{}


message OrderAfterSaleReq {
  //订单ID
  int32 order_id = 1;
  //申请售后原因
  string reason = 2;
  //上传图片
  repeated string images = 3;
}

message OrderAfterSaleRsp {}

message OrderAfterSaleCancelReq {
  // 订单ID
  int32 order_id = 1;
}

message OrderAfterSaleCancelRsp {
  int32 effect_row = 1;
}

message OrderReceivedReq {
  //订单ID
  int32 order_id = 1;
}

message OrderReceivedRsp {
  int32 effect_row = 1;
  //状态码 200=成功，其他=失败
  int32 code = 2;
  //状态信息
  string msg = 3;
}

message OrderUnpaidListReq {
  int32 page = 1;
  int32 page_size = 2;
}

message OrderUnpaidCancelReq {
  //订单号
  string order_number = 1;
  bool expired = 2;
}

message OrderUnpaidCancelRsp {
  int32 effect_row = 1;
}

message OrderCreateReq {
  //商品ID
  int32 goods_id = 1;
  //购买数量
  int32 num = 2;
  //优惠券ID
  int32 coupon_code_id = 3;
  //抵扣积分
  int32 integral = 4;
  //充值账号
  string account = 5;
  //站点唯一标识
  string site_unique_str = 6;
}

message OrderCreateRsp {
  //订单号
  string order_number = 1;
  //支付金额
  string pay_amount = 2;
  //订单创建时间
  string create_time = 3;
}

message OrderCreateNewReq {
  message Goods {
    //商品ID
    int32 goods_id = 1;
    //sku编码
    string sku_no = 2;
    //购买数量
    int32 num = 3;
  }
  message Address {
    //收货人
    string name = 1;
    //收货人电话
    string phone_number = 2;
    //收货地址  用-连接，如： 广东省-广州市-天河区-黄埔大道西xxxx
    string address = 3;
  }
  message AlipayParams {
    // 支付宝订单跟踪ID 支付宝积分商城支付时必传
    string source_id = 1;
    // 优惠前置咨询组件返回的优惠活动咨询ID
    string activity_consult_id = 2;
    // 收货地址
    Address address_info = 3;
    // 抵扣积分
    int32 integral = 4;
  }
  repeated Goods goods = 1;
  //优惠券ID
  int32 coupon_code_id = 3;
  //抵扣积分
  int32 integral = 4;
  //充值账号
  string account = 5;
  //站点唯一标识
  string site_unique_str = 6;
  //收货地址ID
  int32 address_id = 7;
  //下单来源：1=购物车，2=商品详情页，3=订单再次购买
  int32 source = 8;
  //支付宝积分商城订单预创建参数
  AlipayParams alipay_params = 9;
  //礼品卡编号
  repeated string gift_card_no = 10;
  //活动ID
  int32 activity_id = 11;
}

message OrderCreateNewRsp {
  //订单号
  string order_number = 1;
  //支付金额
  string pay_amount = 2;
  //订单创建时间
  string create_time = 3;
  //支付单ID
  int32 pay_order_id = 4;
  // 订单状态：1=待支付,2=已支付，等于2时无需再次请求支付接口
  int32 status = 5;
  //  支付单支付地址
  string pay_url = 6;
}

message OrderRefundReq {
  //订单ID
  string order_no = 1;
}

message OrderRefundRsp {}

message OrderUpdateAddressReq {
  //订单ID
  int32 order_id = 1;
  //用户收货地址ID
  int32 address_id = 2;
}

message OrderUpdateAddressRsp {
  int32 effect_row = 1;
}

message OrderPayAmountReq {
  message Goods {
    //商品ID
    int32 goods_id = 1;
    //sku编码
    string sku_no = 2;
    //购买数量
    int32 num = 3;
  }
  repeated Goods goods = 1;
  //优惠券ID
  int32 coupon_code_id = 3;
  //抵扣积分
  int32 integral = 4;
  //收货地址ID
  int32 address_id = 7;
  //礼品卡编号
  repeated string gift_card_no = 8;
  //活动ID
  int32 activity_id = 9;
}

message OrderPayAmountRsp {
  // 支付金额
  string pay_amount = 1;
  // 优惠券抵扣金额
  string coupon_discount_amount = 2;
  // 积分抵扣金额
  string integral_discount_amount = 3;
  // 运费
  string freight_amount = 4;
  // 总金额
  string total_amount = 5;
  // 积分+钱购商品所需积分
  int32 integral = 6;
  // 总优惠金额
  string total_discount_amount = 7;
  // 礼品卡优惠金额
  string gift_card_discount_amount = 8;
}

message OrderStatusCountReq {}

message OrderStatusCountRsp {
  message Count {
    // 订单状态 待支付=1; 待收货=2; 售后订单=5;
    int32 status = 1;
    // 订单数量
    int32 count = 2;
  }
  repeated Count count = 1;
}

message OrderThirdPayAmountReq {
  //支付单号
  string pay_order_number = 1;
  //礼品卡号
  repeated string  card_gift_no = 2;
}

message OrderThirdPayAmountRsp {
  //订单总金额
  double total_amount = 1;
  //支付金额
  double pay_amount = 2;
  //礼品卡优惠金额
  double card_gift_discount = 3;
}

message OrderThirdDispountReq {
  //支付单号
  string pay_order_number = 1;
  //礼品卡号
  repeated string  card_gift_no = 2;
}

message OrderThirdDispountRsp {
  // 订单号
  string order_number = 1;
  // 支付金额
  double pay_amount   = 2;
  // 订单创建时间
  int32 create_time   = 3;
  // 支付单ID
  int32 pay_order_id  = 4;

  // 订单类型 1 = 直充， 2=卡密，3= 美团外卖，4=千猪电影票，5=千猪肯德基， 6=实物
  int32 order_type    = 5;
  //订单状态 1 = 待支付， 2= 已支付， 3 = 退款中， 4 = 全额退款， 5 = 部分退款， 6 = 已取消
  int32 status = 6;
}

message UpdateOrderSiteReq {
  //订单号
  string order_number = 1;
  //站点唯一标识
  string site_unique_str = 2;
}

message UpdateOrderSiteRsp {}

message OrderDetailMiddlePageReq {
  string pay_serial_no = 1;
}

message OrderDetailMiddlePageRsp {
  message SubOrder {
    message Goods {
      string goods_name = 1;
      int32 quantity = 2;
      string sale_price = 3;
      string goods_image = 4;
    }
    string order_number = 1;
    repeated Goods goods = 2;
    int32 order_id = 3;
  }
  string order_number = 1;
  int32 status = 2;
  string create_time = 3;
  int32 pay_type = 4;
  string pay_time = 5;
  string total_amount = 6;
  string freight_fee = 7;
  string pay_amount = 8;
  string goods_pay_amount = 9;
  repeated SubOrder sub_order = 10;
}
