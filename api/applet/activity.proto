syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//活动
service Activity {
  //获取活动详情
  rpc Detail(ActivityReq) returns (ActivityRsp) {
    option (google.api.http) = {
      get: "/app/v1/activity"
    };
  }
  //获取活动商品详情token
  rpc SkuDetailAccess(ActivitySkuDetailAccessReq) returns (ActivitySkuDetailAccessRsp) {
    option (google.api.http) = {
      get: "/app/v1/activity/skuDetail/access"
    };
  }
}

message ActivityReq {
  int32 id = 1;
}

message ActivityRsp {
  int32 id = 1;
  string name = 2;
  string banner = 3;
  string background = 4;
  //1=活动页，2=秒杀
  int32 activity_type = 5;
  message FlashSaleTime {
    //开始时间
    int32 start_time = 1;
    //结束时间
    int32 end_time = 2;
    //距离开始时间-毫秒
    int32 distance_start = 3;
    //距离结束时间-毫秒
    int32 distance_end = 4;
    //是否默认当前默认时间段
    bool is_default = 5;
  }
  repeated FlashSaleTime flash_sale_time = 6;
}

message ActivitySkuDetailAccessReq {
  //活动ID
  int32 id = 1;
}

message ActivitySkuDetailAccessRsp {
  //是否成功
  bool success = 1;
  //失败原因
  string fail_reason = 2;
}
