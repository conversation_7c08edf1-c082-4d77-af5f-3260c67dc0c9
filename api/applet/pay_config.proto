syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//支付配置-C端
service PayConfig {
  //支付方式
  rpc All(PayConfigAllReq) returns (PayConfigAllRsp) {
    option (google.api.http) = {get: "/app/v1/pay/channel"};
  }
}

message PayConfigAllReq {}

message PayConfigAllRsp {
  //可使用支付方式:1=支付宝,2=微信
  repeated int32 channel = 1;
}
