syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";
import "validate/validate.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//优惠券-C端
service Coupon {
  //领券中心
  rpc List(CouponListReq) returns (CouponListRsp) {
    option (google.api.http) = {get: "/app/v1/coupon/list"};
  }
  //优惠券领取
  rpc Collection(CouponCollectionReq) returns (CouponCollectionRsp) {
    option (google.api.http) = {
      post: "/app/v1/coupon/collection"
      body: "*"
    };
  }
  //我的优惠券
  rpc My(CouponMyReq) returns (CouponMyRsp) {
    option (google.api.http) = {get: "/app/v1/coupon/my"};
  }
  //下单可用优惠券
  rpc UseAble(CouponUseAbleReq) returns (CouponUseAbleRsp) {
    option (google.api.http) = {
      post: "/app/v1/coupon/useAble"
      body: "*"
    };
  }
}

message CouponListReq {
  int32 page = 1;
  int32 page_size = 2;
}

message CouponListRsp {
  message CouponListItem {
    //优惠券活动ID
    int32 id = 1;
    //标题
    string title = 2;
    //说明
    string remark = 3;
    //生效时间类型：1=时间段，2=领取后xx天有效
    int32 effect_time_type = 4;
    //生效起始时间
    string effect_start_time = 5;
    //生效结束时间
    string effect_end_time = 6;
    //领取后生效起始天数
    int32 effect_start_day = 7;
    //领取后生效结束天数
    int32 effect_end_day = 8;
    //使用限制:满xx可使用
    string limit_amount = 9;
    //优惠金额
    string discount_amount = 10;
    //限领数量，0=不限
    int32 collection_quantity = 11;
  }
  repeated CouponListItem list = 1;
  int32 page_size = 2;
  int32 count = 3;
}

message CouponCollectionReq {
  //优惠活动ID
  int32 id = 1;
}

message CouponCollectionRsp {}

message CouponMyReq {
  //状态:1=未使用,2=已使用,3=已失效
  int32 status = 1;
  //商品ID，上传此参数标识获取某商品可用优惠券
  int32 goods_id = 2;
  //商品购买数量
  int32 num = 3;
}

message CouponMyRsp {
  message CouponMyItem {
    //优惠券ID
    int32 coupon_code_id = 1;
    //优惠活动ID
    int32 coupon_id = 2;
    //优惠金额
    string discount_amount = 3;
    //优惠活动标题
    string title = 4;
    //使用时间-起始
    string effect_start_time = 5;
    //使用时间-结束
    string effect_end_time = 6;
    //使用说明
    string remark = 7;
    //状态:1=未领取，2=已领取，3=已使用，4=使用中,5=已作废
    int32 status = 8;
    //使用限制:满xx可使用
    string limit_amount = 9;
    //是否可用，请求参数goods_id不为0时有效
    bool enable = 10;
    //不可使用类型:1=不再使用有效期，2=该商品不可使用此类优惠券，3=金额不满足满减要求
    int32 disable_type = 11;
  }
  repeated CouponMyItem data = 1;
}

message CouponUseAbleReq {
  message Sku {
    //商品Sku编码
    string sku_no = 1;
    //购买数量
    int32 quantity = 2;
  }
  //下单的商品SKU
  repeated Sku sku = 1;
  // 礼品卡编码
  repeated string card_gift_no = 2;
}

message CouponUseAbleRsp {
  message CouponItem {
    //优惠券ID
    int32 coupon_code_id = 1;
    //优惠金额
    string discount_amount = 2;
    //优惠活动标题
    string title = 3;
    //使用时间-起始
    string effect_start_time = 4;
    //使用时间-结束
    string effect_end_time = 5;
    //使用说明
    string remark = 6;
    //使用限制:满xx可使用
    string limit_amount = 7;
    //是否可用
    bool enable = 8;
    //不可使用类型:1=不再使用有效期，2=该商品不可使用此类优惠券，3=金额不满足满减要求,4=优惠券不是待使用状态
    int32 disable_type = 9;
  }
  repeated CouponItem data = 1;
}
