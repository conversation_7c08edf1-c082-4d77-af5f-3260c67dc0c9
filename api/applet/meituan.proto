syntax = "proto3";

package api.applet;

import "google/api/annotations.proto";

option go_package = "cardMall/api/applet;applet";
option java_multiple_files = true;
option java_package = "api.applet";

//获取美团链接-C端
service MeiTuan {
  //获取美团跳转url
  rpc GetLink(MeiTuanLinkReq) returns (MeiTuanLinkRsp) {
    option (google.api.http) = {get: "/app/v1/meiTuan/quickLink"};
  }
  //获取美团订单列表跳转url
  rpc GetOrderLink(MeiTuanOrderLinkReq) returns (MeiTuanOrderLinkRsp) {
    option (google.api.http) = {get: "/app/v1/meiTuan/orderLink"};
  }
  //获取美团订单详情跳转url
  rpc GetOrderDetailLink(MeiTuanOrderDetailLinkReq) returns (MeiTuanOrderDetailLinkRsp) {
    option (google.api.http) = {get: "/app/v1/meiTuan/orderDetailLink"};
  }

  //上传美团订单站点来源
  rpc UpdateOrderSite(MeiTuanUpdateOrderSiteReq) returns (MeiTuanUpdateOrderSiteRsp) {
    option (google.api.http) = {
      post: "/app/v1/meiTuan/updateOrderSite"
      body: "*"
    };
  }
}

message MeiTuanLinkReq {}

message MeiTuanLinkRsp {
  //跳转地址
  string url = 1;
}

message MeiTuanOrderLinkReq {}

message MeiTuanOrderLinkRsp {
  //跳转地址
  string url = 1;
}

message MeiTuanOrderDetailLinkReq {
  //订单号
  string order_number = 1;
}

message MeiTuanOrderDetailLinkRsp {
  //跳转地址
  string url = 1;
}

message MeiTuanUpdateOrderSiteReq {
  //订单号
  string order_number = 1;
  //站点唯一标识
  string site_unique_str = 2;
}

message MeiTuanUpdateOrderSiteRsp {}
